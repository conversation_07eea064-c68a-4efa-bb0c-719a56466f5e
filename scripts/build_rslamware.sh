#!/bin/bash 
SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

# Function to show help
show_help() {
    echo "Usage: $0 [debug|help]"
    echo ""
    echo "Options:"
    echo "  debug    Build with debug information (RelWithDebInfo)"
    echo "  help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0           # Normal build"
    echo "  $0 debug     # Build with debug information"
}

# Check for parameters
CMAKE_ARGS=""
case "$1" in
    "debug")
        CMAKE_ARGS="--cmake-args -DCMAKE_BUILD_TYPE=Debug"
        echo "Building with debug configuration..."
        ;;
    "with-symbol")
        CMAKE_ARGS="--cmake-args -DCMAKE_BUILD_TYPE=RelWithDebInfo"
        echo "Building release with debug information (RelWithDebInfo)..."
        ;;
    "help"|"-h"|"--help")
        show_help
        exit 0
        ;;
    "")
        echo "Building with default configuration (Release)..."
        ;;
    *)
        echo "Unknown parameter: $1"
        show_help
        exit 1
        ;;
esac
 
if [ -e "$RSLAMWARE_ROOT/install/setup.bash" ]; then
    source "$RSLAMWARE_ROOT/install/setup.bash"
fi

colcon build --parallel-workers 2 $CMAKE_ARGS
