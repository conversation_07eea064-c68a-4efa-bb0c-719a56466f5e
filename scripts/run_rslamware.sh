#!/bin/bash

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

mapping=True
use_als=False
use_cartographer_localization=True
explore=False
scan_topic="fusion_scan"
configuration_file="mapping_2d.lua"

for arg in "$@"
do
    if [ "$arg" == "mapping" ]; then
        mapping=True
    fi
    if [ "$arg" == "use_als" ]; then
        use_als=True
        use_cartographer_localization=False
    fi
    if [ "$arg" == "use_cartographer_localization" ]; then
        use_cartographer_localization=True
        use_als=False
    fi
    if [ "$arg" == "explore" ]; then
        explore=True
    fi
done

source "$RSLAMWARE_ROOT/install/setup.bash"

ros2 launch rslamware_bringup rslamware.navigation.launch.py mode:=real scan_topic:=$scan_topic

