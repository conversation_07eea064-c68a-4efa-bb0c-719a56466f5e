#!/bin/bash

# RSlamware Encryption Build Script
# This script builds all packages, prepares the encryption archive, and encrypts the files

set -e  # Exit on any error

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

echo "=========================================="
echo "RSlamware Encryption Build Script"
echo "=========================================="
echo "Workspace: $RSLAMWARE_ROOT"
echo ""

# Function to print colored output
print_status() {
    echo -e "\033[1;32m[INFO]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

# Check if we're in the correct directory
if [ ! -f "$RSLAMWARE_ROOT/src/rslamware_encryption_run/CMakeLists.txt" ]; then
    print_error "rslamware_encryption_run package not found!"
    print_error "Please make sure you're running this script from the correct workspace."
    exit 1
fi

# Step 1: Clean previous build
print_status "Step 1: Cleaning previous build..., skipping..."
cd "$RSLAMWARE_ROOT"

print_status "Cleaning previous build/install/log directories, skipping..."
# if [ -d "build" ]; then
#     print_status "Removing existing build directory..."
#     rm -rf build 2>/dev/null || print_warning "Could not remove build directory, continuing..."
# fi

# if [ -d "install" ]; then
#     print_status "Removing existing install directory..."
#     rm -rf install 2>/dev/null || print_warning "Could not remove install directory, continuing..."
# fi

# if [ -d "log" ]; then
#     print_status "Removing existing log directory..."
#     rm -rf log 2>/dev/null || print_warning "Could not remove log directory, continuing..."
# fi

# Clean up any existing encryption artifacts
if [ -f "$RSLAMWARE_ROOT/install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware.tar.gz" ]; then
    print_status "Removing existing rslamware.tar.gz..."
    rm -f "$RSLAMWARE_ROOT/install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware.tar.gz"
fi

if [ -f "$RSLAMWARE_ROOT/install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware.enc" ]; then
    print_status "Removing existing rslamware.enc..."
    rm -f "$RSLAMWARE_ROOT/install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware.enc"
fi

if [ -d "launch" ]; then
    print_status "Removing existing launch directory..."
    rm -rf launch
fi

if [ -d "config" ]; then
    print_status "Removing existing config directory..."
    rm -rf config
fi

if [ -d "rslamware_encryption_backup" ]; then
    print_status "Removing existing backup directory..."
    rm -rf rslamware_encryption_backup
fi

print_status "Clean completed."
echo ""

# Step 2: Source ROS2 environment
print_status "Step 2: Sourcing ROS2 environment..."
if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
    print_status "ROS2 Humble environment sourced."
else
    print_error "ROS2 Humble not found! Please install ROS2 Humble."
    exit 1
fi
echo ""

# Step 3: Build all packages with release mode
print_status "Step 3: Building all packages in release mode..."
cd "$RSLAMWARE_ROOT"

# Use colcon build with release mode and parallel jobs
colcon build --cmake-args -DCMAKE_BUILD_TYPE=Release --parallel-workers $(nproc)

if [ $? -ne 0 ]; then
    print_error "Build failed!"
    exit 1
fi

print_status "Build completed successfully."
echo ""

# Step 4: Source the built workspace
print_status "Step 4: Sourcing built workspace..."
source "$RSLAMWARE_ROOT/install/setup.bash"
print_status "Workspace sourced."
echo ""

# Step 5: Run the preparation script
print_status "Step 5: Running rslamware_encryption_prepare.py..., use --delete-configs to delete orignal files if needed"

# Make the Python script executable
# chmod +x "$RSLAMWARE_ROOT/install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware_encryption_prepare.py"

# Run the preparation script
python3 "$RSLAMWARE_ROOT/src/rslamware_encryption_run/src/rslamware_encryption_prepare.py"

if [ $? -ne 0 ]; then
    print_error "Preparation script failed!"
    exit 1
fi

print_status "Preparation completed successfully."
echo ""

# Step 6: Check if rslamware.tar.gz was created
if [ ! -f "$RSLAMWARE_ROOT/install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware.tar.gz" ]; then
    print_error "rslamware.tar.gz was not created by the preparation script!"
    exit 1
fi

print_status "rslamware.tar.gz found."
echo ""

# Step 7: Run the encryption
print_status "Step 7: Encrypting rslamware.tar.gz..."

cd "$RSLAMWARE_ROOT"

# Run the encryption tool
"$RSLAMWARE_ROOT/install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware_encryption_run" --encrypt

if [ $? -ne 0 ]; then
    print_error "Encryption failed!"
    exit 1
fi

print_status "Encryption completed successfully."
echo ""

# Step 8: Verify the encrypted file was created
if [ ! -f "$RSLAMWARE_ROOT/install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware.enc" ]; then
    print_error "rslamware.enc was not created!"
    exit 1
fi

print_status "rslamware.enc created successfully."
echo ""

# Step 9: Display summary
print_status "=========================================="
print_status "Build and Encryption Summary"
print_status "=========================================="
print_status "✓ All packages built successfully"
print_status "✓ Launch and config files prepared"
print_status "✓ rslamware.tar.gz created and encrypted"
print_status "✓ rslamware.enc ready for deployment"
print_status ""
print_status "Files created:"
print_status "  - rslamware.enc (encrypted archive)"
print_status "  - rslamware_encryption_backup/ (backup directory)"
print_status ""
print_status "Original launch and config files have been removed from install directories."
print_status "Use run_rslamware_with_encryption.sh to execute the encrypted system."
print_status "=========================================="

echo ""
print_status "Build process completed successfully!"
