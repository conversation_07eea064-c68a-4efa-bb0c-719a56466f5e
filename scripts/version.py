import os
import platform
import sys
import getopt
import subprocess
import time
import argparse
import shutil
import json

RELEASE_ROOT = os.path.abspath(os.path.dirname(__file__)) + "/../"

def check_env(versionFile):
    versionFile = RELEASE_ROOT + versionFile
    if not os.path.exists(versionFile):
        print (versionFile, " not found")
        exit -1
    return versionFile

def get_last_commit(srcPath):
    os.chdir(srcPath)
    cmd = "git rev-parse --short HEAD"
    commit = run_cmd(cmd)
    os.chdir(RELEASE_ROOT)
    return commit

def get_module(versionFile):
    f = open(versionFile, "r")
    for line in f.readlines():
        if line.find("define") > 0:
            line = line[line.find("define") + 6:]
            module = line[:line.find("_VERSION")]
            break
    f.close()
    print (module)
    return module

def setversion(fullVersion, versionFile, module):
    longVersion = fullVersion.split('-')[0]
    suffix = fullVersion.split('-')[1]
    major = longVersion.split('.')[0]
    minor = longVersion.split('.')[1]
    revision = longVersion.split('.')[2]
    shortVersion = major + '.' + minor
    commit = get_last_commit(RELEASE_ROOT)
    f = open(versionFile, "w")
    f.write("#pragma once\n\n")
    f.write("#define " + module + "_VERSION_MAJOR       " + major + "\n")
    f.write("#define " + module + "_VERSION_MINOR       " + minor + "\n")
    f.write("#define " + module + "_VERSION_REVISION    " + revision + "\n")
    f.write("#define " + module + "_VERSION_SUFFIX      \"" + suffix + "\"\n")
    f.write("#define " + module + "_VERSION_SHORT       \"" + shortVersion + "\"\n")
    f.write("#define " + module + "_VERSION_LONG        \"" + longVersion + "\"\n")
    f.write("#define " + module + "_VERSION_FULL        \"" + fullVersion + "\"\n")
    f.write("#define " + module + "_COMMIT              \"commit:" + commit + "\"\n")
    f.close()

def run_cmd(cmd):
    result_str=""
    process = subprocess.Popen(cmd, shell=True,
                stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    result_f = process.stdout
    error_f = process.stderr
    errors = error_f.read()
    if errors: pass
    result_str = result_f.read().strip()
    if result_f:
        result_f.close()
    if error_f:
        error_f.close()
    return bytes.decode(result_str)

def generate_conanfile(rulesPath, state):
    pkg_user = state
    requirement = "scripts/requirement/"
    f = open(rulesPath, "r")
    rules = json.loads(f.read())
    f.close()

    conanfile_sw = requirement + "conanfile_sw.txt"
    conanfile_refresh_sw = requirement + "conanfile_sw_new.txt"
    fsw = open(conanfile_sw, "r")
    fsw_generate = open(conanfile_refresh_sw, "w")

    for line in fsw.readlines():
        if line.find("sdp/") > -1:
            line = "sdp/" + rules["sdp"]["version"] + "-" + rules["sdp"]["commit"] + "@" +  pkg_user + "/" + rules["sdp"]["tag"].replace('/','-') + "\n"
        if line.find("agent/") > -1:
            line = "agent/" + rules["agent"]["version"] + "-" + rules["agent"]["commit"] + "@" + pkg_user + "/" + rules["agent"]["tag"].replace('/','-') + "\n"
        fsw_generate.writelines(line)
    fsw.close()
    fsw_generate.close()
    os.rename(conanfile_refresh_sw, conanfile_sw)

    conanfile_fw = requirement + "conanfile_fw.txt"
    conanfile_refresh_fw = requirement + "conanfile_fw_new.txt"
    ffw = open(conanfile_fw, "r")
    ffw_generate = open(conanfile_refresh_fw, "w")
    for line in ffw.readlines():
        if line.find("cp0-fw/") > -1:
            line = "cp0-fw/" + rules["cp0"]["version"] + "-" + rules["cp0"]["commit"] + "@" + pkg_user + "/" + rules["cp0"]["tag"].replace('/','-') + "\n"
        if line.find("base-fw/") > -1:
            line = "base-fw/" + rules["base"]["version"] + "-" + rules["base"]["commit"] + "@" + pkg_user + "/" + rules["base"]["tag"].replace('/','-') + "\n"
        if line.find("zeus_apollo-fw") > -1:
            line = "zeus_apollo-fw/" + rules["zeus_apollo"]["version"] + "-" + rules["zeus_apollo"]["commit"] + "@" + pkg_user + "/" + rules["zeus_apollo"]["tag"].replace('/','-') + "\n"
        ffw_generate.writelines(line)
    ffw.close()
    ffw_generate.close()
    os.rename(conanfile_refresh_fw, conanfile_fw)

def main(argv):
    parser = argparse.ArgumentParser(description='version manager')
    subparsers = parser.add_subparsers(help='sub-command help', dest='command')
    # add the parser for the "set version" command
    parser_set = subparsers.add_parser('set', help='setup conan env')
    parser_generate = subparsers.add_parser('generate', help='setup conan env')
    # set version
    parser_set.add_argument('-v', '--version', dest='full_version', action='store', required=True, help='set full version')
    parser_set.add_argument('-f', '--filepath', dest='version_file', action='store', required=True, help='set version file path')
    # generate conanfile
    parser_generate.add_argument('-f', '--filepath', dest='rules_file', action='store', required=True, help='set rules file path')
    parser_generate.add_argument('-s', '--state', dest='state', action='store', required=True, help='set state')

    args = parser.parse_args()
    if args.command == 'set':
        versionFile = check_env(args.version_file)
        module = get_module(versionFile)
        setversion(args.full_version, versionFile, module)
    if args.command == 'generate':  
        generate_conanfile(args.rules_file, args.state)
    else:
        parser.print_help()

if __name__ == "__main__":
    main(sys.argv[1:])