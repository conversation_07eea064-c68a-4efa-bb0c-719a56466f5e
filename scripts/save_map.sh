#!/bin/bash

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

mapfile="/opt/rslamware_data/maps/map" 
mapfile_pbstream="/opt/rslamware_data/maps/map.pbstream"

source "$RSLAMWARE_ROOT/install/setup.bash"

ros2 run nav2_map_server map_saver_cli --free 0.196 -f ${mapfile}
ros2 service call /write_state cartographer_ros_msgs/srv/WriteState "{filename: '${mapfile_pbstream}'}"
