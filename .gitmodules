[submodule "src/driver/ascamera"]
	path = src/driver/ascamera
	url = https://git.eng.slamtec.com/slamware/drivers/ascamera.git
[submodule "src/robot_monitor"]
	path = src/robot_monitor
	url = https://git.eng.slamtec.com/slamware/ros/robot_monitor.git
[submodule "src/tools/data_recorder"]
	path = src/tools/data_recorder
	url = https://git.eng.slamtec.com/slamware/tools/data_recorder.git
[submodule "src/data_fusion"]
	path = src/data_fusion
	url = https://git.eng.slamtec.com/slamware/data_fusion.git
[submodule "src/robot_health"]
	path = src/robot_health
	url = https://git.eng.slamtec.com/slamware/ros/robot_health.git
[submodule "src/interfaces"]
	path = src/interfaces
	url = https://git.eng.slamtec.com/slamware/ros/interfaces.git
[submodule "src/navigation"]
	path = src/navigation
	url = https://git.eng.slamtec.com/slamware/ros/navigation.git
[submodule "src/mapping"]
	path = src/mapping
	url = https://git.eng.slamtec.com/slamware/ros/mapping.git
[submodule "src/localization"]
	path = src/localization
	url = https://git.eng.slamtec.com/slamware/ros/localization.git
[submodule "3rdparty/behavior_tree"]
	path = 3rdparty/behavior_tree
	url = https://git.eng.slamtec.com/3rdparty/behavior_tree.git
[submodule "src/driver/rplidar"]
	path = src/driver/rplidar
	url = https://git.eng.slamtec.com/lidar/rplidar_ros.git
[submodule "src/simulator"]
	path = src/simulator
	url = https://git.eng.slamtec.com/slamware/ros/simulator.git
[submodule "src/tools/calibration"]
	path = src/tools/calibration
	url = https://git.eng.slamtec.com/slamware/tools/calibration.git
[submodule "src/sensor_processing/laser_filter"]
	path = src/sensor_processing/laser_filter
	url = https://git.eng.slamtec.com/slamware/ros/laser_filter.git
[submodule "src/rslamware_rviz_plugins"]
	path = src/rslamware_rviz_plugins
	url = https://git.eng.slamtec.com/slamware/ros/rslamware_rviz_plugins.git
[submodule "src/tools/robostudio2"]
	path = src/tools/robostudio2
	url = https://git.eng.slamtec.com/slamware/robostudio2.git
[submodule "src/driver/sl_vcu_all"]
	path = src/driver/sl_vcu_all
	url = https://git.eng.slamtec.com/robots/vcu/sl_vcu_all.git
[submodule "src/agent"]
	path = src/agent
	url = https://git.eng.slamtec.com/slamware/ros/agent.git
[submodule "src/algorithm_utils"]
	path = src/algorithm_utils
	url = https://git.eng.slamtec.com/slamware/ros/algorithm_utils.git
[submodule "3rdparty/eigen"]
	path = 3rdparty/eigen
	url = https://git.eng.slamtec.com/3rdparty/eigen.git
[submodule "src/rpos_common"]
	path = src/rpos_common
	url = https://git.eng.slamtec.com/slamware/ros/rpos_common.git
[submodule "3rdparty/jsoncpp"]
	path = 3rdparty/jsoncpp
	url = https://git.eng.slamtec.com/3rdparty/jsoncpp.git
[submodule "src/stcm_manager"]
	path = src/stcm_manager
	url = https://git.eng.slamtec.com/slamware/ros/stcm_manager.git
[submodule "src/tools/events_executor"]
	path = src/tools/events_executor
	url = https://git.eng.slamtec.com/slamware/ros/events_executor.git
