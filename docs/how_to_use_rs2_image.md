# How to use robostudio2 image

## Pull the latest robostudio2 image

Pull [the latest robostudio2 image](https://git.eng.slamtec.com/slamware/robostudio2/container_registry/75)

```bash
sudo docker pull registry.eng.slamtec.com/slamware/robostudio2/device-console:v0.2
```

## Create network

You have to create a docker network.

```bash
sudo docker network create --driver bridge --subnet **********/16 --gateway ********** slamware
```

## Create nginx.conf

Copy [nginx.conf](./nginx.conf) to ~/rs2/services/device-console

## Start container

```bash
sudo docker run -itd --name device-console --restart unless-stopped --network slamware --add-host host.docker.internal:host-gateway -p 8080:80 -v ~/rs2/services/nginx.conf:/etc/nginx/conf.d/default.conf:ro registry.eng.slamtec.com/slamware/robostudio2/device-console:v0.2
```