# 使用Docker编译

本文介绍如何使用镜像编译rslamware。

## 准备

请至[Gitlab Registry](https://git.eng.slamtec.com/slamware/topics/rslamware/container_registry/79)下载需要的编译镜像。

```bash
docker login registry.eng.slamtec.com
docker pull registry.eng.slamtec.com/slamware/topics/rslamware/rslamware-builder:amd64
docker pull registry.eng.slamtec.com/slamware/topics/rslamware/rslamware-builder:arm64
```

## 编译

### x86_64

```bash
docker run --name rslamware-builder -v {YOUR_RSLAMWARE_PATH_ON_YOUR_HOST}:/root/rslamware -it registry.eng.slamtec.com/slamware/topics/rslamware/rslamware-builder:amd64 bash

# In the container
cd /root/rslamware
source /opt/ros/humble/setup.bash
colcon build
```

### arm64

```bash
docker run --name rslamware-builder -v{YOUR_RSLAMWARE_PATH_ON_YOUR_HOST}:/root/rslamware -it registry.eng.slamtec.com/slamware/topics/rslamware/rslamware-builder:arm64 bash

# In the container
cd /root/rslamware
source /root/arm64-sysroot/opt/ros/humble/setup.bash
colcon build --cmake-force-configure --cmake-args -DCMAKE_TOOLCHAIN_FILE=${PWD}/docker/aarch64-toolchain.cmake -DCMAKE_BUILD_TYPE=Release -DRSLAMWARE_ROOT_PATH=${PWD}
```