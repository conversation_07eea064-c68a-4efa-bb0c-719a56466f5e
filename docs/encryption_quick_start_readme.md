# launch文件和config的yaml文件加密及运行简明使用

## 快速使用

### 1. 编译和加密
- 正常编译  
在rslamware文件夹下，运行加密脚本：
```bash
# Build the system and create encrypted package
./scripts/build_rslamware_with_encryption.sh
```

- docker编译  
在rslamware文件夹下，运行加密脚本：
```bash
# Build the system and create encrypted package
./scripts/docker_build_rslamware_with_encryption_arm64.sh
```


运行完上述脚本后，会生成如下文件夹及文件

```
rslamware/
├── install/rslamware_encryption_run/lib/rslamware_encryption_run/
│   ├── rslamware_encryption_run (executable)
│   └── rslamware.enc --------------（加密后的launch文件夹和config文件夹）
├── launch/
│   ├── rslamware.launch.py
│   ├── mapping.launch.py
│   ├── bringup_launch.py
│   ├── rslamware_startup_mapping.launch.py
│   ├── rslamware_startup_localization.launch.py
│   └── [other discovered launch files]
├── config/
│   ├── combined_config.yaml
│   └── [original config files if not deleted]
└── rslamware_encryption_backup/
    ├── rslamware.tar.gz
    ├── launch/
    └── config/
```
- rslamware/launch文件夹是实机使用到的所有launch文件，并自动生成了rslamware_startup_mapping.launch.py和rslamware_startup_localization.launch.py文件作为主launch启动时使用；

- rslamware/config文件夹，为使用到的yaml文件，原始的yaml文件，包含原路径，并自动生成了合并后的配置文件combined_config.yaml

- rslamware/rslamware_encryption_backup文件夹，是对加密前的launch文件夹和config文件夹的备份，方便对比，rslamware.tar.gz是加密前的压缩包



其中，rslamware/install下的所有launch文件夹及config文件夹，及rslamware/config文件加下处了combined_config.yaml的其他yaml文件，原则上都应该被删除，后续可以通过修改build_rslamware_with_encryption.sh脚本的中的如下内容进行删除：

```bash
# Basic usage (preserve individual config files)
python3 rslamware_encryption_prepare.py
# ↓
# 增加--delete-configs
# Delete individual config files after combining
python3 "$RSLAMWARE_ROOT/src/rslamware_encryption_run/src/rslamware_encryption_prepare.py" --delete-configs

```

### 2. 运行
建图模式或定位模式，分别运行一下脚本，默认为定位模式：
```bash
# Run in mapping mode
./scripts/run_rslamware_with_encryption.sh mapping

# Run in localization mode (default)
./scripts/run_rslamware_with_encryption.sh
```

- 启动运行时，短时间临时会在/tmp/rslamware/test/rslamware_{pid}/下解压launch文件夹和config文件夹，并启动运行rslamware_startup_mapping.launch.py或rslamware_startup_localization.launch.py

- launch启动10秒后，若程序正常运行中，则会直接删除/tmp/rslamware/test/rslamware_{pid}文件夹  
不过本次为了测试方便，没有删除，方便后续对比；不过每次启动时，都会把上次运行时遗留的文件删除

- 由于加密运行程序内单独起了进程来运行bash和ros2 launch，使用脚本运行run_rslamware_with_encryption.sh时，不能通过ctrl+C结束进程，需另起终端，调用scripts/shutdown.sh进行清除


