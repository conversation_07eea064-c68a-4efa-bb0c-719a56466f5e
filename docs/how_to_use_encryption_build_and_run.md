# RSlamware Encryption Build and Run System

This document describes the RSlamware encryption system, which provides secure packaging and execution of the RSlamware SLAM framework. The system consists of build scripts, preparation tools, and encryption/decryption applications that work together to create encrypted deployments.

## Overview

The RSlamware encryption system includes:
- **Build script**: Automates the complete build and encryption process
- **Run script**: Executes encrypted RSlamware in different modes
- **Preparation tool**: Processes launch files and configurations for encryption
- **Encryption application**: Handles file encryption/decryption and secure execution

## System Architecture

```
┌─────────────────────┐    ┌───────────────────────┐    ┌─────────────────────┐
│  Source Code        │    │   Preparation         │    │   Encryption        │
│  Launch Files       │───→│   - Collect files     │───→│   - AES-256-CBC     │
│  Config Files       │    │   - Combine configs   │    │   - Secure storage  │
└─────────────────────┘    └───────────────────────┘    └─────────────────────┘
                                                                      │
                                                                      ▼
┌─────────────────────┐    ┌───────────────────────┐    ┌─────────────────────┐
│  Execution          │    │   Decryption          │    │   Encrypted         │
│  - Mapping mode     │◄───│   - Memory decrypt    │◄───│   rslamware.enc     │
│  - Localization     │    │   - Secure temp dir   │    │                     │
└─────────────────────┘    └───────────────────────┘    └─────────────────────┘
```

## Quick Start

### 1. Build and Encrypt
```bash
# Build the system and create encrypted package
./scripts/build_rslamware_with_encryption.sh
```

### 2. Run Encrypted System
```bash
# Run in mapping mode
./scripts/run_rslamware_with_encryption.sh mapping

# Run in localization mode (default)
./scripts/run_rslamware_with_encryption.sh
```

## Script Reference

### build_rslamware_with_encryption.sh

**Purpose**: Automates the complete build and encryption process for RSlamware.

**Location**: `scripts/build_rslamware_with_encryption.sh`

**Usage**:
```bash
./scripts/build_rslamware_with_encryption.sh
```

**Process Flow**:
1. **Environment Setup**: Sources ROS2 Humble environment
2. **Clean Build**: Removes previous build artifacts (optional)
3. **Package Build**: Builds all packages in release mode using `colcon build`
4. **File Preparation**: Runs `rslamware_encryption_prepare.py` to collect and process files
5. **Encryption**: Encrypts the prepared tar.gz archive using `rslamware_encryption_run`
6. **Cleanup**: Removes original files for security

**Output Files**:
- `rslamware.enc`: Encrypted archive containing launch files and configurations
- `rslamware_encryption_backup/`: Backup directory with unencrypted copies

**Error Handling**:
- Exits immediately on any error (`set -e`)
- Validates required packages and files exist
- Provides colored status messages for better visibility

### run_rslamware_with_encryption.sh

**Purpose**: Executes the encrypted RSlamware system in different operational modes.

**Location**: `scripts/run_rslamware_with_encryption.sh`

**Usage**:
```bash
# Run in localization mode (default)
./scripts/run_rslamware_with_encryption.sh

# Run in mapping mode
./scripts/run_rslamware_with_encryption.sh mapping
```

**Modes**:
- **Localization Mode** (default): Runs RSlamware with navigation and localization
- **Mapping Mode**: Runs RSlamware with SLAM mapping capabilities

**Process Flow**:
1. **Environment Setup**: Sources ROS2 and workspace environments
2. **File Validation**: Checks for encrypted file and encryption tool
3. **Decryption & Execution**: Uses `rslamware_encryption_run` to decrypt and run

**Requirements**:
- ROS2 Humble environment
- Built workspace with `install/setup.bash`
- Existing `rslamware.enc` file
- `rslamware_encryption_run` executable

## Application Reference

### rslamware_encryption_prepare.py

**Purpose**: Prepares RSlamware launch files and configurations for encryption by collecting dependencies, combining configurations, and creating archives.

**Location**: `src/rslamware_encryption_run/src/rslamware_encryption_prepare.py`

**Usage**:
```bash
# Basic usage (preserve individual config files)
python3 rslamware_encryption_prepare.py

# Delete individual config files after combining
python3 rslamware_encryption_prepare.py --delete-configs

# Specify custom workspace
python3 rslamware_encryption_prepare.py --workspace /path/to/workspace
```

**Command Line Options**:
- `--workspace DIR`: Specify workspace root directory (default: current directory)
- `--delete-configs`: Delete individual config files after combining (default: False)

**Detailed Process**:

#### 1. Launch File Discovery
- **Key Launch Files**: Identifies and copies primary launch files:
  - `rslamware_bringup/rslamware.launch.py`
  - `cartographer_ros/mapping.launch.py`
  - `nav2_bringup/bringup_launch.py`
- **Dependency Resolution**: Recursively discovers and copies all dependent launch files
- **File Parsing**: Uses regex patterns to find launch file references:
  ```python
  pattern = r'[^\s\'"\)]+launch\.py'
  ```

#### 2. Configuration Processing
- **Config Discovery**: Parses launch files to find YAML/YML configuration files
- **File Collection**: Searches install directories for configuration files
- **Deep Merging**: Combines all configurations into `combined_config.yaml` using recursive dictionary merging
- **Structure Preservation**: Maintains original directory structure for reference

#### 3. Startup Launch File Generation
Creates two main entry points:
- `rslamware_startup_mapping.launch.py`: Includes RSlamware + mapping launch
- `rslamware_startup_localization.launch.py`: Includes RSlamware + navigation launch

#### 4. Archive Creation
- **Tar.gz Archive**: Creates compressed archive containing:
  - `launch/`: All processed launch files
  - `config/`: Configuration files and combined_config.yaml
- **Backup Creation**: Stores copies in `rslamware_encryption_backup/`

#### 5. Security Cleanup
- **Original File Removal**: Removes launch and config files from install directories
- **Controlled Deletion**: Only removes files when `--delete-configs` is specified

**Directory Structure Created**:
```
workspace/
├── launch/
│   ├── rslamware.launch.py
│   ├── mapping.launch.py
│   ├── bringup_launch.py
│   ├── rslamware_startup_mapping.launch.py
│   ├── rslamware_startup_localization.launch.py
│   └── [other discovered launch files]
├── config/
│   ├── combined_config.yaml
│   └── [original config files if not deleted]
└── rslamware_encryption_backup/
    ├── rslamware.tar.gz
    ├── launch/
    └── config/
```

### rslamware_encryption_run.cpp

**Purpose**: C++ application providing AES-256-CBC encryption/decryption capabilities and secure execution environment for RSlamware.

**Location**: `src/rslamware_encryption_run/src/rslamware_encryption_run.cpp`

**Compilation**: Built automatically as part of the RSlamware workspace using CMake.

**Usage**:

#### Encryption Mode
```bash
# Encrypt with default files
./rslamware_encryption_run --encrypt

# Encrypt with custom input/output
./rslamware_encryption_run --encrypt --input /path/to/rslamware.tar.gz --output /path/to/rslamware.enc
```

#### Execution Mode
```bash
# Run in mapping mode
./rslamware_encryption_run --run --mapping

# Run in localization mode
./rslamware_encryption_run --run --localization

# Run with custom encrypted file
./rslamware_encryption_run --run --mapping --file /path/to/rslamware.enc
```

#### Command Line Options
- `--encrypt`: Enable encryption mode
- `--run`: Enable execution mode
- `--mapping`: Run in mapping mode (requires --run)
- `--localization`: Run in localization mode (requires --run)
- `--input FILE`: Input tar.gz file for encryption (default: rslamware.tar.gz)
- `--output FILE`: Output encrypted file for encryption (default: rslamware.enc)
- `--file FILE`: Encrypted file to run (default: rslamware.enc)
- `--help`: Show help message

**Security Features**:

#### 1. Encryption Implementation
- **Algorithm**: AES-256-CBC with OpenSSL
- **Key Management**: Hard-coded 256-bit encryption key
- **Initialization Vector**: Random 16-byte IV for each encryption
- **Security**: IV prepended to encrypted data

#### 2. Secure Execution Environment
- **Memory Decryption**: Decrypts files directly to memory (no temporary files)
- **Secure Temporary Directory**: Creates protected temp directories with 700 permissions
- **Process Isolation**: Uses user-specific temp directories (`/tmp/rslamware/test`)
- **Cleanup**: Automatic cleanup of temporary files and directories

#### 3. Runtime Security
- **Environment Variables**: Sets secure environment variables for launch files
- **Setup Script Discovery**: Automatically locates ROS2 setup scripts
- **Process Management**: Proper fork/exec handling for launch processes
- **Error Handling**: Comprehensive error checking and reporting

**Technical Details**:

#### File Structure
```cpp
// Hard-coded encryption key (production should use secure key management)
static constexpr unsigned char ENCRYPTION_KEY[32] = { ... };

// Security constants
static constexpr size_t IV_SIZE = 16;  // AES block size
static constexpr size_t KEY_SIZE = 32; // AES-256 key size
```

#### Encryption Process
1. **IV Generation**: Creates random 16-byte initialization vector
2. **Context Setup**: Initializes OpenSSL EVP cipher context
3. **Chunked Processing**: Processes file in 4KB chunks for memory efficiency
4. **Finalization**: Properly finalizes encryption with padding

#### Decryption Process
1. **IV Extraction**: Reads IV from beginning of encrypted file
2. **Memory Decryption**: Decrypts entire file to memory buffer
3. **Secure Extraction**: Extracts tar.gz to protected temporary directory
4. **Immediate Cleanup**: Removes intermediate files after extraction

## Security Considerations

### 1. Encryption Security
- **Algorithm**: Uses industry-standard AES-256-CBC encryption
- **Key Management**: Production deployments should implement secure key management
- **IV Randomization**: Each encryption uses a unique random IV

### 2. Runtime Security
- **Memory-only Decryption**: Sensitive files never written to disk in plaintext
- **Secure Temporary Directories**: Protected with restrictive permissions (700)
- **Process Isolation**: User-specific temporary directories prevent cross-user access
- **Automatic Cleanup**: Temporary files removed on all node run successfully

### 3. File System Security
- **Original File Removal**: Source files deleted after encryption
- **Backup Protection**: Backup directories should be properly secured
- **Permission Control**: Temporary directories use minimal necessary permissions

## Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check ROS2 environment
source /opt/ros/humble/setup.bash

# Verify colcon installation
sudo apt install python3-colcon-common-extensions

# Check workspace structure
ls -la install/rslamware_encryption_run/lib/rslamware_encryption_run/
```

#### Runtime Failures
```bash
# Check encrypted file exists
ls -la install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware.enc

# Verify setup.bash location
find . -name "setup.bash" -type f

# Check temporary directory permissions
ls -la /tmp/rslamware/test/
```

#### Configuration Issues
```bash
# Verify config files collected
ls -la config/
cat config/combined_config.yaml

# Check launch file modifications
grep -r "ENCRYPTION_MODE" launch/
```

### Debug Commands

#### Verbose Build
```bash
# Enable verbose output
export VERBOSE=1
./scripts/build_rslamware_with_encryption.sh
```

#### Manual Preparation
```bash
# Run preparation with debug output
python3 -u src/rslamware_encryption_run/src/rslamware_encryption_prepare.py --delete-configs
```

#### Test Encryption
```bash
# Test encryption/decryption cycle
./install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware_encryption_run --encrypt
./install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware_encryption_run --run --mapping
```

## Best Practices

### 1. Development Workflow
- Always build in release mode for production deployments
- Test both mapping and localization modes after encryption
- Verify all required launch files and configurations are included

### 2. Security Practices
- Regularly rotate encryption keys in production
- Secure backup directories with appropriate permissions
- Monitor temporary directory usage and cleanup

### 3. Deployment Considerations
- Test encrypted system in target environment before deployment
- Ensure ROS2 Humble is properly installed on target systems
- Verify all required dependencies are available

## File Reference

### Generated Files
- `rslamware.enc`: Primary encrypted archive
- `rslamware.tar.gz`: Temporary unencrypted archive (removed after encryption)
- `combined_config.yaml`: Merged configuration file
- `rslamware_startup_*.launch.py`: Generated startup launch files

### Directory Structure
```
workspace/
├── scripts/
│   ├── build_rslamware_with_encryption.sh
│   └── run_rslamware_with_encryption.sh
├── src/rslamware_encryption_run/src/
│   ├── rslamware_encryption_prepare.py
│   └── rslamware_encryption_run.cpp
├── install/rslamware_encryption_run/lib/rslamware_encryption_run/
│   ├── rslamware_encryption_run (executable)
│   └── rslamware.enc
└── rslamware_encryption_backup/
    ├── rslamware.tar.gz
    ├── launch/
    └── config/
```
