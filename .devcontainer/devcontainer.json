{
  "name": "ROS 2 Development",
  "build": {
    "dockerfile": "Dockerfile"
  },
  "runArgs": [
    "-p", "1448:1448",
    "-e", "DISPLAY=${localEnv:DISPLAY}",
    "-e", "QT_X11_NO_MITSHM=1",
    "-e", "XAUTHORITY=/tmp/.docker.xauth",
    "-v", "/tmp/.X11-unix:/tmp/.X11-unix:rw",
    "-v", "/tmp/.docker.xauth:/tmp/.docker.xauth:rw",
    "-e", "LIBVA_DRIVER_NAME=iHD",
    "--gpus=all",
    "--env=NVIDIA_VISIBLE_DEVICES=all",
    "--env=NVIDIA_DRIVER_CAPABILITIES=all",
    "--env=DISPLAY",
    "--device", "/dev/dri",
    "--group-add", "video",
    "--group-add", "audio",
    "--ipc=host",
    "--cap-add=SYS_PTRACE",
    "-e", "RMW_IMPLEMENTATION=rmw_cyclonedds_cpp"
  ],
  "initializeCommand": ".devcontainer/xauth.sh",
  "customizations": {
    "vscode": {
      "extensions": [
        "ms-vscode.cpptools",
        "ms-vscode.cmake-tools",
        "ms-python.python",
        "ms-iot.vscode-ros"
      ]
    },
    "jetbrains": {
      "backend": "PyCharm"
    }
  },
  "remoteUser": "root",
  "workspaceFolder": "/root/rslamware",
  "workspaceMount": "source=${localWorkspaceFolder},target=/root/rslamware,type=bind",
}