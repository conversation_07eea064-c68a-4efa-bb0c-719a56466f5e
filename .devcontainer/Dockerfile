FROM docker.io/osrf/ros:humble-desktop-full

# Install additional packages
RUN apt-get update && apt-get install -y \
    sudo \
    htop \
    git \
    vim \
    wget \
    openssh-server \
    curl \
    build-essential \
    cmake \
    python3-pip \
    python3-colcon-common-extensions \
    python3-rosdep \
    python3-vcstool \
    httpie \
    pkg-config \
    libmodbus-dev \
    libcgal-dev \
    gdb \
    valgrind \
    x11-apps \
    mesa-utils \
    mesa-utils-extra \
    libgl1-mesa-glx \
    libgl1-mesa-dri \
    vulkan-tools \
    openssl \
    libssl-dev \
    libcairo2-dev \
    ros-humble-desktop=0.10.0-1* \
    ros-humble-desktop-full=0.10.0-1* \
    ros-humble-geographic-msgs \
    ros-humble-bond-core \
    ros-humble-gazebo-ros-pkgs \
    ros-humble-gazebo-ros2-control \
    ros-humble-ros-gz \
    ros-humble-rmw-cyclonedds-cpp \
    ros-humble-nav-2d-utils \
    ros-humble-nav2-msgs \
    libgraphicsmagick++-dev \
    ros-humble-diagnostic-updater \
    libxtensor-dev \
    libceres-dev \
    libompl-dev \
    ros-humble-test-msgs \
    ros-humble-libg2o \
    ros-humble-ament-cmake-black \
    libbenchmark-dev \
    graphicsmagick-libmagick-dev-compat \
    ros-humble-rcutils \
    libatlas3-base \
    libdraco-dev \
    qtbase5-dev \
    libtinyxml-dev \
    libblas-dev \
    liblapack-dev \
    libopenblas-dev \
    libatlas-base-dev \
    libboost-serialization-dev \
    libboost-filesystem-dev \
    liblua5.3-dev \
    libpython3-all-dev \
    libabsl-dev

RUN mkdir -p /var/run/sshd && \
    echo "root:password" | chpasswd && \
    echo 'PermitRootLogin yes' >> /etc/ssh/sshd_config && \
    service ssh start

RUN pip3 install "fastapi[standard]" "uvicorn[standard]" "uvloop" "orjson"

RUN curl -LsSf https://astral.sh/uv/install.sh | sh && \
    uv tool install ruff pyrefly

WORKDIR /root/rslamware

# Set up ROS environment for the user
RUN echo "source /opt/ros/humble/setup.bash" >> /root/.bashrc
RUN echo "source /root/rslamware/install/setup.bash" >> /root/.bashrc
RUN echo 'export GAZEBO_MODEL_PATH="/root/rslamware/install/simulator/share/simulator/models:$GAZEBO_MODEL_PATH"' >> /root/.bashrc
RUN echo 'export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp' >> /root/.bashrc
RUN echo "export ROS_DOMAIN_ID=$(( RANDOM % 233 ))" >> /root/.bashrc
RUN echo "export RSLAMWARE_MODE=simulation" >> /root/.bashrc

CMD ["bash"]