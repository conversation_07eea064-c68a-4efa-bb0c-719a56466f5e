variables:
  GIT_SUBMODULE_STRATEGY: recursive

stages:
  - build_rslamware_builder_image
  - build_rslamware
  - build_simulator_image

# Build rslamware for x86_64
amd64:
  image:
    name: $CI_REGISTRY_IMAGE/rslamware-builder:amd64
    pull_policy: always
  interruptible: false
  stage: build_rslamware
  before_script:
    - rm -rf build install log
  script:
    - bash -lc "source /opt/ros/humble/setup.bash && colcon build --cmake-args -DCMAKE_BUILD_TYPE=Release"
  artifacts:
    name: "rslamware-$CI_COMMIT_BRANCH-$CI_COMMIT_SHORT_SHA-amd64"
    paths:
      - install
    when: on_success
    expire_in: 30 days
  tags:
    - rslamware

#  Build rslamware for arm64
arm64:
    image:
      name: $CI_REGISTRY_IMAGE/rslamware-builder:arm64
      pull_policy: always
    interruptible: false
    stage: build_rslamware
    before_script:
      - rm -rf build install log
    script:
      - bash -lc "source /root/arm64-sysroot/opt/ros/humble/setup.bash && colcon build --cmake-force-configure --cmake-args -DCMAKE_TOOLCHAIN_FILE=${PWD}/docker/aarch64-toolchain.cmake -DCMAKE_BUILD_TYPE=Release -DRSLAMWARE_ROOT_PATH=${PWD}"
    artifacts:
      name: "rslamware-$CI_COMMIT_BRANCH-$CI_COMMIT_SHORT_SHA-arm64"
      paths:
        - install
      when: on_success
      expire_in: 30 days
    tags:
      - rslamware

# Build simulator image.
.docker_build_simulator_image: &docker_build_simulator_image
  image:
    name: docker:latest
    pull_policy: if-not-present
  services:
    - docker:dind
  stage: build_simulator_image
  allow_failure: true
  dependencies:
    - amd64
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -f docker/DOCKERFILE-simulator -t $IMAGE_TAG .
    - docker push $IMAGE_TAG
    - docker rmi $IMAGE_TAG
  after_script:
    - docker system prune --force
  tags:
    - rslamware

docker_build_latest:
  <<: *docker_build_simulator_image
  variables:
    IMAGE_TAG: "$CI_REGISTRY_IMAGE/rslamware-simulator:latest"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

docker_build_tag:
  <<: *docker_build_simulator_image
  variables:
    IMAGE_TAG: "$CI_REGISTRY_IMAGE/rslamware-simulator:$CI_COMMIT_TAG"
  rules:
    - if: $CI_COMMIT_TAG

# Build rslamware-builder
.build_rslamware_builder: &build_rslamware_builder
  stage: build_rslamware_builder_image
  image:
    name: docker:latest
    pull_policy: if-not-present
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -f $DOCKERFILE -t $IMAGE_TAG .
    - docker push $IMAGE_TAG
    - docker rmi $IMAGE_TAG
  after_script:
    - docker system prune --force
  tags:
    - rslamware

build_rslamware_builder_x86-64:
  <<: *build_rslamware_builder
  variables:
    IMAGE_TAG: "$CI_REGISTRY_IMAGE/rslamware-builder:amd64"
    DOCKERFILE: "docker/DOCKERFILE-amd64"
  rules:
    - changes:
      - docker/DOCKERFILE-amd64

build_rslamware_builder_arm64:
  <<: *build_rslamware_builder
  variables:
    IMAGE_TAG: "$CI_REGISTRY_IMAGE/rslamware-builder:arm64"
    DOCKERFILE: "docker/DOCKERFILE-arm64"
  rules:
    - changes:
      - docker/DOCKERFILE-arm64