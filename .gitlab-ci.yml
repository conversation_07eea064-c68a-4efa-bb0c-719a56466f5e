variables:
  GIT_SUBMODULE_STRATEGY: recursive

stages:
  - build_amd64
  - build_arm64
  - build_simulator_image

amd64:
  image:
    name: localhost/rslamware-builder:amd64
    pull_policy: if-not-present
  interruptible: false
  stage: build_amd64
  script:
    - colcon build --cmake-args -DCMAKE_BUILD_TYPE=Release
  artifacts:
    name: "rslamware-$CI_COMMIT_BRANCH-$CI_COMMIT_SHORT_SHA-amd64"
    paths:
      - install
    when: on_success
    expire_in: 30 days
  tags:
    - rslamware

arm64:
    image:
      name: localhost/rslamware-builder:arm64
      pull_policy: if-not-present
    interruptible: false
    stage: build_arm64
    script:
      - colcon build --cmake-force-configure --cmake-args -DCMAKE_TOOLCHAIN_FILE=${PWD}/docker/aarch64-toolchain.cmake -DCMAKE_BUILD_TYPE=Release -DRSLAMWARE_ROOT_PATH=${PWD}
    artifacts:
      name: "rslamware-$CI_COMMIT_BRANCH-$CI_COMMIT_SHORT_SHA-arm64"
      paths:
        - install
      when: on_success
      expire_in: 30 days
    tags:
      - rslamware

.docker_build: &docker_build
  image: docker:latest
  services:
    - docker:dind
  stage: build_simulator_image
  dependencies:
    - amd64
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -f docker/Dockerfile.simulator -t $IMAGE_TAG .
    - docker push $IMAGE_TAG
    - docker rmi $IMAGE_TAG
  tags:
    - rslamware

docker_build_latest:
  <<: *docker_build
  variables:
    IMAGE_TAG: "$CI_REGISTRY_IMAGE/rslamware-simulator:latest"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

docker_build_tag:
  <<: *docker_build
  variables:
    IMAGE_TAG: "$CI_REGISTRY_IMAGE/rslamware-simulator:$CI_COMMIT_TAG"
  rules:
    - if: $CI_COMMIT_TAG