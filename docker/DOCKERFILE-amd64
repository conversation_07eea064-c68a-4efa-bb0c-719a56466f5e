FROM docker.io/library/ros:humble-perception

RUN apt update && apt upgrade -y && apt install -y \
    build-essential \
    cmake \
    git \
    python3-pip \
    wget \
    pkg-config \
    libmodbus-dev \
    libcgal-dev \
    openssl \
    libssl-dev \
    libcairo2-dev \
    ros-humble-desktop=0.10.0-1* \
    ros-humble-desktop-full=0.10.0-1* \
    ros-humble-geographic-msgs \
    ros-humble-tf2 \
    ros-humble-tf2-ros \
    ros-humble-tf2-geometry-msgs \
    ros-humble-geometry-msgs \
    ros-humble-bond-core \
    ros-humble-nav-2d-utils \
    ros-humble-nav2-msgs \
    libgraphicsmagick++-dev \
    ros-humble-diagnostic-updater \
    libxtensor-dev \
    libceres-dev \
    libompl-dev \
    ros-humble-test-msgs \
    ros-humble-libg2o \
    ros-humble-ament-cmake-black \
    libbenchmark-dev \
    graphicsmagick-libmagick-dev-compat \
    ros-humble-rcutils \
    libatlas3-base \
    libdraco-dev \
    qtbase5-dev \
    libtinyxml-dev \
    libblas-dev \
    liblapack-dev \
    libopenblas-dev \
    libatlas-base-dev \
    libboost-serialization-dev \
    libboost-filesystem-dev \
    liblua5.3-dev \
    libpython3-all-dev \
    libabsl-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /root