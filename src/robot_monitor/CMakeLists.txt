cmake_minimum_required(VERSION 3.5)
project(robot_monitor)

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 17)
endif()

# Default to C++17
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
# uncomment the following section in order to fill in
# further dependencies manually.
find_package(rclcpp REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(std_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(interfaces REQUIRED)
find_package(sl_vcu_all REQUIRED)
find_package(action_msgs REQUIRED)
find_package(nav2_msgs REQUIRED)

# Generate service interfaces
rosidl_generate_interfaces(${PROJECT_NAME}
  "srv/CancelCurrentAction.srv"
  "srv/GetCurrentAction.srv"
  DEPENDENCIES std_msgs
)

add_executable(${PROJECT_NAME}_node
  src/main.cpp
  src/robot_monitor_node.cpp
  )

# Link health_provider library
target_link_libraries(${PROJECT_NAME}_node
  interfaces::health_provider
)

ament_target_dependencies(${PROJECT_NAME}_node
  rclcpp
  std_msgs
  tf2_ros
  interfaces
  sl_vcu_all
  action_msgs
  nav2_msgs
)

# Add dependency on generated interfaces
rosidl_get_typesupport_target(cpp_typesupport_target ${PROJECT_NAME} "rosidl_typesupport_cpp")
target_link_libraries(${PROJECT_NAME}_node "${cpp_typesupport_target}")

install(TARGETS
  ${PROJECT_NAME}_node
  DESTINATION lib/${PROJECT_NAME}
)

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

install(
  DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}/
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  
  # uncomment the line when this package is not in a git repo
  #set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
