<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>robot_monitor</name>
  <version>1.0.0</version>
  <description>robot health monitor</description>
  <maintainer email="<EMAIL>">jason</maintainer>
  <license>TODO: License declaration</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>rosidl_default_generators</build_depend>
  <member_of_group>rosidl_interface_packages</member_of_group>
  <exec_depend>rosidl_default_runtime</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>rclcpp</exec_depend>
  <depend>builtin_interfaces</depend>
  <depend>interfaces</depend>
  <depend>sl_vcu_all</depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
