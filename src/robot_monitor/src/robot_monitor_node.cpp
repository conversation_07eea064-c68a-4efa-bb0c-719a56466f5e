#include "robot_monitor_node.h"
#include <array>
#include <memory>
#include <cstdio>
#include <string>
#include <vector>
#include <health.h>

namespace rslamware{

RobotMonitorNode::RobotMonitorNode():Node("robot_monitor_node")
{
    // Declare parameters with default values
    declare_parameter<double>("health_monitor_period", 1.0);
    declare_parameter<double>("system_monitor_period", 60.0);
 
    declare_parameter<bool>("enable_disk_monitoring", true);
    declare_parameter<bool>("enable_temperature_monitoring", true);

    declare_parameter<double>("disk_usage_warning_threshold", 90.0);
    declare_parameter<double>("temperature_warning_threshold", 80.0);

    // Sensor topic parameters
    declare_parameter<std::string>("lidar_topic", "/scan");
    declare_parameter<double>("sensor_timeout_threshold", 3.0);
    declare_parameter<double>("localization_quality_threshold", 40.0);

    // Get parameters
    health_period_ = get_parameter("health_monitor_period").as_double();
    system_monitor_period_ = get_parameter("system_monitor_period").as_double();

    enable_disk_monitoring_ = get_parameter("enable_disk_monitoring").as_bool();
    enable_temperature_monitoring_ = get_parameter("enable_temperature_monitoring").as_bool();

    disk_warning_threshold_ = get_parameter("disk_usage_warning_threshold").as_double();
    temperature_warning_threshold_ = get_parameter("temperature_warning_threshold").as_double();

    // Get sensor topic names and timeout
    lidar_topic_ = get_parameter("lidar_topic").as_string();
    sensor_timeout_threshold_ = get_parameter("sensor_timeout_threshold").as_double();

    localization_quality_threshold_ = get_parameter("localization_quality_threshold").as_double();

    // Initialize sensor monitoring state
    lidar_data_received_.store(false);
}

void RobotMonitorNode::initialize()
{
    // Create health provider for robot monitor
    health_provider_ = std::make_shared<rslamware::health::HealthProvider>(
        shared_from_this(),
        "robot_monitor"
    );

    // Create timers
    timer_ = this->create_wall_timer(
        std::chrono::milliseconds(int(health_period_ * 1000)),
        std::bind(&RobotMonitorNode::monitor_software_health, this));

    system_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(int(system_monitor_period_ * 1000)),
        std::bind(&RobotMonitorNode::monitor_system_health, this));

    // Create generic sensor subscriptions for timestamp monitoring 
    last_lidar_time_ = std::chrono::steady_clock::now(); //reset timer
    lidar_subscription_ = this->create_generic_subscription(
        lidar_topic_,
        "sensor_msgs/msg/LaserScan",
        rclcpp::QoS(5).best_effort(),
        [this](std::shared_ptr<rclcpp::SerializedMessage> /* msg */) {
            // Only update timestamp
            last_lidar_time_ = std::chrono::steady_clock::now();
            lidar_data_received_.store(true);
        });

    localization_quality_subscription_ = this->create_subscription<std_msgs::msg::Int32>(
        "localization_quality",
        rclcpp::QoS(10),
        [this](const std_msgs::msg::Int32::SharedPtr msg) {
            bool isLowLocalizationQuality = msg->data < localization_quality_threshold_;
            if (isLowLocalizationQuality) {
                health_provider_->reportHealth(SOFTWARE_ERROR_LOW_LOCALIZAITON_QUALITY, "low localization quality");
            } else {
                health_provider_->removeHealth(SOFTWARE_ERROR_LOW_LOCALIZAITON_QUALITY);
            }
        });

    RCLCPP_INFO(this->get_logger(), "Robot monitor initialized");
}

void RobotMonitorNode::monitor_software_health()
{
    if (!health_provider_) {
        RCLCPP_ERROR(this->get_logger(), "Health provider is not initialized.");
        return;
    }

    // Always check sensor topics
    check_sensor_topics();
}

void RobotMonitorNode::monitor_system_health()
{
    if (!health_provider_) {
        return;
    }
 
    // Check disk usage (runs at system_monitor_period)
    if (enable_disk_monitoring_) {
        check_disk_usage();
    }

    // Check temperature (runs at system_monitor_period)
    if (enable_temperature_monitoring_) {
        check_temperature();
    }
}

double RobotMonitorNode::get_disk_usage(const std::string& path)
{
    // Use df command to get disk usage
    std::string command = "df " + path + " | tail -1 | awk '{print $5}' | sed 's/%//'";
    std::string result = execute_command(command);

    if (result.empty()) {
        RCLCPP_WARN(this->get_logger(), "Failed to read disk usage for path: %s", path.c_str());
        return 0.0;
    }

    try {
        return std::stod(result);
    } catch (const std::exception& e) {
        RCLCPP_WARN(this->get_logger(), "Failed to parse disk usage: %s", e.what());
        return 0.0;
    }
}

double RobotMonitorNode::get_cpu_temperature()
{
    // Try multiple methods to get CPU temperature
    std::vector<std::string> temp_commands = {
        // Method 1: thermal_zone0 (most common)
        "cat /sys/class/thermal/thermal_zone0/temp 2>/dev/null | awk '{print $1/1000}'",
        // Method 2: sensors command (if available)
        "sensors 2>/dev/null | grep 'Core 0' | awk '{print $3}' | sed 's/+//g' | sed 's/°C//g'",
        // Method 3: hwmon
        "find /sys/class/hwmon -name temp*_input 2>/dev/null | head -1 | xargs cat 2>/dev/null | awk '{print $1/1000}'"
    };

    for (const auto& command : temp_commands) {
        std::string result = execute_command(command);

        if (!result.empty()) {
            try {
                double temp = std::stod(result);
                if (temp > 0 && temp < 150) { // Reasonable temperature range
                    return temp;
                }
            } catch (const std::exception& e) {
                // Continue to next method
                continue;
            }
        }
    }

    RCLCPP_DEBUG(this->get_logger(), "Failed to read CPU temperature from all methods");
    return -1.0; // Indicate failure
}

std::string RobotMonitorNode::execute_command(const std::string& command)
{
    std::array<char, 128> buffer;
    std::string result;

    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(command.c_str(), "r"), pclose);

    if (!pipe) {
        RCLCPP_WARN(this->get_logger(), "Failed to execute command: %s", command.c_str());
        return "";
    }

    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }

    // Remove trailing newline
    if (!result.empty() && result.back() == '\n') {
        result.pop_back();
    }

    return result;
}

void RobotMonitorNode::check_disk_usage()
{
    double disk_usage = get_disk_usage("/");
    if (disk_usage >= disk_warning_threshold_) {
        health_provider_->reportHealth(SYSTEM_WARN_DISK_USAGE_HIGH, "high disk usage: " + std::to_string(int(disk_usage)) + "%");
    } else {
        health_provider_->removeHealth(SYSTEM_WARN_DISK_USAGE_HIGH);
    }
}

void RobotMonitorNode::check_temperature()
{
    double temperature = get_cpu_temperature();

    if (temperature >= temperature_warning_threshold_) {
        health_provider_->reportHealth(SYSTEM_WARN_CPU_TEMPERATURE_HIGH,
            "high CPU temperature: " + std::to_string(temperature));
    } else {
        health_provider_->removeHealth(SYSTEM_WARN_CPU_TEMPERATURE_HIGH);
    }
}

void RobotMonitorNode::check_sensor_topics()
{
    auto current_time = std::chrono::steady_clock::now();
 
    auto lidar_elapsed = std::chrono::duration_cast<std::chrono::duration<double>>(
    current_time - last_lidar_time_).count();
    if (lidar_elapsed > sensor_timeout_threshold_) { 
        health_provider_->reportHealth(SENSOR_ERROR_LIDAR_DISCONNECTED, "lidar disconnected");
    } else {
        // Clear any previous lidar timeout errors
        health_provider_->removeHealth(SENSOR_ERROR_LIDAR_DISCONNECTED);
    }
}

}