#include "robot_monitor_node.h"
#include <array>
#include <memory>
#include <cstdio>
#include <string>
#include <vector>
#include <health.h>
#include "version.h"

namespace rslamware{

RobotMonitorNode::RobotMonitorNode():Node("robot_monitor_node")
{
    // Declare parameters with default values
    declare_parameter<double>("health_monitor_period", 1.0);
    declare_parameter<double>("system_monitor_period", 60.0);
 
    declare_parameter<bool>("enable_disk_monitoring", true);
    declare_parameter<bool>("enable_temperature_monitoring", true);

    declare_parameter<double>("disk_usage_warning_threshold", 90.0);
    declare_parameter<double>("temperature_warning_threshold", 80.0);

    // Sensor topic parameters
    declare_parameter<std::string>("lidar_topic", "/scan");
    declare_parameter<double>("sensor_timeout_threshold", 3.0);
    declare_parameter<int>("localization_quality_threshold", 40);
    declare_parameter<double>("localization_quality_time_window", 3.0);

    // Get parameters
    health_period_ = get_parameter("health_monitor_period").as_double();
    system_monitor_period_ = get_parameter("system_monitor_period").as_double();

    enable_disk_monitoring_ = get_parameter("enable_disk_monitoring").as_bool();
    enable_temperature_monitoring_ = get_parameter("enable_temperature_monitoring").as_bool();

    disk_warning_threshold_ = get_parameter("disk_usage_warning_threshold").as_double();
    temperature_warning_threshold_ = get_parameter("temperature_warning_threshold").as_double();

    // Get sensor topic names and timeout
    lidar_topic_ = get_parameter("lidar_topic").as_string();
    sensor_timeout_threshold_ = get_parameter("sensor_timeout_threshold").as_double();

    localization_quality_threshold_ = get_parameter("localization_quality_threshold").as_int();
    localization_quality_time_window_ = get_parameter("localization_quality_time_window").as_double();

    // Initialize sensor monitoring state
    lidar_data_received_.store(false);
}

void RobotMonitorNode::initialize()
{
    // Create health provider for robot monitor
    health_provider_ = std::make_shared<rslamware::health::HealthProvider>(
        shared_from_this(),
        "robot_monitor"
    );

    // Create timers
    timer_ = this->create_wall_timer(
        std::chrono::milliseconds(int(health_period_ * 1000)),
        std::bind(&RobotMonitorNode::monitor_software_health, this));

    system_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(int(system_monitor_period_ * 1000)),
        std::bind(&RobotMonitorNode::monitor_system_health, this));

    // Create generic sensor subscriptions for timestamp monitoring 
    last_lidar_time_ = std::chrono::steady_clock::now(); //reset timer
    lidar_subscription_ = this->create_generic_subscription(
        lidar_topic_,
        "sensor_msgs/msg/LaserScan",
        rclcpp::QoS(1).best_effort(),
        [this](std::shared_ptr<rclcpp::SerializedMessage> /* msg */) {
            // Only update timestamp
            last_lidar_time_ = std::chrono::steady_clock::now();
            lidar_data_received_.store(true);
        });

    localization_quality_subscription_ = this->create_subscription<std_msgs::msg::Int32>(
        "localization_quality",
        rclcpp::QoS(1),
        [this](const std_msgs::msg::Int32::SharedPtr msg) {
            bool isLowLocalizationQuality = msg->data < localization_quality_threshold_;
            if (isLowLocalizationQuality) {
                if(!latest_low_quality_time_) {
                    latest_low_quality_time_ = this->now();
                }
                else{
                    if((this->now() - latest_low_quality_time_.value()).seconds() > localization_quality_time_window_){
                        health_provider_->reportHealth(SOFTWARE_ERROR_LOW_LOCALIZAITON_QUALITY, "low localization quality");
                    }
                } 
            } else {
                latest_low_quality_time_ = std::nullopt;
                health_provider_->removeHealth(SOFTWARE_ERROR_LOW_LOCALIZAITON_QUALITY);
            } 
        });

    motor_state_subscription_ = this->create_subscription<sl_vcu_all::msg::MotorState>("/motor_state",
        rclcpp::QoS(1), std::bind(&RobotMonitorNode::motor_state_callback, this, std::placeholders::_1));

    // Create navigation action monitoring subscription
    navigation_status_subscription_ = this->create_subscription<action_msgs::msg::GoalStatusArray>(
        "/navigate_to_pose/_action/status",
        rclcpp::QoS(1),
        std::bind(&RobotMonitorNode::navigation_status_callback, this, std::placeholders::_1));

    // Create cancel current action service
    cancel_current_action_service_ = this->create_service<robot_monitor::srv::CancelCurrentAction>(
        "cancel_current_action",
        std::bind(&RobotMonitorNode::cancel_current_action_service, this, std::placeholders::_1, std::placeholders::_2));

    // Create client for canceling navigation goals
    navigation_cancel_client_ = this->create_client<action_msgs::srv::CancelGoal>(
        "/navigate_to_pose/_action/cancel_goal");

    RCLCPP_INFO(this->get_logger(), "Robot monitor initialized, version: %s", RSLAMWARE_VERSION_FULL);
}

void RobotMonitorNode::motor_state_callback(const sl_vcu_all::msg::MotorState::SharedPtr msg) 
{
    if (msg->emergency_stop) {
        health_provider_->reportHealth(SYSTEM_ERROR_EMERGENCY_STOP, "Emergency stop activated");
    }
    else{
        health_provider_->removeHealth(SYSTEM_ERROR_EMERGENCY_STOP);
    }
    if (msg->brake_release) {
        health_provider_->reportHealth(SYSTEM_ERROR_BRAKE_RELEASED, "Brake released");
    }
    else{
        health_provider_->removeHealth(SYSTEM_ERROR_BRAKE_RELEASED);
    }
 }
 
void RobotMonitorNode::monitor_software_health()
{
    if (!health_provider_) {
        RCLCPP_ERROR(this->get_logger(), "Health provider is not initialized.");
        return;
    }

    // Always check sensor topics
    check_sensor_topics();
}

void RobotMonitorNode::monitor_system_health()
{
    if (!health_provider_) {
        return;
    }
 
    // Check disk usage (runs at system_monitor_period)
    if (enable_disk_monitoring_) {
        check_disk_usage();
    }

    // Check temperature (runs at system_monitor_period)
    if (enable_temperature_monitoring_) {
        check_temperature();
    }
}

double RobotMonitorNode::get_disk_usage(const std::string& path)
{
    // Use df command to get disk usage
    std::string command = "df " + path + " | tail -1 | awk '{print $5}' | sed 's/%//'";
    std::string result = execute_command(command);

    if (result.empty()) {
        RCLCPP_WARN(this->get_logger(), "Failed to read disk usage for path: %s", path.c_str());
        return 0.0;
    }

    try {
        return std::stod(result);
    } catch (const std::exception& e) {
        RCLCPP_WARN(this->get_logger(), "Failed to parse disk usage: %s", e.what());
        return 0.0;
    }
}

double RobotMonitorNode::get_cpu_temperature()
{
    // Try multiple methods to get CPU temperature
    std::vector<std::string> temp_commands = {
        // Method 1: thermal_zone0 (most common)
        "cat /sys/class/thermal/thermal_zone0/temp 2>/dev/null | awk '{print $1/1000}'",
        // Method 2: sensors command (if available)
        "sensors 2>/dev/null | grep 'Core 0' | awk '{print $3}' | sed 's/+//g' | sed 's/°C//g'",
        // Method 3: hwmon
        "find /sys/class/hwmon -name temp*_input 2>/dev/null | head -1 | xargs cat 2>/dev/null | awk '{print $1/1000}'"
    };

    for (const auto& command : temp_commands) {
        std::string result = execute_command(command);

        if (!result.empty()) {
            try {
                double temp = std::stod(result);
                if (temp > 0 && temp < 150) { // Reasonable temperature range
                    return temp;
                }
            } catch (const std::exception& e) {
                // Continue to next method
                continue;
            }
        }
    }

    RCLCPP_DEBUG(this->get_logger(), "Failed to read CPU temperature from all methods");
    return -1.0; // Indicate failure
}

std::string RobotMonitorNode::execute_command(const std::string& command)
{
    std::array<char, 128> buffer;
    std::string result;

    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(command.c_str(), "r"), pclose);

    if (!pipe) {
        RCLCPP_WARN(this->get_logger(), "Failed to execute command: %s", command.c_str());
        return "";
    }

    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }

    // Remove trailing newline
    if (!result.empty() && result.back() == '\n') {
        result.pop_back();
    }

    return result;
}

void RobotMonitorNode::check_disk_usage()
{
    double disk_usage = get_disk_usage("/");
    if (disk_usage >= disk_warning_threshold_) {
        health_provider_->reportHealth(SYSTEM_WARN_DISK_USAGE_HIGH, "high disk usage: " + std::to_string(int(disk_usage)) + "%");
    } else {
        health_provider_->removeHealth(SYSTEM_WARN_DISK_USAGE_HIGH);
    }
}

void RobotMonitorNode::check_temperature()
{
    double temperature = get_cpu_temperature();

    if (temperature >= temperature_warning_threshold_) {
        health_provider_->reportHealth(SYSTEM_WARN_CPU_TEMPERATURE_HIGH,
            "high CPU temperature: " + std::to_string(temperature));
    } else {
        health_provider_->removeHealth(SYSTEM_WARN_CPU_TEMPERATURE_HIGH);
    }
}

void RobotMonitorNode::navigation_status_callback(const action_msgs::msg::GoalStatusArray::SharedPtr msg)
{
    // Monitor navigation action status
    for (const auto& status : msg->status_list) {
        if (status.status == action_msgs::msg::GoalStatus::STATUS_ABORTED) {
            RCLCPP_WARN(this->get_logger(), "Navigation goal aborted: %s", status.goal_info.goal_id.uuid.data());
            health_provider_->reportHealth(SOFTWARE_WARN_NAVIGATION_ABORTED,
                "Navigation goal was aborted");
        } else if (status.status == action_msgs::msg::GoalStatus::STATUS_CANCELED) {
            RCLCPP_INFO(this->get_logger(), "Navigation goal canceled: %s", status.goal_info.goal_id.uuid.data());
            health_provider_->removeHealth(SOFTWARE_WARN_NAVIGATION_ABORTED);
        } else if (status.status == action_msgs::msg::GoalStatus::STATUS_SUCCEEDED) {
            RCLCPP_INFO(this->get_logger(), "Navigation goal succeeded: %s", status.goal_info.goal_id.uuid.data());
            health_provider_->removeHealth(SOFTWARE_WARN_NAVIGATION_ABORTED);
        }
    }
}

void RobotMonitorNode::cancel_current_action_service(
    const std::shared_ptr<robot_monitor::srv::CancelCurrentAction::Request> request,
    std::shared_ptr<robot_monitor::srv::CancelCurrentAction::Response> response)
{
    RCLCPP_INFO(this->get_logger(), "Received request to cancel current action");

    // Check if navigation cancel service is available
    if (!navigation_cancel_client_->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_ERROR(this->get_logger(), "Navigation cancel service not available");
        response->success = false;
        response->message = "Navigation cancel service not available";
        return;
    }

    // Create a cancel request to cancel all goals
    auto cancel_request = std::make_shared<action_msgs::srv::CancelGoal::Request>();
    // Empty goal_info means cancel all goals

    // Send the cancel request to the navigation action server
    auto future = navigation_cancel_client_->async_send_request(cancel_request);

    // Wait for the response
    if (rclcpp::spin_until_future_complete(this->get_node_base_interface(), future, std::chrono::seconds(5))
        == rclcpp::FutureReturnCode::SUCCESS) {
        auto result = future.get();

        if (result->return_code == action_msgs::srv::CancelGoal::Response::ERROR_NONE) {
            response->success = true;
            response->message = "Successfully canceled " + std::to_string(result->goals_canceling.size()) + " navigation goals";
            RCLCPP_INFO(this->get_logger(), "Successfully canceled %zu navigation goals", result->goals_canceling.size());
        } else {
            response->success = false;
            response->message = "Failed to cancel navigation goals, return code: " + std::to_string(result->return_code);
            RCLCPP_WARN(this->get_logger(), "Failed to cancel navigation goals, return code: %d", result->return_code);
        }
    } else {
        RCLCPP_ERROR(this->get_logger(), "Failed to get response from navigation cancel service");
        response->success = false;
        response->message = "Timeout waiting for navigation cancel service response";
    }
}

void RobotMonitorNode::check_sensor_topics()
{
    auto current_time = std::chrono::steady_clock::now();
 
    auto lidar_elapsed = std::chrono::duration_cast<std::chrono::duration<double>>(
    current_time - last_lidar_time_).count();
    if (lidar_elapsed > sensor_timeout_threshold_) { 
        health_provider_->reportHealth(SENSOR_ERROR_LIDAR_DISCONNECTED, "lidar disconnected");
    } else {
        // Clear any previous lidar timeout errors
        health_provider_->removeHealth(SENSOR_ERROR_LIDAR_DISCONNECTED);
    }
}

}