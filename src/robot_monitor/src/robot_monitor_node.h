#pragma once
#include <rclcpp/rclcpp.hpp>
#include "health_provider/health_provider.h"
#include <sensor_msgs/msg/laser_scan.hpp>
#include <std_msgs/msg/int32.hpp>
#include <chrono>
#include <atomic>
#include <sl_vcu_all/msg/motor_state.hpp>
#include <action_msgs/msg/goal_status_array.hpp>
#include <action_msgs/srv/cancel_goal.hpp>
#include <nav2_msgs/action/navigate_to_pose.hpp>
#include "robot_monitor/srv/cancel_current_action.hpp"

namespace rslamware{

class RobotMonitorNode : public rclcpp::Node
{
public:
    RobotMonitorNode();
    void initialize();
private:
    void monitor_software_health();
    void monitor_system_health();

    // System monitoring functions
    void check_disk_usage();
    void check_temperature();

    // Sensor monitoring functions
    void check_sensor_topics();

    // Navigation action monitoring functions
    void navigation_status_callback(const action_msgs::msg::GoalStatusArray::SharedPtr msg);
    void cancel_current_action_service(
        const std::shared_ptr<robot_monitor::srv::CancelCurrentAction::Request> request,
        std::shared_ptr<robot_monitor::srv::CancelCurrentAction::Response> response);

    // Utility functions
    double get_disk_usage(const std::string& path = "/");
    double get_cpu_temperature();
    std::string execute_command(const std::string& command);
    void motor_state_callback(const sl_vcu_all::msg::MotorState::SharedPtr msg);

    std::shared_ptr<rslamware::health::HealthProvider> health_provider_;
    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::TimerBase::SharedPtr system_timer_;

    // Generic sensor subscriptions for timestamp monitoring
    rclcpp::GenericSubscription::SharedPtr lidar_subscription_;

    double health_period_;
    double system_monitor_period_;
    
    // Configuration parameters
    bool enable_disk_monitoring_;
    bool enable_temperature_monitoring_;

    double disk_warning_threshold_;
    double temperature_warning_threshold_;

    // Sensor topic names from configuration
    std::string lidar_topic_;
    std::string depth_topic_;

    // Sensor monitoring state
    std::chrono::steady_clock::time_point last_lidar_time_;
    std::atomic<bool> lidar_data_received_;
    double sensor_timeout_threshold_; // seconds

    int localization_quality_threshold_;
    double localization_quality_time_window_;
    std::optional<rclcpp::Time> latest_low_quality_time_;
    rclcpp::Subscription<std_msgs::msg::Int32>::SharedPtr localization_quality_subscription_;
    rclcpp::Subscription<sl_vcu_all::msg::MotorState>::SharedPtr motor_state_subscription_;

    // Navigation action monitoring
    rclcpp::Subscription<action_msgs::msg::GoalStatusArray>::SharedPtr navigation_status_subscription_;
    rclcpp::Service<robot_monitor::srv::CancelCurrentAction>::SharedPtr cancel_current_action_service_;
    rclcpp::Client<action_msgs::srv::CancelGoal>::SharedPtr navigation_cancel_client_;
};

}
