#pragma once
#include <rclcpp/rclcpp.hpp>
#include "health_provider/health_provider.h"
#include <sensor_msgs/msg/laser_scan.hpp>
#include <std_msgs/msg/int32.hpp>
#include <chrono>
#include <atomic>

namespace rslamware{

class RobotMonitorNode : public rclcpp::Node
{
public:
    RobotMonitorNode();
    void initialize();
private:
    void monitor_software_health();
    void monitor_system_health();

    // System monitoring functions
    void check_disk_usage();
    void check_temperature();

    // Sensor monitoring functions
    void check_sensor_topics();

    // Utility functions
    double get_disk_usage(const std::string& path = "/");
    double get_cpu_temperature();
    std::string execute_command(const std::string& command);

    std::shared_ptr<rslamware::health::HealthProvider> health_provider_;
    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::TimerBase::SharedPtr system_timer_;

    // Generic sensor subscriptions for timestamp monitoring
    rclcpp::GenericSubscription::SharedPtr lidar_subscription_;

    double health_period_;
    double system_monitor_period_;
    
    // Configuration parameters
    bool enable_disk_monitoring_;
    bool enable_temperature_monitoring_;

    double disk_warning_threshold_;
    double temperature_warning_threshold_;

    // Sensor topic names from configuration
    std::string lidar_topic_;
    std::string depth_topic_;

    // Sensor monitoring state
    std::chrono::steady_clock::time_point last_lidar_time_;
    std::atomic<bool> lidar_data_received_;
    double sensor_timeout_threshold_; // seconds

    int localization_quality_threshold_;
    rclcpp::Subscription<std_msgs::msg::Int32>::SharedPtr localization_quality_subscription_;
};

}