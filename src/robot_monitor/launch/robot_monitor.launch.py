import launch
import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution, PythonExpression, TextSubstitution  


def generate_launch_description():
    ENCRYPTION_MODE=False

    # Declare launch arguments
    mode_arg = DeclareLaunchArgument(
        'mode',
        default_value='real',
        description='Launch mode: real or simulation'
    )

    def get_scan_topic():
        mode = LaunchConfiguration('mode')
        return PythonExpression([
            "'/scan' if '", mode, "' == 'simulation' else '/fusion_scan'"
        ])

    if not ENCRYPTION_MODE:
        # Get the path to the config file from rslamware_bringup
        config_file = os.path.join(
            get_package_share_directory('rslamware_bringup'),
                'config',
                'rslamware.yaml'
            )
    else:
        launch_dir = os.path.dirname(os.path.realpath(__file__))
        config_dir = os.path.join(launch_dir, '..', 'config')
        config_dir = os.path.abspath(config_dir)
        config_file = os.path.join(config_dir, 'combined_config.yaml')

    return LaunchDescription([
        DeclareLaunchArgument(
            'config_file',
            default_value=config_file,
            description='Path to robot monitor configuration file'
        ),

        Node(
            package='robot_monitor',
            executable='robot_monitor_node',
            name='robot_monitor_node',
            output='screen',
            parameters=[
                LaunchConfiguration('config_file'),
                {
                    'lidar_topic': get_scan_topic()
                }
            ],
        ),

        LogInfo(
            msg="Robot monitor launched with HealthProvider integration!"
        ),
    ])
