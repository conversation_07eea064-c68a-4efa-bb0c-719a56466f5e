### area_type: dangerous_area, sensor_disable_area
@area_type = dangerous_area
@area_id = 1

### GET areas
GET http://{{host}}/api/core/artifact/v1/rectangle-areas/{{area_type}}

### Clear areas
DELETE http://{{host}}/api/core/artifact/v1/rectangle-areas/{{area_type}}

### Add an area
POST http://{{host}}/api/core/artifact/v1/rectangle-areas/{{area_type}}
Content-Type: application/json

{
  "area": {
    "start": {
      "x": 0.0,
      "y": 1.0
    },
    "end": {
      "x": 1.0,
      "y": 1.0
    },
    "half_width": 0.5
  },
  "metadata": {
    "sensor_type": "[1,3]"
  }
}

### Edit an area
PUT http://{{host}}/api/core/artifact/v1/rectangle-areas/{{area_type}}
Content-Type: application/json

{
  "id": 1,
  "usage": "dangerous_area",
  "area": {
    "start": {
      "x": 0.5,
      "y": 1.0
    },
    "end": {
      "x": 1.0,
      "y": 1.0
    },
    "half_width": 0.3
  },
  "metadata": {
    "speed_limit": "0.4"
  }
}

### Delete an area
DELETE http://{{host}}/api/core/artifact/v1/rectangle-areas/{{area_type}}/{{area_id}}