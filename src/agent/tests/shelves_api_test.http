@shelf_id = e34d860b-a41e-4b2e-8c8f-63b413b63430

### GET all shelves
GET http://{{host}}/api/industry/v1/shelves

### Get a specific shelf
GET http://{{host}}/api/industry/v1/shelves/{{shelf_id}}

### DELETE all shelves
DELETE http://{{host}}/api/industry/v1/shelves

### Delete a shelf
DELETE http://{{host}}/api/industry/v1/shelves/{{shelf_id}}

### Add a shelf
POST http://{{host}}/api/industry/v1/shelves
Content-Type: application/json

{
  "shelf_columnar_length": 0.62,
  "shelf_columnar_width": 0.7,
  "shelf_columnar_diameter": 0.04,
  "shelf_length_retraction": 0.04
}

### Edit a shelf
PUT http://{{host}}/api/industry/v1/shelves/{{shelf_id}}
Content-Type: application/json

{
  "shelf_columnar_length": 0.62,
  "shelf_columnar_width": 0.7,
  "shelf_columnar_diameter": 0.04,
  "shelf_length_retraction": 0.04
}