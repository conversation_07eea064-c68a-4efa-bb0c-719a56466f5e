"""
System manager
"""
import math

from interfaces.msg import RobotHealthInfo
from rclpy.node import Node
from rclpy.time import Time
from sensor_msgs.msg import LaserScan
from sl_vcu_all.msg import BatteryStatus
from typing import Dict, List, Optional

from .base_manager import BaseManager
from .models import Capability, BaseError, HealthStatus, RobotInfo, PowerStatus
from .robot_pose_listener import RobotPoseListener
from .utils import <PERSON>faultQoSProfile, api_handler


class SystemManager(BaseManager):
    """System manager for handling capabilities and system operations"""
    
    def __init__(self, node: Node, pose_listener: RobotPoseListener):
        """Initialize system manager"""
        super().__init__(node)

        self._pose_listener = pose_listener
        self._laser_scan_buffer: List[LaserScan] = []

        self._capabilities: Dict[str, Capability] = {}
        self._initialize_default_capabilities()

        self._robot_info: RobotInfo = RobotInfo(
            manufacturerId=-1,
            manufacturerName="Slamtec",
            modelId=-1,
            modelName="Test",
            deviceID="-1",
            hardwareVersion="-1",
            softwareVersion="-1",
        )

        self._health_status = None

        self._power_status = PowerStatus(
            batteryPercentage=0,
            dockingStatus="on_dock",
            isCharging=True,
            isDCConnected=False,
            powerStage="running",
            sleepMode="awake"
        )

    def _initialize_default_capabilities(self):
        """Initialize default capabilities"""
        # Add some default system capabilities
        default_capabilities = [
            Capability(
                name="navigation",
                enabled=True,
                version="1.0.0"
            ),
            Capability(
                name="mapping",
                enabled=True,
                version="1.0.0"
            )
        ]
        
        for capability in default_capabilities:
            self._capabilities[capability.name] = capability

    def _subscribe_to_topics(self):
        self._subscriptions.append(
            self._node.create_subscription(
                RobotHealthInfo,
                "robot/health",
                self._health_callback,
                qos_profile=DefaultQoSProfile
            )
        )

        scan_topic = self._node.get_parameter("scan_topic").get_parameter_value().string_value

        self._subscriptions.append(
            self._node.create_subscription(
                LaserScan,
                scan_topic,
                self._laser_callback,
                qos_profile=DefaultQoSProfile
            )
        )

        self._subscriptions.append(
            self._node.create_subscription(
                BatteryStatus,
                "battery_status",
                self._battery_callback,
                qos_profile=DefaultQoSProfile
            )
        )

    def _create_clients(self):
        pass

    def _health_callback(self, msg):
        has_warning = msg.has_warning
        has_error = msg.has_error
        has_fatal = msg.has_fatal
        errors = []

        for err in msg.errors:
            errors.append(BaseError(id=0, component=err.component, errorCode=err.error_code, level=err.level, message=err.message))

        self._health_status = HealthStatus(hasWarning=has_warning, hasError=has_error, hasFatal=has_fatal, baseError=errors)

    def _laser_callback(self, msg):
        self._laser_scan_buffer.append(msg)
        if len(self._laser_scan_buffer) >= 10:
            self._laser_scan_buffer.pop(0)

    def _battery_callback(self, msg):
        self._power_status.batteryPercentage = int(msg.remaining_percent)
        self._power_status.isCharging = msg.is_auto_charging
        self._power_status.powerStage = "running"
        self._power_status.dockingStatus = "on_dock" if msg.is_auto_charging else "off_dock"

    @property
    def all_capabilities(self) -> List[Capability]:
        """Get all system capabilities"""
        capabilities_list = list(self._capabilities.values())
        
        return capabilities_list

    @property
    def robot_info(self) -> RobotInfo:
        return self._robot_info

    async def power_status(self) -> PowerStatus:
        return self._power_status

    async def health_status(self) -> HealthStatus:
        if self._health_status is None:
            return HealthStatus(hasWarning=False, hasError=False, hasFatal=False, baseError=[])
        return self._health_status

    @api_handler(default_return=None)
    async def get_laser_scan(self):
        if len(self._laser_scan_buffer) == 0:
            return None

        try:
            laser_scan = self._laser_scan_buffer.pop(0)
        except IndexError:
            return None

        if laser_scan is None:
            return None

        scan_timestamp = Time.from_msg(laser_scan.header.stamp)
        _pose = await self._pose_listener.get_pose(time=scan_timestamp)

        if _pose is None:
            return None

        _points = []

        for index, rng in enumerate(laser_scan.ranges):
            if math.isnan(rng) or math.isinf(rng) or rng > laser_scan.range_max or rng < laser_scan.range_min:
                continue

            angle = laser_scan.angle_min + (index * laser_scan.angle_increment)
            _points.append({"distance":rng, "angle":angle, "valid":True})

        return {"pose":_pose, "laser_points":_points}