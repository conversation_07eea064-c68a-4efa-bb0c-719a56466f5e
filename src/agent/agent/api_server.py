"""
FastAPI REST API server
"""
import time

from datetime import datetime
from urllib.request import Request
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.requests import Request as FastRequest
from fastapi.responses import PlainTextResponse, Response
from rclpy.node import Node
from typing import Optional, List

from .agent_exceptions import InvalidArtifactUsageError
from .artifact_manager import ArtifactManager
from .multi_floor_manager import MultiFloorManager
from .request_models import GeneralEnableRequest, ActionRequest, POIRequest
from .response_models import (
    CapabilitiesResponse,
    APIResponse,
    HealthStatus,
    RobotInfo,
    PowerStatus,
    Pose,
    LaserScanResponse,
    MotionAction,
    RectangleArea,
    PointOfInterest,
    VirtualLine,
    VirtualArea,
    PathResponse
)
from .robot_pose_listener import RobotPoseListener
from .slam_manager import SLAMManager
from .system_manager import SystemManager
from .motion_manager import MotionManager
from .utils import SLAMMode, ArtifactLineUsage, ArtifactAreaUsage


class APIServer:
    """FastAPI server class"""
    
    def __init__(self, node: Node):
        """Initialize API server"""
        self.app = FastAPI(
            title="Agent REST API",
            description="RESTful API service for ROS2 Agent node",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        pose_listener = RobotPoseListener(node)
        
        # Initialize system manager
        self.system_manager = SystemManager(node, pose_listener)

        # Initialize slam manager
        self.slam_manager = SLAMManager(node, pose_listener)

        # Initialize motion manager
        self.motion_manager = MotionManager(node, pose_listener)

        # Initialise artifact manager
        self.artifact_manager = ArtifactManager(node)

        # Initialise multi-floor manager
        self.multi_floor_manager = MultiFloorManager(node)
        
        # Record startup time
        self.start_time = time.time()

        self._register_error_handlers()
        
        # Register routes
        self._register_routes()

    def _register_error_handlers(self):
        """Register error handlers"""

        @self.app.exception_handler(InvalidArtifactUsageError)
        async def invalid_artifact_usage_error_handler(request: Request, exc):
            return PlatinTextResponse(str(exc), status_code=status.HTTP_400_BAD_REQUEST)

        @self.app.exception_handler(RequestValidationError)
        async def validation_exception_handler(request: Request, exc):
            return PlainTextResponse("Invalid request data", status_code=status.HTTP_400_BAD_REQUEST)

        @self.app.exception_handler(HTTPException)
        async def http_exception_handler(request: Request, exc):
            return PlainTextResponse(exc.detail, status_code=exc.status_code)

        @self.app.exception_handler(Exception)
        async def global_exception_handler(request: Request, exc):
            return PlainTextResponse(str(exc), status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _register_routes(self):
        """Register API routes"""

        @self.app.get("/", response_model=APIResponse)
        async def root():
            """Root endpoint"""
            return APIResponse(
                success=True,
                message="Agent REST API Server is running",
                timestamp=datetime.now().isoformat()
            )

        #
        # System
        #
        @self.app.get("/api/core/system/v1/robot/health", response_model=HealthStatus)
        async def health_check():
            """Health check endpoint"""
            return self.system_manager.health_status

        @self.app.get("/api/core/system/v1/capabilities", response_model=CapabilitiesResponse)
        async def get_capabilities():
            """
            Get system capabilities list
            
            Returns information about all available system capabilities including:
            - Capability name and type
            - Version information
            - Current status
            - Capability description
            - Configuration parameters
            - Dependencies
            """
            return self.system_manager.all_capabilities

        @self.app.get("/api/core/system/v1/robot/info", response_model=RobotInfo)
        async def get_info():
            """
            Get robot information
            """
            return self.system_manager.robot_info

        @self.app.get("/api/core/system/v1/power/status", response_model=PowerStatus)
        async def get_power_status():
            """
            Get power status, including battery percentage, charging status, etc.
            :return: PowerStatus
            """
            return self.system_manager.power_status

        @self.app.get("/api/core/system/v1/laserscan", response_model=LaserScanResponse)
        async def get_laser_scan():
            """
            Get laser sca
            :return: LaserScan
            """
            return self.system_manager.laser_scan

        #
        # Slam
        #

        @self.app.get("/api/core/slam/v1/localization/pose", response_model=Pose)
        async def get_pose():
            """
            Get robot pose.
            :return: Pose
            """
            return self.slam_manager.pose

        @self.app.put("/api/core/slam/v1/localization/pose", status_code=200)
        async def set_pose(pose: Pose):
            """
            Set robot pose.
            :param pose: The robot pose.
            """
            self.slam_manager.pose = pose

        @self.app.get("/api/core/slam/v1/localization/odopose", response_model=Pose)
        async def get_odometry():
            """
            Get odometry
            :return: Pose
            """
            return self.slam_manager.odometry

        @self.app.get("/api/core/slam/v1/localization/quality", response_model=int)
        async def get_localization_quality():
            """
            Get localization quality.
            :return: int which means localization quality.
            """
            return self.slam_manager.localization_quality

        @self.app.get("/api/core/slam/v1/localization/:enable", response_model=bool)
        async def is_localization_enabled():
            """
            Is robot running in localization mode?
            :return: bool
            """
            return self.slam_manager.slam_mode == SLAMMode.LOCALIZATION

        @self.app.put("/api/core/slam/v1/localization/:enable", status_code=200)
        async def enable_localization(enable: GeneralEnableRequest):
            """
            Enable / disable localization. If disable localization, robot will enable mapping mode.
            :return: 200
            """
            if enable.enable:
                mode = SLAMMode.LOCALIZATION
            else:
                mode = SLAMMode.MAPPING
            await self.slam_manager.set_slam_mode(mode)

        @self.app.post("/api/core/slam/v1/localization/status/:reset", status_code=200)
        async def reset_localization():
            """
            Reset localization
            :return: 200
            """
            await self.slam_manager.set_slam_mode(SLAMMode.MAPPING)

        @self.app.get("/api/core/slam/v1/mapping/:enable", response_model=bool)
        async def is_mapping_enabled():
            """
            Is robot running in mapping mode?
            :return: bool
            """
            return self.slam_manager.slam_mode == SLAMMode.MAPPING

        @self.app.put("/api/core/slam/v1/mapping/:enable", status_code=200)
        async def enable_mapping(enable: GeneralEnableRequest):
            """
            Enable / disable mapping mode. If disable mapping mode, robot will enable localization mode.
            :param enable:
            :return:
            """
            if enable.enable:
                mode = SLAMMode.MAPPING
            else:
                mode = SLAMMode.LOCALIZATION
            await self.slam_manager.set_slam_mode(mode)

        @self.app.get("/api/core/slam/v1/maps/explore", response_class=Response)
        async def get_map(min_x: float, min_y: float, max_x: float, max_y: float):
            """
            Get explore map.
            :return: map binary data
            """
            data = self.slam_manager.get_map(min_x, min_y, max_x, max_y)
            return Response(
                data,
                media_type="application/octet-stream",
                headers={
                    "Content-Length": str(len(data))
                }
            )

        @self.app.get("/api/core/slam/v1/maps/stcm", response_class=Response)
        async def get_stcm():
            """
            Get stcm
            :return: stcm binary data
            """
            data = await self.slam_manager.get_stcm_file()
            return Response(
                data,
                media_type="application/octet-stream",
                headers={
                    "Content-Length": str(len(data))
                }
            )

        @self.app.put("/api/core/slam/v1/maps/stcm", status_code=200)
        async def set_stcm(request: FastRequest):
            """
            Set stcm
            """
            data = await request.body()

            if not data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Empty body."
                )

            return await self.slam_manager.upload_stcm_file(data)

        @self.app.delete("/api/core/slam/v1/maps", status_code=200)
        async def clear_map():
            """
            Clear map.
            """
            await self.slam_manager.clear_map()

        @self.app.get("/api/core/slam/v1/knownarea", response_model=RectangleArea)
        async def get_known_area():
            """
            Get map known area
            :return: RectangleArea
            """
            return await self.slam_manager.get_known_area()



        #
        # Motion
        #

        @self.app.get("/api/core/motion/v1/actions/:current", response_model=Optional[MotionAction])
        async def get_current_action():
            """
            Get current action.
            :return: MotionAction or empty body. Empty body means there is no current action.
            """
            return self.motion_manager.current_action

        @self.app.delete("/api/core/motion/v1/actions/:current", status_code=200)
        async def stop_current_action():
            """
            Stop the current action.
            :return: 200 if succeeds
            """
            self.motion_manager.stop_current_action()

        @self.app.post("/api/core/motion/v1/actions", response_model=MotionAction)
        async def create_new_action(action: ActionRequest):
            """
            Create a new action
            :return: MotionAction
            """
            response = self.motion_manager.create_action(action)
            if response is not None:
                return response
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid action name or action options."
                )

        @self.app.get("/api/core/motion/v1/path", response_model=PathResponse)
        async def get_path():
            """
            Get path
            :return: Path
            """
            data = self.motion_manager.path
            return PathResponse(path_points=data)

        @self.app.get("/api/core/motion/v1/milestones", response_model=PathResponse)
        async def get_milestones():
            """
            Get milestones
            :return: Milestone Path
            """
            data = self.motion_manager.milestones
            return PathResponse(path_points=data)

        #
        # Artifacts
        #

        @self.app.get("/api/core/artifact/v1/lines/{usage}", response_model=List[VirtualLine])
        async def get_virtual_lines(usage: str):
            """
            Get virtual lines.
            :param usage: "tracks" -> virtual tracks, "walls" -> virtual walls
            :return: a list of VirtualLine
            """
            line_usage = ArtifactLineUsage.parse_artifact_line_usage(usage)
            if line_usage is None:
                raise InvalidArtifactUsageError(usage)
            return self.artifact_manager.get_virtual_lines(line_usage)

        @self.app.post("/api/core/artifact/v1/lines/{usage}", status_code=200)
        async def add_virtual_lines(usage: str, lines: List[VirtualLine]):
            """
            Add new virtual lines.
            :param usage: "trakcs" -> virtual tracks, "walls" -> virtual walls
            :param lines: The new virtual lines
            :return: 200
            """
            line_usage = ArtifactLineUsage.parse_artifact_line_usage(usage)
            if line_usage is None:
                raise InvalidArtifactUsageError(usage)
            return self.artifact_manager.add_virtual_lines(line_usage, lines)

        @self.app.put("/api/core/artifact/v1/lines/{usage}", status_code=200)
        async def modify_virtual_lines(usage: str, lines: List[VirtualLine]):
            """
            Modify virtual lines.
            :param usage: "tracks" -> virtual tracks, "walls" -> virtual walls
            :param lines: The virtual lines which will be modified.
            :return: 200
            """
            line_usage = ArtifactLineUsage.parse_artifact_line_usage(usage)
            if line_usage is None:
                raise InvalidArtifactUsageError(usage)
            return self.artifact_manager.modify_virtual_lines(line_usage, lines)

        @self.app.delete("/api/core/artifact/v1/lines/{usage}", status_code=200)
        async def clear_virtual_lines(usage: str):
            """
            Clear a type of virtual lines
            :param usage: "tracks" -> virtual tracks, "walls" -> virtual walls
            :return: 200
            """
            line_usage = ArtifactLineUsage.parse_artifact_line_usage(usage)
            if line_usage is None:
                raise InvalidArtifactUsageError(usage)
            return self.artifact_manager.clear_virtual_lines(line_usage)

        @self.app.delete("/api/core/artifact/v1/lines/{usage}/{line_id}", status_code=200)
        async def delete_a_virtual_line(usage: str, line_id: int):
            """
            Delete a virtual line
            :param usage: "tracks" -> virtual tracks, "walls" -> virtual walls
            :param id: Line ID
            :return: 200
            """
            line_usage = ArtifactLineUsage.parse_artifact_line_usage(usage)
            if line_usage is None:
                raise InvalidArtifactUsageError(usage)
            return self.artifact_manager.delete_a_virtual_line(usage, line_id)

        @self.app.get("/api/core/artifact/v1/rectangle-areas/{usage}", response_model=List[VirtualArea])
        async def get_virtual_areas(usage: str):
            """
            Get virtual areas
            :param usage: "forbidden_area", "elevator_area", "dangerous_area", "coverage_area", "maintenance_area", "sensor_disable_area", "restricted_area"
            :return: A virtual area list
            """
            area_usage = ArtifactAreaUsage.parse_artifact_area_usage(usage)
            if area_usage is None:
                raise InvalidArtifactUsageError(usage)
            return self.artifact_manager.get_virtual_areas(usage)

        @self.app.post("/api/core/artifact/v1/rectangle-areas/{usage}", status_code=200)
        async def add_virtual_areas(usage: str, data: VirtualArea):
            """
            Add a new virtual area
            :param usage: Artifact Area Usage
            :param data: VirtualArea
            :return: 200
            """
            area_usage = ArtifactAreaUsage.parse_artifact_area_usage(usage)
            if area_usage is None:
                raise InvalidArtifactUsageError(usage)
            return self.artifact_manager.add_new_virtual_area(area_usage, data)

        @self.app.delete("/api/core/artifact/v1/rectangle-areas/{usage}", status_code=200)
        async def clear_virtual_areas(usage: str):
            """
            Clear virtual areas of a type
            :param usage: Artifact Area Usage
            :return: 200
            """
            area_usage = ArtifactAreaUsage.parse_artifact_area_usage(usage)
            if area_usage is None:
                raise InvalidArtifactUsageError(usage)
            return self.artifact_manager.clear_virtual_areas(usage)

        @self.app.put("/api/core/artifact/v1/rectangle-areas/{usage}/{area_id}", status_code=200)
        async def modify_virtual_area(usage: str, area_id: int, data: VirtualArea):
            """
            Modify a virtual area
            :param usage: Artifact Area Usage
            :param area_id: area id
            :return: 200
            """
            area_usage = ArtifactAreaUsage.parse_artifact_area_usage(usage)
            if area_usage is None:
                raise InvalidArtifactUsageError(usage)
            return self.artifact_manager.modify_virtual_area(usage, area_id, data)

        @self.app.delete("/api/core/artifact/v1/rectangle-areas/{usage}/{area_id}", status_code=200)
        async def delete_virtual_area(usage: str, area_id: int):
            """
            Delete a virtual area
            :param usage: Artifact area usage
            :param area_id: area id
            :return: 200
            """
            area_usage = ArtifactAreaUsage.parse_artifact_area_usage(usage)
            if area_usage is None:
                raise InvalidArtifactUsageError(usage)
            return self.artifact_manager.delete_virtual_area(usage, area_id)

        @self.app.get("/api/core/artifact/v1/pois", response_model=List[PointOfInterest])
        async def get_pois():
            """
            Get POIs
            :return: POI List
            """
            return self.artifact_manager.pois

        @self.app.post("/api/core/artifact/v1/pois", status_code=200)
        async def add_poi(data: POIRequest):
            """
            Add a POI. You should use a random UUID string as POI id. When robot is in mapping mode, leave pose to null and robot will use current pose as the POI pose.
            :param data: POIRequest
            :return: 200
            """
            self.artifact_manager.add_poi(data)

        @self.app.delete("/api/core/artifact/v1/pois", status_code=200)
        async def clear_poi():
            """
            Clear POIs
            :return: 200
            """
            self.artifact_manager.clear_pois()

        @self.app.get("/api/core/artifact/v1/pois/{poi_id}", response_model=PointOfInterest)
        async def get_poi_by_id(poi_id: str):
            """
            Get a POI by POI ID
            :param poi_id: POI ID
            :return: POI data
            """
            return self.artifact_manager.get_poi_by_id(poi_id)

        @self.app.put("/api/core/artifact/v1/pois/{poi_id}", status_code=200)
        async def modify_poi_by_id(poi_id: str, data: PointOfInterest):
            """
            Edit a POI by its ID
            :param poi_id: POI ID
            :param data: POI data
            :return: 200
            """
            return self.artifact_manager.modify_poi_by_id(poi_id, data)

        @self.app.delete("/api/core/artifact/v1/pois/{poi_id}", status_code=200)
        async def delete_poi_by_id(poi_id: str):
            """
            Delete a POI by its ID
            :param poi_id: POI ID
            :return: 200
            """
            return self.artifact_manager.delete_poi_by_id(poi_id)

        #
        # multi-floor
        #

        @self.app.post("/api/multi-floor/map/v1/stcm/:save", status_code=200)
        async def save_map_to_disk():
            """
            Save the current map to disk
            :return: 200
            """
            return await self.multi_floor_manager.save_map_to_disk()

    def get_app(self) -> FastAPI:
        """Get FastAPI application instance"""
        return self.app
