"""
FastAPI REST API server
"""
import asyncio
import time

from datetime import datetime
from urllib.request import Request
from fastapi import FastAP<PERSON>, HTTPException, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.requests import Request as FastRequest
from fastapi.responses import PlainTextResponse, Response, ORJSONResponse
from rclpy.node import Node
from threading import Lock
from typing import Optional, List, Dict

from .action_handler import ActionTaskSupervisor, ActionTypeHelper
from .agent_exceptions import InvalidArtifactUsageError, ConflictingCommandError, ROSServiceNoResponseError, UnsupportedActionError, InvalidActionOptionsError, UnsupportedArtifactUsageError, InvalidRequestDataError, InternalAgentError
from .artifact_manager import ArtifactManager
from .home_dock_manager import HomeDockManager
from .industry_manager import IndustryManager
from .models import *
from .models.jack_command import JackCommandEnum
from .multi_floor_manager import MultiFloorManager
from .robot_pose_listener import <PERSON>Pose<PERSON><PERSON><PERSON>
from .slam_manager import SLAMManager
from .system_manager import SystemManager
from .motion_manager import MotionManager


class APIServer:
    """FastAPI server class"""
    
    def __init__(self, node: Node, mode: DeviceModeEnum):
        # FastAPI doc tags
        tags_metadata = [
            {
                "name": "system",
                "description": "System resources"
            },
            {
                "name": "slam",
                "description": "Localization, Slam"
            },
            {
                "name": "artifact",
                "description": "Map elements"
            },
            {
                "name": "motion",
                "description": "Motion control"
            },
            {
                "name": "multi-floor",
                "description": "Multi-floor features"
            },
            {
                "name": "industry",
                "descrption": "Industry features"
            }
        ]
        
        
        """Initialize API server"""
        self.app = FastAPI(
            title="Agent REST API",
            description="RESTful API service for ROS2 Agent node",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc",
            openapi_tags=tags_metadata,
            default_response_class=ORJSONResponse
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,         # type: ignore
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        self._is_switching_mode = False
        self._switching_lock = Lock()

        ActionTypeHelper.initialize(node)

        pose_listener = RobotPoseListener(node)

        self._action_task_supervisor = ActionTaskSupervisor()

        # Initialize home dock manager
        self._home_dock_manager = HomeDockManager(node, pose_listener, mode)

        # Initialize system manager
        self.system_manager = SystemManager(node, pose_listener)

        # Initialize slam manager
        self.slam_manager = SLAMManager(node, pose_listener, self._home_dock_manager)

        # Initialize motion manager
        self.motion_manager = MotionManager(node, pose_listener, self._home_dock_manager, self._action_task_supervisor)

        # Initialise artifact manager
        self.artifact_manager = ArtifactManager(node, pose_listener)

        # Initialise multi-floor manager
        self.multi_floor_manager = MultiFloorManager(node)

        # Initialise industry manager
        self.industry_manager = IndustryManager(node, pose_listener, self.artifact_manager, self._home_dock_manager, self._action_task_supervisor, mode)
        
        # Record startup time
        self.start_time = time.time()

        self._register_error_handlers()
        
        # Register routes
        self._register_routes()

    def _register_error_handlers(self):
        """Register error handlers"""
        @self.app.exception_handler(InternalAgentError)
        async def internal_agent_error_exception_handler(request: Request, exc):
            return PlainTextResponse(str(exc), status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

        @self.app.exception_handler(UnsupportedArtifactUsageError)
        async def unsupported_artifact_usage_exception_handler(request: Request, exc):
            return PlainTextResponse(str(exc), status_code=status.HTTP_400_BAD_REQUEST)

        @self.app.exception_handler(InvalidRequestDataError)
        async def invalid_request_exception_handler(request: Request, exc):
            return PlainTextResponse(str(exc), status_code=status.HTTP_400_BAD_REQUEST)

        @self.app.exception_handler(InvalidActionOptionsError)
        async def invalid_action_options_exception_handler(request: Request, exc):
            return PlainTextResponse(str(exc), status_code=status.HTTP_400_BAD_REQUEST)

        @self.app.exception_handler(UnsupportedActionError)
        async def unsupported_action_error_exception_handler(request: Request, exc):
            return PlainTextResponse(str(exc), status_code=status.HTTP_400_BAD_REQUEST)

        @self.app.exception_handler(ROSServiceNoResponseError)
        async def ros_service_no_response_error_exception_handler(request: Request, exc):
            return PlainTextResponse(str(exc), status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        @self.app.exception_handler(ConflictingCommandError)
        async def conflicting_command_error_handler(request: Request, exc):
            return PlainTextResponse(str(exc), status_code=status.HTTP_409_CONFLICT)

        @self.app.exception_handler(InvalidArtifactUsageError)
        async def invalid_artifact_usage_error_handler(request: Request, exc):
            return PlainTextResponse(str(exc), status_code=status.HTTP_400_BAD_REQUEST)

        @self.app.exception_handler(RequestValidationError)
        async def validation_exception_handler(request: Request, exc):
            return PlainTextResponse("Invalid request data", status_code=status.HTTP_400_BAD_REQUEST)

        @self.app.exception_handler(HTTPException)
        async def http_exception_handler(request: Request, exc):
            return PlainTextResponse(exc.detail, status_code=exc.status_code)

        @self.app.exception_handler(Exception)
        async def global_exception_handler(request: Request, exc):
            return PlainTextResponse(str(exc), status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _register_routes(self):
        """Register API routes"""

        @self.app.get("/")
        async def root() -> APIResponse:
            """Root endpoint"""
            return APIResponse(
                success=True,
                message="Agent REST API Server is running",
                timestamp=datetime.now().isoformat()
            )

        #
        # System
        #
        @self.app.get("/api/core/system/v1/robot/health", tags=["system"])
        async def health_check() -> HealthStatus:
            """Get robot health info"""
            return await self.system_manager.health_status()

        @self.app.get("/api/core/system/v1/capabilities", tags=["system"])
        async def get_capabilities() -> List[Capability]:
            """
            Get system capabilities list
            
            Returns information about all available system capabilities including:
            - Capability name and type
            - Version information
            - Current status
            - Capability description
            - Configuration parameters
            - Dependencies
            """
            return self.system_manager.all_capabilities

        @self.app.get("/api/core/system/v1/robot/info", tags=["system"])
        async def get_info() -> RobotInfo:
            """
            Get robot information
            """
            return self.system_manager.robot_info

        @self.app.get("/api/core/system/v1/power/status", tags=["system"])
        async def get_power_status() -> PowerStatus:
            """
            Get power status, including battery percentage, charging status, etc.
            """
            return await self.system_manager.power_status()

        @self.app.get("/api/core/system/v1/laserscan", tags=["system"])
        async def get_laser_scan() -> LaserScan:
            """
            Get laser scan
            """
            result = await self.system_manager.get_laser_scan()
            if result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Laser scan is not available."
                )
            return result

        @self.app.get("/api/core/system/v1/jack/status", tags=["system"])
        async def get_system_jack_status() -> JackStatus:
            """
            It's **DEPRECATED**.
            Please use the <a href="#/industry/get_jack_status_api_industry_v1_jack_status_get">api</a> in the industry section.
            """
            return await get_jack_status()

        @self.app.post("/api/core/system/v1/jack/status", tags=["system"])
        async def set_system_jack_status(command: str) -> None:
            """
            It's **DEPRECATED**.
            Please use the <a href="#/industry/set_jack_control_api_industry_v1_jack_status_post">api</a> in the industry section.
            """
            if command == "On":
               control = JackCommand(command=JackCommandEnum.LIFT_UP)
            elif command == "Down":
                control = JackCommand(command=JackCommandEnum.LIFT_DOWN)
            elif command == "Stop":
                control = JackCommand(command=JackCommandEnum.STOP)
            elif command == "ClearAlarm":
                control = JackCommand(command=JackCommandEnum.CLEAR_ALARM)
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Unsupported jack command."
                )
            return await set_jack_control(control)


        #
        # Slam
        #

        @self.app.get("/api/core/slam/v1/mode", tags=["slam"])
        async def get_slam_mode() -> SlamModeResponse:
            """
            Get current slam mode.
            """
            mode = await self.slam_manager.get_slam_mode()
            if mode is None:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to get current slam mode."
                )
            return SlamModeResponse(mode=mode.value)

        @self.app.put("/api/core/slam/v1/mode", status_code=status.HTTP_200_OK, tags=["slam"])
        async def set_slam_mode(request: SetSlamModeRequest) -> None:
            """
            Set slam mode.
            """
            with self._switching_lock:
                if self._is_switching_mode:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Device is switching mode. You are not allowed to switch it before the latest operation being finished."
                    )

            mode = request.mode

            with self._switching_lock:
                self._is_switching_mode = True
            await self.motion_manager.stop_current_action()
            result = await self.slam_manager.set_slam_mode(mode, request.reload_map if request.reload_map is not None else False)
            with self._switching_lock:
                self._is_switching_mode = False

            if not result:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to set slam mode: {request.mode}"
                )

        @self.app.get("/api/core/slam/v1/localization/pose", tags=["slam"])
        async def get_pose() -> Pose:
            """
            Get robot pose.
            """
            result = await self.slam_manager.get_pose()
            if result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Failed to get current pose."
                )
            return result

        @self.app.put("/api/core/slam/v1/localization/pose", status_code=status.HTTP_200_OK, tags=["slam"])
        async def set_pose(pose: Pose) -> None:
            """
            Set robot pose.
            """
            await self.slam_manager.set_pose(pose)

        @self.app.get("/api/core/slam/v1/localization/odopose", tags=["slam"])
        async def get_odometry() -> Pose:
            """
            Get odometry
            """
            result = await self.slam_manager.get_odometry()
            if result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Failed to get current pose."
                )
            return result

        @self.app.get("/api/core/slam/v1/localization/quality", response_model=int, tags=["slam"], responses={
            200: {
                "description": "Return localization quality.",
                "content": {
                    "text/plain": {
                        "example": "100"
                    }
                }
            }
        })
        async def get_localization_quality() -> int:
            """
            Get localization quality.
            """
            return await self.slam_manager.get_localization_quality()

        @self.app.get("/api/core/slam/v1/localization/:enable", response_model=bool, tags=["slam"])
        async def is_localization_enabled() -> bool:
            """
            It's **DEPRECATED**.
            Please use <a href="#/slam/get_slam_mode_api_core_slam_v1_mode_get">Get Slam Mode</a>.
            """
            return await self.slam_manager.get_slam_mode() == SLAMMode.LOCALIZATION

        @self.app.put("/api/core/slam/v1/localization/:enable", status_code=200, tags=["slam"])
        async def enable_localization(enable: GeneralEnableRequest) -> None:
            """
            It's **DEPRECATED**.
            Please use <a href="#/slam/set_slam_mode_api_core_slam_v1_mode_put">Set Slam Mode</a>.
            """
            if enable.enable:
                mode = SLAMMode.LOCALIZATION
            else:
                mode = SLAMMode.MAPPING
            await set_slam_mode(SetSlamModeRequest(mode=mode))

        @self.app.get("/api/core/slam/v1/mapping/:enable", response_model=bool, tags=["slam"])
        async def is_mapping_enabled() -> bool:
            """
            It's **DEPRECATED**.
            Please use <a href="#/slam/get_slam_mode_api_core_slam_v1_mode_get">Get Slam Mode</a>.
            """
            return await self.slam_manager.get_slam_mode() == SLAMMode.MAPPING

        @self.app.put("/api/core/slam/v1/mapping/:enable", status_code=200, tags=["slam"])
        async def enable_mapping(enable: GeneralEnableRequest) -> None:
            """
            It's **DEPRECATED**.
            Please use <a href="#/slam/set_slam_mode_api_core_slam_v1_mode_put">Set Slam Mode</a>.
            """
            if enable.enable:
                mode = SLAMMode.MAPPING
            else:
                mode = SLAMMode.LOCALIZATION
            await set_slam_mode(SetSlamModeRequest(mode=mode))

        @self.app.get("/api/core/slam/v1/maps/explore", response_class=Response, tags=["slam"])
        async def get_map(min_x: float, min_y: float, max_x: float, max_y: float) -> Response:
            """
            Get explore map.

            You have to set **min_x**, **min_y**, **max_x**, **max_y** in the query.

            The return data is binary defined as below.

            | Position | type | Description |
            | - | - | - |
            | 0-3 | float | The x of the map start position. |
            | 4-7 | float | The y of the map start position. |
            | 9-11 | uint32 | The grid length in X-axis. |
            | 12-15 | uint32 | The grid length in Y-axis. |
            | 16-19 | float | The resolution of the map. |
            | 20-31 | byte[] | Reserved. |
            | 32-35 | uint32 | The byte count of the following raw map data. It should be equal to the grid length in X-axis multiply the grid length in Y-axis. |
            """
            data = await self.slam_manager.get_map(min_x, min_y, max_x, max_y)
            return Response(
                data,
                media_type="application/octet-stream",
                headers={
                    "Content-Length": str(len(data))
                }
            )

        @self.app.get("/api/core/slam/v1/maps/stcm/:display", tags=["slam"])
        async def get_display_stcm() -> Response:
            """
            Get a stcm file for displaying. If you want to a stcm file to edit and then upload, please use GET /api/core/slam/v1/maps/stcm .
            """
            data = await self.slam_manager.get_stcm_file(lite_version=True)
            if data is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="stcm file not found"
                )
            return Response(
                data,
                media_type="application/octet-stream",
                headers={
                    "Content-Length": str(len(data))
                }
            )

        @self.app.get("/api/core/slam/v1/maps/stcm", response_class=Response, tags=["slam"])
        async def get_stcm() -> Response:
            """
            Get stcm file which is used for editing and then uploading to the robot.
            """
            data = await self.slam_manager.get_stcm_file(lite_version=False)
            if data is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="stcm file not found"
                )
            return Response(
                data,
                media_type="application/octet-stream",
                headers={
                    "Content-Length": str(len(data))
                }
            )

        @self.app.put("/api/core/slam/v1/maps/stcm", response_model=bool, tags=["slam"])
        async def set_stcm(request: FastRequest) -> bool:
            """
            Upload a stcm file to the robot.
            """
            data = await request.body()

            if not data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Empty body."
                )

            return await self.slam_manager.upload_stcm_file(data)

        @self.app.delete("/api/core/slam/v1/maps", status_code=200, tags=["slam"])
        async def clear_map() -> None:
            """
            Clear map.
            """
            with self._switching_lock:
                if self._is_switching_mode:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Device is switching mode. You are not allowed to switch it before the latest operation being finished."
                    )

            with self._switching_lock:
                self._is_switching_mode = True
            result = await self.slam_manager.clear_map()
            with self._switching_lock:
                self._is_switching_mode = False

            if not result:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to clear map."
                )

        @self.app.get("/api/core/slam/v1/knownarea", tags=["slam"])
        async def get_known_area() -> RectangleArea:
            """
            Get map known area.
            """
            result = await self.slam_manager.get_known_area()
            if result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Failed to get known area."
                )
            return result

        @self.app.get("/api/core/slam/v1/homepose", tags=["slam"])
        async def get_home_pose() -> Pose:
            """
            It's **DEPRECATED**.
            Please use <a href="#/slam/get_bound_home_dock_api_core_slam_v1_homepose_bind_get">Get Bound Home Dock</a> instead.
            """
            result = await self._home_dock_manager.get_home_dock()
            if result is None or result.pose is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No bound home pose."
                )
            return Pose(x=result.pose.x, y=result.pose.y, z=0.0, roll=0.0, pitch=0.0, yaw=result.pose.yaw)

        @self.app.put("/api/core/slam/v1/homepose", tags=["slam"])
        async def set_home_pose(pose: Pose) -> bool:
            """
            It's **DEPRECATED**.
            Please use <a href="#/slam/set_bound_home_dock_api_core_slam_v1_homsepose_bind_put">Set Bound Home Dock</a> instead.
            """
            result = await self._home_dock_manager.set_home_pose(pose)
            if not result:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="The pose to bind should be an existing home dock."
                )
            return result

        @self.app.delete("/api/core/slam/v1/homepose", tags=["slam"])
        async def clear_home_pose() -> None:
            """
            It's **DEPRECATED**.
            Please use <a href="$/slam/clear_bound_home_dock_api_core_slam_v1_homepose_bind_delete">Clear Bound Home Dock</a> instead.
            """
            return self._home_dock_manager.clear_home_dock()

        @self.app.get("/api/core/slam/v1/homepose/:bind", tags=["slam"])
        async def get_bound_home_dock() -> HomeDock:
            """
            Get bound home dock.
            """
            result = await self._home_dock_manager.get_home_dock()
            if result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No bound home dock."
                )
            return result

        @self.app.put("/api/core/slam/v1/homepose/:bind", tags=["slam"], openapi_extra={
            "requestBody": {
                "description": "'id' is necessary to bind a home dock."
            }
        })
        async def set_bound_home_dock(dock: HomeDock) -> bool:
            """
            Bind a home dock.
            You should pass a dock id to bind the home dock.
            """
            result = await self._home_dock_manager.set_home_dock(dock)
            if not result:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="The dock to bind should be an existing home dock."
                )
            return result

        @self.app.delete("/api/core/slam/v1/homepose/:bind", tags=["slam"])
        async def clear_bound_home_dock() -> None:
            """
            Unbind the home dock.
            """
            self._home_dock_manager.clear_home_dock()

        @self.app.get("/api/core/slam/v1/homedocks", tags=["slam"])
        async def get_home_docks() -> List[HomeDock]:
            """
            Get all home docks
            """
            return await self._home_dock_manager.get_all_docks()

        @self.app.put("/api/core/slam/v1/homedocks", tags=["slam"])
        async def set_home_docks(docks: List[HomeDock]) -> bool:
            """
            Set home docks. This operation will clear the existing home docks first and then add the docks.
            """
            return await self._home_dock_manager.add_home_docks(docks, True)

        @self.app.post("/api/core/slam/v1/homedocks", tags=["slam"])
        async def add_home_dock(dock: HomeDock) -> bool:
            """
            Add a home dock
            """
            return await self._home_dock_manager.add_home_docks([dock], False)

        @self.app.delete("/api/core/slam/v1/homedocks", tags=["slam"])
        async def delete_all_home_docks() -> bool:
            """
            Delete all home docks
            """
            return await self._home_dock_manager.delete_all_docks()

        @self.app.post("/api/core/slam/v1/homedocks/:register", tags=["slam"])
        async def register_home_dock(dock: HomeDock|None=None) -> HomeDock:
            """
            Register a home dock at current pose. You can provide a display_name for the new dock otherwise a random display_name will be generated.
            """
            display_name = dock.metadata.display_name if dock is not None and dock.metadata is not None else None
            result = await self._home_dock_manager.register_home_dock(display_name)
            if result is None:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Failed to register a dock."
                )
            return result

        @self.app.put("/api/core/slam/v1/homedocks/{dock_id}", tags=["slam"])
        async def modify_home_dock(dock_id: str, dock: HomeDock) -> bool:
            """
            Edit a home dock.
            """
            return await self._home_dock_manager.modify_home_dock(dock_id, dock)

        @self.app.delete("/api/core/slam/v1/homedocks/{dock_id}", tags=["slam"])
        async def delete_home_dock(dock_id: str) -> bool:
            """
            Delete a home dock.
            """
            return await self._home_dock_manager.delete_dock(dock_id)


        #
        # Motion
        #
        @self.app.get("/api/core/motion/v1/action-factories", tags=["motion"])
        async def get_action_factories() -> List[ActionFactory]:
            """
            Get all supported actions.
            """
            return [ActionFactory(action_name=mem.value) for mem in ActionDefinition] # type: ignore

        @self.app.get("/api/core/motion/v1/actions/:current", tags=["motion"])
        async def get_current_action() -> MotionAction:
            """
            Get current action. If the robot is idle, it will return 404.
            """
            result = await self.motion_manager.get_current_action()
            if result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No current action."
                )
            return result

        @self.app.delete("/api/core/motion/v1/actions/:current", status_code=200, tags=["motion"])
        async def stop_current_action() -> None:
            """
            Stop the current action.
            """
            await self.motion_manager.stop_current_action()

        @self.app.post("/api/core/motion/v1/actions", tags=["motion"])
        async def create_new_action(action: ActionRequest) -> MotionAction:
            """
            Create a new action.
            """
            with self._switching_lock:
                if self._is_switching_mode:
                    raise ConflictingCommandError("Robot is switching mode. You are not allowed to create actions.")

            response = await self.motion_manager.create_action(action)
            if response is not None:
                return response
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid action name or action options."
                )

        @self.app.get("/api/core/motion/v1/actions/{action_id}", tags=["motion"])
        async def get_action_by_id(action_id: int) -> MotionAction:
            """
            Get the action by a given id. The robot keeps the latest 20 actions.
            """
            action = self.motion_manager.get_action_by_id(action_id)
            if action is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Action {action_id} does not exist."
                )
            return action

        @self.app.get("/api/core/motion/v1/path", tags=["motion"], responses={
            200: {
                "description": "Path points",
                "content": {
                    "application/json": {
                        "example": { "path_points": [[1.23, 0.5], [1.33, 0.5], [1.48, 0.6], [1.63, 0.63]] }
                    }
                }
            }
        })
        async def get_path() -> Dict[str, List[List[float]]]:
            """
            Get path.
            """
            return {"path_points": await self.motion_manager.get_path()}

        @self.app.get("/api/core/motion/v1/milestones", tags=["motion"], responses={
            200: {
                "description": "Milestone points",
                "content": {
                    "application/json": {
                        "example": { "path_points": [[1.23, 0.5], [1.33, 0.5], [1.48, 0.6], [1.63, 0.63]] }
                    }
                }
            }
        })
        async def get_milestones() -> Dict[str, List[List[float]]]:
            """
            Get milestones.
            """
            return {"path_points": await self.motion_manager.get_milestones()}

        #
        # Artifacts
        #

        @self.app.get("/api/core/artifact/v1/lines/{usage}", tags=["artifact"])
        async def get_virtual_lines(usage: ArtifactLineUsage) -> List[VirtualLine]:
            """
            Get virtual lines.
            """
            return await self.artifact_manager.get_virtual_lines(usage)

        @self.app.post("/api/core/artifact/v1/lines/{usage}", response_model=bool, tags=["artifact"])
        async def add_virtual_lines(usage: ArtifactLineUsage, lines: List[VirtualLine]) -> bool:
            """
            Add new virtual lines.
            """
            return await self.artifact_manager.add_virtual_lines(usage, lines)

        @self.app.put("/api/core/artifact/v1/lines/{usage}", response_model=bool, tags=["artifact"])
        async def modify_virtual_lines(usage: ArtifactLineUsage, lines: List[VirtualLine]) -> bool:
            """
            Modify virtual lines.
            """
            return await self.artifact_manager.modify_virtual_lines(usage, lines)

        @self.app.delete("/api/core/artifact/v1/lines/{usage}", response_model=bool, tags=["artifact"])
        async def clear_virtual_lines(usage: ArtifactLineUsage) -> bool:
            """
            Clear a type of virtual lines.
            """
            return await self.artifact_manager.clear_virtual_lines(usage)

        @self.app.delete("/api/core/artifact/v1/lines/{usage}/{line_id}", response_model=bool, tags=["artifact"])
        async def delete_a_virtual_line(usage: ArtifactLineUsage, line_id: int) -> bool:
            """
            Delete a virtual line.
            """
            return await self.artifact_manager.delete_a_virtual_line(usage, line_id)

        @self.app.get("/api/core/artifact/v1/rectangle-areas/{usage}", tags=["artifact"])
        async def get_virtual_areas(usage: ArtifactAreaUsage) -> List[VirtualArea]:
            """
            Get virtual areas.
            """
            return await self.artifact_manager.get_virtual_areas(usage)

        @self.app.post("/api/core/artifact/v1/rectangle-areas/{usage}", response_model=bool, tags=["artifact"])
        async def add_virtual_area(usage: ArtifactAreaUsage, data: VirtualArea) -> bool:
            """
            Add a new virtual area.
            """
            return await self.artifact_manager.add_new_virtual_area(usage, data)

        @self.app.delete("/api/core/artifact/v1/rectangle-areas/{usage}", response_model=bool, tags=["artifact"])
        async def clear_virtual_areas(usage: ArtifactAreaUsage) -> bool:
            """
            Clear virtual areas of a type.
            """
            return await self.artifact_manager.clear_virtual_areas(usage)

        @self.app.put("/api/core/artifact/v1/rectangle-areas/{usage}/{area_id}", response_model=bool, tags=["artifact"])
        async def modify_virtual_area(usage: ArtifactAreaUsage, area_id: int, data: VirtualArea) -> bool:
            """
            Modify a virtual area.
            """
            return await self.artifact_manager.modify_virtual_area(usage, area_id, data)

        @self.app.delete("/api/core/artifact/v1/rectangle-areas/{usage}/{area_id}", response_model=bool, tags=["artifact"])
        async def delete_virtual_area(usage: ArtifactAreaUsage, area_id: int) -> bool:
            """
            Delete a virtual area.
            """
            return await self.artifact_manager.delete_virtual_area(usage, area_id)

        @self.app.get("/api/core/artifact/v1/pois", tags=["artifact"])
        async def get_pois() -> List[PointOfInterest]:
            """
            Get POIs.
            """
            result = await self.artifact_manager.get_pois()
            return result if result is not None else []

        @self.app.post("/api/core/artifact/v1/pois", response_model=bool, tags=["artifact"])
        async def add_poi(data: PointOfInterest) -> bool:
            """
            Add a POI. You should use a random UUID string as POI id. When robot is in mapping mode, leave pose to null and robot will use current pose as the POI pose.
            """
            return await self.artifact_manager.add_poi(data)

        @self.app.delete("/api/core/artifact/v1/pois", response_model=bool, tags=["artifact"])
        async def clear_poi() -> bool:
            """
            Clear POIs
            """
            return await self.artifact_manager.clear_pois()

        @self.app.get("/api/core/artifact/v1/pois/{poi_id}", tags=["artifact"])
        async def get_poi_by_id(poi_id: str) -> PointOfInterest:
            """
            Get a POI by POI ID
            """
            response = await self.artifact_manager.get_poi_by_id(poi_id)
            if response is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"POI {poi_id} does not exist."
                )
            return response

        @self.app.put("/api/core/artifact/v1/pois/{poi_id}", response_model=bool, tags=["artifact"])
        async def modify_poi_by_id(poi_id: str, data: PointOfInterest) -> bool:
            """
            Edit a POI by its ID
            """
            return await self.artifact_manager.modify_poi_by_id(poi_id, data)

        @self.app.delete("/api/core/artifact/v1/pois/{poi_id}", response_model=bool, tags=["artifact"])
        async def delete_poi_by_id(poi_id: str) -> bool:
            """
            Delete a POI by its ID
            """
            return await self.artifact_manager.delete_poi_by_id(poi_id)

        #
        # multi-floor
        #

        @self.app.post("/api/multi-floor/map/v1/stcm/:save", response_model=bool, tags=["multi-floor"])
        async def save_map_to_disk() -> bool:
            """
            Save the current map to disk
            """
            return await self.multi_floor_manager.save_map_to_disk()

        @self.app.get("/api/multi-floor/map/v1/pois", tags=["multi-floor"])
        async def get_multi_floor_pois() -> List[MultiFloorPOI]:
            """
            Currently it's no difference from <a href="#/artifact/v1/pois">Get POIs</a>.
            """
            pois = await self.artifact_manager.get_pois()
            multi: List[MultiFloorPOI] = []
            for poi in pois:
                display_name = poi.metadata.display_name if poi.metadata is not None and poi.metadata.display_name is not None else ""
                type = poi.metadata.type if poi.metadata is not None and poi.metadata.type is not None else ""
                multi.append(MultiFloorPOI(id=poi.id, poi_name=display_name, type=type, floor="1", building="1", pose=poi.pose))
            return multi


        #
        # Industry
        #

        @self.app.get("/api/industry/v1/jack/status", response_model=JackStatus, tags=["industry"])
        async def get_jack_status() -> JackStatus:
            """
            Get jack status.
            """
            result = await self.industry_manager.get_jack_status()
            if result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Jack status is not available."
                )
            return result

        @self.app.post("/api/industry/v1/jack/status", status_code=200, tags=["industry"])
        async def set_jack_control(control: JackCommand) -> None:
            """
            Send a jack command

            It's not recommanded to use this api directly. To control jack, it's better to create a JackMoveAction.

            This api will not modify robot size. It will control jack directly. If you want to lifet up a shelf and then move, Please control jack by creating a JackMoveAction.
            """
            if not await self.industry_manager.set_jack_status(control):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Jack is not idle."
                )

        @self.app.get("/api/industry/v1/stage", tags=["industry"])
        async def get_industry_device_stage():
            """
            Get device stage.
            """
            result = await self.industry_manager.get_industry_stage()
            if result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Failed to get device stage."
                )
            return {"stage": result}

        @self.app.post("/api/industry/v1/tasks", tags=["industry"])
        async def create_industry_task(task: IndustryTaskModel) -> IndustryTaskResponse:
            """
            Create an industry task.
            """
            result = await self.industry_manager.create_industry_task(task)
            if result is not None:
                return result
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create industry task."
                )

        @self.app.put("/api/industry/v1/tasks/:end_operation", tags=["industry"])
        async def stop_waiting_and_run_next_action():
            """
            Stop current action and start next action.
            """
            if not await self.industry_manager.stop_current_action_and_run_next_action():
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to stop current action."
                )

        @self.app.delete("/api/industry/v1/tasks", tags=["industry"])
        async def clear_industry_tasks():
            """
            Stop the running industry task and clear all queuing industry tasks.
            """
            if not await self.industry_manager.delete_all_tasks():
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to clear industry tasks."
                )

        @self.app.get("/api/industry/v1/shelves", tags=["industry"])
        async def get_shelves() -> List[ConfigShelfInfo]:
            """
            Get saved shelves.
            """
            return await self.industry_manager.get_shelves()

        @self.app.get("/api/industry/v1/shelves/{shelf_id}", tags=["industry"])
        async def get_shelf(shelf_id: str) -> ConfigShelfInfo:
            """
            Get a specific shelf.
            """
            result = await self.industry_manager.get_shelf(shelf_id)
            if result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Shelf {shelf_id} does not exist."
                )
            return result

        @self.app.post("/api/industry/v1/shelves", tags=["industry"])
        async def add_shelf(data: ShelfInfo) -> ConfigShelfInfo:
            """
            Add a shelf.
            """
            return await self.industry_manager.add_shelf(data)

        @self.app.delete("/api/industry/v1/shelves", tags=["industry"])
        async def delete_all_shelves():
            """
            Delete all shelves.
            """
            if not await self.industry_manager.clear_shelves():
                raise InternalAgentError("Failed to delete shelves.")

        @self.app.delete("/api/industry/v1/shelves/{shelf_id}", tags=["industry"])
        async def delete_shelf(shelf_id: str):
            """
            Delete a shelf.
            """
            if not await self.industry_manager.delete_shelf(shelf_id):
                raise InternalAgentError("Failed to delete the shelf.")

        @self.app.put("/api/industry/v1/shelves/{shelf_id}", tags=["industry"])
        async def edit_shelf(shelf_id: str, data: ShelfInfo):
            """
            Edit a shelf.
            """
            if not await self.industry_manager.modify_shelf(shelf_id, data):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Shelf {shelf_id} does not exist."
                )


    def get_app(self) -> FastAPI:
        """Get FastAPI application instance"""
        return self.app
