"""
Data model definitions
"""
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional


class Capability(BaseModel):
    """Single capability data model"""
    name: str = Field(..., description="Capability name")
    version: str = Field(..., description="Version number")
    enabled: bool = Field(..., description="Current status")


# CapabilitiesResponse is now just a list of capabilities
CapabilitiesResponse = List[Capability]


class APIResponse(BaseModel):
    """Generic API response model"""
    success: bool = Field(..., description="Request success status")
    message: str = Field(..., description="Response message")
    data: Optional[Any] = Field(None, description="Response data")
    timestamp: str = Field(..., description="Response timestamp")


class BaseError(BaseModel):
    """Base error model for HealthStatus"""
    id: int = Field(..., description="Error ID")
    component: int = Field(..., description="Component ID")
    errorCode: int = Field(..., description="Error code")
    level: int = Field(..., description="Error level")
    message: str = Field(..., description="Error message")


class HealthStatus(BaseModel):
    """Health status model"""
    hasWarning: bool = Field(..., description="Has warning")
    hasError: bool = Field(..., description="Has error")
    hasFatal: bool = Field(..., description="Has fatal")
    baseError: List[BaseError] = Field(..., description="List of errors")


class RobotInfo(BaseModel):
    """Robot Info model"""
    manufacturerId: int = Field(..., description="Manufacturer ID")
    manufacturerName: str = Field(..., description="Manufacturer Name")
    modelId: int = Field(..., description="Model ID")
    modelName: str = Field(..., description="Model Name")
    deviceID: str = Field(..., description="Device ID")
    hardwareVersion: str = Field(..., description="Hardware Version")
    softwareVersion: str = Field(..., description="Software Version")


class PowerStatus(BaseModel):
    """Power status model"""
    batteryPercentage: int = Field(..., description="Battery percentage")
    dockingStatus: str = Field(..., description="Docking status")
    isCharging: bool = Field(..., description="Is charging")
    isDCConnected: bool = Field(..., description="Is DC connected")
    powerStage: str = Field(..., description="Power stage")
    sleepMode: str = Field(..., description="Sleep mode")


class SimplePose(BaseModel):
    """Pose of no roll, pitch, yaw"""
    x: float = Field(..., description="x position")
    y: float = Field(..., description="y position")
    z: Optional[float] = Field(default=0.0, description="z position")


class Pose(BaseModel):
    """Pose model"""
    x: float = Field(..., description="X position")
    y: float = Field(..., description="Y position")
    z: float = Field(..., description="Z position")
    roll: float = Field(..., description="Roll angle")
    pitch: float = Field(..., description="Pitch angle")
    yaw: float = Field(..., description="Yaw angle")


class MotionActionState(BaseModel):
    status: int = Field(..., description="State")
    result: int = Field(..., description="State result")
    reason: str = Field(..., description="Reason")


class MotionAction(BaseModel):
    """Action Model"""
    action_id: int = Field(..., description="Action ID")
    action_name: str = Field(..., description="Action Name")
    stage: str = Field(..., description="Action Stage")
    state: MotionActionState = Field(..., description="Motion Action State")


class LaserPoint(BaseModel):
    """LaserPoint Model"""
    distance: float = Field(..., description="Distance between laser point and device pose")
    angle: float = Field(..., description="Angle of laser point to device pose")
    valid: bool = Field(..., description="Whether the laser point is valid or not.")


class LaserScanResponse(BaseModel):
    """LaserScan model"""
    pose: Pose = Field(..., description="Device Pose")
    laser_points: List[LaserPoint] = Field(..., description="Laser points")


class RectangleArea(BaseModel):
    x: float = Field(description="X")
    y: float = Field(description="Y")
    width: float = Field(description="Width")
    height: float = Field(description="Height")


class PointOfInterest(BaseModel):
    id: Optional[str] = Field(default=None, description="POI id")
    pose: SimplePose = Field(..., description="Pose")
    metadata: Dict[str, str] = Field(description="Pose metadata")


class VirtualLine(BaseModel):
    id: Optional[int] = Field(default=None, description="Line ID")
    start: SimplePose = Field(description="Start point")
    end: SimplePose = Field(description="End point")
    metadata: Dict[str, str] = Field(description="Metadata")


class VirtualAreaData(BaseModel):
    start: SimplePose = Field(description="Start point")
    end: SimplePose = Field(description="End point")
    half_width: float = Field(description="Half width")


class VirtualArea(BaseModel):
    id: Optional[int] = Field(default=None, description="Area ID")
    usage: str = Field(description="Area usage")
    metadata: Dict[str, str] = Field(description="Metadata")
    area: VirtualAreaData = Field(description="Area data")


class PathResponse(BaseModel):
    path_points: List[List[float]] = Field(description="Path point")