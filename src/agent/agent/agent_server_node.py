#!/usr/bin/env python3
"""
Main entry point for ROS2 Agent node and FastAPI server
"""
from events_executor import EventsExecutor
import rclpy
from rclpy.node import Node
import uvicorn
import threading

# Handle both relative and absolute imports
try:
    from .api_server import APIServer # type: ignore
except ImportError:
    from agent.api_server import APIServer # type: ignore

try:
    from .models import DeviceMode<PERSON>num # type: ignore
except ImportError:
    from agent.models import DeviceMode<PERSON>num # type: ignore


class AgentServerNode(Node):
    """ROS2 Agent node with integrated FastAPI server"""
    
    def __init__(self):
        """Initialize the node"""
        super().__init__('agent_server_node')

        # declare parameters
        self.declare_parameter("api_port", 1448)
        self.declare_parameter("api_host", "0.0.0.0")
        self.declare_parameter("log_level", "warning")
        self.declare_parameter('scan_topic', "fusion_scan")
        self.declare_parameter("mode", "real")

        mode_str = self.get_parameter("mode").get_parameter_value().string_value
        mode = DeviceModeEnum.REAL if mode_str == "real" else DeviceModeEnum.SIMULATION

        # Create API server
        self.api_server = APIServer(self, mode) # type: ignore
        
        # Create API server thread
        self.api_thread = threading.Thread(
            target=self._run_api_server,
            daemon=True
        )
        
        # Start API server
        self.api_thread.start()
        
        self.get_logger().info('Agent Server Node started')
    
    def _run_api_server(self):
        """Run API server in separate thread"""

        host = self.get_parameter("api_host").get_parameter_value().string_value
        port = self.get_parameter("api_port").get_parameter_value().integer_value
        log_level = self.get_parameter("log_level").get_parameter_value().string_value

        uvicorn.run(
            self.api_server.get_app(),
            loop='uvloop',
            host=host,
            port=port,
            log_level=log_level,
        )


def main(args=None):
    """Main function"""
    try:
        # Initialize ROS2
        rclpy.init(args=args)
        
        # Create node
        node = AgentServerNode()
        
        # Run node
        rclpy.spin(node, executor=EventsExecutor())
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Error occurred: {e}")
    finally:
        # Clean up resources
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()