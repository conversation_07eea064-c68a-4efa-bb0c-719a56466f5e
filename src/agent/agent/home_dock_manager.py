import orj<PERSON>
import os

from ament_index_python.packages import get_package_share_directory
from geometry_msgs.msg import Po<PERSON>
from opennav_docking_msgs.msg import Dock
from opennav_docking_msgs.srv import DeleteDock, GetAllDocks, RegisterDock, SetDocks, UpdateDock
from rclpy.node import Node
from typing import List

from .agent_exceptions import ROSServiceNoResponseError
from .base_manager import BaseManager
from .models import HomeDock, Pose2D, DeviceModeEnum, Pose as APIPose, HomeDockMetadata
from .robot_pose_listener import RobotPoseListener
from .utils import quaternion_to_euler, generate_random_str, euler_to_quaternion, is_same_dock, read_from_file, write_to_file, delete_file, api_handler


_HOME_POSE_BOUND_FILE_PATH = "/opt/rslamware_data/maps/bound_home_pose.json"


class HomeDockManager(BaseManager):
    _home_pose: HomeDock|None = None

    def __init__(self, node: Node, pose_listener: RobotPoseListener, mode: <PERSON><PERSON><PERSON>odeEnum):
        super().__init__(node)

        self._pose_listener = pose_listener

        self._home_pose_file = _HOME_POSE_BOUND_FILE_PATH
        if mode == DeviceModeEnum.SIMULATION:
            self._home_pose_file = os.path.join(get_package_share_directory('simulator'), 'map/bound_home_pose.json')

        self._init_home_pose()

    def _subscribe_to_topics(self):
        pass

    def _create_clients(self):
        self._create_client("docking_server/delete_dock", DeleteDock)
        self._create_client("docking_server/get_all_docks", GetAllDocks)
        self._create_client("docking_server/register_dock", RegisterDock)
        self._create_client("docking_server/set_docks", SetDocks)
        self._create_client("docking_server/update_dock", UpdateDock)

    def _init_home_pose(self):
        raw_data = read_from_file(self._home_pose_file)
        if raw_data is None:
            return

        try:
            json_data = orjson.loads(raw_data)
            model = HomeDock.model_validate(json_data)
            self._home_pose = model
        except Exception:
            pass

    def clear_home_dock(self):
        self._home_pose = None
        delete_file(self._home_pose_file)

    @api_handler(default_return=None)
    async def get_home_dock(self) -> HomeDock | None:
        return self._home_pose

    @api_handler(default_return=False)
    async def set_home_pose(self, pose: APIPose) -> bool:
        pose_in_2d = Pose2D(x=pose.x, y=pose.y, yaw=pose.yaw)
        all_docks = await self.get_all_docks()
        dock: HomeDock|None = None
        for d in all_docks:
            if d.pose is not None and is_same_dock(d.pose, pose_in_2d):
                dock = d
                break

        if dock is None:
            return False

        data = orjson.dumps(dock.model_dump(mode='json')).decode(encoding="utf-8")
        result = write_to_file(self._home_pose_file, data)
        if result:
            self._home_pose = dock
        return result

    @api_handler(default_return=False)
    async def set_home_dock(self, dock: HomeDock) -> bool:
        if dock.id is None:
            return False

        dock_to_bind: HomeDock|None = None

        all_docks = await self.get_all_docks()
        for d in all_docks:
            if d.id == dock.id:
                dock_to_bind = d
                break

        if dock_to_bind is None:
            return False

        data = orjson.dumps(dock_to_bind.model_dump(mode='json')).decode(encoding="utf-8")
        result = write_to_file(self._home_pose_file, data)
        if result:
            self._home_pose = dock_to_bind
        return result

    @api_handler(default_return=[])
    async def get_all_docks(self) -> List[HomeDock]:
        client = self._get_client("docking_server/get_all_docks", GetAllDocks)
        if client is None:
            raise ROSServiceNoResponseError("get_all_docks")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("get_all_docks")

        response = await client.call_async(GetAllDocks.Request())

        if response is None or not response.success:
            raise ROSServiceNoResponseError("get_all_docks")

        docks: List[HomeDock] = []
        for data in response.docks:
            _, _, yaw = quaternion_to_euler(data.pose.orientation.x, data.pose.orientation.y, data.pose.orientation.z, data.pose.orientation.w)
            pose = Pose2D(x=data.pose.position.x, y=data.pose.position.y, yaw=yaw)
            if data.display_name is not None:
                metadata = HomeDockMetadata(display_name=data.display_name)
            else:
                metadata = None
            docks.append(HomeDock(id=data.dock_id, pose=pose, metadata=metadata))

        return docks

    async def add_home_docks(self, docks: List[HomeDock], clear_existing: bool) -> bool:
        client = self._get_client("docking_server/set_docks", SetDocks)
        if client is None:
            raise ROSServiceNoResponseError("set_docks")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("set_docks")

        data: List[Dock] = []
        for dock in docks:
            if dock.id is None or dock.pose is None:
                continue
            qx, qy, qz, qw = euler_to_quaternion(0, 0, dock.pose.yaw)
            pose = Pose()
            pose.position.x = dock.pose.x
            pose.position.y = dock.pose.y
            pose.position.z = 0.0
            pose.orientation.x = qx
            pose.orientation.y = qy
            pose.orientation.z = qz
            pose.orientation.w = qw
            display_name = dock.get_display_name()
            if display_name is None:
                display_name = generate_random_str(prefix="DOCK")
            data.append(Dock(dock_id=dock.id, pose=pose, display_name=display_name))

        response = await client.call_async(SetDocks.Request(clear_existing=clear_existing, docks=data))
        return response is not None and response.success

    @api_handler(default_return=False)
    async def delete_all_docks(self) -> bool:
        client = self._get_client("docking_server/delete_dock", DeleteDock)
        if client is None:
            raise ROSServiceNoResponseError("delete_dock")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("delete_dock")

        response = await client.call_async(DeleteDock.Request(delete_all=True))

        if response is not None and response.success:
            self.clear_home_dock()
            return True
        else:
            return False

    @api_handler(default_return=False)
    async def delete_dock(self, dock_id: str) -> bool:
        client = self._get_client("docking_server/delete_dock", DeleteDock)
        if client is None:
            raise ROSServiceNoResponseError("delete_dock")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("delete_dock")

        response = await client.call_async(DeleteDock.Request(delete_all=False, dock_id=dock_id))

        if response is not None and response.success:
            if self._home_pose is not None and dock_id == self._home_pose.id:
                self.clear_home_dock()
            return True
        else:
            return False

    @api_handler(default_return=None)
    async def register_home_dock(self, display_name: str|None) -> HomeDock|None:
        client = self._get_client("docking_server/register_dock", RegisterDock)
        if client is None:
            raise ROSServiceNoResponseError("register_dock")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("register_dock")

        if display_name is None:
            display_name = generate_random_str(prefix="DOCK")
        response = await client.call_async(RegisterDock.Request(display_name=display_name))

        if response is not None and response.success:
            pose = self._pose_listener.get_pose()
            if pose is None:
                return None
            return HomeDock(id=response.dock_id, pose=Pose2D(x=pose.x, y=pose.y, yaw=pose.yaw), metadata=HomeDockMetadata(display_name=display_name))
        else:
            return None

    @api_handler(default_return=False)
    async def modify_home_dock(self, dock_id: str, home_dock: HomeDock) -> bool:
        client = self._get_client("docking_server/update_dock", UpdateDock)
        if client is None:
            raise ROSServiceNoResponseError("update_dock")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("update_dock")

        dock = Dock(dock_id=dock_id)
        if home_dock.pose is not None:
            qx, qy, qz, qw = euler_to_quaternion(0, 0, home_dock.pose.yaw)
            pose = Pose()
            pose.position.x = home_dock.pose.x
            pose.position.y = home_dock.pose.y
            pose.position.z = 0.0
            pose.orientation.x = qx
            pose.orientation.y = qy
            pose.orientation.z = qz
            pose.orientation.w = qw
            dock.pose = pose
        display_name = home_dock.get_display_name()
        if display_name is not None:
            dock.display_name = display_name

        response = await client.call_async(UpdateDock.Request(dock=dock))
        return response is not None and response.success
