import asyncio
import time

from abc import ABC
from action_msgs.msg import Goal<PERSON>tatus
from enum import Enum
from rclpy.action import ActionClient
from rclpy.node import Node
from rclpy.task import Future
from typing import TypeVar, Generic, Type, cast

from cartographer_ros_msgs.action import Relocalize
from interfaces.action import ModifyMonitorParameters, ResetMonitorParameters
from nav2_msgs.action import NavigateToPose
from opennav_docking_msgs.action import DockRobot, UndockRobot
from sl_vcu_all.action import JackControl
from ..agent_exceptions import ROSServiceNoResponseError
from .base_executor import BaseExecutor, ExecutorStatus
from ..models import ActionDefinition
from ..utils import rclpy_to_wrapped_future

T = TypeVar('T')


class ActionExecutorType(Enum):
    DOCK_EXECUTOR=0,
    JACK_EXECUTOR=1,
    MODIFY_MONITOR_EXECUTOR=2,
    NAVIGATE_EXECUTOR=3,
    RELOCATE_EXECUTOR=4,
    RESET_MONITOR_EXECUTOR=5,
    UNDOCK_EXECUTOR=6


class ActionTypeHelper:
    _instance: 'ActionTypeHelper|None' = None

    def __new__(cls, *args, **kwargs) -> 'ActionTypeHelper':
        if cls._instance is None:
            created = super().__new__(cls, *args, **kwargs)
            cls._instance = cast('ActionTypeHelper', created)
        return cls._instance

    def __init__(self, node: Node):
        self._dock_client = ActionClient(node, DockRobot, "dock_robot")
        self._jack_client = ActionClient(node, JackControl, "jack_control")
        self._modify_monitor_client = ActionClient(node, ModifyMonitorParameters, "modify_monitor_parameters")
        self._navigate_client = ActionClient(node, NavigateToPose, "navigate_to_pose")
        self._relocate_client = ActionClient(node, Relocalize, "relocalize")
        self._reset_monitor_client = ActionClient(node, ResetMonitorParameters, "reset_monitor_parameters")
        self._undock_client = ActionClient(node, UndockRobot, "undock_robot")

    @classmethod
    def initialize(cls, node: Node):
        if cls._instance is None:
            inst = cls.__new__(cls)
            inst.__init__(node)
            cls._instance = inst

    @classmethod
    def instance(cls) -> 'ActionTypeHelper':
        if cls._instance is None:
            raise RuntimeError("ActionTypeHelper not initialized")
        return cls._instance

    def get_action_client(self, action_type: ActionExecutorType) -> ActionClient:
        match action_type:
            case ActionExecutorType.DOCK_EXECUTOR:
                return self._dock_client
            case ActionExecutorType.JACK_EXECUTOR:
                return self._jack_client
            case ActionExecutorType.MODIFY_MONITOR_EXECUTOR:
                return self._modify_monitor_client
            case ActionExecutorType.NAVIGATE_EXECUTOR:
                return self._navigate_client
            case ActionExecutorType.RELOCATE_EXECUTOR:
                return self._relocate_client
            case ActionExecutorType.RESET_MONITOR_EXECUTOR:
                return self._reset_monitor_client
            case ActionExecutorType.UNDOCK_EXECUTOR:
                return self._undock_client
            case _:
                raise ValueError(f"Unknown action executor type: {action_type}")


class ActionExecutor(Generic[T], BaseExecutor, ABC):
    def __init__(self, node: Node, action_type: ActionExecutorType, ros_action_name: str, agent_action_name: ActionDefinition):
        super().__init__()

        self._node = node

        self._agent_action_name = agent_action_name

        self._client = ActionTypeHelper.instance().get_action_client(action_type=action_type)

        self._ros_action_name = ros_action_name

        self._goal_request_future: Future|None = None
        self._goal_result_future: Future|None = None
        self._goal_handle = None

        self._goal: Type[T]|None = None

        self._is_canceling = False

        self._is_canceling = False

    @property
    def agent_action_name(self) -> ActionDefinition:
        return self._agent_action_name

    def prepare_goal(self, goal):
        self._goal = goal
        self._is_canceling = False
        self._update_status(ExecutorStatus.NOT_STARTED)

    async def execute_async(self) -> ExecutorStatus:
        retry_count = 0
        while not self._client.wait_for_server(timeout_sec=0.1):
            retry_count += 1
            if retry_count > 5:
                raise ROSServiceNoResponseError(self._ros_action_name)

        if self._goal is None:
            self._update_status(ExecutorStatus.FAILED)
            return ExecutorStatus.FAILED

        request_future = self._client.send_goal_async(self._goal)
        goal_handle = await request_future

        if goal_handle is None:
            self._update_status(ExecutorStatus.SUCCEEDED)
            return ExecutorStatus.SUCCEEDED

        if not goal_handle.accepted:
            self._update_status(ExecutorStatus.REJECTED)
            return ExecutorStatus.REJECTED

        self._update_status(ExecutorStatus.RUNNING)

        result_future = goal_handle.get_result_async()
        result = await result_future

        if result.status == GoalStatus.STATUS_SUCCEEDED:
            final = ExecutorStatus.SUCCEEDED
        elif result.status in (
            GoalStatus.STATUS_CANCELED,
            GoalStatus.STATUS_ABORTED,
            GoalStatus.STATUS_CANCELING,
        ):
            final = ExecutorStatus.CANCELED
        else:
            final = ExecutorStatus.FAILED

        self._update_status(final)
        return final

    def execute(self) -> bool:
        retry_count = 0
        while not self._client.wait_for_server(timeout_sec=0.1):
            retry_count += 1
            if retry_count > 5:
                raise ROSServiceNoResponseError(self._ros_action_name)

        if self._goal is None:
            self._update_status(ExecutorStatus.FAILED)
            return False

        self._is_canceling = False
        self._goal_request_future = self._client.send_goal_async(self._goal)
        self._goal_request_future.add_done_callback(self._goal_request_callback)
        return True

    async def cancel(self) -> bool:
        if self.status != ExecutorStatus.RUNNING:
            return True

        self._is_canceling = True

        if self._goal_handle is not None:
            wrapped_future = rclpy_to_wrapped_future(self._goal_handle.cancel_goal_async())
            await asyncio.wrap_future(wrapped_future)
            await asyncio.wrap_future(rclpy_to_wrapped_future(self._goal_handle.get_result_async()))

        if self._goal_request_future is not None and not self._goal_request_future.done():
            self._goal_request_future.cancel()

        # if self._goal_result_future is not None and not self._goal_result_future.done():
        #     self._goal_result_future.cancel()

        # self._goal_handle = None
        # self._goal_request_future = None
        # self._goal_result_future = None

        self._update_status(ExecutorStatus.CANCELED)

        # Some goals are complex and their cancel method may be not well implemented.
        # Adding 0.1s delay is helpful.
        time.sleep(0.1)

        return True

    def _goal_request_callback(self, future: Future):
        if future != self._goal_request_future or self._is_canceling:
            return

        goal_handle = future.result()

        if goal_handle is None:
            self._update_status(ExecutorStatus.SUCCEEDED)
            self._goal_request_future = None
            return

        if not goal_handle.accepted:
            self._update_status(ExecutorStatus.REJECTED)
            return

        self._update_status(ExecutorStatus.RUNNING)
        self._goal_handle = goal_handle
        self._goal_request_future = None
        self._goal_result_future = goal_handle.get_result_async()
        self._goal_result_future.add_done_callback(self._goal_result_callback)

    def _goal_result_callback(self, future: Future):
        if future != self._goal_result_future:
            return

        result = future.result()

        if result is None:
            return

        if result.status == GoalStatus.STATUS_SUCCEEDED:
            self._update_status(ExecutorStatus.SUCCEEDED)
        elif result.status == GoalStatus.STATUS_CANCELED or result.status == GoalStatus.STATUS_ABORTED or result.status == GoalStatus.STATUS_CANCELING:
            self._update_status(ExecutorStatus.CANCELED)
        else:
            # GoalStatus.STATUS_UNKNOWN, GoalStatus.STATUS_ACCEPTED, GoalStatus.STATUS_EXECUTING
            return

        self._goal_result_future = None
        self._goal_handle = None
        self._is_canceling = False