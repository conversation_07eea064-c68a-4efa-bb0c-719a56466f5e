from abc import ABC, abstractmethod
from enum import IntEnum
from typing import Callable


class ExecutorStatus(IntEnum):
    NOT_STARTED = 0,
    RUNNING = 1,
    SUCCEEDED = 2,
    FAILED = 3,
    CANCELED = 4,
    REJECTED = 5


ExecutorStatusCallback = Callable[[ExecutorStatus], None]


class BaseExecutor(ABC):
    def __init__(self):
        self.__status: ExecutorStatus = ExecutorStatus.NOT_STARTED
        self._callback: ExecutorStatusCallback | None = None

    @property
    def status(self) -> ExecutorStatus:
        return self.__status

    def is_finished(self) -> bool:
        return self.__status != ExecutorStatus.NOT_STARTED and self.__status != ExecutorStatus.RUNNING

    def is_running(self) -> bool:
        return self.__status == ExecutorStatus.RUNNING

    def is_succeeded(self) -> bool:
        return self.__status == ExecutorStatus.SUCCEEDED

    @abstractmethod
    async def execute_async(self) -> ExecutorStatus:
        pass

    @abstractmethod
    def execute(self) -> bool:
        pass

    @abstractmethod
    async def cancel(self) -> bool:
        pass

    @abstractmethod
    def _status_updated(self):
        pass

    def set_status_callback(self, callback: ExecutorStatusCallback):
        self._callback = callback

    def _update_status(self, status: ExecutorStatus):
        if self.__status == status:
            return
        self.__status = status
        self._status_updated()
        if self._callback is not None:
            self._callback(status)