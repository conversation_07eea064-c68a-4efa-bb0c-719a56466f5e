from cartographer_ros_msgs.action import Relocalize
from rclpy.node import Node

from .action_executor import ActionExecutor, ActionExecutorType
from ..models import ActionDefinition


class RelocateExecutor(ActionExecutor[Relocalize]):
    def __init__(self, node: Node):
        super().__init__(node, ActionExecutorType.RELOCATE_EXECUTOR, "relocalize", ActionDefinition.RECOVER_LOCALIZATION)

    def _status_updated(self):
        pass

    async def cancel(self) -> bool:
        # Relocalize must not be interrupted.
        return False