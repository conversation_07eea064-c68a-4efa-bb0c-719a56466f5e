from threading import Lock

from .action_task import ActionTask, TaskStatus
from .industry_stage_maintainer import IndustryStageEnum, IndustryStageMaintainer


class ActionTaskSupervisor:
    def __init__(self):
        self._current_task: ActionTask|None = None
        self._task_lock = Lock()

        self._task_queue: list[ActionTask] = []
        self._queue_lock = Lock()

        self._auto_return = False
        self._auto_return_task: ActionTask|None = None
        self._auto_return_task_lock = Lock()

    def _action_task_callback(self, task_status: TaskStatus):
        match task_status:
            case TaskStatus.NOT_STARTED | TaskStatus.RUNNING:
                pass
            case TaskStatus.SUCCESS | TaskStatus.FAILED | TaskStatus.CANCELED:
                self._run_task()

    async def cancel(self):
        self.clear()
        if self._current_task is not None:
            try:
                await self._current_task.cancel()
            except Exception:
                pass
            finally:
                with self._task_lock:
                    self._current_task = None

        auto_return_task: ActionTask|None = None
        with self._auto_return_task_lock:
            auto_return_task = self._auto_return_task

        if auto_return_task is not None:
            await auto_return_task.cancel()
            with self._auto_return_task_lock:
                self._auto_return_task = None

    async def run_next_action(self):
        if self._current_task is not None:
            await self._current_task.run_next_non_wait_action()

    async def append(self, executor: ActionTask):
        with self._queue_lock:
            self._task_queue.append(executor)

        with self._auto_return_task_lock:
            if self._auto_return_task is not None:
                await self._auto_return_task.cancel()
                self._auto_return_task = None

        self._auto_return = True

        self._run_task()

    async def reset(self, executor: ActionTask):
        await self.cancel()

        self._auto_return = False

        await self.append(executor)

    def clear(self):
        with self._queue_lock:
            self._task_queue.clear()

    def set_auto_return_task(self, task: ActionTask):
        with self._auto_return_task_lock:
            self._auto_return_task = task

    def _run_task(self):
        if self._current_task is not None:
            if self._current_task.task_status == TaskStatus.NOT_STARTED or self._current_task.task_status == TaskStatus.RUNNING:
                return

        task: ActionTask|None = None
        with self._queue_lock:
            if len(self._task_queue) == 0:
                with self._task_lock:
                    self._current_task = None
                IndustryStageMaintainer().update_stage(IndustryStageEnum.IDLE)

                self._check_auto_return()
                return

            task = self._task_queue.pop(0)

        if task is None:
            return

        with self._task_lock:
            self._current_task = task

        task.run(self._action_task_callback)

    def _check_auto_return(self):
        with self._auto_return_task_lock:
            if self._auto_return and self._auto_return_task is not None:
                self._auto_return_task.run(self._action_task_callback)
                self._auto_return = False