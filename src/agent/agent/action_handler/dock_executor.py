import time

from opennav_docking_msgs.action import DockRobot
from rclpy.node import Node

from .action_executor import ActionExecutor, ActionExecutorType
from ..models import ActionDefinition


class DockExecutor(ActionExecutor[DockRobot]):
    def __init__(self, node: Node, agent_action_name: ActionDefinition):
        super().__init__(node, ActionExecutorType.DOCK_EXECUTOR, "dock_robot", agent_action_name)

    def _status_updated(self):
        pass
