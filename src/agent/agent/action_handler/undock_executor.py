from opennav_docking_msgs.action import UndockRobot
from rclpy.node import Node

from .action_executor import ActionExecutor, ActionExecutorType
from ..models import ActionDefinition


class UndockExecutor(ActionExecutor[UndockRobot]):
    def __init__(self, node: Node):
        super().__init__(node, ActionExecutorType.UNDOCK_EXECUTOR, "undock_robot", ActionDefinition.BACK_OFF_FROM_TAG)

    def _status_updated(self):
        pass