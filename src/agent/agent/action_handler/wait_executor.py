import asyncio
import threading

from concurrent.futures import Future as ThreadFuture
from functools import partial

from .base_executor import BaseExecutor, ExecutorStatus


class WaitExecutor(BaseExecutor):
    def __init__(self, wait_in_sec: float):
        super().__init__()

        self._wait_in_sec = wait_in_sec

        self._wait_task: asyncio.Task|None = None
        self._thread_future: ThreadFuture|None = None

        self._loop_thread: threading.Thread|None = None
        self._loop: asyncio.AbstractEventLoop|None = None
        self._loop_started = threading.Event()
        self._loop_task: asyncio.Task|None = None

    def __del__(self):
        self._shutdown()

    def _status_updated(self):
        pass

    def _shutdown(self):
        if self._loop is not None and self._loop.is_running():
            self._loop.call_soon_threadsafe(partial(self._loop.stop))
        if self._loop_thread is not None and self._loop_thread.is_alive():
            self._loop_thread.join(timeout=1.0)
        self._loop = None
        self._loop_thread = None

    async def _wait_coro(self):
        try:
            await asyncio.sleep(self._wait_in_sec)
            self._update_status(ExecutorStatus.SUCCEEDED)
        except asyncio.CancelledError:
            self._update_status(ExecutorStatus.CANCELED)
        except Exception:
            self._update_status(ExecutorStatus.FAILED)

    def _ensure_loop_thread(self):
        if self._loop is not None and self._loop.is_running():
            return

        def _runner():
            # Create a brand-new loop for THIS thread only; do not set global policy.
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self._loop = loop
            self._loop_started.set()
            try:
                loop.run_forever()
            finally:
                # Drain pending tasks gracefully on shutdown.
                pending = asyncio.all_tasks(loop)
                for t in pending:
                    t.cancel()
                try:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                finally:
                    loop.close()

        self._loop_thread = threading.Thread(target=_runner, name="WaitExecutorLoop", daemon=True)
        self._loop_thread.start()
        self._loop_started.wait()

    def _create_wait_task(self):
        async def wait_task():
            await asyncio.sleep(self._wait_in_sec)
            self._update_status(ExecutorStatus.SUCCEEDED)
        return wait_task()

    async def execute_async(self) -> ExecutorStatus:
        self._update_status(ExecutorStatus.RUNNING)
        # Schedule on the current running loop
        self._wait_task = asyncio.create_task(self._wait_coro())
        try:
            await self._wait_task
        finally:
            self._wait_task = None
        # Status is set within _wait_coro; return it for convenience
        return self.status

    def execute(self) -> bool:
        self._update_status(ExecutorStatus.RUNNING)

        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = None

        if loop is not None and loop.is_running():
            self._wait_task = loop.create_task(self._create_wait_task())
        else:
            self._ensure_loop_thread()
            if self._loop is not None:
                def _create_task():
                    self._loop_task = asyncio.create_task(self._wait_coro())

                # Wrap method call to satisfy static type checkers
                self._loop.call_soon_threadsafe(partial(_create_task))
            else:
                return False

        return True

    def _request_cancel(self) -> bool:
        cancelled = False

        t = self._wait_task
        if t is not None and not t.done():
            t.cancel()
            cancelled = True

        if self._loop is not None and self._loop.is_running() and self._loop_task is not None and not self._loop_task.done():
            self._loop.call_soon_threadsafe(partial(self._loop_task.cancel))
            cancelled = True

        return cancelled

    async def cancel(self) -> bool:
        cancelled = self._request_cancel()

        if cancelled:
            self._update_status(ExecutorStatus.CANCELED)
        return cancelled
