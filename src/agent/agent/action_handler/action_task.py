#
# Action Task contains a group of executors.
#

from enum import Enum
from threading import Lock
from typing import Callable

from .action_executor import ActionExecutor
from ..agent_exceptions import ConflictingCommandError
from .base_executor import BaseExecutor, ExecutorStatus
from .executor_factory import ExecutorFactory
from .industry_stage_maintainer import IndustryStageMaintainer
from .motion_action_maintainer import MotionActionMaintainer
from ..models import ActionDefinition
from .navigate_executor import NavigateExecutor
from .wait_executor import WaitExecutor


class TaskStatus(Enum):
    NOT_STARTED = 0
    RUNNING = 1
    SUCCESS = 2
    FAILED = 3
    CANCELED = 4


ActionTaskCallback = Callable[[TaskStatus], None]


class ActionTask:
    def __init__(self):
        self._current_executor: BaseExecutor|None = None
        self._task_status: TaskStatus = TaskStatus.NOT_STARTED
        self._executors: list[ExecutorFactory] = []
        self._callback: ActionTaskCallback|None = None

        self._status_lock = Lock()
        self._queue_lock = Lock()
        self._current_executor_lock = Lock()

        self._should_skip_action = False
        self._skip_lock = Lock()

    @property
    def task_status(self):
        with self._status_lock:
            return self._task_status

    def append_executor(self, executor: ExecutorFactory) -> bool:
        with self._status_lock:
            if self._task_status != TaskStatus.NOT_STARTED:
                return False

        with self._queue_lock:
            self._executors.append(executor)

        return True

    def is_empty(self) -> bool:
        with self._queue_lock:
            return len(self._executors) == 0

    def run(self, callback: ActionTaskCallback):
        self._callback = callback

        if self.is_empty():
            with self._status_lock:
                self._task_status = TaskStatus.SUCCESS
            callback(self._task_status)
            return

        self._run_next_executor()

    async def cancel(self):
        with self._current_executor_lock:
            if self._current_executor is not None and isinstance(self._current_executor, ActionExecutor) and self._current_executor.agent_action_name == ActionDefinition.RECOVER_LOCALIZATION and self._current_executor.status == ExecutorStatus.RUNNING:
                raise ConflictingCommandError("Robot is relocalizing!!!")

            if self._current_executor is not None:
                try:
                    await self._current_executor.cancel()
                except Exception:
                    pass

        with self._status_lock:
            self._task_status = TaskStatus.CANCELED
        if self._callback is not None:
            self._callback(self._task_status)

    async def run_next_non_wait_action(self):
        executor: BaseExecutor|None = None
        with self._current_executor_lock:
            if self._current_executor is not None and isinstance(self._current_executor, ActionExecutor) and self._current_executor.agent_action_name == ActionDefinition.RECOVER_LOCALIZATION and self._current_executor.status == ExecutorStatus.RUNNING:
                raise ConflictingCommandError("Robot is relocalizing!!!")

            executor = self._current_executor

        with self._skip_lock:
            self._should_skip_action = True

        if executor is not None:
            try:
                await executor.cancel()
            except Exception as e:
                with self._skip_lock:
                    self._should_skip_action = False

    def _executor_callback(self, status: ExecutorStatus):
        MotionActionMaintainer().update_current_action(status)

        match status:
            case ExecutorStatus.NOT_STARTED:
                pass
            case ExecutorStatus.RUNNING:
                with self._status_lock:
                    self._task_status = TaskStatus.RUNNING
                if self._callback is not None:
                    self._callback(self._task_status)
            case ExecutorStatus.SUCCEEDED:
                with self._current_executor_lock:
                    if self._current_executor is not None and isinstance(self._current_executor, NavigateExecutor):
                        IndustryStageMaintainer().arrived_at_point()

                if self.is_empty():
                    with self._status_lock:
                        self._task_status = TaskStatus.SUCCESS
                    if self._callback is not None:
                        self._callback(self._task_status)
                else:
                    self._run_next_executor()
            case ExecutorStatus.CANCELED:
                should_skip_action = False
                with self._skip_lock:
                    should_skip_action = self._should_skip_action
                if should_skip_action:
                    self._run_next_executor()
                    return

                with self._status_lock:
                    self._task_status = TaskStatus.CANCELED
                if self._callback is not None:
                    self._callback(self._task_status)
            case ExecutorStatus.FAILED | ExecutorStatus.REJECTED:
                with self._status_lock:
                    self._task_status = TaskStatus.FAILED
                if self._callback is not None:
                    self._callback(self._task_status)

    def _run_next_executor(self):
        with self._queue_lock:
            found_executor = False
            while not found_executor:
                if len(self._executors) == 0:
                    with self._status_lock:
                        self._task_status = TaskStatus.SUCCESS
                        if self._callback is not None:
                            self._callback(self._task_status)
                    return

                factory = self._executors.pop(0)
                executor = factory.produce()
                with self._skip_lock:
                    if self._should_skip_action and isinstance(executor, WaitExecutor):
                        continue
                    else:
                        self._should_skip_action = False
                        found_executor = True

        IndustryStageMaintainer().update_stage_by_executor(executor)

        executor.set_status_callback(self._executor_callback)
        executor.execute()

        if isinstance(executor, ActionExecutor):
            MotionActionMaintainer().create_action(executor.agent_action_name)

        with self._current_executor_lock:
            self._current_executor = executor