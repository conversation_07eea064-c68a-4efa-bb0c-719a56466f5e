#
# This executor calls reset_monitor_parameters to change robot monitoring size after putting down shelf.
#

from interfaces.action import ResetMonitorParameters
from rclpy.node import Node

from .action_executor import ActionExecutor, ActionExecutorType
from ..models import ActionDefinition


class ResetMonitorExecutor(ActionExecutor[ResetMonitorParameters]):
    def __init__(self, node: Node):
        super().__init__(node, ActionExecutorType.RESET_MONITOR_EXECUTOR, "reset_monitor_parameters", ActionDefinition.JACK_MOVE)

        self._goal = ResetMonitorParameters.Goal()

    def _status_updated(self):
        pass
