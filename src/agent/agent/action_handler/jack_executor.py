#
# This executor controls jack.
# If the jack command is lifting up, this executor will call ModifyMonitorExecutor automatically.
# If the jack command is lifting down, this executor will call ResetMonitorExecutor automatically.
#

from rclpy.node import Node
from sl_vcu_all.action import JackControl

from .action_executor import ActionExecutor, ActionExecutorType
from ..models import Jack<PERSON>om<PERSON>, JackCommandEnum, ActionDefinition


class JackExecutor(ActionExecutor[JackControl]):
    def __init__(self, node: Node):
        super().__init__(node, ActionExecutorType.JACK_EXECUTOR, "jack_control", ActionDefinition.JACK_MOVE)

        self._command: Jack<PERSON>ommandEnum|None = None

    def _status_updated(self):
        pass

    def set_jack_command(self, command: JackCommand):
        self._command = command.command

        goal = JackControl.Goal()
        goal.command = command.command
        if command.target_position is not None:
            goal.target_position = command.target_position
        if command.speed is not None:
            goal.speed = command.speed

        self.prepare_goal(goal)

    @property
    def jack_command(self) -> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|None:
        return self._command