from enum import Enum
from threading import Lock
from typing import cast

from .base_executor import <PERSON><PERSON><PERSON>cutor
from .dock_executor import Dock<PERSON>xecutor
from .jack_executor import <PERSON><PERSON><PERSON>cutor
from ..models import ActionDefinition, JackCommandEnum
from .navigate_executor import NavigateExecutor


class IndustryStageEnum(str, Enum):
    GOING_TO_TASK_POINT = "GOING_TO_TASK_POINT"
    GOING_TO_TARGET_POINT = "GOING_TO_TARGET_POINT"
    GOING_HOME = "GOING_HOME"
    ON_RETURNING = "ON_RETURNING"
    ARRIVED_AT_TASK_POINT = "ARRIVED_AT_TASK_POINT"
    ARRIVED_AT_TARGET_POINT = "ARRIVED_AT_TARGET_POINT"
    IDLE = "IDLE"
    DEVICE_ERROR = "DEVICE_ERROR"
    ON_DELIVERING = "ON_DELIVERING"


class IndustryStageMaintainer:
    # Singleton
    _instance: 'IndustryStageMaintainer|None' = None

    # Lock
    _lock = Lock()

    # Stage
    _stage: IndustryStageEnum = IndustryStageEnum.IDLE

    # error occurs
    _device_error: bool = False

    # has lift shelf
    _has_lift_shelf: bool = False

    def __new__(cls, *args, **kwargs) -> 'IndustryStageMaintainer':
        if cls._instance is None:
            created = super().__new__(cls, *args, **kwargs)
            cls._instance = cast('IndustryStageMaintainer', created)
        return cls._instance

    @property
    def current_stage(self) -> IndustryStageEnum:
        with self._lock:
            if self._device_error:
                return IndustryStageEnum.DEVICE_ERROR
            return self._stage

    def update_stage(self, stage: IndustryStageEnum):
        with self._lock:
            self._stage = stage

    def set_device_error_occurs(self, occurs):
        with self._lock:
            self._device_error = occurs

    def update_stage_by_executor(self, executor: BaseExecutor):
        if isinstance(executor, DockExecutor):
            if executor.agent_action_name == ActionDefinition.GO_HOME:
                with self._lock:
                    self._stage = IndustryStageEnum.GOING_HOME
            elif executor.agent_action_name == ActionDefinition.MOVE_TO_TAG:
                with self._lock:
                    self._stage = IndustryStageEnum.GOING_TO_TASK_POINT
        elif isinstance(executor, JackExecutor):
            if executor.jack_command == JackCommandEnum.LIFT_UP:
                self._has_lift_shelf = True
            elif executor.jack_command == JackCommandEnum.LIFT_DOWN:
                self._has_lift_shelf = False
        elif isinstance(executor, NavigateExecutor):
            with self._lock:
                self._stage = IndustryStageEnum.ON_DELIVERING if self._has_lift_shelf else IndustryStageEnum.GOING_TO_TASK_POINT

    def arrived_at_point(self):
        with self._lock:
            if self._stage == IndustryStageEnum.GOING_TO_TASK_POINT:
                self._stage = IndustryStageEnum.ARRIVED_AT_TASK_POINT
            elif self._stage == IndustryStageEnum.GOING_TO_TARGET_POINT:
                self._stage = IndustryStageEnum.ARRIVED_AT_TARGET_POINT