from nav2_msgs.action import NavigateToPose
from rclpy.node import Node

from .action_executor import ActionExecutor, ActionExecutorType
from ..models import ActionDefinition


class NavigateExecutor(ActionExecutor[NavigateToPose]):
    def __init__(self, node: Node):
        super().__init__(node, ActionExecutorType.NAVIGATE_EXECUTOR, "navigate_to_pose", ActionDefinition.MOVE_TO)

    def _status_updated(self):
        pass