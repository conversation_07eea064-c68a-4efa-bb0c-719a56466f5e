import math

from geometry_msgs.msg import PoseStamped
from interfaces.msg import ShelfIn<PERSON>
from nav2_msgs.action import NavigateToPose
from opennav_docking_msgs.action import DockRobot, UndockRobot
from rclpy.node import Node
from typing import List

from .action_task import ActionTask
from ..agent_exceptions import InvalidRequestDataError, InternalAgentError
from .dock_executor import DockExecutor
from .executor_factory import ExecutorFactory
from .jack_executor import Jack<PERSON>xecutor, JackCommand, JackCommandEnum
from ..models import IndustryTaskActionEnum, IndustryTaskModel, PointOfInterest, HomeDock, ActionDefinition, ConfigShelfInfo
from .modify_monitor_executor import ModifyMonitorExecutor
from .navigate_executor import NavigateExecutor
from .reset_monitor_executor import ResetMonitorExecutor
from .undock_executor import UndockExecutor
from ..utils import euler_to_quaternion
from .wait_executor import WaitExecutor


def _get_poi(target_id: str|None, target_name: str|None, pois: List[PointOfInterest]) -> PointOfInterest:
    if target_id is not None:
        found = [x for x in pois if x.id == target_id]
        if len(found) == 0:
            raise InvalidRequestDataError("Invalid target ID.")
        poi = found[0]
    elif target_name is not None:
        found = [x for x in pois if x.metadata is not None and x.metadata.display_name is not None and x.metadata.display_name == target_name]
        if len(found) == 0:
            raise InvalidRequestDataError("Invalid target name.")
        poi = found[0]
    else:
        raise InvalidRequestDataError("Missing target ID and target name.")

    return poi


def _create_move_to_action(node: Node, poi: PointOfInterest) -> ExecutorFactory:
    if poi.pose is None:
        raise InternalAgentError("Corrupted POI data.")

    goal = NavigateToPose.Goal()

    goal.pose.header.frame_id = "map"
    goal.pose.header.stamp = node.get_clock().now().to_msg()
    goal.pose.pose.position.x = poi.pose.x
    goal.pose.pose.position.y = poi.pose.y
    goal.pose.pose.position.z = 0.0

    half_yaw = poi.pose.yaw / 2.0
    goal.pose.pose.orientation.x = 0.0
    goal.pose.pose.orientation.y = 0.0
    goal.pose.pose.orientation.z = math.sin(half_yaw)
    goal.pose.pose.orientation.w = math.cos(half_yaw)

    def create_navigate_executor() -> NavigateExecutor:
        executor = NavigateExecutor(node)
        executor.prepare_goal(goal)
        return executor

    return ExecutorFactory(create_navigate_executor)


def _create_move_to_tag_action(node: Node, poi: PointOfInterest, shelves: List[ConfigShelfInfo]) -> ExecutorFactory:
    if poi.pose is None:
        raise InternalAgentError("Corrupted POI data.")

    goal = DockRobot.Goal()
    goal.dock_type = "shelf_dock"

    docking_pose = PoseStamped()
    docking_pose.header.stamp = node.get_clock().now().to_msg()
    docking_pose.header.frame_id = "map"
    docking_pose.pose.position.x = 0.0
    docking_pose.pose.position.y = 0.0
    docking_pose.pose.position.z = 0.0
    docking_pose.pose.orientation.x, docking_pose.pose.orientation.y, docking_pose.pose.orientation.z, docking_pose.pose.orientation.w = euler_to_quaternion(
        0.0, 0.0, 0.0)
    goal.dock_pose = docking_pose

    landing_pose = PoseStamped()
    landing_pose.header.stamp = node.get_clock().now().to_msg()
    landing_pose.header.frame_id = "map"
    landing_pose.pose.position.x = poi.pose.x
    landing_pose.pose.position.y = poi.pose.y
    landing_pose.pose.position.z = 0.0
    landing_pose.pose.orientation.x, landing_pose.pose.orientation.y, landing_pose.pose.orientation.z, landing_pose.pose.orientation.w = euler_to_quaternion(0.0, 0.0, poi.pose.yaw)
    goal.landing_pose = landing_pose

    goal.dock_allowance = 0.05

    goal_shelves: List[ShelfInfo] = []
    for shelf in shelves:
        item = ShelfInfo()
        item.shelf_columnar_length = shelf.data.shelf_columnar_length
        item.shelf_columnar_width = shelf.data.shelf_columnar_width
        item.shelf_columnar_diameter = shelf.data.shelf_columnar_diameter
        item.shelf_length_retraction = shelf.data.shelf_length_retraction
        goal_shelves.append(item)
    goal.shelves = goal_shelves

    def create_dock_executor() -> DockExecutor:
        executor = DockExecutor(node, ActionDefinition.MOVE_TO_TAG)
        executor.prepare_goal(goal)
        return executor

    return ExecutorFactory(create_dock_executor)


def _create_jack_action(node: Node, command: JackCommandEnum) -> ExecutorFactory:
    def create_jack_executor() -> JackExecutor:
        executor = JackExecutor(node)
        executor.set_jack_command(JackCommand(command=command))
        return executor

    return ExecutorFactory(create_jack_executor)


def _create_modify_monitor_action(node: Node) -> ExecutorFactory:
    def create_modify_monitor_executor() -> ModifyMonitorExecutor:
        return ModifyMonitorExecutor(node)
    return ExecutorFactory(create_modify_monitor_executor)


def _create_reset_monitor_action(node: Node) -> ExecutorFactory:
    def create_reset_monitor_executor() -> ResetMonitorExecutor:
        return ResetMonitorExecutor(node)
    return ExecutorFactory(create_reset_monitor_executor)


def _create_back_off_from_shelf_action(node: Node) -> ExecutorFactory:
    goal = UndockRobot.Goal()
    goal.dock_type = "shelf_dock"
    def create_undock_executor() -> UndockExecutor:
        executor = UndockExecutor(node)
        executor.prepare_goal(goal)
        return executor
    return ExecutorFactory(create_undock_executor)


def _create_go_home_action(node: Node, home: List[HomeDock], bound_dock: HomeDock|None) -> ExecutorFactory:
    goal = DockRobot.Goal()
    goal.dock_type = "simple_charging_dock"
    if bound_dock is not None and bound_dock.id is not None:
        goal.use_dock_id = True
        goal.dock_id = bound_dock.id
    elif len(home) == 1 and home[0].id is not None:
        goal.use_dock_id = True
        goal.dock_id = home[0].id
    else:
        raise InvalidRequestDataError("Missing bound home dock.")

    def create_dock_executor() -> DockExecutor:
        executor = DockExecutor(node, ActionDefinition.GO_HOME)
        executor.prepare_goal(goal)
        return executor

    return ExecutorFactory(create_dock_executor)


def _create_wait_action(wait_time: float) -> ExecutorFactory:
    def create_wait_executor() -> WaitExecutor:
        return WaitExecutor(wait_time)
    return ExecutorFactory(create_wait_executor)


def parse_industry_task_to_action_task(node: Node, model: IndustryTaskModel, pois: List[PointOfInterest], bound_dock: HomeDock|None, home: List[HomeDock], shelves: List[ConfigShelfInfo]) -> ActionTask:
    if model.type != "INDUSTRY":
        raise InvalidRequestDataError("Invalid type.")

    action_task = ActionTask()

    for target in model.task_targets:
        match target.action:
            case IndustryTaskActionEnum.MOVE_TO_POI | IndustryTaskActionEnum.CARRY_MOVE:
                action_task.append_executor(_create_move_to_action(node, _get_poi(target.target_id, target.target_name, pois)))
                if target.wait_time is not None and target.wait_time > 0.0:
                    action_task.append_executor(_create_wait_action(target.wait_time))
            case IndustryTaskActionEnum.ACCURATE_DOCKING_COLUMNAR_TAG:
                action_task.append_executor(_create_move_to_tag_action(node, _get_poi(target.target_id, target.target_name, pois), shelves))
                if target.wait_time is not None and target.wait_time > 0.0:
                    action_task.append_executor(_create_wait_action(target.wait_time))
            case IndustryTaskActionEnum.JACK_UP:
                action_task.append_executor(_create_modify_monitor_action(node))
                action_task.append_executor(_create_jack_action(node, JackCommandEnum.LIFT_UP))
                if target.wait_time is not None and target.wait_time > 0.0:
                    action_task.append_executor(_create_wait_action(target.wait_time))
            case IndustryTaskActionEnum.JACK_DOWN:
                action_task.append_executor(_create_jack_action(node, JackCommandEnum.LIFT_DOWN))
                action_task.append_executor(_create_reset_monitor_action(node))
                if target.wait_time is not None and target.wait_time > 0.0:
                    action_task.append_executor(_create_wait_action(target.wait_time))
            case IndustryTaskActionEnum.BACK_OFF_FROM_SHELF:
                action_task.append_executor(_create_back_off_from_shelf_action(node))
                if target.wait_time is not None and target.wait_time > 0.0:
                    action_task.append_executor(_create_wait_action(target.wait_time))
            case IndustryTaskActionEnum.GO_HOME:
                action_task.append_executor(_create_go_home_action(node, home, bound_dock))
                if target.wait_time is not None and target.wait_time > 0.0:
                    action_task.append_executor(_create_wait_action(target.wait_time))

    return action_task


def create_auto_return_task(node: Node, bound_dock: HomeDock|None, home: List[HomeDock]) -> ActionTask:
    action_task = ActionTask()

    wait_executor = _create_wait_action(60.0 * 5)
    if wait_executor is not None:
        action_task.append_executor(wait_executor)

    action_task.append_executor(_create_go_home_action(node, home, bound_dock))

    return action_task