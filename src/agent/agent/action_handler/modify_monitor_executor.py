#
# This executor calls modify_monitor_parameters to change robot monitoring size after lifting shelf.
#

from interfaces.action import ModifyMonitorParameters
from rclpy.node import Node

from .action_executor import ActionExecutor, ActionExecutorType
from ..models import ActionDefinition


class ModifyMonitorExecutor(ActionExecutor[ModifyMonitorParameters]):
    def __init__(self, node: Node):
        super().__init__(node, ActionExecutorType.MODIFY_MONITOR_EXECUTOR, "modify_monitor_parameters", ActionDefinition.JACK_MOVE)

        self._goal = ModifyMonitorParameters.Goal()

    def _status_updated(self):
        pass