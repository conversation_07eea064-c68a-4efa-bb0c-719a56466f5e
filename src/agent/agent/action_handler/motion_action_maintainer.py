from threading import Lock
from typing import Dict, ClassVar, cast

from .base_executor import Executor<PERSON>tatus
from ..models import MotionAction, MotionActionState, ActionDefinition, ActionStatusEnum, ActionResultEnum


class MotionActionMaintainer:
    # singleton
    _instance: 'MotionActionMaintainer|None' = None

    # action id starts from 1
    _current_action_id = 0

    # current motion action
    _current_action: MotionAction | None = None

    # action history. only keeps the least 20 actions
    _action_history: ClassVar[Dict[int, MotionAction]] = dict()

    # lock
    _lock = Lock()

    # ros2 goal id to action id
    _ros2_goal_id_map: ClassVar[Dict[str, int]] = dict()
    _ros2_action_id_map: ClassVar[Dict[int, str]] = dict()

    def __new__(cls, *args, **kwargs) -> 'MotionActionMaintainer':
        if cls._instance is None:
            created = super().__new__(cls, *args, **kwargs)
            cls._instance = cast('MotionActionMaintainer', created)
        return cls._instance

    @property
    def current_action(self) -> MotionAction|None:
        return self._current_action

    @property
    def current_action_id(self) -> int:
        with self._lock:
            return self._current_action_id

    def get_action_by_id(self, action_id: int) -> MotionAction|None:
        with self._lock:
            if action_id in self._action_history:
                return self._action_history[action_id]
        return None

    def create_action(self, action_name: ActionDefinition) -> MotionAction:
        self._current_action_id += 1
        new_action = MotionAction(
            action_id=self._current_action_id,
            action_name=action_name.value,
            stage="Working",
            state=MotionActionState(status=ActionStatusEnum.NewBorn, result=ActionResultEnum.Success, reason="")
        )

        with self._lock:
            self._current_action = new_action
            self._action_history[self._current_action_id] = new_action
            if len(self._action_history) > 20:
                self._action_history.pop(next(iter(self._action_history)))

        return new_action

    def update_current_action(self, status: ExecutorStatus):
        with self._lock:
            if self._current_action is not None:
                match status:
                    case ExecutorStatus.NOT_STARTED:
                        self._current_action.state.status = ActionStatusEnum.NewBorn
                    case ExecutorStatus.RUNNING:
                        self._current_action.state.status = ActionStatusEnum.Working
                    case ExecutorStatus.SUCCEEDED:
                        self._current_action.state.status = ActionStatusEnum.Done
                        self._current_action.state.result = ActionResultEnum.Success
                        self._current_action.state.reason = "Success"
                        self._current_action.stage = "Done"
                        self._current_action = None
                    case ExecutorStatus.FAILED:
                        self._current_action.state.status = ActionStatusEnum.Done
                        self._current_action.state.result = ActionResultEnum.Failed
                        self._current_action.state.reason = "Failed"
                        self._current_action.stage = "Done"
                        self._current_action = None
                    case ExecutorStatus.CANCELED:
                        self._current_action.state.status = ActionStatusEnum.Done
                        self._current_action.state.result = ActionResultEnum.Aborted
                        self._current_action.state.reason = "Canceled"
                        self._current_action.stage = "Done"
                        self._current_action = None
                    case ExecutorStatus.REJECTED:
                        self._current_action.state.status = ActionStatusEnum.Done
                        self._current_action.state.result = ActionResultEnum.Aborted
                        self._current_action.state.reason = "Rejected"
                        self._current_action.stage = "Done"
                        self._current_action = None

    def get_action_id_by_ros2_goal_id(self, ros2_goal_id: str) -> int|None:
        if ros2_goal_id in self._ros2_goal_id_map:
            return self._ros2_goal_id_map[ros2_goal_id]
        return None

    def is_action_id_bound_to_ros2_goal_id(self, action_id: int) -> bool:
        return action_id in self._ros2_action_id_map

    def bind_ros2_goal_id(self, ros2_goal_id: str, action_name: ActionDefinition) -> int|None:
        with self._lock:
            self._current_action_id += 1
            current_action_id = self._current_action_id
            self._action_history[current_action_id] = MotionAction(action_id=current_action_id, action_name=action_name.value, stage="Working", state=MotionActionState(status=ActionStatusEnum.Working, result=ActionResultEnum.Success, reason=""))

        self._ros2_goal_id_map[ros2_goal_id] = current_action_id
        self._ros2_action_id_map[current_action_id] = ros2_goal_id

        return current_action_id