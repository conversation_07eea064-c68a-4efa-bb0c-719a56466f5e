import orjson
import os
import uuid

from ament_index_python import get_package_share_directory
from rclpy.node import Node
from sl_vcu_all.msg import Jack<PERSON>tat<PERSON>
from typing import List

from .action_handler import ActionTaskSupervisor, JackExecutor, ActionTask, ExecutorFactory, ModifyMonitorExecutor, ResetMonitorExecutor, parse_industry_task_to_action_task, IndustryStageEnum, IndustryStageMaintainer, create_auto_return_task
from .agent_exceptions import InternalAgentError
from .artifact_manager import ArtifactManager
from .base_manager import BaseManager
from .home_dock_manager import HomeDockManager
from .models import JackCommand, JackStatus as APIJackStatus, JackCommandEnum, ConfigShelfInfo, DeviceModeEnum, ShelfInfo, IndustryTaskModel, IndustryTaskResponse
from .robot_pose_listener import RobotPoseListener
from .utils import api_handler, read_from_file, write_to_file, delete_file


_SHELF_FILE_PATH = "/opt/rslamware_data/maps/shelves.json"


class IndustryManager(BaseManager):

    def __init__(self, node: Node, pose_listener: <PERSON><PERSON>ose<PERSON><PERSON><PERSON>, artifact_manager: ArtifactManager, home_dock_manager: HomeDockManager, action_task_supervisor: ActionTaskSupervisor, mode: DeviceModeEnum):
        super().__init__(node)

        self._pose_listener = pose_listener

        self._artifact_manager = artifact_manager

        self._home_dock_manager = home_dock_manager

        self._action_task_supervisor = action_task_supervisor

        self._jack_status_cache: JackStatus|None = None

        self._shelf_file_path = _SHELF_FILE_PATH
        if mode == DeviceModeEnum.SIMULATION:
            self._shelf_file_path = os.path.join(get_package_share_directory('simulator'), 'map/shelves.json')

        self._shelves_cache: List[ConfigShelfInfo]|None = None

        self._init_shelves()

    def _create_clients(self):
        pass

    def _subscribe_to_topics(self):
        self._subscriptions.append(
            self._node.create_subscription(
                JackStatus,
                "jack_status",
                self._jack_status_callback,
                1
            )
        )

    def _jack_status_callback(self, msg: JackStatus):
        self._jack_status_cache = msg

    def _init_shelves(self):
        raw_data = read_from_file(self._shelf_file_path)
        if raw_data is None:
            return

        try:
            json_data = orjson.loads(raw_data)
            self._shelves_cache = [ConfigShelfInfo.model_validate(x) for x in json_data]
        except Exception as e:
            self._node.get_logger().warning(e)
            return

    @api_handler(default_return=None)
    async def get_jack_status(self) -> APIJackStatus|None:
        cache = self._jack_status_cache
        if cache is None:
            return None
        return APIJackStatus(stage=cache.current_stage, position=cache.current_position, status=cache.current_status, alarm=cache.current_alarm)

    @api_handler(default_return=False)
    async def set_jack_status(self, command: JackCommand) -> bool:
        def create_jack_executor() -> JackExecutor:
            executor = JackExecutor(self._node)
            executor.set_jack_command(command)
            return executor

        jack_factory = ExecutorFactory(create_jack_executor)

        task = ActionTask()

        task.append_executor(jack_factory)

        await self._action_task_supervisor.reset(task)
        return True

    @api_handler(default_return=list())
    async def get_shelves(self) -> List[ConfigShelfInfo]:
        return self._shelves_cache if self._shelves_cache is not None else []

    @api_handler(default_return=None)
    async def get_shelf(self, shelf_id: str) -> ConfigShelfInfo|None:
        if self._shelves_cache is None or len(self._shelves_cache) == 0:
            return None

        return next((x for x in self._shelves_cache if x.id == shelf_id), None)

    @api_handler(default_return=None)
    async def add_shelf(self, data: ShelfInfo) -> ConfigShelfInfo:
        temp = self._shelves_cache.copy() if self._shelves_cache is not None else []

        shelf_id = str(uuid.uuid4())
        config_data = ConfigShelfInfo(id=shelf_id, data=data)
        temp.append(config_data)
        if write_to_file(self._shelf_file_path, orjson.dumps([x.model_dump(mode="json") for x in temp]).decode(encoding="utf-8")):
            self._shelves_cache = temp
            return config_data
        else:
            raise InternalAgentError("Failed to save shelf data.")

    @api_handler(default_return=False)
    async def clear_shelves(self) -> bool:
        delete_file(self._shelf_file_path)
        self._shelves_cache = None
        return True

    @api_handler(default_return=False)
    async def delete_shelf(self, shelf_id: str) -> bool:
        if self._shelves_cache is None:
            return True

        temp = self._shelves_cache.copy()

        for pending in [x for x in temp if x.id == shelf_id]:
            temp.remove(pending)

        if write_to_file(self._shelf_file_path, orjson.dumps([x.model_dump(mode="json") for x in temp]).decode(encoding="utf-8")):
            self._shelves_cache = temp
            return True
        else:
            raise InternalAgentError("Failed to delete the shelf.")

    @api_handler(default_return=False)
    async def modify_shelf(self, shelf_id: str, data: ShelfInfo) -> bool:
        if self._shelves_cache is None:
            return False

        temp = self._shelves_cache.copy()

        shelf = next((x for x in temp if x.id == shelf_id), None)
        if shelf is None:
            return False

        temp.remove(shelf)

        shelf.data = data
        temp.append(shelf)
        if write_to_file(self._shelf_file_path, orjson.dumps([x.model_dump(mode="json") for x in temp]).decode(encoding="utf-8")):
            self._shelves_cache = temp
            return True
        else:
            raise InternalAgentError("Failed to modify the shelf.")

    @api_handler(default_return=None)
    async def create_industry_task(self, task: IndustryTaskModel) -> IndustryTaskResponse|None:
        pois = await self._artifact_manager.get_pois()
        bound_home_dock = await self._home_dock_manager.get_home_dock()
        homes = await self._home_dock_manager.get_all_docks()
        shelves = self._shelves_cache if self._shelves_cache is not None else []

        action_task = parse_industry_task_to_action_task(self._node, task, pois, bound_home_dock, homes, shelves)
        await self._action_task_supervisor.append(action_task)

        return IndustryTaskResponse(order_id=str(uuid.uuid4()), result=True)

    @api_handler(default_return=False)
    async def stop_current_action_and_run_next_action(self) -> bool:
        await self._action_task_supervisor.run_next_action()
        return True

    @api_handler(default_return=False)
    async def delete_all_tasks(self) -> bool:
        await self._action_task_supervisor.cancel()
        return True

    @api_handler(default_return=None)
    async def get_industry_stage(self) -> IndustryStageEnum|None:
        return IndustryStageMaintainer().current_stage

    async def _set_auto_return_task(self):
        bound_home_dock = await self._home_dock_manager.get_home_dock()
        homes = await self._home_dock_manager.get_all_docks()

        try:
            action_task = create_auto_return_task(self._node, bound_home_dock, homes)
            self._action_task_supervisor.set_auto_return_task(action_task)
        except Exception:
            self._node.get_logger().warning("Unable to create auto return task. Missing home docks.")