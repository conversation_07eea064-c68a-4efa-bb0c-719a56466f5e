import uuid

from cartographer_ros_msgs.msg import POI
from cartographer_ros_msgs.srv import <PERSON><PERSON><PERSON>, UpdatePOI, RemovePOI, GetPOI, AddPOI, RemoveAllPOI
from geometry_msgs.msg import Pose2D as RosPose2D
from rclpy.node import Node
from typing import List, Dict

from .agent_exceptions import ROSServiceNoResponseError
from .base_manager import BaseManager
from .models import Pose2D, PointOfInterest, VirtualLine, VirtualArea, ArtifactLineUsage, ArtifactAreaUsage
from .robot_pose_listener import RobotPoseListener
from .utils import api_handler


class ArtifactManager(BaseManager):

    def __init__(self, node: Node, pose_listener: RobotPoseListener):
        super().__init__(node)

        self._pose_listener = pose_listener

    def _subscribe_to_topics(self):
        pass

    def _create_clients(self):
        self._create_client("list_poi", ListPOI)
        self._create_client("add_poi", AddPOI)
        self._create_client("remove_all_poi", RemoveAllPOI)
        self._create_client("get_poi", GetPOI)
        self._create_client("update_poi", UpdatePOI)
        self._create_client("remove_poi", RemovePOI)

    @api_handler(default_return=None)
    async def get_pois(self) -> List[PointOfInterest]:
        client = self._get_client("list_poi", ListPOI)
        if client is None:
            raise ROSServiceNoResponseError("list_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("list_poi")

        response = await client.call_async(ListPOI.Request())

        if response.success:
            data: List[PointOfInterest] = []
            for poi in response.pois:
                poi_id = poi.id
                metadata: Dict[str, str] = dict()
                if poi.name is not None and poi.name != "":
                    metadata["display_name"] = poi.name
                if poi.type is not None and poi.type != "":
                    metadata["type"] = poi.type
                pose = Pose2D(x=poi.pose.x, y=poi.pose.y, yaw=poi.pose.theta)
                data.append(PointOfInterest(id=poi_id, pose=pose, metadata=metadata))
            return data
        else:
            raise ROSServiceNoResponseError("list_poi")

    async def add_poi(self, data: PointOfInterest) -> bool:
        client = self._get_client("add_poi", AddPOI)
        if client is None:
            raise ROSServiceNoResponseError("add_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("add_poi")

        poi_id = data.id if data.id is not None else str(uuid.uuid4())
        poi_name = ""
        poi_type = ""
        if data.metadata is not None:
            if data.metadata["display_name"] is not None:
                poi_name = data.metadata["display_name"]
            if data.metadata["type"] is not None:
                poi_type = data.metadata["type"]
        if data.pose is not None:
            pose = RosPose2D(x=data.pose.x, y=data.pose.y, theta=data.pose.yaw)
        else:
            current_pose = await self._pose_listener.get_pose()
            pose = RosPose2D(x=current_pose["x"], y=current_pose["y"], theta=current_pose["yaw"])
        request = AddPOI.Request(poi=POI(id=poi_id, name=poi_name, type=poi_type, pose=pose))

        response = await client.call_async(request)

        return response.success

    @api_handler(default_return=False)
    async def clear_pois(self) -> bool:
        client = self._get_client("remove_all_poi", RemoveAllPOI)
        if client is None:
            raise ROSServiceNoResponseError("remove_all_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("remove_all_poi")

        response = await client.call_async(RemoveAllPOI.Request())

        return response.success

    @api_handler(default_return=None)
    async def get_poi_by_id(self, poi_id: str) -> PointOfInterest|None:
        client = self._get_client("get_poi", GetPOI)
        if client is None:
            raise ROSServiceNoResponseError("get_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("get_poi")

        response = await client.call_async(GetPOI.Request(id=poi_id))

        if not response.success:
            return None

        poi_id = response.poi.id
        pose = Pose2D(x=response.poi.pose.x, y=response.poi.pose.y, yaw=response.poi.pose.theta)
        metadata: Dict[str, str] = dict()
        if response.poi.name is not None and response.poi.name != "":
            metadata["display_name"] = response.poi.name
        if response.poi.type is not None and response.poi.type != "":
            metadata["type"] = response.poi.type

        return PointOfInterest(id=poi_id, pose=pose, metadata=metadata)

    async def modify_poi_by_id(self, poi_id: str, poi: PointOfInterest) -> bool:
        client = self._get_client("update_poi", UpdatePOI)
        if client is None:
            raise ROSServiceNoResponseError("update_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("update_poi")

        poi_name = ""
        poi_type = ""
        if poi.metadata["display_name"] is not None:
            poi_name = poi.metadata["display_name"]
        if poi.metadata["type"] is not None:
            poi_type = poi.metadata["type"]
        pose = RosPose2D(x=poi.pose.x, y=poi.pose.y, theta=poi.pose.yaw)
        request = UpdatePOI.Request(poi=POI(id=poi_id, name=poi_name, type=poi_type, pose=pose))

        response = await client.call_async(request)
        return response.success

    @api_handler(default_return=False)
    async def delete_poi_by_id(self, poi_id: str) -> bool:
        client = self._get_client("remove_poi", RemovePOI)
        if client is None:
            raise ROSServiceNoResponseError("remove_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("remove_poi")

        response = await client.call_async(RemovePOI.Request(id=poi_id))
        return response.success

    @api_handler(default_return=[])
    async def get_virtual_lines(self, usage: ArtifactLineUsage) -> List[VirtualLine]:
        return list()

    async def add_virtual_lines(self, usage: ArtifactLineUsage, lines: List[VirtualLine]):
        return False

    async def modify_virtual_lines(self, usage: ArtifactLineUsage, lines: List[VirtualLine]):
        return False

    @api_handler(default_return=False)
    async def clear_virtual_lines(self, usage: ArtifactLineUsage) -> bool:
        return False

    @api_handler(default_return=False)
    async def delete_a_virtual_line(self, usage: ArtifactLineUsage, line_id: int) -> bool:
        return False

    @api_handler(default_return=[])
    async def get_virtual_areas(self, usage: ArtifactAreaUsage) -> List[VirtualArea]:
        return list()

    async def add_new_virtual_area(self, usage: ArtifactAreaUsage, data: VirtualArea):
        return False

    @api_handler(default_return=False)
    async def clear_virtual_areas(self, usage: ArtifactAreaUsage) -> bool:
        return False

    async def modify_virtual_arae(self, usage: ArtifactAreaUsage, area_id: int, data: VirtualArea):
        return False

    @api_handler(default_return=False)
    async def delete_virtual_area(self, usage: ArtifactAreaUsage, area_id: int) -> bool:
        return False