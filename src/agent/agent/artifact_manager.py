import uuid

from cartographer_ros_msgs.msg import POI
from cartographer_ros_msgs.srv import List<PERSON><PERSON>, UpdatePOI, RemovePOI, GetPOI, AddPOI, RemoveAllPOI
from geometry_msgs.msg import Pose2D as RosPose2D, Point
from rclpy.node import Node
from stcm_manager.srv import AddVirtualWall, ClearVirtualWalls, DeleteVirtualWall, GetVirtualWalls, ModifyVirtualWall,\
    AddDangerousArea, AddSensorDisableArea, ClearDangerousAreas, ClearSensorDisableAreas, DeleteDangerousArea, \
    DeleteSensorDisableArea, GetDangerousAreas, GetSensorDisableAreas, ModifyDangerousArea, ModifySensorDisableArea
from typing import List

try:
    from voronoi_topo_planner.srv import GetTrack, GetTracks, AddTrack, DeleteTrack, DeleteAllTracks, MoveTrack # type: ignore
    FEATURE_TRACK = True
except ImportError:
    FEATURE_TRACK = False

from .agent_exceptions import ROSServiceNoResponseError, UnsupportedArtifactUsageError, InvalidRequestDataError
from .base_manager import BaseManager
from .models import Pose2D, PointOfInterest, VirtualLine, VirtualArea, ArtifactLineUsage, ArtifactAreaUsage, SimplePose, VirtualAreaData, DangerousAreaMetadata, SensorDisableAreaMetadata, POIMetadata
from .robot_pose_listener import RobotPoseListener
from .utils import api_handler, generate_random_str


class ArtifactManager(BaseManager):

    def __init__(self, node: Node, pose_listener: RobotPoseListener):
        super().__init__(node)

        self._pose_listener = pose_listener

    def _subscribe_to_topics(self):
        pass

    def _create_clients(self):
        self._create_client("list_poi", ListPOI)
        self._create_client("add_poi", AddPOI)
        self._create_client("remove_all_poi", RemoveAllPOI)
        self._create_client("get_poi", GetPOI)
        self._create_client("update_poi", UpdatePOI)
        self._create_client("remove_poi", RemovePOI)

        self._create_client("add_virtual_wall", AddVirtualWall)
        self._create_client("clear_virtual_walls", ClearVirtualWalls)
        self._create_client("delete_virtual_wall", DeleteVirtualWall)
        self._create_client("get_virtual_walls", GetVirtualWalls)
        self._create_client("modify_virtual_wall", ModifyVirtualWall)

        self._create_client("add_dangerous_area", AddDangerousArea)
        self._create_client("clear_dangerous_area", ClearDangerousAreas)
        self._create_client("delete_dangerous_area", DeleteDangerousArea)
        self._create_client("get_dangerous_areas", GetDangerousAreas)
        self._create_client("modify_dangerous_area", ModifyDangerousArea)

        self._create_client("add_sensor_disable_area", AddSensorDisableArea)
        self._create_client("clear_sensor_disable_area", ClearSensorDisableAreas)
        self._create_client("delete_sensor_disable_area", DeleteSensorDisableArea)
        self._create_client("get_sensor_disable_areas", GetSensorDisableAreas)
        self._create_client("modify_sensor_disable_area", ModifySensorDisableArea)

        if FEATURE_TRACK:
            self._create_client("add_track", AddTrack)
            self._create_client("delete_track", DeleteTrack)
            self._create_client("delete_all_tracks", DeleteAllTracks)
            self._create_client("get_track", GetTrack)
            self._create_client("get_tracks", GetTracks)
            self._create_client("move_track", MoveTrack)

    @api_handler(default_return=None)
    async def get_pois(self) -> List[PointOfInterest]:
        client = self._get_client("list_poi", ListPOI)
        if client is None:
            raise ROSServiceNoResponseError("list_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("list_poi")

        response = await client.call_async(ListPOI.Request())

        if response is not None and response.success:
            data: List[PointOfInterest] = []
            for poi in response.pois:
                poi_id = poi.id
                metadata = POIMetadata()
                if poi.name is not None and poi.name != "":
                    metadata.display_name = poi.name
                if poi.type is not None and poi.type != "":
                    metadata.type = poi.type
                pose = Pose2D(x=poi.pose.x, y=poi.pose.y, yaw=poi.pose.theta)
                data.append(PointOfInterest(id=poi_id, pose=pose, metadata=metadata))
            return data
        else:
            raise ROSServiceNoResponseError("list_poi")

    async def add_poi(self, data: PointOfInterest) -> bool:
        client = self._get_client("add_poi", AddPOI)
        if client is None:
            raise ROSServiceNoResponseError("add_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("add_poi")

        poi_id = data.id if data.id is not None else str(uuid.uuid4())
        poi_name = ""
        poi_type = ""
        if data.metadata is not None:
            if data.metadata.display_name is not None:
                poi_name = data.metadata.display_name
            if data.metadata.type is not None:
                poi_type = data.metadata.type
        if poi_name == "":
            prefix = poi_type if poi_type != "" else ""
            poi_name = generate_random_str(length=10, prefix=prefix)
        if data.pose is not None:
            pose = RosPose2D(x=data.pose.x, y=data.pose.y, theta=data.pose.yaw)
        else:
            current_pose = self._pose_listener.get_pose()
            if current_pose is None:
                return False
            pose = RosPose2D(x=current_pose.x, y=current_pose.y, theta=current_pose.yaw)
        request = AddPOI.Request(poi=POI(id=poi_id, name=poi_name, type=poi_type, pose=pose))

        response = await client.call_async(request)

        return response is not None and response.success

    @api_handler(default_return=False)
    async def clear_pois(self) -> bool:
        client = self._get_client("remove_all_poi", RemoveAllPOI)
        if client is None:
            raise ROSServiceNoResponseError("remove_all_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("remove_all_poi")

        response = await client.call_async(RemoveAllPOI.Request())

        return response is not None and response.success

    @api_handler(default_return=None)
    async def get_poi_by_id(self, poi_id: str) -> PointOfInterest|None:
        client = self._get_client("get_poi", GetPOI)
        if client is None:
            raise ROSServiceNoResponseError("get_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("get_poi")

        response = await client.call_async(GetPOI.Request(id=poi_id))

        if response is None or not response.success:
            return None

        poi_id = response.poi.id
        pose = Pose2D(x=response.poi.pose.x, y=response.poi.pose.y, yaw=response.poi.pose.theta)
        metadata = POIMetadata()
        if response.poi.name is not None and response.poi.name != "":
            metadata.display_name = response.poi.name
        if response.poi.type is not None and response.poi.type != "":
            metadata.type = response.poi.type

        return PointOfInterest(id=poi_id, pose=pose, metadata=metadata)

    async def modify_poi_by_id(self, poi_id: str, poi: PointOfInterest) -> bool:
        client = self._get_client("update_poi", UpdatePOI)
        if client is None:
            raise ROSServiceNoResponseError("update_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("update_poi")

        if poi.pose is None:
            raise InvalidRequestDataError("Missing pose data.")

        poi_name = ""
        poi_type = ""
        if poi.metadata is not None and poi.metadata.display_name is not None:
            poi_name = poi.metadata.display_name
        if poi.metadata is not None and poi.metadata.type is not None:
            poi_type = poi.metadata.type
        pose = RosPose2D(x=poi.pose.x, y=poi.pose.y, theta=poi.pose.yaw)
        request = UpdatePOI.Request(poi=POI(id=poi_id, name=poi_name, type=poi_type, pose=pose))

        response = await client.call_async(request)
        return response is not None and response.success

    @api_handler(default_return=False)
    async def delete_poi_by_id(self, poi_id: str) -> bool:
        client = self._get_client("remove_poi", RemovePOI)
        if client is None:
            raise ROSServiceNoResponseError("remove_poi")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("remove_poi")

        response = await client.call_async(RemovePOI.Request(id=poi_id))
        return response is not None and response.success

    @api_handler(default_return=[])
    async def get_virtual_lines(self, usage: ArtifactLineUsage) -> List[VirtualLine]:
        if not FEATURE_TRACK and usage == ArtifactLineUsage.TRACKS:
            raise UnsupportedArtifactUsageError(ArtifactLineUsage.TRACKS.value)

        if usage == ArtifactLineUsage.TRACKS:
            return await self._get_virtual_tracks()
        else:
            return await self._get_virtual_walls()

    async def _get_virtual_tracks(self) -> List[VirtualLine]:
        client = self._get_client("get_tracks", GetTracks)
        if client is None:
            raise ROSServiceNoResponseError("get_tracks")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("get_tracks")

        response = await client.call_async(GetTracks.Request())

        if response is None or not response.success:
            raise ROSServiceNoResponseError("get_tracks")

        data: List[VirtualLine] = []
        for line in response.tracks:
            start = SimplePose(x=line.start_point.x, y=line.start_point.y)
            end = SimplePose(x=line.end_point.x, y=line.end_point.y)
            data.append(VirtualLine(id=line.track_id, start=start, end=end, direction=line.direction))
        return data

    async def _get_virtual_walls(self) -> List[VirtualLine]:
        client = self._get_client("get_virtual_walls", GetVirtualWalls)
        if client is None:
            raise ROSServiceNoResponseError("get_virtual_walls")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("get_virtual_walls")

        response = await client.call_async(GetVirtualWalls.Request())

        if response is not None and response.success:
            data: List[VirtualLine] = []
            for line in response.lines:
                start = SimplePose(x=line.start.x, y=line.start.y)
                end = SimplePose(x=line.end.x, y=line.end.y)
                data.append(VirtualLine(start=start, end=end, id=line.id))
            return data
        else:
            raise ROSServiceNoResponseError("get_virtual_walls")

    async def add_virtual_lines(self, usage: ArtifactLineUsage, lines: List[VirtualLine]) -> bool:
        if not FEATURE_TRACK and usage == ArtifactLineUsage.TRACKS:
            raise UnsupportedArtifactUsageError(ArtifactLineUsage.TRACKS.value)

        if usage == ArtifactLineUsage.TRACKS:
            return await self._add_virtual_tracks(lines)
        else:
            return await self._add_virtual_walls(lines)

    async def _add_virtual_tracks(self, lines: List[VirtualLine]) -> bool:
        client = self._get_client("add_track", AddTrack)
        if client is None:
            raise ROSServiceNoResponseError("add_track")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("add_track")

        for track in lines:
            start = Point(x=track.start.x, y=track.start.y, z=0.0)
            end = Point(x=track.end.x, y=track.end.y, z=0.0)
            direction = track.direction if track.direction is not None else 0
            track_id = track.id if track.id is not None else 0
            request = AddTrack.Request(track_id=track_id, start_point=start, end_point=end, direction=direction)
            response = await client.call_async(request)
            if response is None or not response.success:
                return False
        return True

    async def _add_virtual_walls(self, lines: List[VirtualLine]) -> bool:
        client = self._get_client("add_virtual_wall", AddVirtualWall)
        if client is None:
            raise ROSServiceNoResponseError("add_virtual_wall")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("add_virtual_wall")

        for line in lines:
            start = Point(x=line.start.x, y=line.start.y, z=0.0)
            end = Point(x=line.end.x, y=line.end.y, z=0.0)

            request = AddVirtualWall.Request(start_point=start, end_point=end)
            response = await client.call_async(request)
            if response is None or not response.success:
                return False
        return True

    async def modify_virtual_lines(self, usage: ArtifactLineUsage, lines: List[VirtualLine]) -> bool:
        if not FEATURE_TRACK and usage == ArtifactLineUsage.TRACKS:
            raise UnsupportedArtifactUsageError(ArtifactLineUsage.TRACKS.value)

        if usage == ArtifactLineUsage.TRACKS:
            return await self._modify_virtual_tracks(lines)
        else:
            return await self._modify_virtual_walls(lines)

    async def _modify_virtual_tracks(self, lines: List[VirtualLine]) -> bool:
        client = self._get_client("move_track", MoveTrack)
        if client is None:
            raise ROSServiceNoResponseError("move_track")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("move_track")

        for track in lines:
            if track.id is None:
                raise InvalidRequestDataError("Invalid virtual track data. Missing track ID.")
            start = Point(x=track.start.x, y=track.start.y, z=0.0)
            end = Point(x=track.end.x, y=track.end.y, z=0.0)

            request = MoveTrack.Request(track_id=track.id, new_start_point=start, new_end_point=end)
            response = await client.call_async(request)
            if response is None or not response.success:
                return False
        return True

    async def _modify_virtual_walls(self, lines: List[VirtualLine]) -> bool:
        client = self._get_client("modify_virtual_wall", ModifyVirtualWall)
        if client is None:
            raise ROSServiceNoResponseError("modify_virtual_wall")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("modify_virtual_wall")

        for line in lines:
            if line.id is None:
                raise InvalidRequestDataError("Invalid virtual wall data. Missing wall ID.")
            start = Point(x=line.start.x, y=line.start.y, z=0.0)
            end = Point(x=line.end.x, y=line.end.y, z=0.0)

            request = ModifyVirtualWall.Request(start_point=start, end_point=end, id=line.id)
            response = await client.call_async(request)
            if response is None or not response.success:
                return False
        return True

    @api_handler(default_return=False)
    async def clear_virtual_lines(self, usage: ArtifactLineUsage) -> bool:
        if not FEATURE_TRACK and usage == ArtifactLineUsage.TRACKS:
            raise ROSServiceNoResponseError(ArtifactLineUsage.TRACKS.value)

        if usage == ArtifactLineUsage.TRACKS:
            return await self._clear_virtual_tracks()
        else:
            return await self._clear_virtual_walls()

    async def _clear_virtual_tracks(self) -> bool:
        client = self._get_client("delete_all_tracks", DeleteAllTracks)
        if client is None:
            raise ROSServiceNoResponseError("delete_all_tracks")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("delete_all_tracks")

        response = await client.call_async(DeleteAllTracks.Request())

        return response is not None and response.success

    async def _clear_virtual_walls(self) -> bool:
        client = self._get_client("clear_virtual_walls", ClearVirtualWalls)
        if client is None:
            raise ROSServiceNoResponseError("clear_virtual_walls")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("clear_virtual_walls")

        response = await client.call_async(ClearVirtualWalls.Request())

        return response is not None and response.success

    @api_handler(default_return=False)
    async def delete_a_virtual_line(self, usage: ArtifactLineUsage, line_id: int) -> bool:
        if not FEATURE_TRACK and usage == ArtifactLineUsage.TRACKS:
            raise UnsupportedArtifactUsageError(ArtifactLineUsage.TRACKS.value)

        if usage == ArtifactLineUsage.TRACKS:
            return await self._delete_a_virtual_track(line_id)
        else:
            return await self._delete_a_virtual_wall(line_id)

    async def _delete_a_virtual_track(self, track_id: int) -> bool:
        client = self._get_client("delete_track", DeleteTrack)
        if client is None:
            raise ROSServiceNoResponseError("delete_track")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("delete_track")

        response = await client.call_async(DeleteTrack.Request(track_id=track_id))

        return response is not None and response.success

    async def _delete_a_virtual_wall(self, line_id: int) -> bool:
        client = self._get_client("delete_virtual_wall", DeleteVirtualWall)
        if client is None:
            raise ROSServiceNoResponseError("delete_virtual_wall")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("delete_virtual_wall")

        response = await client.call_async(DeleteVirtualWall.Request(id=line_id))

        return response is not None and response.success

    @api_handler(default_return=[])
    async def get_virtual_areas(self, usage: ArtifactAreaUsage) -> List[VirtualArea]:
        match usage:
            case ArtifactAreaUsage.DANGEROUS_AREA:
                return await self._get_dangerous_areas()
            case ArtifactAreaUsage.SENSOR_DISABLE_AREA:
                return await self._get_sensor_disable_areas()
            case _:
                raise UnsupportedArtifactUsageError(usage.value)

    async def _get_dangerous_areas(self) -> List[VirtualArea]:
        client = self._get_client("get_dangerous_areas", GetDangerousAreas)
        if client is None:
            raise ROSServiceNoResponseError("get_dangerous_areas")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("get_dangerous_areas")

        response = await client.call_async(GetDangerousAreas.Request())

        if response is None or not response.success:
            raise ROSServiceNoResponseError("get_dangerous_areas")

        areas: List[VirtualArea] = []
        for area in response.areas:
            start = SimplePose(x=area.start.x, y=area.start.y)
            end = SimplePose(x=area.end.x, y=area.end.y)
            area_id = area.id
            half_width = area.half_width
            metadata = DangerousAreaMetadata(speed_limit=str(area.speed_limit))
            areas.append(VirtualArea(id=area_id, usage=ArtifactAreaUsage.DANGEROUS_AREA, metadata=metadata, area=VirtualAreaData(start=start, end=end, half_width=half_width)))
        return areas

    async def _get_sensor_disable_areas(self) -> List[VirtualArea]:
        client = self._get_client("get_sensor_disable_areas", GetSensorDisableAreas)
        if client is None:
            raise ROSServiceNoResponseError("get_sensor_disable_areas")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("get_sensor_disable_areas")

        response = await client.call_async(GetSensorDisableAreas.Request())

        if response is None or not response.success:
            raise ROSServiceNoResponseError("get_sensor_disable_areas")

        areas: List[VirtualArea] = []
        for area in response.areas:
            start = SimplePose(x=area.start_point.x, y=area.start_point.y)
            end = SimplePose(x=area.end_point.x, y=area.end_point.y)
            area_id = area.id
            half_width = area.half_width
            sensor_types = ",".join([str(x) for x in area.sensor_types])
            metadata = SensorDisableAreaMetadata(sensor_type=sensor_types)
            areas.append(VirtualArea(id=area_id, usage=ArtifactAreaUsage.SENSOR_DISABLE_AREA, metadata=metadata, area=VirtualAreaData(start=start, end=end, half_width=half_width)))
        return areas

    async def add_new_virtual_area(self, usage: ArtifactAreaUsage, data: VirtualArea) -> bool:
        match usage:
            case ArtifactAreaUsage.DANGEROUS_AREA:
                return await self._add_new_dangerous_area(data)
            case ArtifactAreaUsage.SENSOR_DISABLE_AREA:
                return await self._add_new_sensor_disable_area(data)
            case _:
                raise UnsupportedArtifactUsageError(usage.value)

    async def _add_new_dangerous_area(self, data: VirtualArea) -> bool:
        client = self._get_client("add_dangerous_area", AddDangerousArea)
        if client is None:
            raise ROSServiceNoResponseError("add_dangerous_area")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("add_dangerous_area")

        if not isinstance(data.metadata, DangerousAreaMetadata):
            raise InvalidRequestDataError("Invalid metadata.")

        try:
            speed_limit = float(data.metadata.speed_limit)
        except ValueError:
            raise InvalidRequestDataError("Speed limit data is invalid.")
        start = Point(x=data.area.start.x, y=data.area.start.y, z=0.0)
        end = Point(x=data.area.end.x, y=data.area.end.y, z=0.0)
        half_width = data.area.half_width
        request = AddDangerousArea.Request(start_point=start, end_point=end, half_width=half_width, speed_limit=speed_limit)

        response = await client.call_async(request)
        return response is not None and response.success

    async def _add_new_sensor_disable_area(self, data: VirtualArea) -> bool:
        client = self._get_client("add_sensor_disable_area", AddSensorDisableArea)
        if client is None:
            raise ROSServiceNoResponseError("add_sensor_disable_area")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("add_sensor_disable_area")

        if not isinstance(data.metadata, SensorDisableAreaMetadata):
            raise InvalidRequestDataError("Invalid metadata.")

        if not data.metadata.sensor_type.startswith("[") or not data.metadata.sensor_type.endswith("]"):
            raise InvalidRequestDataError("Invalid sensor type data.")

        types: List[int] = []
        for sensor_id in data.metadata.sensor_type[1:-1].split(","):
            try:
                types.append(int(sensor_id))
            except ValueError:
                raise InvalidRequestDataError("Sensor types data is invalid.")

        if len(types) == 0:
            raise InvalidRequestDataError("Missing sensor types data.")

        sensor_types = types
        start = Point(x=data.area.start.x, y=data.area.start.y, z=0.0)
        end = Point(x=data.area.end.x, y=data.area.end.y, z=0.0)
        half_width = data.area.half_width
        request = AddSensorDisableArea.Request(start_point=start, end_point=end, half_width=half_width, sensor_types=sensor_types)

        response = await client.call_async(request)
        return response is not None and response.success


    @api_handler(default_return=False)
    async def clear_virtual_areas(self, usage: ArtifactAreaUsage) -> bool:
        match usage:
            case ArtifactAreaUsage.DANGEROUS_AREA:
                return await self._clear_dangerous_areas()
            case ArtifactAreaUsage.SENSOR_DISABLE_AREA:
                return await self._clear_sensor_disable_areas()
            case _:
                raise UnsupportedArtifactUsageError(usage.value)

    async def _clear_dangerous_areas(self) -> bool:
        client = self._get_client("clear_dangerous_areas", ClearDangerousAreas)
        if client is None:
            raise ROSServiceNoResponseError("clear_dangerous_areas")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("clear_dangerous_areas")

        response = await client.call_async(ClearDangerousAreas.Request())
        return response is not None and response.success

    async def _clear_sensor_disable_areas(self) -> bool:
        client = self._get_client("clear_sensor_disable_areas", ClearSensorDisableAreas)
        if client is None:
            raise ROSServiceNoResponseError("clear_sensor_disable_areas")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("clear_sensor_disable_areas")

        response = await client.call_async(ClearSensorDisableAreas.Request())
        return response is not None and response.success

    async def modify_virtual_area(self, usage: ArtifactAreaUsage, area_id: int, data: VirtualArea) -> bool:
        match usage:
            case ArtifactAreaUsage.DANGEROUS_AREA:
                return await self._modify_dangerous_area(area_id, data)
            case ArtifactAreaUsage.SENSOR_DISABLE_AREA:
                return await self._modify_sensor_disable_area(area_id, data)
            case _:
                raise UnsupportedArtifactUsageError(usage.value)

    async def _modify_dangerous_area(self, area_id: int, data: VirtualArea) -> bool:
        client = self._get_client("modify_dangerous_area", ModifyDangerousArea)
        if client is None:
            raise ROSServiceNoResponseError("modify_dangerous_area")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("modify_dangerous_area")

        if not isinstance(data.metadata, DangerousAreaMetadata):
            raise InvalidRequestDataError("Invalid metadata.")

        speed = data.metadata.speed_limit
        try:
            speed_limit = float(speed)
        except ValueError:
            raise InvalidRequestDataError("Speed limit data is invalid.")
        start = Point(x=data.area.start.x, y=data.area.start.y, z=0.0)
        end = Point(x=data.area.end.x, y=data.area.end.y, z=0.0)
        half_width = data.area.half_width
        request = ModifyDangerousArea.Request(area_id=area_id, start_point=start, end_point=end, half_width=half_width, speed_limit=speed_limit)

        response = await client.call_async(request)
        return response is not None and response.success

    async def _modify_sensor_disable_area(self, area_id: int, data: VirtualArea) -> bool:
        client = self._get_client("modify_sensor_disable_area", ModifySensorDisableArea)
        if client is None:
            raise ROSServiceNoResponseError("modify_sensor_disable_area")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("modify_sensor_disable_area")

        if "sensor_type" not in data.metadata:
            raise InvalidRequestDataError("Missing sensor types data.")

        if not isinstance(data.metadata, SensorDisableAreaMetadata):
            raise InvalidRequestDataError("Invalid metadata.")

        if not data.metadata.sensor_type.startswith("[") or not data.metadata.sensor_type.endswith("]"):
            raise InvalidRequestDataError("Sensor types data is invalid.")

        types: List[int] = []
        for sensor_id in data.metadata.sensor_type[1:-1].split(","):
            try:
                types.append(int(sensor_id))
            except ValueError:
                raise InvalidRequestDataError("Sensor types data is invalid.")

        if len(types) == 0:
            raise InvalidRequestDataError("Missing sensor types data.")

        sensor_types = types
        start = Point(x=data.area.start.x, y=data.area.start.y, z=0.0)
        end = Point(x=data.area.end.x, y=data.area.end.y, z=0.0)
        half_width = data.area.half_width
        request = ModifySensorDisableArea.Request(id=area_id, start_point=start, end_point=end, half_width=half_width, sensor_types=sensor_types)

        response = await client.call_async(request)
        return response is not None and response.success

    @api_handler(default_return=False)
    async def delete_virtual_area(self, usage: ArtifactAreaUsage, area_id: int) -> bool:
        match usage:
            case ArtifactAreaUsage.DANGEROUS_AREA:
                return await self._delete_dangerous_area(area_id)
            case ArtifactAreaUsage.SENSOR_DISABLE_AREA:
                return await self._delete_sensor_disable_area(area_id)
            case _:
                raise UnsupportedArtifactUsageError(usage.value)

    async def _delete_dangerous_area(self, area_id: int) -> bool:
        client = self._get_client("delete_dangerous_area", DeleteDangerousArea)
        if client is None:
            raise ROSServiceNoResponseError("delete_dangerous_area")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("delete_dangerous_area")

        response = await client.call_async(DeleteDangerousArea.Request(area_id=area_id))
        return response is not None and response.success

    async def _delete_sensor_disable_area(self, area_id: int) -> bool:
        client = self._get_client("delete_sensor_disable_area", DeleteSensorDisableArea)
        if client is None:
            raise ROSServiceNoResponseError("delete_sensor_disable_area")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("delete_sensor_disable_area")

        response = await client.call_async(DeleteSensorDisableArea.Request(id=area_id))
        return response is not None and response.success