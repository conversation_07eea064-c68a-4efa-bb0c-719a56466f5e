from enum import Enum
from pydantic import Field
from typing import List, Optional

from .base_json_model import BaseJsonModel


class IndustryTaskActionEnum(str, Enum):
    MOVE_TO_POI = "to_point"
    ACCURATE_DOCKING_COLUMNAR_TAG = "accurate_docking_columnar_tag"
    CARRY_MOVE = "carry_move"
    JACK_UP = "rise"
    JACK_DOWN = "down"
    BACK_OFF_FROM_SHELF = "back_off_from_shelf"
    GO_HOME = "go_home"


class IndustryTargetModel(BaseJsonModel):
    """
    If the action is MOVE_TO_POI, ACCURATE_DOCKING_COLUMNAR_TAG, CARRY_MOVE, you have to provide either target_id or target_name. If both are provided, target_id will be used.
    """
    target_id: Optional[str] = Field(default=None, description="POI ID.")
    target_name: Optional[str] = Field(default=None, description="POI display name.")
    action: IndustryTaskActionEnum = Field(description="Industry task action.")
    wait_time: Optional[float] = Field(default=0.0, description="Wait time in seconds after action done. Default is 0.")


class IndustryTaskModel(BaseJsonModel):
    type: str = Field(description="This field should always be 'INDUSTRY'.")
    task_targets: List[IndustryTargetModel] = Field(description="Industry targets")


class IndustryTaskResponse(BaseJsonModel):
    order_id: str = Field(description="Task ID")
    result: bool = Field(description="Task result")