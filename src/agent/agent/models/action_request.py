from pydantic import Field, BaseModel
from typing import Optional

from .action_definition_enum import ActionDefinition
from .action_options import *


class ActionRequest(BaseModel):
    action_name: str = Field(description="Action name")
    options: Optional[dict] = Field(default=None, description="Action options")

    @property
    def action_type(self) -> ActionDefinition|None:
        return ActionDefinition.parse_action_name(self.action_name)

    def get_parsed_options(self) -> ActionOptionsUnion|None:
        if self.options is None:
            return None

        match self.action_type:
            case ActionDefinition.MOVE_TO:
                return MoveToActionOptions.model_validate(self.options)
            case ActionDefinition.MOVE_BY:
                return MoveByActionOptions.model_validate(self.options)
            case ActionDefinition.ROTATE:
                return GeneralRotateActionOptions.model_validate(self.options)
            case ActionDefinition.ROTATE_TO:
                return GeneralRotateActionOptions.model_validate(self.options)
            case ActionDefinition.GO_HOME:
                return GoHomeActionOptions.model_validate(self.options)
            case ActionDefinition.MOVE_TO_TAG:
                return MoveToTagActionOptions.model_validate(self.options)
            case ActionDefinition.BACK_OFF_FROM_TAG:
                return BackOffFromTagActionOptions.model_validate(self.options)
            case _:
                return None
