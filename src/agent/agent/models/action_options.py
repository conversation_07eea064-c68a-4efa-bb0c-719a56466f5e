from abc import ABC
from pydantic import Field
from typing import Optional, List, Union, Any

from .base_json_model import BaseJsonModel
from .jack_command import JackMoveDirectionEnum
from .location import Location
from .move_options import MoveOptions
from .pose import Pose
from .pose_2d import Pose2D
from .rectangle_area import Rectangle<PERSON><PERSON>
from .shelf_info import ShelfInfo


class BaseActionOptions(ABC, BaseJsonModel):
    pass


class MoveToActionOptions(BaseActionOptions):
    target: Location = Field(description="Target point")
    move_options: Optional[MoveOptions] = Field(default=None, description="Move options")


class MoveByActionOptions(BaseActionOptions):
    direction: Optional[int] = Field(default=None, description="Direction. Forward: 0. Backward: 1. Turn left: 2. Turn right: 3")
    theta: Optional[float] = Field(default=None, description="Theta")
    duration: Optional[int] = Field(default=500, description="Action duration")


class GeneralRotateActionOptions(BaseActionOptions):
    angle: float = Field(description="Rotate by angle")


class GoHomeActionOptions(BaseActionOptions):
    dock_id: Optional[str] = Field(default=None, description="Dock ID. If you set dock id, robot will ignore dock pose.")
    dock_pose: Optional[Pose] = Field(default=None, description="Dock pose. Only work when the dock_id is not set. If you leave dock_id and dock_pose to None, robot will go back to the bound dock. If there is no bound dock, robot will go to the zero point.")
    gohome_options: Optional[Any] = Field(default=None, description="Leave it null. The declaration of it is for compatibility.")


class MoveToTagActionOptions(BaseActionOptions):
    target: Pose = Field(description="Target pose")
    tag_type: int = Field(description="0: QRCode, 1: Laser Tag, 2: Laser Board, 3: Shelf")
    dock_allowance: Optional[float] = Field(default=None, description="Allowance of docking distance. Only available when tag_type is 3.")
    shelves: Optional[List[ShelfInfo]] = Field(default=None, description="Shelves. Only available when tag_type is 3.")


class BackOffFromTagActionOptions(BaseActionOptions):
    tag_type: Optional[int] = Field(default=0, description="0: QRCode, 1: Laser Tag, 2: Laser Board, 3: Shelf, 4: Home")


class JackMoveActionOptions(BaseActionOptions):
    move_direction: JackMoveDirectionEnum = Field(description="Jack move direction.")


class JackTopMoveToActionOptions(BaseActionOptions):
    target: Pose2D = Field(description="Target location")


class RelocateOptions(BaseJsonModel):
    """Options in RelocateActionOptions"""
    max_recover_time: Optional[int] = Field(default=None, description="Maximum time in milliseconds to recover from failure. If it's none, use the default value.")


class RelocateActionOptions(BaseActionOptions):
    area: Optional[RectangleArea] = Field(default=None, description="Bounding rectangle for limit range relocalization. If it's none, no range limit.")
    relocalization_options: Optional[RelocateOptions] = Field(default=None, description="Relocalization options")


ActionOptionsUnion = Union[
    MoveByActionOptions,
    MoveToActionOptions,
    GeneralRotateActionOptions,
    GoHomeActionOptions,
    MoveToTagActionOptions,
    BackOffFromTagActionOptions,
    JackMoveActionOptions,
    JackTopMoveToActionOptions,
    RelocateActionOptions,
]
