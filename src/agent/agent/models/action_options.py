from abc import ABC
from pydantic import Field, BaseModel
from typing import Optional, List, Union

from .move_options import MoveOptions
from .pose import Pose
from .shelf_info import ShelfIn<PERSON>
from .simple_pose import SimplePose


class BaseActionOptions(ABC, BaseModel):
    pass


class MoveToActionOptions(BaseActionOptions):
    target: SimplePose = Field(description="Target point")
    move_options: Optional[MoveOptions] = Field(default=None, description="Move options")


class MoveByActionOptions(BaseActionOptions):
    direction: Optional[int] = Field(default=None, description="Direction. Forward: 0. Backward: 1. Turn left: 2. Turn right: 3")
    theta: Optional[float] = Field(default=None, description="Theta")
    duration: Optional[int] = Field(default=500, description="Action duration")


class GeneralRotateActionOptions(BaseActionOptions):
    angle: float = Field(description="Rotate by angle")


class GoHomeOptionModel(BaseModel):
    flag: Optional[str] = Field(default=None, description="dock or no_dock")
    back_to_landing: Optional[bool] = Field(default=None, description="Back to landing point if fails")
    charging_retry_count: Optional[int] = Field(default=None, description="Retry count")
    move_options: Optional[MoveOptions] = Field(default=None, description="Move options")


class GoHomeActionOptions(BaseActionOptions):
    dock_pose: Optional[Pose] = Field(default=Pose(x=0.0, y=0.0, z=0.0, yaw=0.0, roll=0.0, pitch=0.0), description="Dock pose")
    dock_type: Optional[str] = Field(default="", description="Dock type")
    gohome_options: Optional[GoHomeOptionModel] = Field(default=None, description="Go home options")


class MoveToTagActionOptions(BaseActionOptions):
    target: Pose = Field(description="Target pose")
    tag_type: int = Field(description="0: QRCode, 1: Laser Tag, 2: Laser Board, 3: Shelf")
    target_relative_pose: Optional[SimplePose] = Field(default=None, description="The relative distance between robot and tag. If tag_type is 3, this parameter will be ignored.")
    backward_docking: Optional[bool] = Field(default=None, description="Use backward docking")
    turn_radian: Optional[float] = Field(default=None, description="The turning radian after docking successfully.")
    tag_ids: Optional[List[int]] = Field(default=None, description="QRCode IDs. Only available when tag_type is 0.")
    reflect_tag_num: Optional[int] = Field(default=None, description="Tag number. Only available when tag_type is 2.")
    dock_retry_count: Optional[int] = Field(default=None, description="Retry count if docking fails.")
    dock_allowance: Optional[float] = Field(default=None, description="Allowance of docking distance. Only available when tag_type is 3.")
    shelves: Optional[List[ShelfInfo]] = Field(default=None, description="Shelves. Only available when tag_type is 3.")


ActionOptionsUnion = Union[
    MoveByActionOptions,
    MoveToActionOptions,
    GeneralRotateActionOptions,
    GoHomeActionOptions,
    MoveToTagActionOptions,
]
