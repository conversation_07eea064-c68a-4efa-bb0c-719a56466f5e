from enum import Enum


class ActionDefinition(Enum):
    MOVE_TO = "slamtec.agent.actions.MoveToAction"
    MOVE_BY = "slamtec.agent.actions.MoveByAction"
    GO_HOME = "slamtec.agent.actions.GoHomeAction"
    ROTATE = "slamtec.agent.actions.RotateAction"
    ROTATE_TO = "slamtec.agent.actions.RotateToAction"
    MOVE_TO_TAG = "slamtec.agent.actions.MoveToTagAction"
    BACK_OFF_FROM_TAG = "slamtec.agent.actions.BackOffFromTagAction"
    RECOVER_LOCALIZATION = "slamtec.agent.actions.RecoverLocalizationAction"

    @classmethod
    def parse_action_name(cls, action: str):
        for value in ActionDefinition:
            if action == value.value:
                return value
        return None
