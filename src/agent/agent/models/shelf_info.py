from pydantic import Field
from typing import Optional

from .base_json_model import BaseJsonModel


class ShelfInfo(BaseJsonModel):
    shelf_columnar_length: float = Field(description="Columnar length of shelf.")
    shelf_columnar_width: float = Field(description="Columnar width of shelf.")
    shelf_columnar_diameter: float = Field(description="Columnar diameter of shelf leg.")
    shelf_length_retraction: float = Field(description="Retraction length.")


class ConfigShelfInfo(BaseJsonModel):
    id: Optional[str] = Field(default=None, description="Shelf ID")
    data: ShelfInfo = Field(description="Shelf data")