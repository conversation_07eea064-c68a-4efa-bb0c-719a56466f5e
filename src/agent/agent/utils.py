import math

from enum import Enum

def quaternion_to_euler(qx, qy, qz, qw):
    """
    Convert a quaternion into Euler angles (roll, pitch, yaw)
    roll  is rotation around x-axis
    pitch is rotation around y-axis
    yaw   is rotation around z-axis
    """

    # roll (x-axis rotation)
    sinr_cosp = 2.0 * (qw * qx + qy * qz)
    cosr_cosp = 1.0 - 2.0 * (qx * qx + qy * qy)
    roll = math.atan2(sinr_cosp, cosr_cosp)

    # pitch (y-axis rotation)
    sinp = 2.0 * (qw * qy - qz * qx)
    # clamp to handle numerical errors (gimbal‐lock)
    if abs(sinp) >= 1:
        pitch = math.copysign(math.pi / 2, sinp)
    else:
        pitch = math.asin(sinp)

    # yaw (z-axis rotation)
    siny_cosp = 2.0 * (qw * qz + qx * qy)
    cosy_cosp = 1.0 - 2.0 * (qy * qy + qz * qz)
    yaw = math.atan2(siny_cosp, cosy_cosp)

    return roll, pitch, yaw


class ActionDefinition(Enum):
    MOVE_TO = "slamtec.agent.actions.MoveToAction"
    MOVE_BY = "slamtec.agent.actions.MoveByAction"
    GO_HOME = "slamtec.agent.actions.GoHomeAction"
    ROTATE = "slamtec.agent.actions.RotateAction"
    ROTATE_TO = "slamtec.agent.actions.RotateToAction"

    @classmethod
    def parse_action_name(cls, action: str):
        for value in ActionDefinition:
            if action == value.value:
                return value
        return None


class MoveDirection(Enum):
    FORWARD = 0
    BACKWARD = 1
    TURN_LEFT = 2
    TURN_RIGHT = 3

    @classmethod
    def parse_move_direction(cls, direction: int):
        for value in MoveDirection:
            if direction == value.value:
                return value
        return None
    
    
class SLAMMode(Enum):
    MAPPING = "mapping"
    LOCALIZATION = "localization"


class ArtifactLineUsage(Enum):
    TRACKS = "tracks"
    WALLS = "walls"

    @classmethod
    def parse_artifact_line_usage(cls, usage: str):
        for value in ArtifactLineUsage:
            if usage == value.value:
                return value
        return None


class ArtifactAreaUsage(Enum):
    FORBIDDEN_AREA="forbidden_area"
    ELEVATOR_AREA="elevator_area"
    DANGEROUS_AREA="dangerous_area"
    COVERAGE_AREA="coverage_area"
    MAINTENANCE_AREA="maintenance_area"
    SENSOR_DISABLE_AREA="sensor_disable_area"
    RESTRICTED_AREA="restricted_area"

    @classmethod
    def parse_artifact_area_usage(cls, usage: str):
        for value in ArtifactAreaUsage:
            if usage == value.value:
                return value
        return None