import asyncio
import functools
import inspect

from typing import Any, Coroutine, TypeVar, Callable
from typing_extensions import ParamSpec

from ..agent_exceptions import AgentBaseError


T = TypeVar("T")
P = ParamSpec("P")


def api_handler(default_return: Any) -> Callable[[Callable[P, Coroutine[Any, Any, T]]], Callable[P, Coroutine[Any, Any, T]]]:
    def decorator(method: Callable[P, Coroutine[Any, Any, T]]) -> Callable[P, Coroutine[Any, Any, T]]:
        sig = inspect.signature(method)

        @functools.wraps(method)
        async def wrapper(self, *args, **kwargs) -> T:      # pyrefly: ignore[bad-return]
            if not hasattr(self, "_api_locks"):
                self._api_locks = {}
            if not hasattr(self, "_api_tasks"):
                self._api_tasks = {}

            bound = sig.bind(self, *args, **kwargs)
            bound.apply_defaults()

            key = (method.__name__, *tuple(bound.arguments.items())[1:])

            lock = self._api_locks.setdefault(key, asyncio.Lock())

            async with lock:
                current_task: asyncio.Task = self._api_tasks.get(key)
                if current_task is not None and not current_task.done():
                    try:
                        return await current_task
                    except AgentBaseError as e:
                        raise e
                    except Exception:
                        return default_return

                new_task = asyncio.create_task(method(self, *args, **kwargs))   # type: ignore
                self._api_tasks[key] = new_task

            try:
                return await new_task
            except AgentBaseError as e:
                raise e
            except Exception:
                return default_return
            finally:
                self._api_tasks.pop(key, None)

        return wrapper      # type: ignore

    return decorator
