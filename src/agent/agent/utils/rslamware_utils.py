from unique_identifier_msgs.msg import UUID

from ..models import Pose2D


def is_same_dock(left: Pose2D, right: Pose2D) -> bool:
    x_diff = abs(left.x - right.x)
    y_diff = abs(left.y - right.y)
    yaw_diff = abs(left.yaw - right.yaw)
    return x_diff < 0.01 and y_diff < 0.01 and yaw_diff < 0.01


def generate_random_str(length: int=6, prefix: str|None = None) -> str:
    import random
    import string

    character = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(character) for _ in range(length))

    if prefix is not None:
        return f"{prefix}_{random_string}"
    else:
        return random_string


def convert_ros2_uuid_to_str(uuid: UUID) -> str:
    uuid_bytes = bytes(uuid.uuid)
    return str(uuid_bytes)
