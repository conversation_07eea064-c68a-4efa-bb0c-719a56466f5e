import async<PERSON>

from concurrent.futures import Future
from rclpy.task import Future as RclpyFuture


def rclpy_to_wrapped_future(rf: RclpyFuture) -> Future:
    cfut = Future()  # thread-safe
    def _done(future):
        try:
            if future.cancelled():
                cfut.cancel()
            elif future.exception() is not None:
                cfut.set_exception(future.exception())
            else:
                cfut.set_result(rf.result())
        except Exception as e:
            if cfut.done():
                cfut.set_exception(e)

    try:
        rf.add_done_callback(_done)
    except Exception as e:
        cfut.set_exception(e)

    return cfut