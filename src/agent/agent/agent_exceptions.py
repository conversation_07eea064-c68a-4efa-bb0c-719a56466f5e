# Invalid Artifact Usage Exception
class InvalidArtifactUsageError(Exception):

    def __init__(self, raw_value):
        super().__init__(f"{raw_value} is an invalid artifact usage value.")


class ConflictingCommandError(Exception):

    def __init__(self, raw_value):
        super().__init__(f"{raw_value}")


class ROSServiceNoResponseError(Exception):
    def __init__(self, service_name):
        super().__init__(f"Service {service_name} has no response.")


class UnsupportedActionError(Exception):
    def __init__(self, action_name):
        super().__init__(f"Action {action_name} is not supported.")


class InvalidActionOptionsError(Exception):
    def __init__(self, action_name):
        super().__init__(f"Action {action_name} has an invalid option.")