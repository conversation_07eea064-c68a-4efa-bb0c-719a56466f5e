# Invalid Artifact Usage Exception

class AgentBaseError(Exception):
    pass


class InvalidArtifactUsageError(AgentBaseError):
    def __init__(self, raw_value):
        super().__init__(f"{raw_value} is an invalid artifact usage value.")


class ConflictingCommandError(AgentBaseError):
    def __init__(self, raw_value):
        super().__init__(f"{raw_value}")


class ROSServiceNoResponseError(AgentBaseError):
    def __init__(self, service_name):
        super().__init__(f"Service {service_name} has no response.")


class UnsupportedActionError(AgentBaseError):
    def __init__(self, action_name):
        super().__init__(f"Action {action_name} is not supported.")


class InvalidActionOptionsError(AgentBaseError):
    def __init__(self, action_name):
        super().__init__(f"Action {action_name} has an invalid option.")


class UnsupportedArtifactUsageError(AgentBaseError):
    def __init__(self, usage):
        super().__init__(f"Usage {usage} is not supported.")


class InvalidRequestDataError(AgentBaseError):
    def __init__(self, msg):
        super().__init__(msg)


class InternalAgentError(AgentBaseError):
    def __init__(self, msg):
        super().__init__(msg)