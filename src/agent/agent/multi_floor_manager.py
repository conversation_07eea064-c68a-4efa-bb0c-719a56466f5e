from rclpy.node import Node

from stcm_manager.srv import SaveMap

from .agent_exceptions import ROSServiceNoResponseError
from .base_manager import BaseManager
from .utils import api_handler


class MultiFloorManager(BaseManager):

    def __init__(self, node: Node):
        super().__init__(node)

    def _subscribe_to_topics(self):
        pass

    def _create_clients(self):
        self._create_client("save_map", SaveMap)

    @api_handler(default_return=False)
    async def save_map_to_disk(self) -> bool:
        client = self._get_client("save_map", SaveMap)
        if client is None:
            raise ROSServiceNoResponseError("save_map")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("save_map")

        response = await client.call_async(SaveMap.Request())

        return response is not None and response.success