import math

import numpy as np
import struct

from asyncio import Lock as AsLock, Task, create_task
from nav_msgs.msg import Odometry, OccupancyGrid
from nav_msgs.srv import GetMap
from rclpy.node import Node
from rclpy.qos import qos_profile_services_default
from stcm_manager.srv import ClearMap, GetKnownArea, GetStcmFile, UploadStcmFile
from std_msgs.msg import Int32
from threading import Lock
from typing import Optional, Dict

from .agent_exceptions import ConflictingCommandError
from .base_manager import BaseManager
from .models import Pose, RectangleArea, SLAMMode
from .robot_pose_listener import RobotPoseListener
from .utils import quaternion_to_euler, DefaultQoSProfile, api_handler


class SLAMManager(BaseManager):
    """Slam manager for handling SLAM related operations"""

    def __init__(self, node: Node, pose_listener: RobotPoseListener):
        super().__init__(node)

        self._odometry_msg: Optional[Odometry] = None
        self._localization_quality = 0
        self._slam_mode = None
        self._known_area = None

        self._pose_listener = pose_listener

        self._map_data_fetcher = MapDataFetcher(node)

        self._get_stcm_lock = AsLock()
        self._upload_stcm_lock = AsLock()
        self._current_get_stcm_task: Optional[Task] = None
        self._current_upload_stcm_task: Optional[Task] = None

        self._detect_slam_mode()

    def _subscribe_to_topics(self):
        self._subscriptions.append(
            self._node.create_subscription(
                Odometry,
                "odom",
                self._odom_callback,
                qos_profile=DefaultQoSProfile
            )
        )
        self._subscriptions.append(
            self._node.create_subscription(
                Int32,
                "localization_quality",
                self._localization_quality_callback,
                qos_profile=DefaultQoSProfile
            )
        )

    def _create_clients(self):
        self._create_client("clear_map", ClearMap)
        self._create_client("get_known_area", GetKnownArea)
        self._create_client("get_stcm_file", GetStcmFile)
        self._create_client("upload_stcm_file", UploadStcmFile)

    def _odom_callback(self, msg):
        self._odometry_msg = msg

    def _localization_quality_callback(self, msg):
        self._localization_quality = int(msg.data)

    def _detect_slam_mode(self):
        try:
            node_names = self._node.get_node_names()
            if "nav2_container" in node_names:
                self._slam_mode = SLAMMode.LOCALIZATION
            else:
                self._slam_mode = SLAMMode.MAPPING
        except:
            pass

    @api_handler(default_return=None)
    async def get_pose(self):
        return await self._pose_listener.get_pose()

    async def set_pose(self, pose: Pose):
        from tf2_ros import TransformBroadcaster, TransformStamped

        broadcaster = TransformBroadcaster(self._node)

        stamped = TransformStamped()

        stamped.header.stamp = self._node.get_clock().now().to_msg()
        stamped.header.frame_id = "map"
        stamped.child_frame_id = "base_link"

        stamped.transform.translation.x = pose.x
        stamped.transform.translation.y = pose.y
        stamped.transform.translation.z = 0.0

        half_yaw = pose.yaw / 2.0
        stamped.transform.rotation.x = 0.0
        stamped.transform.rotation.y = 0.0
        stamped.transform.rotation.z = math.sin(half_yaw)
        stamped.transform.rotation.w = math.cos(half_yaw)

        broadcaster.sendTransform(stamped)

    @api_handler(default_return=None)
    async def get_odometry(self) -> Dict[str, float]| None:
        msg: Optional[Odometry] = self._odometry_msg

        if msg is None:
            return None

        x = msg.pose.pose.position.x
        y = msg.pose.pose.position.y
        z = msg.pose.pose.position.z

        qx, qy, qz, qw = msg.pose.pose.orientation.x, msg.pose.pose.orientation.y, msg.pose.pose.orientation.z, msg.pose.pose.orientation.w
        roll, pitch, yaw = quaternion_to_euler(qx, qy, qz, qw)
        return {"x":x, "y":y, "z":z, "roll":roll, "pitch":pitch, "yaw":yaw}

    @api_handler(default_return=0)
    async def get_localization_quality(self) -> int:
        return self._localization_quality

    @property
    def slam_mode(self) -> SLAMMode | None:
        self._detect_slam_mode()
        return self._slam_mode

    async def set_slam_mode(self, mode: SLAMMode):
        if mode == self._slam_mode:
            return
        await self._call_shell_script("switch_mapping_localization.sh", mode.value)

    @api_handler(default_return=b'')
    async def get_map(self, x_min: float, y_min: float, x_max: float, y_max: float):
        if not await self._map_data_fetcher.has_map():
            return b''

        ros2_map_data = self._map_data_fetcher.get_map_data()

        total_width = ros2_map_data.info.width
        total_height = ros2_map_data.info.height
        map_origin_x = ros2_map_data.info.origin.position.x
        map_origin_y = ros2_map_data.info.origin.position.y
        resolution = ros2_map_data.info.resolution
        raw_data = ros2_map_data.data

        start_x = math.floor((max(map_origin_x, x_min) - map_origin_x) / resolution)
        start_y = math.floor((max(map_origin_y, y_min) - map_origin_y) / resolution)
        requested_width = math.ceil((x_max - x_min) / resolution)
        requested_height = math.ceil((y_max - y_min) / resolution)
        width = min(total_width - start_x, requested_width - start_x)
        height = min(total_height - start_y, requested_height - start_y)

        data: bytes = b''

        row = start_y
        while row < start_y + height:
            data += raw_data[row * total_width + start_x:row * total_width + start_x + width]
            row += 1

        temp_data = np.frombuffer(data, dtype=np.int8)
        converted_data = np.zeros_like(temp_data, dtype=np.int8)

        free_mask = (temp_data == 0)
        converted_data[free_mask] = 127

        other = (temp_data != 0)
        converted_data[other] = 255 - temp_data[other]

        data = converted_data.tobytes()

        header_map_start_x = struct.pack('<f', max(x_min, map_origin_x))
        header_map_start_y = struct.pack('<f', max(y_min, map_origin_y))
        header_map_width = struct.pack('<I', width)
        header_map_height = struct.pack('<I', height)
        header_map_resolution = struct.pack('<f', resolution)
        header_placeholder = struct.pack('<f', 0.0) + struct.pack("<f", 0.0) + struct.pack("<f", 0.0)
        header_data_count = struct.pack('<I', len(data))

        return (header_map_start_x +
                header_map_start_y +
                header_map_width +
                header_map_height +
                header_map_resolution +
                header_placeholder +
                header_data_count +
                data)

    @api_handler(default_return=False)
    async def clear_map(self) -> bool:
        client = self._get_client("clear_map", ClearMap)
        if client is None:
            self._node.get_logger().error("clear_map service not found.")
            return False

        if not self._wait_for_service(client):
            return False

        await client.call_async(ClearMap.Request())

        await self._call_shell_script("clear_map.sh")

        self._map_data_fetcher.clear_map_data()

        return True

    @api_handler(default_return=None)
    async def get_known_area(self) -> RectangleArea | None:
        client = self._get_client("get_known_area", GetKnownArea)
        if client is None:
            self._node.get_logger().error("get_known_area service not found.")
            return None

        if not self._wait_for_service(client):
            return None

        response = await client.call_async(GetKnownArea.Request())

        if response is not None:
            data = response.known_area
            self._known_area = RectangleArea(x=data.x_min, y=data.y_min, width=data.width, height=data.height)
        else:
            self._node.get_logger().error("known area future is not ready.")

        return self._known_area

    async def get_stcm_file(self) -> bytes|None:
        async with self._upload_stcm_lock:
            if self._current_upload_stcm_task is not None and not self._current_upload_stcm_task.done():
                raise ConflictingCommandError("Working on uploading stcm. Please try later.")

        async with self._get_stcm_lock:
            if self._current_get_stcm_task is not None and not self._current_get_stcm_task.done():
                self._node.get_logger().info("get stcm file is already running.")
                try:
                    return await self._current_get_stcm_task
                except Exception as e:
                    self._node.get_logger().error(f"Error get stcm file: {str(e)}")
                    return None

            self._current_get_stcm_task = create_task(self._execute_get_stcm_file())

        try:
            return await self._current_get_stcm_task
        except Exception as e:
            self._node.get_logger().error(f"Error get stcm file: {str(e)}")
            return None
        finally:
            self._current_get_stcm_task = None

    async def _execute_get_stcm_file(self) -> bytes|None:
        client = self._get_client("get_stcm_file", GetStcmFile)
        if client is None:
            self._node.get_logger().error("get_stcm_file service not found.")
            return None

        if not self._wait_for_service(client):
            return None

        self._node.get_logger().info(f"get stcm file start: {self._node.get_clock().now()}")
        response = await client.call_async(GetStcmFile.Request())
        self._node.get_logger().info(f"get stcm file end: {self._node.get_clock().now()}")

        if response is not None and response.success:
            return bytes(response.file_data)
        else:
            return None

    async def upload_stcm_file(self, data: bytes) -> bool:
        async with self._get_stcm_lock:
            if self._current_get_stcm_task is not None and not self._current_get_stcm_task.done():
                raise ConflictingCommandError("Working on getting stcm. Please try later.")

        """Upload STCM file with singleton behavior - if already running, wait for current operation"""
        async with self._upload_stcm_lock:
            # If there's already a running task, wait for it and return its result
            if self._current_upload_stcm_task is not None and not self._current_upload_stcm_task.done():
                self._node.get_logger().info("upload_stcm_file already in progress, waiting for result...")
                try:
                    return await self._current_upload_stcm_task
                except Exception as e:
                    self._node.get_logger().error(f"Error waiting for existing upload_stcm_file task: {str(e)}")
                    return False

            self._current_upload_stcm_task = create_task(self._execute_upload_stcm_file(data))

        try:
            return await self._current_upload_stcm_task
        except Exception as e:
            self._node.get_logger().error(f"Error in upload_stcm_file: {str(e)}")
            return False
        finally:
            # Clean up the task reference
            self._current_upload_stcm_task = None

    async def _execute_upload_stcm_file(self, data: bytes) -> bool:
        """Internal method to execute the actual upload_stcm_file operation"""
        client = self._get_client("upload_stcm_file", UploadStcmFile)
        if client is None:
            self._node.get_logger().error("upload_stcm_file service not found.")
            return False

        if not self._wait_for_service(client):
            return False

        response = await client.call_async(UploadStcmFile.Request(file_data=data))

        return response is not None and response.success


class MapDataFetcher:

    def __init__(self, node):
        self._node = node
        self._map_data: Optional[OccupancyGrid] = None

        self._subscription = node.create_subscription(
            OccupancyGrid,
            "map",
            self._map_callback,
            1
        )

        self._lock = Lock()

        self._initialize_map_data()

    def _initialize_map_data(self):
        def future_callback(fut):
            try:
                response = fut.result()
                if response is not None:
                    with self._lock:
                        self._map_data = response.map
            except Exception as e:
                self._node.get_logger().error(f"Error initialize map: {str(e)}")

        client = self._node.create_client(GetMap, "map_server/map", qos_profile=qos_profile_services_default)

        wait_count = 0
        while not client.wait_for_service(timeout_sec=1.0):
            self._node.get_logger().info("Wait for map_server/map service")
            wait_count += 1
            if wait_count > 5:
                return
            pass

        future = client.call_async(GetMap.Request())
        future.add_done_callback(future_callback)

    def _map_callback(self, msg):
        with self._lock:
            self._map_data = msg

    def clear_map_data(self):
        with self._lock:
            self._map_data = None

    def get_map_data(self):
        with self._lock:
            return self._map_data

    async def has_map(self) -> bool:
        with self._lock:
            return self._map_data is not None

    def __del__(self):
        self._node.destroy_subscription(self._subscription)
