import math
import struct

from nav_msgs.msg import Odometry, OccupancyGrid
from nav_msgs.srv import GetMap
from rclpy.node import Node
from rclpy.timer import Timer
from stcm_manager.srv import ClearMap, GetKnownArea, GetStcmFile, UploadStcmFile
from std_msgs.msg import Int32
from threading import Lock
from typing import Optional

from .base_manager import BaseManager
from .response_models import Pose, RectangleArea
from .robot_pose_listener import RobotPoseListener
from .utils import quaternion_to_euler, SLAMMode


class SLAMManager(BaseManager):
    """Slam manager for handling SLAM related operations"""

    def __init__(self, node: Node, pose_listener: RobotPoseListener):
        super().__init__(node)

        self._laser_scan = None
        self._odometry = None
        self._localization_quality = 0
        self._slam_mode = None
        self._known_area = None

        self._pose_listener = pose_listener

        self._map_data_fetcher = MapDataFetcher(node)

        self._detect_slam_mode()

    def _subscribe_to_topics(self):
        self._subscriptions = []
        self._subscriptions.append(
            self._node.create_subscription(
                Odometry,
                "odom",
                self._odom_callback,
                10
            )
        )
        self._subscriptions.append(
            self._node.create_subscription(
                Int32,
                "localization_quality",
                self._localization_quality_callback,
                10
            )
        )

    def _odom_callback(self, msg):
        x = msg.pose.pose.position.x
        y = msg.pose.pose.position.y
        z = msg.pose.pose.position.z

        qx, qy, qz, qw = msg.pose.pose.orientation.x, msg.pose.pose.orientation.y, msg.pose.pose.orientation.z, msg.pose.pose.orientation.w
        roll, pitch, yaw = quaternion_to_euler(qx, qy, qz, qw)

        self._odometry = Pose(x=x, y=y, z=z, roll=roll, pitch=pitch, yaw=yaw)



    def _localization_quality_callback(self, msg):
        self._localization_quality = int(msg.data)

    def _detect_slam_mode(self):
        try:
            node_names = self._node.get_node_names()
            if "nav2_container" in node_names:
                self._slam_mode = SLAMMode.LOCALIZATION
            else:
                self._slam_mode = SLAMMode.MAPPING
        except:
            pass

    @property
    def pose(self) -> Pose:
        return self._pose_listener.pose

    @pose.setter
    def pose(self, pose: Pose):
        from tf2_ros import TransformBroadcaster, TransformStamped

        broadcaster = TransformBroadcaster(self._node)

        stamped = TransformStamped()

        stamped.header.stamp = self._node.get_clock().now().to_msg()
        stamped.header.frame_id = "map"
        stamped.child_frame_id = "base_link"

        stamped.transform.translation.x = pose.x
        stamped.transform.translation.y = pose.y
        stamped.transform.translation.z = 0.0

        half_yaw = pose.yaw / 2.0
        stamped.transform.rotation.x = 0.0
        stamped.transform.rotation.y = 0.0
        stamped.transform.rotation.z = math.sin(half_yaw)
        stamped.transform.rotation.w = math.cos(half_yaw)

        broadcaster.sendTransform(stamped)

    @property
    def odometry(self) -> Pose | None:
        return self._odometry

    @property
    def localization_quality(self) -> int:
        return self._localization_quality

    @property
    def slam_mode(self) -> SLAMMode | None:
        self._detect_slam_mode()
        return self._slam_mode

    async def set_slam_mode(self, mode: SLAMMode):
        if mode == self._slam_mode:
            return
        await self._call_shell_script("switch_mapping_localization.sh", mode.value)

    def get_map(self, x_min: float, y_min: float, x_max: float, y_max: float) -> bytes:
        if not self._map_data_fetcher.has_map:
            return b''

        ros2_map_data = self._map_data_fetcher.map_data

        total_width = ros2_map_data.info.width
        total_height = ros2_map_data.info.height
        map_origin_x = ros2_map_data.info.origin.position.x
        map_origin_y = ros2_map_data.info.origin.position.y
        resolution = ros2_map_data.info.resolution
        raw_data = ros2_map_data.data

        start_x = math.floor((max(map_origin_x, x_min) - map_origin_x) / resolution)
        start_y = math.floor((max(map_origin_y, y_min) - map_origin_y) / resolution)
        requested_width = math.ceil((x_max - x_min) / resolution)
        requested_height = math.ceil((y_max - y_min) / resolution)
        width = min(total_width - start_x, requested_width - start_x)
        height = min(total_height - start_y, requested_height - start_y)

        data: bytes = b''

        row = start_y
        while row < start_y + height:
            data += raw_data[row * total_width + start_x:row * total_width + start_x + width]
            row += 1

        header_map_start_x = struct.pack('<f', max(x_min, map_origin_x))
        header_map_start_y = struct.pack('<f', max(y_min, map_origin_y))
        header_map_width = struct.pack('<I', width)
        header_map_height = struct.pack('<I', height)
        header_map_resolution = struct.pack('<f', resolution)
        header_placeholder = struct.pack('<f', 0.0) + struct.pack("<f", 0.0) + struct.pack("<f", 0.0)
        header_data_count = struct.pack('<I', len(data))

        return (header_map_start_x +
                header_map_start_y +
                header_map_width +
                header_map_height +
                header_map_resolution +
                header_placeholder +
                header_data_count +
                data)


    async def clear_map(self):
        client = self._node.create_client(ClearMap, "clear_map")

        while not client.wait_for_service(timeout_sec=1.0):
            pass

        await client.call_async(ClearMap.Request())

        await self._call_shell_script("clear_map.sh")

    async def get_known_area(self) -> RectangleArea | None:
        client = self._node.create_client(GetKnownArea, "get_known_area")

        while not client.wait_for_service(timeout_sec=1.0):
            pass

        response = await client.call_async(GetKnownArea.Request())

        if response is not None:
            data = response.known_area
            self._known_area = RectangleArea(x=data.x_min, y=data.y_min, width=data.width, height=data.height)
        else:
            self._node.get_logger().error("known area future is not ready.")

        return self._known_area

    async def get_stcm_file(self) -> bytes|None:
        client = self._node.create_client(GetStcmFile, "get_stcm_file")

        while not client.wait_for_service(timeout_sec=1.0):
            pass

        response = await client.call_async(GetStcmFile.Request())

        if response is not None and response.success:
            return bytes(response.file_data)
        else:
            return None

    async def upload_stcm_file(self, data: bytes) -> bool:
        client = self._node.create_client(UploadStcmFile, "upload_stcm_file")

        while not client.wait_for_service(timeout_sec=1.0):
            pass

        response = await client.call_async(UploadStcmFile.Request(file_data=data))

        return response is not None and response.success


class MapDataFetcher:

    def __init__(self, node):
        self._node = node
        self._client = node.create_client(GetMap, "map_server/map")

        self._map_data: Optional[OccupancyGrid] = None

        self._timer: Optional[Timer] = None
        self._lock = Lock()
        self._is_requesting = False

        self.start_periodic_updates()

    def start_periodic_updates(self):
        if self._timer is not None:
            self._timer.cancel()

        self._timer = self._node.create_timer(0.5, self._request_map_callback)

    def stop_periodic_updates(self):
        if self._timer is not None:
            self._timer.cancel()
            self._timer = None

    def _request_map_callback(self):
        if self._is_requesting:
            return

        if not self._client.wait_for_service(timeout_sec=0.1):
            return

        self._is_requesting = True
        future = self._client.call_async(GetMap.Request())
        future.add_done_callback(self._map_response_callback)

    def _map_response_callback(self, future):
        try:
            response = future.result()
            if response is not None:
                with self._lock:
                    self._map_data = response.map
        except Exception as e:
            self._node.get_logger().error(f"Error get map: {str(e)}")
        finally:
            self._is_requesting = False

    @property
    def map_data(self):
        with self._lock:
            return self._map_data

    @property
    def has_map(self) -> bool:
        return self._map_data is not None

    def __del__(self):
        self.stop_periodic_updates()
