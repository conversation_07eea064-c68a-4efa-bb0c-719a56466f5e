import math
import numpy as np
import struct

from asyncio import Lock as AsLock, Task, create_task
from geometry_msgs.msg import PoseWithCovarianceStamped
from interfaces.srv import GetSLAMMode, SetSLAMMode
from nav_msgs.msg import Odometry, OccupancyGrid
from rclpy.node import Node
from stcm_manager.srv import ClearMap, GetKnownArea, GetStcmFile, UploadStcmFile, SaveMap
from std_msgs.msg import Int32
from typing import Optional

from .agent_exceptions import ConflictingCommandError, ROSServiceNoResponseError
from .base_manager import BaseManager
from .home_dock_manager import HomeDockManager
from .models import Pose as APIPose, RectangleArea, SLAMMode
from .robot_pose_listener import RobotPoseListener
from .utils import quaternion_to_euler, euler_to_quaternion, api_handler


class SLAMManager(BaseManager):
    """Slam manager for handling SLAM related operations"""

    def __init__(self, node: <PERSON>de, pose_listener: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, home_dock_manager: HomeDockManager):
        super().__init__(node)

        self._home_dock_manager = home_dock_manager

        self._odometry_msg: Optional[Odometry] = None
        self._localization_quality = 0
        self._slam_mode: SLAMMode|None = None

        self._pose_listener = pose_listener

        self._map_data_fetcher = MapDataFetcher(node)

        self._get_stcm_lock = AsLock()
        self._upload_stcm_lock = AsLock()
        self._current_get_stcm_task: Optional[Task] = None
        self._current_upload_stcm_task: Optional[Task] = None

        # self._detect_slam_mode()

    def _subscribe_to_topics(self):
        self._subscriptions.append(
            self._node.create_subscription(
                Odometry,
                "odom",
                self._odom_callback,
                1
            )
        )
        self._subscriptions.append(
            self._node.create_subscription(
                Int32,
                "localization_quality",
                self._localization_quality_callback,
                1
            )
        )

    def _create_clients(self):
        self._create_client("clear_map", ClearMap)
        self._create_client("get_known_area", GetKnownArea)
        self._create_client("get_stcm_file", GetStcmFile)
        self._create_client("upload_stcm_file", UploadStcmFile)
        self._create_client("save_map", SaveMap)

        self._create_client("get_slam_mode", GetSLAMMode)
        self._create_client("set_slam_mode", SetSLAMMode)

    def _odom_callback(self, msg):
        self._odometry_msg = msg

    def _localization_quality_callback(self, msg):
        if isinstance(msg, Int32):
            self._localization_quality = int(msg.data)

    @api_handler(default_return=None)
    async def get_pose(self):
        return self._pose_listener.get_pose()

    async def set_pose(self, pose: APIPose):
        stamped = PoseWithCovarianceStamped()
        stamped.header.frame_id = "map"
        stamped.header.stamp = self._node.get_clock().now().to_msg()

        stamped.pose.pose.position.x = pose.x
        stamped.pose.pose.position.y = pose.y
        stamped.pose.pose.position.z = pose.z

        stamped.pose.pose.orientation.x, stamped.pose.pose.orientation.y, stamped.pose.pose.orientation.z, stamped.pose.pose.orientation.w = euler_to_quaternion(pose.roll, pose.pitch, pose.yaw)

        stamped.pose.covariance[0] = 0.5 ** 2
        stamped.pose.covariance[7] = 0.5 ** 2
        stamped.pose.covariance[35] = (3.141592653589793 / 12.0) ** 2

        publisher = self._node.create_publisher(PoseWithCovarianceStamped, "initialpose", 10)

        publisher.publish(stamped)

    @api_handler(default_return=None)
    async def get_odometry(self) -> APIPose| None:
        msg: Optional[Odometry] = self._odometry_msg

        if msg is None:
            return None

        x = msg.pose.pose.position.x
        y = msg.pose.pose.position.y
        z = msg.pose.pose.position.z

        qx, qy, qz, qw = msg.pose.pose.orientation.x, msg.pose.pose.orientation.y, msg.pose.pose.orientation.z, msg.pose.pose.orientation.w
        roll, pitch, yaw = quaternion_to_euler(qx, qy, qz, qw)
        return APIPose(x=x, y=y, z=z, roll=roll, pitch=pitch, yaw=yaw)

    @api_handler(default_return=0)
    async def get_localization_quality(self) -> int:
        return self._localization_quality

    @api_handler(default_return=None)
    async def get_slam_mode(self) -> SLAMMode | None:
        client = self._get_client("get_slam_mode", GetSLAMMode)
        if client is None:
            raise ROSServiceNoResponseError("get_slam_mode")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("get_slam_mode")

        response = await client.call_async(GetSLAMMode.Request())

        if response is not None and response.success:
            return SLAMMode.parse_slam_mode(response.mode)
        else:
            return None

    @api_handler(default_return=False)
    async def set_slam_mode(self, mode: SLAMMode, reload_map: bool) -> bool:
        current_mode = await self.get_slam_mode()
        if current_mode == mode:
            return True

        client = self._get_client("set_slam_mode", SetSLAMMode)
        if client is None:
            raise ROSServiceNoResponseError("set_slam_mode")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("set_slam_mode")

        response = await client.call_async(SetSLAMMode.Request(mode=mode.value, reload_map=reload_map))

        if response is None or not response.success:
            return False

        return True

    @api_handler(default_return=False)
    async def reset_localization(self) -> bool:
        # publisher = self._node.create_publisher(msg_type=Empty, topic="/global_relocate", qos_profile=10)
        # publisher.publish(Empty())
        return False

    @api_handler(default_return=b'')
    async def get_map(self, x_min: float, y_min: float, x_max: float, y_max: float):
        ros2_map_data = self._map_data_fetcher.get_map_data()

        if ros2_map_data is None:
            return b''

        total_width = ros2_map_data.info.width
        total_height = ros2_map_data.info.height
        map_origin_x = ros2_map_data.info.origin.position.x
        map_origin_y = ros2_map_data.info.origin.position.y
        resolution = ros2_map_data.info.resolution
        raw_data = ros2_map_data.data

        start_x = math.floor((max(map_origin_x, x_min) - map_origin_x) / resolution)
        start_y = math.floor((max(map_origin_y, y_min) - map_origin_y) / resolution)
        requested_width = math.ceil((x_max - x_min) / resolution)
        requested_height = math.ceil((y_max - y_min) / resolution)
        width = min(total_width - start_x, requested_width - start_x)
        height = min(total_height - start_y, requested_height - start_y)

        data: bytes = b''

        row = start_y
        while row < start_y + height:
            data += raw_data[row * total_width + start_x:row * total_width + start_x + width]
            row += 1

        temp_data = np.frombuffer(data, dtype=np.int8)
        converted_data = np.zeros_like(temp_data, dtype=np.int8)

        free_mask = (temp_data == 0)
        converted_data[free_mask] = 127

        other = (temp_data != 0)
        converted_data[other] = 255 - temp_data[other]

        data = converted_data.tobytes()

        header_map_start_x = struct.pack('<f', max(x_min, map_origin_x))
        header_map_start_y = struct.pack('<f', max(y_min, map_origin_y))
        header_map_width = struct.pack('<I', width)
        header_map_height = struct.pack('<I', height)
        header_map_resolution = struct.pack('<f', resolution)
        header_placeholder = struct.pack('<f', 0.0) + struct.pack("<f", 0.0) + struct.pack("<f", 0.0)
        header_data_count = struct.pack('<I', len(data))

        return (header_map_start_x +
                header_map_start_y +
                header_map_width +
                header_map_height +
                header_map_resolution +
                header_placeholder +
                header_data_count +
                data)

    @api_handler(default_return=False)
    async def clear_map(self) -> bool:
        client = self._get_client("clear_map", ClearMap)
        if client is None:
            raise ROSServiceNoResponseError("clear_map")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("clear_map")

        response = await client.call_async(ClearMap.Request())

        if response is None or not response.success:
            return False


        self._map_data_fetcher.clear_map_data()
        self._home_dock_manager.clear_home_dock()

        return True

    @api_handler(default_return=None)
    async def get_known_area(self) -> RectangleArea | None:
        client = self._get_client("get_known_area", GetKnownArea)
        if client is None:
            raise ROSServiceNoResponseError("get_known_area")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("get_known_area")

        response = await client.call_async(GetKnownArea.Request())

        if response is not None:
            data = response.known_area
            return RectangleArea(x=data.x_min, y=data.y_min, width=data.width, height=data.height)
        else:
            return None

    async def get_stcm_file(self, lite_version: bool) -> bytes|None:
        async with self._upload_stcm_lock:
            if self._current_upload_stcm_task is not None and not self._current_upload_stcm_task.done():
                raise ConflictingCommandError("Working on uploading stcm. Please try later.")

        async with self._get_stcm_lock:
            if self._current_get_stcm_task is not None and not self._current_get_stcm_task.done():
                self._node.get_logger().info("get stcm file is already running.")
                try:
                    return await self._current_get_stcm_task
                except Exception as e:
                    self._node.get_logger().error(f"Error get stcm file: {e}")
                    return None

            self._current_get_stcm_task = create_task(self._execute_get_stcm_file(lite_version=lite_version))

        try:
            return await self._current_get_stcm_task
        except Exception as e:
            self._node.get_logger().error(f"Error get stcm file: {e}")
            return None
        finally:
            self._current_get_stcm_task = None

    async def _execute_get_stcm_file(self, lite_version: bool) -> bytes|None:
        client = self._get_client("get_stcm_file", GetStcmFile)
        if client is None:
            raise ROSServiceNoResponseError("get_stcm_file")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("get_stcm_file")

        response = await client.call_async(GetStcmFile.Request(require_lite_version=lite_version))

        if response is not None and response.success:
            return bytes(response.file_data)
        else:
            return None

    async def upload_stcm_file(self, data: bytes) -> bool:      # pyrefly: ignore[bad-return]
        async with self._get_stcm_lock:
            if self._current_get_stcm_task is not None and not self._current_get_stcm_task.done():
                raise ConflictingCommandError("Working on getting stcm. Please try later.")

        """Upload STCM file with singleton behavior - if already running, wait for current operation"""
        async with self._upload_stcm_lock:
            # If there's already a running task, wait for it and return its result
            if self._current_upload_stcm_task is not None and not self._current_upload_stcm_task.done():
                self._node.get_logger().info("upload_stcm_file already in progress, waiting for result...")
                try:
                    return await self._current_upload_stcm_task
                except Exception as e:
                    self._node.get_logger().error(f"Error waiting for existing upload_stcm_file task: {e}")
                    return False

            self._current_upload_stcm_task = create_task(self._execute_upload_stcm_file(data))

        try:
            return await self._current_upload_stcm_task
        except Exception as e:
            self._node.get_logger().error(f"Error in upload_stcm_file: {e}")
            return False
        finally:
            self._map_data_fetcher.clear_map_data()
            self._home_dock_manager.clear_home_dock()
            # Clean up the task reference
            self._current_upload_stcm_task = None

    async def _execute_upload_stcm_file(self, data: bytes) -> bool:
        """Internal method to execute the actual upload_stcm_file operation"""
        client = self._get_client("upload_stcm_file", UploadStcmFile)
        if client is None:
            raise ROSServiceNoResponseError("upload_stcm_file")

        if not self._wait_for_service(client):
            raise ROSServiceNoResponseError("upload_stcm_file")

        response = await client.call_async(UploadStcmFile.Request(file_data=data))

        return response is not None and response.success


class MapDataFetcher:

    def __init__(self, node):
        self._node = node
        self._map_data: OccupancyGrid|None = None

        self._subscription = node.create_subscription(
            OccupancyGrid,
            "map",
            self._map_callback,
            1
        )

    def _map_callback(self, msg):
        self._map_data = msg

    def clear_map_data(self):
        self._map_data = None

    def get_map_data(self) -> OccupancyGrid|None:
        return self._map_data

    def has_map(self) -> bool:
        return self._map_data is not None

    def __del__(self):
        self._node.destroy_subscription(self._subscription)
