import asyncio
import math
import rclpy

from action_msgs.msg import <PERSON><PERSON>tat<PERSON>
from geometry_msgs.msg import Twist, PoseStamped
from interfaces.msg import ShelfInfo
from nav2_msgs.action import NavigateToPose
from nav_msgs.msg import Path
from opennav_docking_msgs.action import DockRobot
from rclpy.action import ActionClient
from rclpy.node import Node
from rclpy.task import Future
from typing import List, Dict, Optional

from .base_manager import BaseManager
from .models import ActionRequest, GoHomeActionOptions, MotionAction, MotionActionState, MoveDirection, ActionDefinition, MoveToTagActionOptions
from .robot_pose_listener import RobotPoseListener
from .utils import euler_to_quaternion, DefaultQoSProfile, api_handler


class MotionManager(BaseManager):

    def __init__(self, node: Node, pose_listener: RobotPoseListener):
        super().__init__(node)

        self._current_action: MotionAction|None = None
        self._goal_request_future: Future | None = None
        self._goal_response_future: Future | None = None
        self._goal_handle = None
        self._action_history: Dict[int, MotionAction] = dict()

        self._path_msg: Optional[Path] = None
        self._milestones: List[List[float]] = []

        self._pose_listener = pose_listener

        self._created_action_id = 0
        self._action_publisher = None

        self._action_client = ActionClient(self._node, NavigateToPose, "navigate_to_pose")
        self._dock_client = ActionClient(self._node, DockRobot, "dock_robot")
        self._manual_control_manager = ManualControlManager(node)

    def __del__(self):
        self._action_client.destroy()

    def _subscribe_to_topics(self):
        self._subscriptions.append(
            self._node.create_subscription(
                Path,
                "plan",
                self._path_callback,
                qos_profile=DefaultQoSProfile
            )
        )

    def _create_clients(self):
        pass

    def _path_callback(self, msg):
        self._path_msg = msg

    @property
    def current_action(self):
        return self._current_action

    @api_handler(default_return=[])
    async def get_path(self):
        msg: Optional[Path] = self._path_msg

        if msg is None:
            return []

        if msg.poses is None or len(msg.poses) == 0:
            self._milestones = []
            return []

        path: List[List[float]] = []
        milestones: List[List[float]] = []

        current_milestone_pose = msg.poses[0]

        for index, pose in enumerate(msg.poses):
            pt = [pose.pose.position.x, pose.pose.position.y]
            path.append(pt)

            if index + 1 == len(msg.poses):
                milestones.append(pt)
                break

            # calculate the distance between current_milestone_pose and pose
            dist = math.sqrt((current_milestone_pose.pose.position.x - pose.pose.position.x)**2 + (current_milestone_pose.pose.position.y - pose.pose.position.y)**2)
            if dist >= 1:
                current_milestone_pose = pose
                milestones.append(pt)

        self._milestones = milestones
        return path

    @api_handler(default_return=[])
    async def get_milestones(self):
        return self._milestones

    def get_action_by_id(self, action_id: int) -> MotionAction | None:
        if action_id in self._action_history:
            return self._action_history[action_id]
        return None

    def stop_current_action(self):
        self._manual_control_manager.stop()
        if self._goal_handle is not None:
            self._goal_handle.cancel_goal_async()
        if self._goal_request_future is not None and not self._goal_request_future.done():
            self._goal_request_future.cancel()
        if self._goal_response_future is not None and not self._goal_response_future.done():
            self._goal_response_future.cancel()
        self._goal_request_future = None
        self._goal_response_future = None
        self._goal_handle = None
        self._current_action = None

        self._path_msg = None
        self._milestones.clear()

    async def create_action(self, action: ActionRequest):
        action_define = action.action_type
        action_options = action.get_parsed_options()

        if action_define == ActionDefinition.MOVE_BY and action_options is not None:
            direction = None
            theta = None
            if action_options.direction is not None:
                direction = MoveDirection.parse_move_direction(action_options.direction)
            elif direction is None and action_options.theta is not None:
                theta = action_options.theta
            duration = action_options.duration
            return self._create_move_by_action(direction, theta, duration)
        elif action_define == ActionDefinition.MOVE_TO and action_options is not None:
            yaw = 0.0
            if action_options.move_options is not None and action_options.move_options.yaw is not None:
                yaw = action_options.move_options.yaw
            return self._create_move_to_action(action_options.target.x, action_options.target.y, yaw)
        elif action_define == ActionDefinition.ROTATE and action_options is not None:
            return await self._create_rotate_action(math.radians(action_options.angle))
        elif action_define == ActionDefinition.ROTATE_TO and action_options is not None:
            return self._create_rotate_to_action(math.radians(action_options.angle))
        elif action_define == ActionDefinition.GO_HOME and action_options is not None:
            return self._create_go_home_action(action_options)
        elif action_define == ActionDefinition.MOVE_TO_TAG and action_options is not None:
            return self._create_move_to_tag_action(action_options)
        else:
            return None

    def _create_go_home_action(self, options: GoHomeActionOptions):
        self.stop_current_action()

        goal = DockRobot.Goal()
        goal.use_dock_id = False
        goal.dock_type = "simple_charging_dock"

        pose = PoseStamped()
        pose.header.stamp = self._node.get_clock().now().to_msg()
        pose.header.frame_id = "map"
        pose.pose.position.x = options.dock_pose.x
        pose.pose.position.y = options.dock_pose.y
        pose.pose.position.z = options.dock_pose.z
        pose.pose.orientation.x, pose.pose.orientation.y, pose.pose.orientation.z, pose.pose.orientation.w = euler_to_quaternion(options.dock_pose.roll, options.dock_pose.pitch, options.dock_pose.yaw)
        goal.dock_pose = pose

        count = 0
        while not self._dock_client.wait_for_server(timeout_sec=0.1):
            count += 1
            if count > 5:
                self._node.get_logger().info(f"no action client")
                return None

        self._goal_request_future = self._dock_client.send_goal_async(goal)
        self._goal_request_future.add_done_callback(self._action_response_callback)

        return self._create_motion_action_response(ActionDefinition.GO_HOME)

    def _create_move_to_tag_action(self, options: MoveToTagActionOptions):
        self.stop_current_action()

        if options.tag_type == 3:
            if options.shelves is None or len(options.shelves) == 0:
                return None

            goal = DockRobot.Goal()
            goal.use_dock_id = False
            goal.dock_type = "shelf_dock"
            if options.dock_allowance is not None:
                goal.dock_allowance = options.dock_allowance

            pose = PoseStamped()
            pose.header.stamp = self._node.get_clock().now().to_msg()
            pose.header.frame_id = "map"
            pose.pose.position.x = options.dock_pose.x
            pose.pose.position.y = options.dock_pose.y
            pose.pose.position.z = options.dock_pose.z
            pose.pose.orientation.x, pose.pose.orientation.y, pose.pose.orientation.z, pose.pose.orientation.w = euler_to_quaternion(options.dock_pose.roll, options.dock_pose.pitch, options.dock_pose.yaw)
            goal.landing_pose = pose

            shelves: List[ShelfInfo] = []
            for info in options.shelves:
                shelf = ShelfInfo()
                shelf.shelf_columnar_length = info.shelf_columnar_length
                shelf.shelf_columnar_width = info.shelf_columnar_width
                shelf.shelf_columnar_diameter = info.shelf_columnar_diameter
                shelf.shelf_length_retraction = info.shelf_length_retraction
                shelves.append(shelf)

            goal.shelves = shelves

            count = 0
            while not self._dock_client.wait_for_server(timeout_sec=0.1):
                count += 1
                if count > 5:
                    return None

            self._goal_request_future = self._dock_client.send_goal_async(goal)
            self._goal_request_future.add_done_callback(self._action_response_callback)
        else:
            return None


        return self._create_motion_action_response(ActionDefinition.MOVE_TO_TAG)

    def _create_move_to_action(self, x: float, y: float, yaw: float=0.0):
        if not self._create_navigate_to_pose_action(x, y, yaw):
            return None

        return self._create_motion_action_response(ActionDefinition.MOVE_TO)

    async def _create_rotate_action(self, yaw: float):
        self.stop_current_action()

        current_pose = await self._pose_listener.get_pose()

        target_yaw = current_pose.yaw + yaw
        while target_yaw > math.pi:
            target_yaw -= 2 * math.pi
        while target_yaw < -math.pi:
            target_yaw += 2 * math.pi

        if not self._create_navigate_to_pose_action(current_pose.x, current_pose.y, target_yaw):
            return None

        return self._create_motion_action_response(ActionDefinition.ROTATE)

    async def _create_rotate_to_action(self, yaw: float):
        self.stop_current_action()

        current_pose = await self._pose_listener.get_pose()

        target_yaw = yaw
        while target_yaw > math.pi:
            target_yaw -= 2 * math.pi
        while target_yaw < -math.pi:
            target_yaw += 2 * math.pi

        if not self._create_navigate_to_pose_action(current_pose.x, current_pose.y, target_yaw):
            return None

        return self._create_motion_action_response(ActionDefinition.ROTATE_TO)

    def _create_move_by_action(self, direction: MoveDirection|None=None, theta: float|None=None, duration:int|None=None):
        self.stop_current_action()

        if duration is None:
            duration = 500

        linear_speed = 1.0 * duration / 1000.0
        angular_speed = 1.0 * duration / 1000.0

        x = 0.0
        z = 0.0

        if direction is not None:
            match direction:
                case MoveDirection.FORWARD:
                    x = linear_speed
                case MoveDirection.BACKWARD:
                    x = -linear_speed
                case MoveDirection.TURN_LEFT:
                    z = angular_speed
                case MoveDirection.TURN_RIGHT:
                    z = -angular_speed
                case _:
                    return None
        elif theta is not None:
            z = theta
        else:
            return None

        self._manual_control_manager.start_twist(x, z, duration)

        return self._create_motion_action_response(ActionDefinition.MOVE_BY)

    def _create_motion_action_response(self, action: ActionDefinition) -> MotionAction:
        self._created_action_id += 1

        self._current_action = MotionAction(
            action_id=self._created_action_id,
            action_name=action.value,
            stage="running",
            state=MotionActionState(status=0, result=0, reason="")
        )

        return self._current_action

    def _create_navigate_to_pose_action(self, x: float, y: float, yaw: float) -> bool:
        self.stop_current_action()

        goal_msg = NavigateToPose.Goal()

        goal_msg.pose.header.frame_id = "map"
        goal_msg.pose.header.stamp = self._node.get_clock().now().to_msg()
        goal_msg.pose.pose.position.x = x
        goal_msg.pose.pose.position.y = y
        goal_msg.pose.pose.position.z = 0.0

        half_yaw = yaw / 2.0
        goal_msg.pose.pose.orientation.x = 0.0
        goal_msg.pose.pose.orientation.y = 0.0
        goal_msg.pose.pose.orientation.z = math.sin(half_yaw)
        goal_msg.pose.pose.orientation.w = math.cos(half_yaw)

        count = 0
        while not self._action_client.wait_for_server(timeout_sec=0.1):
            count += 1
            if count > 5:
                return False

        self._goal_request_future = self._action_client.send_goal_async(goal_msg)

        self._goal_request_future.add_done_callback(self._action_response_callback)

        return True

    def _action_response_callback(self, future: Future):
        if future != self._goal_request_future:
            return

        goal_handle = future.result()
        if not goal_handle.accepted:
            self._node.get_logger().error("Action request was rejected.")
            self._current_action.stage = "failed"
            self._current_action.state.status = 4
            self._current_action.state.result = -1
            self._current_action.state.reason = "Action has been rejected."
            self._action_history[self._current_action.action_id] = self._current_action
            self._current_action = None
            return

        if self._current_action is not None:
            self._current_action.stage = "working"
            self._current_action.state.status = 1
            self._current_action.state.result = 0
            self._current_action.state.reason = ""

        self._goal_handle = goal_handle
        self._goal_request_future = None
        self._goal_response_future = goal_handle.get_result_async()
        self._goal_response_future.add_done_callback(self._action_result_callback)

    def _action_result_callback(self, future: Future):
        if self._goal_response_future != future:
            return

        result = future.result()

        if result.status == GoalStatus.STATUS_SUCCEEDED:
            if self._current_action is not None:
                self._current_action.stage = "finished"
                self._current_action.state.status = 4
                self._current_action.state.result = 0
        elif result.status == GoalStatus.STATUS_ABORTED:
            if self._current_action is not None:
                self._current_action.stage = "aborted"
                self._current_action.state.status = 4
                self._current_action.state.result = -2
        elif result.status == GoalStatus.STATUS_CANCELED:
            if self._current_action is not None:
                self._current_action.stage = "canceled"
                self._current_action.state.status = 4
                self._current_action.state.result = -2
        elif result.status == GoalStatus.STATUS_CANCELING:
            if self._current_action is not None:
                self._current_action.stage = "canceling"
                self._current_action.state.status = 4
                self._current_action.state.result = -2
        elif result.status == GoalStatus.STATUS_UNKNOWN or result.status == GoalStatus.STATUS_ACCEPTED or result.status == GoalStatus.STATUS_EXECUTING:
            return

        self._goal_response_future = None
        if self._current_action is not None:
            self._action_history[self._current_action.action_id] = self._current_action
            while len(self._action_history) > 20:
                self._action_history.popitem()

            self._current_action = None
            self._goal_handle = None


class ManualControlManager:

    def __init__(self, node: Node):
        self._cmd_publisher = node.create_publisher(Twist, "/cmd_vel", 10)

        self._async_task = None

    def __del__(self):
        self.stop()
        self._cmd_publisher.destroy()

    def _create_twist_timeout(self, duration):
        async def twist_timeout():
            await asyncio.sleep(duration / 1000.0)
            self.stop()
        return twist_timeout()

    def stop(self):
        if rclpy.utilities.ok():
            self._cmd_publisher.publish(Twist())

    def start_twist(self, x: float, z: float, duration: float):
        if self._async_task is not None and not self._async_task.done():
            self._async_task.cancel()

        twist = Twist()

        twist.linear.x = x
        twist.angular.z = z

        self._cmd_publisher.publish(twist)

        self._async_task = asyncio.create_task(self._create_twist_timeout(duration))
