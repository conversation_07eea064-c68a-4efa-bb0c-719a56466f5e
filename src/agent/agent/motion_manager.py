import asyncio
import math

from geometry_msgs.msg import Twist
from nav2_msgs.action import NavigateToPose
from nav_msgs.msg import Path
from rclpy.action import ActionClient
from rclpy.node import Node
from rclpy.task import Future
from typing import List

from .base_manager import BaseManager
from .request_models import ActionRequest
from .response_models import MotionAction, MotionActionState
from .robot_pose_listener import RobotPoseListener
from .utils import MoveDirection, ActionDefinition


class MotionManager(BaseManager):

    def __init__(self, node: Node, pose_listener: RobotPoseListener):
        super().__init__(node)

        self._current_action = None
        self._action_future = None

        self._path: List[List[float]] = []
        self._milestones: List[List[float]] = []

        self._pose_listener = pose_listener

        self._created_action_id = 0
        self._action_publisher = None

        self._action_client = ActionClient(self._node, NavigateToPose, "navigate_to_pose")
        self._manual_control_manager = ManualControlManager(node)

    def _subscribe_to_topics(self):
        self._subscriptions = []
        self._subscriptions.append(
            self._node.create_subscription(
                Path,
                "plan",
                self._path_callback,
                10
            )
        )

    def _path_callback(self, msg):
        if msg.poses is None or len(msg.poses) == 0:
            self._path = []
            self._milestones = []
            return

        path: List[List[float]] = []
        milestones: List[List[float]] = []

        current_milestone_pose = msg.poses[0]

        for index, pose in enumerate(msg.poses):
            pt = [pose.pose.position.x, pose.pose.position.y]
            path.append(pt)

            if index + 1 == len(msg.poses):
                milestones.append(pt)
                break

            # calculate the distance between current_milestone_pose and pose
            dist = math.sqrt((current_milestone_pose.pose.position.x - pose.pose.position.x)**2 + (current_milestone_pose.pose.position.y - pose.pose.position.y)**2)
            if dist >= 1:
                current_milestone_pose = pose
                milestones.append(pt)

        self._path = path
        self._milestones = milestones

    @property
    def current_action(self):
        return self._current_action

    @property
    def path(self):
        return self._path

    @property
    def milestones(self):
        return self._milestones

    def stop_current_action(self):
        if self._action_future is not None and not self._action_future.done():
            self._action_future.cancel()

        self._manual_control_manager.stop()

    def create_action(self, action: ActionRequest):
        action_define = action.action_type
        action_options = action.get_parsed_options()

        if action_define == ActionDefinition.MOVE_BY and action_options is not None:
            direction = None
            theta = None
            if action_options.direction is not None:
                direction = MoveDirection.parse_move_direction(action_options.direction)
            elif direction is None and action_options.theta is not None:
                theta = action_options.theta
            duration = action_options.duration
            return self._create_move_by_action(direction, theta, duration)
        elif action_define == ActionDefinition.MOVE_TO and action_options is not None:
            yaw = 0.0
            if action_options.move_options is not None and action_options.move_options.yaw is not None:
                yaw = action_options.move_options.yaw
            return self._create_move_to_action(action_options.target.x, action_options.target.y, yaw)
        elif action_define == ActionDefinition.ROTATE and action_options is not None:
            return self._create_rotate_action(math.radians(action_options.angle))
        elif action_define == ActionDefinition.ROTATE_TO and action_options is not None:
            return self._create_rotate_to_action(math.radians(action_options.angle))
        else:
            return None

    def _create_move_to_action(self, x: float, y: float, yaw: float=0.0):
        self._create_navigate_to_pose_action(x, y, yaw)

        return self._create_motion_action_response(ActionDefinition.MOVE_TO)

    def _create_rotate_action(self, yaw: float):
        current_pose = self._pose_listener.pose

        target_yaw = current_pose.yaw + yaw
        while target_yaw > math.pi:
            target_yaw -= 2 * math.pi
        while target_yaw < -math.pi:
            target_yaw += 2 * math.pi

        self._create_navigate_to_pose_action(current_pose.x, current_pose.y, target_yaw)

        return self._create_motion_action_response(ActionDefinition.ROTATE)

    def _create_rotate_to_action(self, yaw: float):
        current_pose = self._pose_listener.pose

        target_yaw = yaw
        while target_yaw > math.pi:
            target_yaw -= 2 * math.pi
        while target_yaw < -math.pi:
            target_yaw += 2 * math.pi

        self._create_navigate_to_pose_action(current_pose.x, current_pose.y, target_yaw)

        return self._create_motion_action_response(ActionDefinition.ROTATE_TO)

    def _create_move_by_action(self, direction: MoveDirection|None=None, theta: float|None=None, duration:int|None=None):
        if self._action_future is not None and not self._action_future.done():
            self._action_future.cancel()

        if duration is None:
            duration = 500

        linear_speed = 1.0 * duration / 1000.0
        angular_speed = 1.0 * duration / 1000.0

        x = 0.0
        z = 0.0

        if direction is not None:
            match direction:
                case MoveDirection.FORWARD:
                    x = linear_speed
                case MoveDirection.BACKWARD:
                    x = -linear_speed
                case MoveDirection.TURN_LEFT:
                    z = angular_speed
                case MoveDirection.TURN_RIGHT:
                    z = -angular_speed
                case _:
                    return None
        elif theta is not None:
            z = theta
        else:
            return None

        self._manual_control_manager.start_twist(x, z, duration)

        return self._create_motion_action_response(ActionDefinition.MOVE_BY)

    def _create_motion_action_response(self, action: ActionDefinition) -> MotionAction:
        self._created_action_id += 1

        self._current_action = MotionAction(
            action_id=self._created_action_id,
            action_name=action.value,
            stage="running",
            state=MotionActionState(status=0, result=0, reason="")
        )

        return self._current_action

    def _create_navigate_to_pose_action(self, x: float, y: float, yaw: float):
        if self._action_future is not None and not self._action_future.done():
            self._action_future.cancel()

        goal_msg = NavigateToPose.Goal()

        goal_msg.pose.header.frame_id = "map"
        goal_msg.pose.header.stamp = self._node.get_clock().now().to_msg()
        goal_msg.pose.pose.position.x = x
        goal_msg.pose.pose.position.y = y
        goal_msg.pose.pose.position.z = 0.0

        half_yaw = yaw / 2.0
        goal_msg.pose.pose.orientation.x = 0.0
        goal_msg.pose.pose.orientation.y = 0.0
        goal_msg.pose.pose.orientation.z = math.sin(half_yaw)
        goal_msg.pose.pose.orientation.w = math.cos(half_yaw)

        self._action_client.wait_for_server()

        self._action_future = self._action_client.send_goal_async(goal_msg)

        def callback(future: Future):
            if future != self._action_future:
                return
            if self._current_action is not None:
                exception = future.exception()
                if future.done():
                    if exception is not None:
                        self._current_action.stage = "failed"
                        self._current_action.state.status = 4
                        self._current_action.state.result = -1
                        self._current_action.reason = str(exception)
                    else:
                        self._current_action.stage = "finished"
                        self._current_action.state.status = 4
                        self._current_action.state.result = 0
                elif future.cancelled():
                    self._current_action.stage = "cancelled"
                    self._current_action.state.status = 4
                    self._current_action.state.result = -2
                    self._current_action.reason = "Action has been cancelled."

        self._action_future.add_done_callback(callback)


class ManualControlManager:

    def __init__(self, node: Node):
        self._cmd_publisher = node.create_publisher(Twist, "/cmd_vel", 10)

        self._async_task = None

    def _create_twist_timeout(self, duration):
        async def twist_timeout():
            await asyncio.sleep(duration / 1000.0)
            self.stop()
        return twist_timeout()

    def stop(self):
        self._cmd_publisher.publish(Twist())

    def start_twist(self, x: float, z: float, duration: float):
        if self._async_task is not None and not self._async_task.done():
            self._async_task.cancel()

        twist = Twist()

        twist.linear.x = x
        twist.angular.z = z

        self._cmd_publisher.publish(twist)

        self._async_task = asyncio.create_task(self._create_twist_timeout(duration))