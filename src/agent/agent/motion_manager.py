import asyncio
import math
import rclpy
import rclpy.utilities

from cartographer_ros_msgs.action import Relocalize
from cartographer_ros_msgs.msg import Rectangle
from geometry_msgs.msg import Twist, PoseStamped
from interfaces.msg import ShelfInfo
from interfaces.srv import GetCurrentAction, CancelCurrentAction
from nav2_msgs.action import NavigateToPose
from nav_msgs.msg import Path
from opennav_docking_msgs.action import DockRobot, UndockRobot
from rclpy.node import Node
from typing import List, Dict, Optional

from .action_handler import NavigateExecutor, JackExecutor, RelocateExecutor, DockExecutor, UndockExecutor, \
    ActionTaskSupervisor, MotionActionMaintainer, ActionTask, ExecutorStatus, ExecutorFactory, ModifyMonitorExecutor, \
    ResetMonitorExecutor
from .agent_exceptions import UnsupportedActionError, ROSServiceNoResponseError, InvalidActionOptionsError, ConflictingCommandError
from .base_manager import BaseManager
from .home_dock_manager import HomeDockManager
from .models import ActionRequest, GoHomeActionOptions, MotionAction, MoveDirection, \
    ActionDefinition, MoveToTagActionOptions, JackCommand, Pose as APIPose, GeneralRotateActionOptions, \
    MoveToActionOptions, MoveByActionOptions, JackMoveDirectionEnum, MotionActionState, ActionStatusEnum, ActionResultEnum
from .models.action_options import BackOffFromTagActionOptions, JackMoveActionOptions, JackTopMoveToActionOptions, RelocateActionOptions
from .robot_pose_listener import RobotPoseListener
from .utils import euler_to_quaternion, api_handler, convert_ros2_uuid_to_str


class MotionManager(BaseManager):

    def __init__(self, node: Node, pose_listener: RobotPoseListener, home_dock_manager: HomeDockManager, action_task_supervisor: ActionTaskSupervisor):
        super().__init__(node)

        self._home_dock_manager = home_dock_manager

        self._ros2_action_id_map: Dict[str, int] = dict()

        self._path_msg: Optional[Path] = None
        self._milestones: List[List[float]] = []

        self._pose_listener = pose_listener

        self._manual_controller = ManualController(node)
        self._action_task_supervisor = action_task_supervisor

    def _subscribe_to_topics(self):
        self._subscriptions.append(
            self._node.create_subscription(
                Path,
                "plan",
                self._path_callback,
                1
            )
        )

    def _create_clients(self):
        self._create_client("cancel_current_action", CancelCurrentAction)
        self._create_client("get_current_action", GetCurrentAction)

    def _path_callback(self, msg):
        self._path_msg = msg

    async def get_current_action(self) -> MotionAction|None:
        motion_action = MotionActionMaintainer().current_action
        if motion_action is not None:
            return motion_action

        client = self._get_client("get_current_action", GetCurrentAction)
        if client is None:
            return None

        if not self._wait_for_service(client):
            return None

        response = await client.call_async(GetCurrentAction.Request())

        if response is None or not response.success:
            return None

        if response.action_name is None or response.goal_id is None:
            return None

        action_name = ActionDefinition.parse_ros2_action_name(response.action_name)
        if action_name is None:
            return None

        goal_id = convert_ros2_uuid_to_str(response.goal_id)

        action_id = MotionActionMaintainer().get_action_id_by_ros2_goal_id(goal_id)
        if action_id is None:
            action_id = MotionActionMaintainer().bind_ros2_goal_id(goal_id, action_name)
        return MotionAction(action_id=action_id, action_name=action_name.value, stage="running", state=MotionActionState(status=ActionStatusEnum.Working, result=ActionResultEnum.Success, reason=""))

    @api_handler(default_return=[])
    async def get_path(self) -> List[List[float]]:
        msg: Optional[Path] = self._path_msg

        if msg is None:
            return []

        if msg.poses is None or len(msg.poses) == 0:
            self._milestones = []
            return []

        path: List[List[float]] = []
        milestones: List[List[float]] = []

        current_milestone_pose = msg.poses[0]   # type: ignore

        for index, pose in enumerate(msg.poses):
            pt = [pose.pose.position.x, pose.pose.position.y]
            path.append(pt)

            if index + 1 == len(msg.poses):
                milestones.append(pt)
                break

            # calculate the distance between current_milestone_pose and pose
            dist = math.sqrt((current_milestone_pose.pose.position.x - pose.pose.position.x)**2 + (current_milestone_pose.pose.position.y - pose.pose.position.y)**2)
            if dist >= 1:
                current_milestone_pose = pose
                milestones.append(pt)

        self._milestones = milestones
        return path

    @api_handler(default_return=[])
    async def get_milestones(self):
        return self._milestones

    def get_action_by_id(self, action_id: int) -> MotionAction | None:
        action = MotionActionMaintainer().get_action_by_id(action_id)

        if (action is not None and
                MotionActionMaintainer().is_action_id_bound_to_ros2_goal_id(action_id) and
                MotionActionMaintainer().current_action_id != action_id):
            action.stage = "Success"
            action.state.status = ActionStatusEnum.Done
            action.state.result = ActionResultEnum.Success

        return action

    async def stop_current_action(self):
        await self._action_task_supervisor.cancel()
        self._manual_controller.stop()

        self._path_msg = None
        self._milestones.clear()

        client = self._get_client("cancel_current_action", CancelCurrentAction)
        if client is not None:
            if not self._wait_for_service(client):
                self._node.get_logger().info("Service cancel_current_action does not respond.")
            else:
                await client.call_async(CancelCurrentAction.Request())

    async def create_action(self, action: ActionRequest) -> MotionAction|None:
        action_define = action.action_name
        action_options = action.options

        if action_define == ActionDefinition.MOVE_BY and isinstance(action_options, MoveByActionOptions):
            direction = None
            theta = None
            if action_options.direction is not None:
                direction = MoveDirection.parse_move_direction(action_options.direction)
            elif direction is None and action_options.theta is not None:
                theta = action_options.theta
            duration = action_options.duration
            return await self._create_move_by_action(direction, theta, duration)
        elif action_define == ActionDefinition.MOVE_TO and isinstance(action_options, MoveToActionOptions):
            yaw = 0.0
            if action_options.move_options is not None and action_options.move_options.yaw is not None:
                yaw = action_options.move_options.yaw
            return await self._create_move_to_action(action_options.target.x, action_options.target.y, yaw)
        elif action_define == ActionDefinition.ROTATE and isinstance(action_options, GeneralRotateActionOptions):
            return await self._create_rotate_action(math.radians(action_options.angle))
        elif action_define == ActionDefinition.ROTATE_TO and isinstance(action_options, GeneralRotateActionOptions):
            return await self._create_rotate_to_action(math.radians(action_options.angle))
        elif action_define == ActionDefinition.GO_HOME:
            if action_options is not None and not isinstance(action_options, GoHomeActionOptions):
                raise InvalidActionOptionsError(action_define.value)
            return await self._create_go_home_action(action_options)
        elif action_define == ActionDefinition.MOVE_TO_TAG and isinstance(action_options, MoveToTagActionOptions):
            return await self._create_move_to_tag_action(action_options)
        elif action_define == ActionDefinition.BACK_OFF_FROM_TAG and isinstance(action_options, BackOffFromTagActionOptions):
            return await self._create_back_off_from_tag_action(action_options)
        elif action_define == ActionDefinition.RECOVER_LOCALIZATION:
            if action_options is not None and not isinstance(action_options, RelocateActionOptions):
                raise InvalidActionOptionsError(action_define.value)
            return await self._create_relocate_action(action_options)
        elif action_define == ActionDefinition.JACK_MOVE and isinstance(action_options, JackMoveActionOptions):
            return await self._create_jack_move_action(action_options)
        elif action_define == ActionDefinition.JACK_TOP_MOVE_TO and isinstance(action_options, JackTopMoveToActionOptions):
            return await self._create_jack_top_move_to_action(action_options)
        else:
            raise UnsupportedActionError(action.action_name)

    async def _create_jack_top_move_to_action(self, options: JackTopMoveToActionOptions) -> MotionAction|None:
        await self.stop_current_action()

        await self._create_navigate_to_pose_action(options.target.x, options.target.y, options.target.yaw)

        return MotionActionMaintainer().current_action

    async def _create_jack_move_action(self, options: JackMoveActionOptions) -> MotionAction|None:
        await self.stop_current_action()

        command = options.move_direction.to_JackCommandEnum()
        if command is None:
            raise InvalidActionOptionsError(ActionDefinition.JACK_MOVE.value)

        def create_jack_executor() -> JackExecutor:
            executor = JackExecutor(self._node)
            executor.set_jack_command(JackCommand(command=command))
            return executor

        factory = ExecutorFactory(create_jack_executor)

        task = ActionTask()

        if options.move_direction == JackMoveDirectionEnum.UP:
            def create_modify_executor() -> ModifyMonitorExecutor:
                return ModifyMonitorExecutor(self._node)

            modify_factory = ExecutorFactory(create_modify_executor)
            task.append_executor(modify_factory)

        task.append_executor(factory)

        if options.move_direction == JackMoveDirectionEnum.DOWN:
            def create_reset_executor() -> ResetMonitorExecutor:
                return ResetMonitorExecutor(self._node)

            reset_factory = ExecutorFactory(create_reset_executor)
            task.append_executor(reset_factory)

        await self._action_task_supervisor.reset(task)

        return MotionActionMaintainer().current_action

    async def _create_relocate_action(self, options: RelocateActionOptions | None) -> MotionAction|None:
        await self.stop_current_action()

        goal = Relocalize.Goal()
        if options is not None:
            if options.area is not None:
                goal.bounding_rect = Rectangle(x=options.area.x, y=options.area.y, width=options.area.width, height=options.area.height)
            if options.relocalization_options is not None and options.relocalization_options.max_recover_time is not None:
                goal.timeout_seconds = options.relocalization_options.max_recover_time / 1000.0

        def create_relocate_executor() -> RelocateExecutor:
            executor = RelocateExecutor(self._node)
            executor.prepare_goal(goal)
            return executor

        factory = ExecutorFactory(create_relocate_executor)

        task = ActionTask()
        task.append_executor(factory)
        await self._action_task_supervisor.reset(task)

        return MotionActionMaintainer().current_action

    async def _create_back_off_from_tag_action(self, options: BackOffFromTagActionOptions) -> MotionAction|None:
        await self.stop_current_action()

        if options.tag_type not in (3, 4):
            raise UnsupportedActionError(ActionDefinition.BACK_OFF_FROM_TAG.value)

        msg = UndockRobot.Goal()
        msg.dock_type = "shelf_dock" if options.tag_type == 3 else "simple_charging_dock"

        def create_undock_executor() -> UndockExecutor:
            executor = UndockExecutor(self._node)
            executor.prepare_goal(msg)
            return executor

        factory = ExecutorFactory(create_undock_executor)

        task = ActionTask()
        task.append_executor(factory)
        await self._action_task_supervisor.reset(task)

        return MotionActionMaintainer().current_action

    async def _create_go_home_action(self, options: Optional[GoHomeActionOptions]=None) -> MotionAction|None:
        await self.stop_current_action()

        goal = DockRobot.Goal()
        goal.dock_type = "simple_charging_dock"

        has_set_goal = False
        if options is not None:
            if options.dock_id is not None:
                has_set_goal = True
                goal.use_dock_id = True
                goal.dock_id = options.dock_id
            elif options.dock_pose is not None:
                has_set_goal = True
                pose = PoseStamped()
                pose.header.stamp = self._node.get_clock().now().to_msg()
                pose.header.frame_id = "map"
                pose.pose.position.x = options.dock_pose.x
                pose.pose.position.y = options.dock_pose.y
                pose.pose.position.z = options.dock_pose.z
                pose.pose.orientation.x, pose.pose.orientation.y, pose.pose.orientation.z, pose.pose.orientation.w = euler_to_quaternion(options.dock_pose.roll, options.dock_pose.pitch, options.dock_pose.yaw)
                goal.dock_pose = pose

        if not has_set_goal:
            bound_dock = await self._home_dock_manager.get_home_dock()
            if bound_dock is not None and bound_dock.id is not None:
                has_set_goal = True
                goal.use_dock_id = True
                goal.dock_id = bound_dock.id
            else:
                all_docks = await self._home_dock_manager.get_all_docks()
                if len(all_docks) > 0:
                    if len(all_docks) > 1:
                        raise ConflictingCommandError("There are more than 1 home dock and no bound home dock. Please specify a dock id or bind a home dock.")
                    first = all_docks[0]
                    if first.id is not None:
                        has_set_goal = True
                        goal.use_dock_id = True
                        goal.dock_id = first.id
                if not has_set_goal:
                    dock = APIPose(x=0.0, y=0.0, z=0.0, roll=0.0, pitch=0.0, yaw=3.142)
                    pose = PoseStamped()
                    pose.header.stamp = self._node.get_clock().now().to_msg()
                    pose.header.frame_id = "map"
                    pose.pose.position.x = dock.x
                    pose.pose.position.y = dock.y
                    pose.pose.position.z = dock.z
                    pose.pose.orientation.x, pose.pose.orientation.y, pose.pose.orientation.z, pose.pose.orientation.w = euler_to_quaternion(dock.roll, dock.pitch, dock.yaw)
                    goal.dock_pose = pose

        def create_dock_executor() -> DockExecutor:
            executor = DockExecutor(self._node, ActionDefinition.GO_HOME)
            executor.prepare_goal(goal)
            return executor

        factory = ExecutorFactory(create_dock_executor)

        task = ActionTask()
        task.append_executor(factory)
        await self._action_task_supervisor.reset(task)

        return MotionActionMaintainer().current_action

    async def _create_move_to_tag_action(self, options: MoveToTagActionOptions) -> MotionAction|None:
        await self.stop_current_action()

        if options.tag_type == 3:
            if options.shelves is None or len(options.shelves) == 0:
                raise InvalidActionOptionsError(ActionDefinition.MOVE_TO_TAG.value)

            goal = DockRobot.Goal()
            goal.use_dock_id = False
            goal.dock_type = "shelf_dock"
            if options.dock_allowance is not None:
                goal.dock_allowance = options.dock_allowance

            docking_pose = PoseStamped()
            docking_pose.header.stamp = self._node.get_clock().now().to_msg()
            docking_pose.header.frame_id = "map"
            docking_pose.pose.position.x = 0.0
            docking_pose.pose.position.y = 0.0
            docking_pose.pose.position.z = 0.0
            docking_pose.pose.orientation.x, docking_pose.pose.orientation.y, docking_pose.pose.orientation.z, docking_pose.pose.orientation.w = euler_to_quaternion(0.0, 0.0, 0.0)
            goal.dock_pose = docking_pose

            landing_pose = PoseStamped()
            landing_pose.header.stamp = self._node.get_clock().now().to_msg()
            landing_pose.header.frame_id = "map"
            landing_pose.pose.position.x = options.target.x
            landing_pose.pose.position.y = options.target.y
            landing_pose.pose.position.z = options.target.z
            landing_pose.pose.orientation.x, landing_pose.pose.orientation.y, landing_pose.pose.orientation.z, landing_pose.pose.orientation.w = euler_to_quaternion(options.target.roll, options.target.pitch, options.target.yaw)
            goal.landing_pose = landing_pose

            shelves: List[ShelfInfo] = []
            for info in options.shelves:
                shelf = ShelfInfo()
                shelf.shelf_columnar_length = info.shelf_columnar_length
                shelf.shelf_columnar_width = info.shelf_columnar_width
                shelf.shelf_columnar_diameter = info.shelf_columnar_diameter
                shelf.shelf_length_retraction = info.shelf_length_retraction
                shelves.append(shelf)

            goal.shelves = shelves

            def create_dock_executor() -> DockExecutor:
                executor = DockExecutor(self._node, ActionDefinition.MOVE_TO_TAG)
                executor.prepare_goal(goal)
                return executor

            factory = ExecutorFactory(create_dock_executor)

            task = ActionTask()
            task.append_executor(factory)
            await self._action_task_supervisor.reset(task)

            return MotionActionMaintainer().current_action
        else:
            raise UnsupportedActionError(ActionDefinition.MOVE_TO_TAG.value)

    async def _create_move_to_action(self, x: float, y: float, yaw: float=0.0) -> MotionAction|None:
        await self.stop_current_action()

        await self._create_navigate_to_pose_action(x, y, yaw)

        return MotionActionMaintainer().current_action

    async def _create_rotate_action(self, yaw: float) -> MotionAction|None:
        await self.stop_current_action()

        current_pose = self._pose_listener.get_pose()

        if current_pose is None:
            raise ROSServiceNoResponseError("get_current_pose")

        target_yaw = current_pose.yaw + yaw
        while target_yaw > math.pi:
            target_yaw -= 2 * math.pi
        while target_yaw < -math.pi:
            target_yaw += 2 * math.pi

        await self._create_navigate_to_pose_action(current_pose.x, current_pose.y, target_yaw)

        return MotionActionMaintainer().current_action

    async def _create_rotate_to_action(self, yaw: float) -> MotionAction|None:
        await self.stop_current_action()

        current_pose = self._pose_listener.get_pose()

        if current_pose is None:
            raise ROSServiceNoResponseError("get_current_pose")

        target_yaw = yaw
        while target_yaw > math.pi:
            target_yaw -= 2 * math.pi
        while target_yaw < -math.pi:
            target_yaw += 2 * math.pi

        await self._create_navigate_to_pose_action(current_pose.x, current_pose.y, target_yaw)

        return MotionActionMaintainer().current_action

    async def _create_move_by_action(self, direction: MoveDirection|None=None, theta: float|None=None, duration:int|None=None) -> MotionAction|None:
        await self.stop_current_action()

        if duration is None:
            duration = 500

        linear_speed = 1.0 * duration / 1000.0
        angular_speed = 1.0 * duration / 1000.0

        x = 0.0
        z = 0.0

        if direction is not None:
            match direction:
                case MoveDirection.FORWARD:
                    x = linear_speed
                case MoveDirection.BACKWARD:
                    x = -linear_speed
                case MoveDirection.TURN_LEFT:
                    z = angular_speed
                case MoveDirection.TURN_RIGHT:
                    z = -angular_speed
                case _:
                    raise InvalidActionOptionsError(ActionDefinition.MOVE_BY.value)
        elif theta is not None:
            z = theta
        else:
            raise InvalidActionOptionsError(ActionDefinition.MOVE_BY.value)

        self._manual_controller.start_twist(x, z, duration)

        return MotionActionMaintainer().current_action

    async def _create_navigate_to_pose_action(self, x: float, y: float, yaw: float):
        goal_msg = NavigateToPose.Goal()

        goal_msg.pose.header.frame_id = "map"
        goal_msg.pose.header.stamp = self._node.get_clock().now().to_msg()
        goal_msg.pose.pose.position.x = x
        goal_msg.pose.pose.position.y = y
        goal_msg.pose.pose.position.z = 0.0

        half_yaw = yaw / 2.0
        goal_msg.pose.pose.orientation.x = 0.0
        goal_msg.pose.pose.orientation.y = 0.0
        goal_msg.pose.pose.orientation.z = math.sin(half_yaw)
        goal_msg.pose.pose.orientation.w = math.cos(half_yaw)

        def create_navigate_executor() -> NavigateExecutor:
            executor = NavigateExecutor(self._node)
            executor.prepare_goal(goal_msg)
            return executor

        factory = ExecutorFactory(create_navigate_executor)

        task = ActionTask()
        task.append_executor(factory)
        await self._action_task_supervisor.reset(task)


class ManualController:

    def __init__(self, node: Node):
        self._cmd_publisher = node.create_publisher(Twist, "/cmd_vel", 1)

        self._async_task: asyncio.Task|None = None

    def __del__(self):
        self.stop()
        self._cmd_publisher.destroy()

    def _create_twist_timeout(self, duration):
        async def twist_timeout():
            await asyncio.sleep(duration / 1000.0)
            self.stop()
        return twist_timeout()

    def stop(self):
        if rclpy.utilities.ok():
            self._cmd_publisher.publish(Twist())

        if self._async_task is not None:
            MotionActionMaintainer().update_current_action(ExecutorStatus.SUCCEEDED)
            if not self._async_task.done():
                self._async_task.cancel()

    def start_twist(self, x: float, z: float, duration: float):
        if self._async_task is not None and not self._async_task.done():
            self._async_task.cancel()

        MotionActionMaintainer().create_action(ActionDefinition.MOVE_BY)

        twist = Twist()

        twist.linear.x = x
        twist.angular.z = z

        self._cmd_publisher.publish(twist)

        MotionActionMaintainer().update_current_action(ExecutorStatus.RUNNING)

        self._async_task = asyncio.create_task(self._create_twist_timeout(duration))
