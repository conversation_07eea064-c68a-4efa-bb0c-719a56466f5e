<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>agent</name>
  <version>0.0.1</version>
  <description>Agent ROS2 node with FastAPI REST server</description>
  <maintainer email="<EMAIL>">Slamtec</maintainer>
  <license>MIT</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>ament_cmake_python</buildtool_depend>

  <depend>rclpy</depend>
  <depend>std_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>interfaces</depend>
  <depend>events_executor</depend>
  <depend>cartographer_ros_msgs</depend>
  <depend>action_msgs</depend>
  <depend>nav2_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>stcm_manager</depend>
  <depend>tf2_ros</depend>
  <depend>sl_vcu_all</depend>
  <depend>opennav_docking_msgs</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package> 