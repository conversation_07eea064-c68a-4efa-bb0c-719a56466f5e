#!/usr/bin/env bash

SCRIPT_ROOT="`dirname $(realpath ${BASH_SOURCE[0]})`"

echo $SCRIPT_ROOT
echo $0
echo $1

source /opt/ros/humble/setup.bash
source $SCRIPT_ROOT/../../../setup.bash

echo "Switch mode..."

if [ -z "${RSLAMWARE_MODE+x}" ]; then
  RSLAMWARE_MODE="real"
fi

# Shutdown nodes
echo "Shutting down nodes..."
ros2 topic pub --once /shutdown_signal std_msgs/msg/Empty '{}'

if [ $? -ne 0 ]; then
  return 1
fi

nodes=(
  behavior_server
  bt_navigator
  bt_navigator_navigate_through_poses_rclcpp_node
  bt_navigator_navigate_to_pose_rclcpp_node
  cartographer_node
  cartographer_occupancy_grid_node
  controller_server
  costmap_filter_info_server
  global_costmap/global_costmap
  lifecycle_manager_navigation
  lifecycle_manager_costmap_filters
  lifecycle_manager_localization
  lifecycle_manager_navigation
  local_costmap/local_costmap
  map_server
  nav2_container
  planner_server
  smoother_server
  velocity_smoother
  waypoint_follower
)

while ros2 node list | grep -Ff <(printf '%s\n' "${nodes[@]}") >/dev/null; do
  echo "waiting nodes shutdown"
  sleep 1
done

if [ "$1" = "mapping" ]; then
  echo "Switching to mapping mode..."
  if [ "$RSLAMWARE_MODE" = "simulation"]; then
    echo "Starting in simulation mode..."
    ros2 launch cartographer_ros mapping.launch.py mode:=simulation scan_topic:=scan &
  else
    echo "Starting in real mode"
    ros2 launch cartographer_ros mapping.launch.py mode:=real scan_topic:=fusion_scan &
  fi
else
  echo "Switching to localization mode"
  if [ "$RSLAMWARE_MODE" = "simulation"]; then
    echo "Starting in simulation mode..."
    ros2 launch nav2_bringup bringup_launch.py mode:=simulation scan_topic:=scan &
  else
    echo "Starting in real mode..."
    ros2 launch nav2_bringup bringup_launch.py mode:=real scan_topic:=fusion_scan &
  fi
fi
