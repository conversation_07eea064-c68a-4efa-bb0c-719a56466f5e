#!/usr/bin/env bash

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"

source /opt/ros/humble/setup.bash
source SCRIPT_ROOT/../../../setup.bash

echo "Switch mode..."

if [ -z "${RSLAMWARE_MODE+x}" ]; then
  RSLAMWARE_MODE="real"
fi

# Shutdown nodes
echo "Shutting down nodes..."
ros2 topic pub --once /shutdown_signal std_msgs/msg/Empty '{}'

sleep 1

if [ "$1" = "mapping" ]; then
  echo "Switching to mapping mode..."
  if [ "$RSLAMWARE_MODE" = "simulation"]; then
    echo "Starting in simulation mode..."
    ros2 launch cartographer_ros mapping.launch.py mode:=simulation scan_topic:=scan &
  else
    echo "Starting in real mode"
    ros2 launch cartographer_ros mapping.launch.py mode:=real scan_topic:=fusion_scan &
  fi
else
  echo "Switching to localization mode"
  if [ "$RSLAMWARE_MODE" = "simulation"]; then
    echo "Starting in simulation mode..."
    ros2 launch nav2_bringup bringup_launch.py mode:=simulation scan_topic:=scan &
  else
    echo "Starting in real mode..."
    ros2 launch nav2_bringup bringup_launch.py mode:=real scan_topic:=fusion_scan &
  fi
fi
