#!/usr/bin/env bash

# Check if the folder exists. If not create it.
if [ ! -d "/opt/rslamware_data/rosbag" ]; then
    mkdir -p /opt/rslamware_data/rosbag
fi

cd /opt/rslamware_data/rosbag

if [ -f "/opt/rslamware_data/topics_to_record.txt" ]; then
    topics=$(tr '\n' ' ' < /opt/rslamware_data/topics_to_record.txt | xargs)
elif [ -f "/opt/rslamware/data_recorder/share/data_recorder/topics_to_record.txt" ]; then
    topics=$(tr '\n' ' ' < /opt/rslamware/data_recorder/share/data_recorder/topics_to_record.txt | xargs)
else
    echo "No default topics file. Use the default topics configuration."
    topics="/lidar_front/scan
    /lidar_back/scan
    /odometry/odom_wheel
    /imu/data_raw
    /imu/data
    /imu/rpy/filtered
    /sensor/sonar
    /sensor/bumper
    /depth_lower/image_raw
    /depth_lower/points
    /camera/image_raw"
fi

filename=/opt/rslamware_data/rosbag/$(date +%Y%m%d%H%M%S)

if [ -n "$1" ]; then
    size=$(($1 * 1024))
else
    size=5120000
fi

ros2 bag record --max-bag-size $size -o $filename $topics
