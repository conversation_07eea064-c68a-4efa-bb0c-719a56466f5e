#!/usr/bin/env bash
    
MONITOR_DIR="/opt/rslamware_data/rosbag"

# Maximum size 20GB
MAX_SIZE=$((1024 * 1024 * 1024 * 20))

# Check current dir size
current_size=$(du -sb "$MONITOR_DIR" | awk '{print $1}')

# Find the oldest rosbag and remove it.
while [ "$current_size" -gt "$MAX_SIZE" ]; do
    # If there is only one folder in it, then break
    if [ $(ls -1 "$MONITOR_DIR" | wc -l) -eq 1 ]; then
        break
    fi

    # Get the oldest file
    oldest_file=$(ls -t "$MONITOR_DIR" | tail -1)
    # Remove the oldest file
    rm "$MONITOR_DIR/$oldest_file"
    # Update the current size
    current_size=$(du -sb "$MONITOR_DIR" | awk '{print $1}')
done

if [ "$current_size" -gt "$MAX_SIZE" ] && [ $(ls -1 "$MONITOR_DIR" | wc -l) -eq 1 ]; then
    last_folder = $(ls -t "$MONITOR_DIR" | tail -1)
    cd "$MONITOR_DIR/$last_folder"
    
    while [ "$current_size" -gt "$MAX_SIZE" ]; do
        oldest_file=$(ls -t | tail -1)
        rm "$oldest_file"
        current_size=$(du -sb "$MONITOR_DIR" | awk '{print $1}')
    done
fi