#!/usr/bin/env bash
    
if [ -z "$1" ]; then
    echo "No rosbag provided."
    exit 1
fi

folder=/opt/rslamware_data/rosbag

if [ -f "/opt/rslamware_data/topics_to_record.txt" ]; then
    topics=$(cat /opt/rslamware_data/topics_to_record.txt)
elif [ -f "/opt/rslamware/data_recorder/share/data_recorder/topics_to_record.txt" ]; then
    topics=$(cat /opt/rslamware/data_recorder/share/data_recorder/topics_to_record.txt)
else
    echo "No default topics file. Use the default topics configuration."
    topics="/lidar_front/scan 
    /lidar_back/scan 
    /odometry/odom_wheel 
    /imu/data_raw 
    /imu/data 
    /imu/rpy/filtered 
    /sensor/sonar 
    /sensor/bumper 
    /depth_lower/image_raw 
    /depth_lower/points 
    /camera/image_raw"
fi

for topic in $topics; do
    ros2 lifecycle set $topic inactive
    ros2 lifecycle set $topic shutdown
done

ros2 bag play $1
