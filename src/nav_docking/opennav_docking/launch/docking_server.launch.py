#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    pkg_share = FindPackageShare('opennav_docking').find('opennav_docking')

    default_params_file = PathJoinSubstitution([
        pkg_share,
        'config',
        'docking_server_params.yaml'
    ])

    params_file_arg = DeclareLaunchArgument(
        'params_file',
        default_value=default_params_file,
        description='Path to the parameters file for docking server'
    )

    docking_server_node = Node(
        package='opennav_docking',
        executable='opennav_docking',
        name='docking_server',
        parameters=[
            LaunchConfiguration('params_file')
        ],
        output='screen',
        emulate_tty=True,
    )

    lifecycle_manager_node = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_docking',
        output='screen',
        parameters=[{
            'autostart': True,
            'node_names': ['docking_server']
        }]
    )

    return LaunchDescription([
        params_file_arg,
        docking_server_node,
        lifecycle_manager_node
    ]) 