#include <cmath>

#include "nav2_util/node_utils.hpp"
#include "opennav_docking/shelf_dock.hpp"
#include "nav2_util/geometry_utils.hpp"

namespace opennav_docking
{

void ShelfDock::configure(
  const rclcpp_lifecycle::LifecycleNode::WeakPtr & parent,
  const std::string & name, std::shared_ptr<tf2_ros::Buffer> tf)
{
  name_ = name;
  tf2_buffer_ = tf;
  node_ = parent.lock();
  if (!node_) {
    throw std::runtime_error{"Failed to lock node"};
  }

  // Parameters for optional external detection of dock pose
  nav2_util::declare_parameter_if_not_declared(
    node_, name + ".use_external_detection_pose", rclcpp::ParameterValue(false));
  nav2_util::declare_parameter_if_not_declared(
    node_, name + ".external_detection_timeout", rclcpp::ParameterValue(10.0));
  nav2_util::declare_parameter_if_not_declared(
    node_, name + ".external_detection_translation_x", rclcpp::ParameterValue(0.0));
  nav2_util::declare_parameter_if_not_declared(
    node_, name + ".external_detection_translation_y", rclcpp::ParameterValue(0.0));
  nav2_util::declare_parameter_if_not_declared(
    node_, name + ".external_detection_rotation_yaw", rclcpp::ParameterValue(0.0));
  nav2_util::declare_parameter_if_not_declared(
    node_, name + ".external_detection_rotation_pitch", rclcpp::ParameterValue(0.0));
  nav2_util::declare_parameter_if_not_declared(
    node_, name + ".external_detection_rotation_roll", rclcpp::ParameterValue(0.0));
  nav2_util::declare_parameter_if_not_declared(
    node_, name + ".filter_coef", rclcpp::ParameterValue(0.1));

  // If not using stall detection, this is how close robot should get to pose
  nav2_util::declare_parameter_if_not_declared(
    node_, name + ".docking_threshold", rclcpp::ParameterValue(0.05));
  
  // Shelf detection configuration
  nav2_util::declare_parameter_if_not_declared(
    node_, name + ".shelf_detect_timeout", rclcpp::ParameterValue(5.0));

  node_->get_parameter(name + ".use_external_detection_pose", use_external_detection_pose_);
  node_->get_parameter(name + ".external_detection_timeout", external_detection_timeout_);
  node_->get_parameter(
    name + ".external_detection_translation_x", external_detection_translation_x_);
  node_->get_parameter(
    name + ".external_detection_translation_y", external_detection_translation_y_);
  double yaw, pitch, roll;
  node_->get_parameter(name + ".external_detection_rotation_yaw", yaw);
  node_->get_parameter(name + ".external_detection_rotation_pitch", pitch);
  node_->get_parameter(name + ".external_detection_rotation_roll", roll);
  external_detection_rotation_.setEuler(pitch, roll, yaw);
  node_->get_parameter(name + ".docking_threshold", docking_threshold_);
  node_->get_parameter("base_frame", base_frame_id_);  // Get server base frame ID
  
  // Get shelf detection parameters
  node_->get_parameter(name + ".shelf_detect_timeout", shelf_detect_timeout_);

  // Setup filter
  double filter_coef;
  node_->get_parameter(name + ".filter_coef", filter_coef);
  filter_ = std::make_unique<PoseFilter>(filter_coef, external_detection_timeout_);

  if (use_external_detection_pose_) {
    dock_pose_.header.stamp = rclcpp::Time(0);
    dock_pose_sub_ = node_->create_subscription<geometry_msgs::msg::PoseStamped>(
      "detected_shelf_dock_pose", 1,
      [this](const geometry_msgs::msg::PoseStamped::SharedPtr pose) {
        detected_dock_pose_ = *pose;
      });
  }

  dock_pose_pub_ = node_->create_publisher<geometry_msgs::msg::PoseStamped>("shelf_dock_pose", 1);
  filtered_dock_pose_pub_ = node_->create_publisher<geometry_msgs::msg::PoseStamped>(
    "filtered_shelf_dock_pose", 1);
  staging_pose_pub_ = node_->create_publisher<geometry_msgs::msg::PoseStamped>("shelf_staging_pose", 1);
  
  // Create shelf detection service client
  shelf_detect_client_ = node_->create_client<interfaces::srv::DetectShelf>("detect_shelf");
  RCLCPP_INFO(node_->get_logger(), "Shelf detection enabled, waiting for detect_shelf service...");
}

geometry_msgs::msg::PoseStamped ShelfDock::getStagingPose(
  const geometry_msgs::msg::Pose & pose, const std::string & frame)
{
  // If not using detection, set the dock pose as the given dock pose estimate
  if (!use_external_detection_pose_) {
    // This gets called at the start of docking
    // Reset our internally tracked dock pose
    dock_pose_.header.frame_id = frame;
    dock_pose_.pose = pose;
  }

  // Publish staging pose for debugging purposes
  staging_pose_pub_->publish(staging_pose_);
  return staging_pose_;
}

bool ShelfDock::getRefinedPose(geometry_msgs::msg::PoseStamped & pose)
{
  // If using not detection, set the dock pose to the static fixed-frame version
  if (!use_external_detection_pose_) {
    dock_pose_pub_->publish(pose);
    dock_pose_ = pose;
    return true;
  }

  if(!callShelfDetectionService(detected_dock_pose_))
  {
    return false;
  }

  // If using detections, get current detections, transform to frame, and apply offsets
  geometry_msgs::msg::PoseStamped detected = detected_dock_pose_;

  // Validate that external pose is new enough
  auto timeout = rclcpp::Duration::from_seconds(external_detection_timeout_);
  if (node_->now() - detected.header.stamp > timeout) {
    RCLCPP_WARN(node_->get_logger(), "Lost detection or did not detect: timeout exceeded");
    return false;
  }

  // Transform detected pose into fixed frame. Note that the argument pose
  // is the output of detection, but also acts as the initial estimate
  // and contains the frame_id of docking
  if (detected.header.frame_id != pose.header.frame_id) {
    try {
      if (!tf2_buffer_->canTransform(
          pose.header.frame_id, detected.header.frame_id,
          detected.header.stamp, rclcpp::Duration::from_seconds(0.2)))
      {
        RCLCPP_WARN(node_->get_logger(), "Failed to transform detected dock pose");
        return false;
      }
      tf2_buffer_->transform(detected, detected, pose.header.frame_id);
      RCLCPP_INFO(node_->get_logger(), "Detected dock pose in frame %s: %f, %f, %f, yaw: %f", 
        pose.header.frame_id.c_str(),
        detected.pose.position.x, detected.pose.position.y, detected.pose.position.z, tf2::getYaw(detected.pose.orientation));
    } catch (const tf2::TransformException & ex) {
      RCLCPP_WARN(node_->get_logger(), "Failed to transform detected dock pose");
      return false;
    }
  }

  // Filter the detected pose
  detected = filter_->update(detected);
  filtered_dock_pose_pub_->publish(detected);

  // Rotate the just the orientation, then remove roll/pitch
  geometry_msgs::msg::PoseStamped just_orientation;
  just_orientation.pose.orientation = tf2::toMsg(external_detection_rotation_);
  geometry_msgs::msg::TransformStamped transform;
  transform.transform.rotation = detected.pose.orientation;
  tf2::doTransform(just_orientation, just_orientation, transform);

  tf2::Quaternion orientation;
  orientation.setEuler(0.0, 0.0, tf2::getYaw(just_orientation.pose.orientation));
  dock_pose_.pose.orientation = tf2::toMsg(orientation);

  // Construct dock_pose_ by applying translation/rotation
  dock_pose_.header = detected.header;
  dock_pose_.pose.position = detected.pose.position;
  const double yaw = tf2::getYaw(dock_pose_.pose.orientation);
  dock_pose_.pose.position.x += cos(yaw) * external_detection_translation_x_ -
    sin(yaw) * external_detection_translation_y_;
  dock_pose_.pose.position.y += sin(yaw) * external_detection_translation_x_ +
    cos(yaw) * external_detection_translation_y_;
  dock_pose_.pose.position.z = 0.0;

  // Publish & return dock pose for debugging purposes
  dock_pose_pub_->publish(dock_pose_);
  pose = dock_pose_;
  return true;
}

bool ShelfDock::isDocked()
{
  if (dock_pose_.header.frame_id.empty()) {
    // Dock pose is not yet valid
    return false;
  }

  // Find base pose in target frame
  geometry_msgs::msg::PoseStamped base_pose;
  base_pose.header.stamp = rclcpp::Time(0);
  base_pose.header.frame_id = base_frame_id_;
  base_pose.pose.orientation.w = 1.0;
  try {
    tf2_buffer_->transform(base_pose, base_pose, dock_pose_.header.frame_id);
  } catch (const tf2::TransformException & ex) {
    return false;
  }

  // If we are close enough, pretend we are charging
  double d = std::hypot(
    base_pose.pose.position.x - dock_pose_.pose.position.x,
    base_pose.pose.position.y - dock_pose_.pose.position.y);
  return d < docking_threshold_;
}

bool ShelfDock::isCharging()
{
  return isDocked();
}

bool ShelfDock::disableCharging()
{
  return true;
}

bool ShelfDock::hasStoppedCharging()
{
  return !isCharging();
}

bool ShelfDock::callShelfDetectionService(geometry_msgs::msg::PoseStamped& detected_dock_pose)
{
  if (!shelf_detect_client_) {
    RCLCPP_ERROR(node_->get_logger(), "Shelf detection client not initialized");
    return false;
  }

  // Wait for service to be available
  if (!shelf_detect_client_->wait_for_service(std::chrono::seconds(5))) {
    RCLCPP_ERROR(node_->get_logger(), "Shelf detection service not available");
    return false;
  }

  // Prepare service request
  auto request = std::make_shared<interfaces::srv::DetectShelf::Request>();
  request->shelves = shelf_lists_;
  request->dock_allowance = dock_allowance_;
  request->landing_pose = staging_pose_.pose;
  request->backward = backward_;
  if (backward_) 
  {
    request->landing_pose.orientation = nav2_util::geometry_utils::orientationAroundZAxis(
      tf2::getYaw(request->landing_pose.orientation) + M_PI);
  }
  
  RCLCPP_INFO(node_->get_logger(), "Calling shelf detection service...");

  // Call service synchronously
  auto future = shelf_detect_client_->async_send_request(request);
  
  // Wait for response with timeout
  auto timeout = std::chrono::seconds(static_cast<int>(shelf_detect_timeout_));
  if (future.wait_for(timeout) != std::future_status::ready) {
    RCLCPP_ERROR(node_->get_logger(), "Shelf detection service call timeout");
    return false;
  }

  auto response = future.get();
  if (!response->success) {
    RCLCPP_WARN(node_->get_logger(), "Shelf detection failed: %s", response->message.c_str());
    return false;
  }

  detected_dock_pose = response->shelf_in_robot_view;
  return true;
}

void ShelfDock::setShelfLists(const std::vector<interfaces::msg::ShelfInfo> & shelf_lists)
{
  shelf_lists_ = shelf_lists;
}

void ShelfDock::setDockAllowance(const float dock_allowance)
{
  dock_allowance_ = dock_allowance;
}

void ShelfDock::setLandingPose(const geometry_msgs::msg::PoseStamped & landing_pose)
{
  staging_pose_ = landing_pose;
}

void ShelfDock::setBackward(const bool backward)
{
  backward_ = backward;
}

}  // namespace opennav_docking

#include "pluginlib/class_list_macros.hpp"
PLUGINLIB_EXPORT_CLASS(opennav_docking::ShelfDock, opennav_docking_core::ChargingDock)
