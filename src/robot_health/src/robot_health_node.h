#pragma once
#include <rclcpp/rclcpp.hpp>
#include "interfaces/msg/component_health_info.hpp"
#include "interfaces/msg/robot_health_info.hpp"

namespace rslamware{

class RobotHealthNode : public rclcpp::Node
{
public:
    RobotHealthNode();
private: 
    void publish_health();
    void component_health_callback(const interfaces::msg::ComponentHealthInfo::SharedPtr msg);
private:
    rclcpp::Subscription<interfaces::msg::ComponentHealthInfo>::SharedPtr subscriber_;
    rclcpp::Publisher<interfaces::msg::RobotHealthInfo>::SharedPtr health_publisher_;
    rclcpp::TimerBase::SharedPtr timer_;
    std::mutex mutex_;
    std::map<std::string, interfaces::msg::ComponentHealthInfo::SharedPtr> merged_health_;
};

}
