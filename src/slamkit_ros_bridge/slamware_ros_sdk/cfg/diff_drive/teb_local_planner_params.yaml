TebLocalPlannerROS:

 odom_topic: slamware_ros_sdk_server_node/odom
    
#Trajectory
teb_autosize: True
dt_ref: 0.3
dt_hysteresis: 0.1
global_plan_overwrite_orientation: True
allow_init_with_backwards_motion: True
max_global_plan_lookahead_dist: 3.0
feasibility_check_no_poses: 5
    
# Robot
max_vel_x: 0.7
max_vel_x_backwards: 0.5
max_vel_y: 0.0
max_vel_theta: 0.2
acc_lim_x: 0.2
acc_lim_theta: 0.2
min_turning_radius: 0.0

footprint_model:
    type:  "polygon" 
    vertices: [[-0.4,0.25],[0.4,0.25],[0.4,-0.25],[-0.4,-0.25]]  

# GoalTolerance
xy_goal_tolerance: 0.2
yaw_goal_tolerance: 0.1
free_goal_vel: False
    
# Obstacles
min_obstacle_dist: 0.1 
include_costmap_obstacles: True
costmap_obstacles_behind_robot_dist: 0.5
obstacle_poses_affected: 20
costmap_converter_plugin: "costmap_converter::CostmapToLinesDBSRANSAC"
costmap_converter_spin_thread: True
costmap_converter_rate: 5
costmap_converter/CostmapToLinesDBSRANSAC:
    cluster_max_distance: 0.4
    cluster_min_pts: 2
    ransac_inlier_distance: 0.15
    ransac_min_inliers: 10
    ransac_no_iterations: 1500
    ransac_remainig_outliers: 3
    ransac_convert_outlier_pts: True
    ransac_filter_remaining_outlier_pts: False
    convex_hull_min_pt_separation: 0.1

# Optimization
no_inner_iterations: 4
no_outer_iterations: 3
optimization_activate: True
optimization_verbose: False
penalty_epsilon: 0.05
weight_max_vel_x: 5
weight_max_vel_theta: 1
weight_acc_lim_x: 1
weight_acc_lim_theta: 1
weight_kinematics_nh: 1000
weight_kinematics_forward_drive: 1
weight_kinematics_turning_radius: 1
weight_optimaltime: 1
weight_obstacle: 50
weight_dynamic_obstacle: 10 
weight_adapt_factor: 2

# Homotopy Class Planner
enable_homotopy_class_planning: True
enable_multithreading: True
simple_exploration: False
max_number_classes: 3
selection_cost_hysteresis: 1.0
selection_obst_cost_scale: 1.0
selection_alternative_time_cost: False
 
roadmap_graph_no_samples: 15
roadmap_graph_area_width: 5
h_signature_prescaler: 0.5
h_signature_threshold: 0.1
obstacle_keypoint_offset: 0.1
obstacle_heading_threshold: 0.45
visualize_hc_graph: False

 