/**
 kint.z<PERSON>  <EMAIL>
   2017.0721

  <NAME_EMAIL>, 2019.
*/

#include  "slamware_ros_sdk_server.h"

#include <rpos/system/io/memory_read_stream.h>
#include <rpos/system/io/memory_write_stream.h>
#include <rpos/robot_platforms/objects/composite_map_reader.h>
#include <rpos/robot_platforms/objects/composite_map_writer.h>
#include <rpos/system/util/time_util.h>

#include <boost/assert.hpp>

#include <stdexcept>
#include <cmath>
#include <memory>

namespace slamware_ros_sdk {

    using namespace boost::placeholders;
    //////////////////////////////////////////////////////////////////////////

    SlamwareRosSdkServer::SlamwareRosSdkServer()
        : Node("slamware_ros_sdk_server")
        , state_(ServerStateNotInit)
        , isStopRequested_(false)
        , nh_(rclcpp::Node::make_shared("slamware_ros_sdk_server") )
    {
        tfBrdcstr_ = std::make_unique<tf2_ros::TransformBroadcaster>(*this);
    }

    SlamwareRosSdkServer::~SlamwareRosSdkServer()
    {
        cleanup_();
    }

    bool SlamwareRosSdkServer::startRun(std::string& errMsg)
    {
        errMsg.clear();
        const auto oldState = state_.load();
        if (ServerStateNotInit != oldState && ServerStateStopped != oldState)
        {
            errMsg = "it is running or already initialized.";
            return false;
        }

        auto startupRosTime = get_clock()->now();
        auto startupSteadyTime = rpos::system::util::high_resolution_clock::get_time_in_us();
        rosTimeOffset_ = int64_t(startupRosTime.nanoseconds() / 1000) - int64_t(startupSteadyTime);

        isStopRequested_.store(false);
        bool bRet = init_(errMsg);
        if (bRet)
        {
            state_.store(ServerStateRunning);
            workThread_ = boost::move(boost::thread(boost::bind(&SlamwareRosSdkServer::workThreadFun_, this)));
        }

        if (!bRet)
        {
            cleanup_();
        }
        return bRet;
    }

    void SlamwareRosSdkServer::requestStop()
    {
        isStopRequested_.store(true);
    }

    void SlamwareRosSdkServer::waitUntilStopped()
    {
        if (workThread_.joinable())
            workThread_.join();
        BOOST_ASSERT(!isRunning_());
    }

    void SlamwareRosSdkServer::requestSyncMap()
    {
        auto wkDat = safeGetMutableWorkData_();
        if (wkDat)
            wkDat->syncMapRequested.store(true);
    }

    boost::chrono::milliseconds SlamwareRosSdkServer::sfConvFloatSecToBoostMs_(float fSec)
    {
        if (fSec < 0.0f)
            throw std::runtime_error("invalid float value of seconds.");

        const std::uint32_t uMs = static_cast<std::uint32_t>(std::floor(fSec * 1000));
        return boost::chrono::milliseconds(uMs);
    }

    bool SlamwareRosSdkServer::shouldContinueRunning_() const
    {
        return (!isStopRequested_.load());
    }

    ServerWorkData_ConstPtr SlamwareRosSdkServer::safeGetWorkData_() const
    {
        boost::lock_guard<boost::mutex> lkGuard(workDatLock_);
        return workDat_;
    }

    ServerWorkData_Ptr SlamwareRosSdkServer::safeGetMutableWorkData_()
    {
        boost::lock_guard<boost::mutex> lkGuard(workDatLock_);
        return workDat_;
    }

    bool SlamwareRosSdkServer::safeIsSlamwarePlatformConnected_() const
    {
        boost::lock_guard<boost::mutex> lkGuard(slamwarePltfmLock_);
        return bool(slamwarePltfm_);
    }

    SlamwareRosSdkServer::slamware_platform_t SlamwareRosSdkServer::safeGetSlamwarePlatform_() const
    {
        boost::lock_guard<boost::mutex> lkGuard(slamwarePltfmLock_);
        return slamwarePltfm_;
    }

    void SlamwareRosSdkServer::safeSetSlamwarePlatform_(const slamware_platform_t& pltfm)
    {
        auto tmpPltfm = pltfm;
        {
            boost::lock_guard<boost::mutex> lkGuard(slamwarePltfmLock_);
            std::swap(slamwarePltfm_, tmpPltfm);
        }
    }

    void SlamwareRosSdkServer::safeReleaseSlamwarePlatform_()
    {
        slamware_platform_t tmpPltfm;
        {
            boost::lock_guard<boost::mutex> lkGuard(slamwarePltfmLock_);
            std::swap(slamwarePltfm_, tmpPltfm);
        }
        disconnectSlamwarePlatform_(tmpPltfm);
    }
    
    SlamwareRosSdkServer::slamware_platform_t SlamwareRosSdkServer::connectSlamwarePlatform_(const std::string& ip, int port) const
    {
        try
        {
            auto pltfm = slamware_platform_t::connect(ip, port);
            return pltfm;
        }
        catch (const std::exception& excp)
        {
            RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "connectSlamwarePlatform_(), exception: %s.", excp.what());
        }
        catch (...)
        {
            RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "connectSlamwarePlatform_(), unknown exception.");
        }
        return slamware_platform_t();
    }
    
    void SlamwareRosSdkServer::disconnectSlamwarePlatform_(slamware_platform_t& pltfm) const
    {
        if (pltfm)
        {
            try
            {
                pltfm.disconnect();
            }
            catch (const std::exception& excp)
            {
                RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "disconnectSlamwarePlatform_(), exception: %s.", excp.what());
            }
            catch (...)
            {
                RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "disconnectSlamwarePlatform_(), unknown exception.");
            }
        }
    }

    bool SlamwareRosSdkServer::init_(std::string& /*errMsg*/)
    {
        // params_.resetToDefault();  // TODO:
        std::string ip = params_.getParameter<std::string>("ip_address");
        int port = params_.getParameter<int>("robot_port");
        RCLCPP_INFO(rclcpp::get_logger("slamware ros sdk server"), "ip:%s, port:%d.", ip.c_str(), port);
        
        params_.setBy(nh_);
        {
            boost::lock_guard<boost::mutex> lkGuard(workDatLock_);
            workDat_ = boost::make_shared<ServerWorkData>();
        }

        if (!safeIsSlamwarePlatformConnected_())
        {
            loopTryConnectToSlamwarePlatform_();
        }

        // init all workers
        {
            serverWorkers_.clear();

            const auto defaultUpdateIntervalForNoneUpdateWorkers = boost::chrono::milliseconds(1000u * 60u);

            {
                auto svrWk = boost::make_shared<ServerRobotDeviceInfoWorker>(this, "RobotDeviceInfo", defaultUpdateIntervalForNoneUpdateWorkers);
                serverWorkers_.push_back(svrWk);
            }

            if (0 < params_.getParameter<float>("odometry_pub_period"))
            {
                auto svrWk = boost::make_shared<ServerOdometryWorker>(this, "Odometry", sfConvFloatSecToBoostMs_(params_.getParameter<float>("odometry_pub_period")));
                serverWorkers_.push_back(svrWk);
            }

            if (0 < params_.getParameter<float>("robot_pose_pub_period"))
            {
                auto svrWk = boost::make_shared<ServerRobotPoseWorker>(this, "RobotPose", sfConvFloatSecToBoostMs_(params_.getParameter<float>("robot_pose_pub_period")));
                serverWorkers_.push_back(svrWk);
            }

            if (0 < params_.getParameter<float>("map_update_period"))
            {
                auto svrWk = boost::make_shared<ServerExploreMapUpdateWorker>(this, "ExploreMapUpdate", sfConvFloatSecToBoostMs_(params_.getParameter<float>("map_update_period")));
                serverWorkers_.push_back(svrWk);
            }

            if (0 < params_.getParameter<float>("map_pub_period"))
            {
                auto svrWk = boost::make_shared<ServerExploreMapPublishWorker>(this, "ExploreMapPublish", sfConvFloatSecToBoostMs_(params_.getParameter<float>("map_pub_period")));
                serverWorkers_.push_back(svrWk);
            }

            if (0 < params_.getParameter<float>("scan_pub_period"))
            {
                auto svrWk = boost::make_shared<ServerLaserScanWorker>(this, "LaserScan", sfConvFloatSecToBoostMs_(params_.getParameter<float>("scan_pub_period")));
                serverWorkers_.push_back(svrWk);
            }
            
            if (0 < params_.getParameter<float>("basic_sensors_info_update_period"))
            {
                auto svrWk = boost::make_shared<ServerBasicSensorsInfoWorker>(this, "BasicSensorsInfo", sfConvFloatSecToBoostMs_(params_.getParameter<float>("basic_sensors_info_update_period")));
                serverWorkers_.push_back(svrWk);
            }

            if (0 < params_.getParameter<float>("basic_sensors_values_pub_period"))
            {
                auto svrWk = boost::make_shared<ServerBasicSensorsValuesWorker>(this, "BasicSensorsValues", sfConvFloatSecToBoostMs_(params_.getParameter<float>("basic_sensors_values_pub_period")));
                serverWorkers_.push_back(svrWk);
            }

            if (0 < params_.getParameter<float>("path_pub_period"))
            {
                auto svrWk = boost::make_shared<ServerPlanPathWorker>(this, "PlanPath", sfConvFloatSecToBoostMs_(params_.getParameter<float>("path_pub_period")));
                serverWorkers_.push_back(svrWk);
            }

            if (0 < params_.getParameter<float>("robot_basic_state_pub_period"))
            {
                auto svrWk = boost::make_shared<ServerRobotBasicStateWorker>(this, "RobotBasicState", sfConvFloatSecToBoostMs_(params_.getParameter<float>("robot_basic_state_pub_period")));
                serverWorkers_.push_back(svrWk);
            }

            if (0 < params_.getParameter<float>("virtual_walls_pub_period"))
            {
                ServerArtifactLinesWorker::params_t tParams;
                tParams.usage.usage = slamware_ros_sdk::msg::ArtifactUsage::VIRTUAL_WALL;
                tParams.topic = "virtual_walls";
                auto svrWk = boost::make_shared<ServerArtifactLinesWorker>(this, "VirtualWalls", sfConvFloatSecToBoostMs_(params_.getParameter<float>("virtual_walls_pub_period")), tParams);
                serverWorkers_.push_back(svrWk);
            }

            if (0 < params_.getParameter<float>("virtual_tracks_pub_period"))
            {
                ServerArtifactLinesWorker::params_t tParams;
                tParams.usage.usage = slamware_ros_sdk::msg::ArtifactUsage::VIRTUAL_TRACK;
                tParams.topic = "/slamware_ros_sdk_server_node/virtual_tracks";
                auto svrWk = boost::make_shared<ServerArtifactLinesWorker>(this, "VirtualTracks", sfConvFloatSecToBoostMs_(params_.getParameter<float>("virtual_tracks_pub_period")), tParams);
                serverWorkers_.push_back(svrWk);
            }

            if(params_.getParameter<float>("robot_basic_state_pub_period") > 0)
            {
                auto svrWk = boost::make_shared<ServerReadEventsWorker>(this, "ReadEvents", sfConvFloatSecToBoostMs_(params_.getParameter<float>("robot_basic_state_pub_period")));
                serverWorkers_.push_back(svrWk);
            }

            if(params_.getParameter<float>("imu_raw_data_period") >  0)
            {
                auto svrWk = boost::make_shared<ServerImuRawDataWorker>(this, "ServerImuRawDataWorker", sfConvFloatSecToBoostMs_(params_.getParameter<float>("imu_raw_data_period")));
                serverWorkers_.push_back(svrWk);
            }

            {
                auto svrWk = boost::make_shared<RosConnectWorker>(this, "RosConnectWorker", sfConvFloatSecToBoostMs_(params_.getParameter<float>("robot_basic_state_pub_period")));
                serverWorkers_.push_back(svrWk);
            }
        }

        // init all subscriptions
        {
            subRobotControl_ = subscribe_T_<geometry_msgs::msg::Twist>(params_.getParameter<std::string>("vel_control_topic"), 10U, &SlamwareRosSdkServer::msgCbRobotControl_);
            subRobotControlNoLimit_ = subscribe_T_<geometry_msgs::msg::Twist>("cmd_vel_no_limit", 10U, &SlamwareRosSdkServer::msgCbRobotControlNoLimit_);
            subMoveToGoal_ = subscribe_T_<geometry_msgs::msg::PoseStamped>(params_.getParameter<std::string>("goal_topic"), 1U, &SlamwareRosSdkServer::msgCbMoveToGoal_);
            
            subSyncMap_ = subscribe_T_<slamware_ros_sdk::msg::SyncMapRequest>("/slamware_ros_sdk_server_node/sync_map", 1U, &SlamwareRosSdkServer::msgCbSyncMap_);
            subSetPose_ = subscribe_T_<geometry_msgs::msg::Pose>("/slamware_ros_sdk_server_node/set_pose", 1U, &SlamwareRosSdkServer::msgCbSetPose_);

            subRecoverLocalization_ = subscribe_T_<slamware_ros_sdk::msg::RecoverLocalizationRequest>("/slamware_ros_sdk_server_node/recover_localization", 1U, &SlamwareRosSdkServer::msgCbRecoverLocalization_);
            subClearMap_ = subscribe_T_<slamware_ros_sdk::msg::ClearMapRequest>("/slamware_ros_sdk_server_node/clear_map", 1U, &SlamwareRosSdkServer::msgCbClearMap_);
            subSetMapUpdate_ = subscribe_T_<slamware_ros_sdk::msg::SetMapUpdateRequest>("/slamware_ros_sdk_server_node/set_map_update", 1U, &SlamwareRosSdkServer::msgCbSetMapUpdate_);
            subSetMapLocalization_ = subscribe_T_<slamware_ros_sdk::msg::SetMapLocalizationRequest>("/slamware_ros_sdk_server_node/set_map_localization", 1U, &SlamwareRosSdkServer::msgCbSetMapLocalization_);

            subMoveByDirection_ = subscribe_T_<slamware_ros_sdk::msg::MoveByDirectionRequest>("/slamware_ros_sdk_server_node/move_by_direction", 1U, &SlamwareRosSdkServer::msgCbMoveByDirection_);
            subMoveByTheta_ = subscribe_T_<slamware_ros_sdk::msg::MoveByThetaRequest>("/slamware_ros_sdk_server_node/move_by_theta", 1U, &SlamwareRosSdkServer::msgCbMoveByTheta_);
            subMoveTo_ = subscribe_T_<slamware_ros_sdk::msg::MoveToRequest>("/slamware_ros_sdk_server_node/move_to", 1U, &SlamwareRosSdkServer::msgCbMoveTo_);
            subMoveToLocations_ = subscribe_T_<slamware_ros_sdk::msg::MoveToLocationsRequest>("/slamware_ros_sdk_server_node/move_to_locations", 1U, &SlamwareRosSdkServer::msgCbMoveToLocations_);
            subRotateTo_ = subscribe_T_<slamware_ros_sdk::msg::RotateToRequest>("/slamware_ros_sdk_server_node/rotate_to", 1U, &SlamwareRosSdkServer::msgCbRotateTo_);
            subRotate_ = subscribe_T_<slamware_ros_sdk::msg::RotateRequest>("/slamware_ros_sdk_server_node/rotate", 1U, &SlamwareRosSdkServer::msgCbRotate_);

            subGoHome_ = subscribe_T_<slamware_ros_sdk::msg::GoHomeRequest>("/slamware_ros_sdk_server_node/go_home", 1U, &SlamwareRosSdkServer::msgCbGoHome_);
            subCancelAction_ = subscribe_T_<slamware_ros_sdk::msg::CancelActionRequest>("/slamware_ros_sdk_server_node/cancel_action", 1U, &SlamwareRosSdkServer::msgCbCancelAction_);

            subAddLine_ = subscribe_T_<slamware_ros_sdk::msg::AddLineRequest>("/slamware_ros_sdk_server_node/add_line", 1U, &SlamwareRosSdkServer::msgCbAddLine_);
            subAddLines_ = subscribe_T_<slamware_ros_sdk::msg::AddLinesRequest>("/slamware_ros_sdk_server_node/add_lines", 1U, &SlamwareRosSdkServer::msgCbAddLines_);
            subRemoveLine_ = subscribe_T_<slamware_ros_sdk::msg::RemoveLineRequest>("/slamware_ros_sdk_server_node/remove_line", 1U, &SlamwareRosSdkServer::msgCbRemoveLine_);
            subClearLines_ = subscribe_T_<slamware_ros_sdk::msg::ClearLinesRequest>("/slamware_ros_sdk_server_node/clear_lines", 1U, &SlamwareRosSdkServer::msgCbClearLines_);
            subMoveLine_ = subscribe_T_<slamware_ros_sdk::msg::MoveLineRequest>("/slamware_ros_sdk_server_node/move_line", 1U, &SlamwareRosSdkServer::msgCbMoveLine_);
            subMoveLines_ = subscribe_T_<slamware_ros_sdk::msg::MoveLinesRequest>("/slamware_ros_sdk_server_node/move_lines", 1U, &SlamwareRosSdkServer::msgCbMoveLines_);
        }

        // init all services
        {
            srvSyncGetStcm_ = advertiseService_T_<slamware_ros_sdk::srv::SyncGetStcm>("sync_get_stcm", &SlamwareRosSdkServer::srvCbSyncGetStcm_);
            srvSyncSetStcm_ = advertiseService_T_<slamware_ros_sdk::srv::SyncSetStcm>("sync_set_stcm", &SlamwareRosSdkServer::srvCbSyncSetStcm_);
        }
        return true;
    }

    void SlamwareRosSdkServer::cleanup_()
    {
        if (isRunning_())
            requestStop();
        waitUntilStopped();

        safeReleaseSlamwarePlatform_();

        // de-init all services
        {
            // srvSyncGetStcm_ = ros::ServiceServer();
            // srvSyncSetStcm_ = ros::ServiceServer();
        }

        // de-init all subscriptions
        {
            // subRobotControl_ = ros::Subscriber();
            // subRobotControlNoLimit_ = ros::Subscriber();
            // subMoveToGoal_ = ros::Subscriber();
            
            // subSyncMap_ = ros::Subscriber();
            // subSetPose_ = ros::Subscriber();

            // subRecoverLocalization_ = ros::Subscriber();
            // subClearMap_ = ros::Subscriber();
            // subSetMapUpdate_ = ros::Subscriber();
            // subSetMapLocalization_ = ros::Subscriber();

            // subMoveByDirection_ = ros::Subscriber();
            // subMoveByTheta_ = ros::Subscriber();
            // subMoveTo_ = ros::Subscriber();
            // subMoveToLocations_ = ros::Subscriber();
            // subRotateTo_ = ros::Subscriber();
            // subRotate_ = ros::Subscriber();

            // subGoHome_ = ros::Subscriber();
            // subCancelAction_ = ros::Subscriber();

            // subAddLine_ = ros::Subscriber();
            // subAddLines_ = ros::Subscriber();
            // subRemoveLine_ = ros::Subscriber();
            // subClearLines_ = ros::Subscriber();
            // subMoveLine_ = ros::Subscriber();
            // subMoveLines_ = ros::Subscriber();
        }

        // de-init all publishers
        {
            serverWorkers_.clear();
        }

        {
            boost::lock_guard<boost::mutex> lkGuard(workDatLock_);
            workDat_.reset();
        }

        state_.store(ServerStateNotInit);
    }

    void SlamwareRosSdkServer::workThreadFun_()
    {
        BOOST_ASSERT(ServerStateRunning == state_.load());
        RCLCPP_INFO(rclcpp::get_logger("slamware ros sdk server"), "SlamwareRosSdkServer, work thread begin.");

        while (shouldContinueRunning_()
            && rclcpp::ok() // ros::ok()
            )
        {
            if (!safeIsSlamwarePlatformConnected_())
            {
                loopTryConnectToSlamwarePlatform_();

                if (!safeIsSlamwarePlatformConnected_())
                    continue;
            }
            BOOST_ASSERT(safeIsSlamwarePlatformConnected_());

            try
            {
                loopWork_();
            }
            catch (const std::exception& excp)
            {
                RCLCPP_FATAL(rclcpp::get_logger("slamware ros sdk server"), "loopWork_(), exception: %s.", excp.what());
            }
            catch (...)
            {
                RCLCPP_FATAL(rclcpp::get_logger("slamware ros sdk server"), "loopWork_(), unknown exception.");
            }

            safeReleaseSlamwarePlatform_();
            if (shouldContinueRunning_())
            {
                const std::uint32_t maxSleepMs = (1000u * 3u);
                RCLCPP_INFO(rclcpp::get_logger("slamware ros sdk server"), "wait %u ms to reconnect and restart work loop.", maxSleepMs);
                roughSleepWait_(maxSleepMs, 100U);
            }
        }

        RCLCPP_INFO(rclcpp::get_logger("slamware ros sdk server"), "SlamwareRosSdkServer, work thread end.");
        state_.store(ServerStateStopped);
    }

    void SlamwareRosSdkServer::roughSleepWait_(std::uint32_t maxSleepMs, std::uint32_t onceSleepMs)
    {
        const auto durOnceSleep = boost::chrono::milliseconds(onceSleepMs);
        auto tpNow = boost::chrono::steady_clock::now();
        const auto maxSleepTimepoint = tpNow + boost::chrono::milliseconds(maxSleepMs);
        while (shouldContinueRunning_()
            && tpNow < maxSleepTimepoint
            )
        {
            boost::this_thread::sleep_for(durOnceSleep);
            tpNow = boost::chrono::steady_clock::now();
        }
    }

    void SlamwareRosSdkServer::loopTryConnectToSlamwarePlatform_()
    {
        std::uint32_t tryCnt = 1;
        while (shouldContinueRunning_())
        {
            {
                std::string ip = params_.getParameter<std::string>("ip_address");
                int port = params_.getParameter<int>("robot_port");
                RCLCPP_INFO(rclcpp::get_logger("slamware ros sdk server"), "try to connect to %s:%d, tryCnt: %u.", ip.c_str(), port, tryCnt);
                auto pltfm = connectSlamwarePlatform_(ip, port);
                if (pltfm)
                {
                    RCLCPP_INFO(rclcpp::get_logger("slamware ros sdk server"), "connect to %s:%d, OK, tryCnt: %u.", ip.c_str(), port, tryCnt);
                    safeSetSlamwarePlatform_(pltfm);
                    return;
                }
                int iVal = params_.getParameter<int>("reconn_wait_ms");
                RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "connect to %s:%d, FAILED, tryCnt: %u, wait %d ms to retry."
                    , ip.c_str(), port
                    , tryCnt, iVal);
            }
            int iVal = params_.getParameter<int>("reconn_wait_ms");
            const std::uint32_t maxSleepMs = (0 <= iVal ? (std::uint32_t)iVal : 0U);
            roughSleepWait_(maxSleepMs, 100U);
            ++tryCnt;
        }
    }

    bool SlamwareRosSdkServer::reinitWorkLoop_(slamware_platform_t& pltfm)
    {
        const std::uint32_t cntWorkers = static_cast<std::uint32_t>(serverWorkers_.size());

        RCLCPP_INFO(rclcpp::get_logger("slamware ros sdk server"), "reset all %u workers on work loop begin.", cntWorkers);
        for (auto it = serverWorkers_.begin(), itEnd = serverWorkers_.end(); itEnd != it; ++it)
        {
            const auto& svrWk = (*it);
            const auto wkName = svrWk->getWorkerName();
            try
            {
                svrWk->resetOnWorkLoopBegin();
            }
            catch (const std::exception& excp)
            {
                RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker: %s, resetOnWorkLoopBegin(), exception: %s.", wkName.c_str(), excp.what());
                return false;
            }
        }

        const std::uint32_t maxSleepMs = (1000u * 2u);
        const std::uint32_t maxLoopTryCnt = 3;
        for (std::uint32_t t = 1; (t <= maxLoopTryCnt && shouldContinueRunning_()); ++t)
        {
            std::uint32_t cntOk = 0;
            for (auto it = serverWorkers_.begin(), itEnd = serverWorkers_.end(); itEnd != it; ++it)
            {
                const auto& svrWk = (*it);
                const auto wkName = svrWk->getWorkerName();
                try
                {
                    if (svrWk->isWorkLoopInitOk())
                    {
                        ++cntOk;
                    }
                    else
                    {
                        if (svrWk->reinitWorkLoop(pltfm))
                            ++cntOk;
                        else
                            RCLCPP_WARN(rclcpp::get_logger("slamware ros sdk server"), "failed to init work loop, woker: %s.", wkName.c_str());
                    }
                }
                catch (const rpos::robot_platforms::ConnectionLostException& excp)
                {
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker: %s, reinitWorkLoop(), exception: %s.", wkName.c_str(), excp.what());
                    throw;
                }
                catch (const rpos::robot_platforms::ConnectionTimeOutException& excp)
                {
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker: %s, reinitWorkLoop(), exception: %s.", wkName.c_str(), excp.what());
                    throw;
                }
                catch (const rpos::robot_platforms::ConnectionFailException& excp)
                {
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker: %s, reinitWorkLoop(), exception: %s.", wkName.c_str(), excp.what());
                    throw;
                }
                catch (const std::exception& excp)
                {
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker: %s, reinitWorkLoop(), exception: %s.", wkName.c_str(), excp.what());
                }
            }
            // check if all workers are ok.            
            if (cntWorkers == cntOk)
            {
                return true;
            }
            else if (t < maxLoopTryCnt)
            {
                RCLCPP_WARN(rclcpp::get_logger("slamware ros sdk server"), "(%u / %u) cntWorkers: %u, cntOk: %u, wait %u ms to retry.", t, maxLoopTryCnt, cntWorkers, cntOk, maxSleepMs);
                roughSleepWait_(maxSleepMs, 100U);
            }
            else
            {
                RCLCPP_WARN(rclcpp::get_logger("slamware ros sdk server"), "(%u / %u) cntWorkers: %u, cntOk: %u.", t, maxLoopTryCnt, cntWorkers, cntOk);
            }
        }
        return false;
    }

    void SlamwareRosSdkServer::loopWork_()
    {
        auto pltfm = safeGetSlamwarePlatform_();
        BOOST_ASSERT(pltfm);

        if (reinitWorkLoop_(pltfm))
        {
            RCLCPP_INFO(rclcpp::get_logger("slamware ros sdk server"), "successed to reinit all workers on work loop begin.");
        }
        else
        {
            RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "failed or cancelled to reinit work loop.");
            return;
        }

        while (shouldContinueRunning_())
        {
            boost::chrono::steady_clock::time_point minNextTriggerTimepoint = boost::chrono::steady_clock::now() + boost::chrono::milliseconds(100U);

            for (auto it = serverWorkers_.begin(), itEnd = serverWorkers_.end(); itEnd != it; ++it)
            {
                const auto& svrWk = (*it);
                const auto wkName = svrWk->getWorkerName();
                bool shouldReconnect = false;
                try
                {
                    svrWk->checkToPerform(pltfm);
                }
                catch (const rpos::robot_platforms::ConnectionLostException& excp)
                {
                    shouldReconnect = true;
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker name: %s, exception: %s.", wkName.c_str(), excp.what());
                }
                catch (const rpos::robot_platforms::ConnectionTimeOutException& excp)
                {
                    shouldReconnect = true;
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker name: %s, exception: %s.", wkName.c_str(), excp.what());
                }
                catch (const rpos::robot_platforms::ConnectionFailException& excp)
                {
                    shouldReconnect = true;
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker name: %s, exception: %s.", wkName.c_str(), excp.what());
                }
                catch (const rpos::robot_platforms::OperationFailException& excp)
                {
                    RCLCPP_WARN(rclcpp::get_logger("slamware ros sdk server"), "worker name: %s, exception: %s.", wkName.c_str(), excp.what());
                }
                catch (const rpos::system::detail::ExceptionBase& excp)
                {
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker name: %s, exception: %s.", wkName.c_str(), excp.what());
                }
                catch (const std::exception& excp)
                {
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker name: %s, exception: %s.", wkName.c_str(), excp.what());
                }
                catch (...)
                {
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "worker name: %s, unknown exception.", wkName.c_str());
                }

                if (shouldReconnect)
                {
                    RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "it should reconnect to slamware.");
                    return;
                }

                const auto tmpNextTp = svrWk->getNextTimepointToTrigger();
                if (tmpNextTp < minNextTriggerTimepoint)
                    minNextTriggerTimepoint = tmpNextTp;
            }

            auto tpNow = boost::chrono::steady_clock::now();
            if (tpNow <= minNextTriggerTimepoint)
            {
                const auto durSleep = boost::chrono::duration_cast<boost::chrono::milliseconds>(minNextTriggerTimepoint - tpNow);
                boost::this_thread::sleep_for(durSleep);
            }
        }
    }

    //////////////////////////////////////////////////////////////////////////

    template<class MsgT>
    void SlamwareRosSdkServer::msgCbWrapperFun_T_(typename msg_cb_help_t<MsgT>::msg_cb_perform_fun_t mfpCbPerform
        , const std::string& msgTopic
        ,  typename msg_cb_help_t<MsgT>::msg_shared_ptr  msg
        )
    {
        BOOST_ASSERT(nullptr != mfpCbPerform);
        auto pltfm = safeGetSlamwarePlatform_();
        if (!pltfm)
        {
            RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "process msgTopic: %s, not connected.", msgTopic.c_str());
            return;
        }

        try
        {
            (this->*mfpCbPerform)(pltfm, msg);
        }
        catch (const std::exception& excp)
        {
            RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "process msgTopic: %s, exception: %s.", msgTopic.c_str(), excp.what());
        }
        catch (...)
        {
            RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "process msgTopic: %s, unknown exception.", msgTopic.c_str());
        }
    }

    template<class MsgT>
    typename rclcpp::Subscription<MsgT>::SharedPtr SlamwareRosSdkServer::subscribe_T_(const std::string& msgTopic
        , std::uint32_t queueSize
        , typename msg_cb_help_t<MsgT>::msg_cb_perform_fun_t mfpCbPerform
        )
    {
        typedef msg_cb_help_t<MsgT>     TheMsgCbHelpT;

        typename TheMsgCbHelpT::ros_cb_fun_t rosCbFun(
            boost::bind(&SlamwareRosSdkServer::msgCbWrapperFun_T_<MsgT>, this, mfpCbPerform, msgTopic, _1)
            );
        return nh_->create_subscription<MsgT>(msgTopic, queueSize, rosCbFun);
    }

    void SlamwareRosSdkServer::msgCbRobotControl_(slamware_platform_t& pltfm, const geometry_msgs::msg::Twist::SharedPtr msg)
    {
        if(velocityControllAction_.isEmpty())
        {
            try
            {
                velocityControllAction_ = slamwarePltfm_.velocityControl();
            }
            catch(const std::exception& e)
            {
                RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "Can't construct velocity controll action:%s.",e.what());
                return;
            }            
        }
        else
        {
            try
            {
                velocityControllAction_.setVelocity(msg->linear.x, msg->linear.y, msg->angular.z);
            }
            catch(const std::exception& e)
            {
                RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "Can't set velocity:%s.",e.what());
                velocityControllAction_ = rpos::actions::VelocityControlMoveAction();
                return;
            }
        }
    }

    void SlamwareRosSdkServer::msgCbRobotControlNoLimit_(slamware_platform_t& pltfm, const geometry_msgs::msg::Twist::SharedPtr msg)
    {
        if(velocityControllAction_.isEmpty())
        {
            try
            {
                velocityControllAction_ = slamwarePltfm_.velocityControl(rpos::features::motion_planner::NotMonitored);
            }
            catch(const std::exception& e)
            {
                RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "Can't construct not monitored velocity controll action:%s.",e.what());
                return;
            }            
        }
        else
        {
            try
            {
                velocityControllAction_.setVelocity(msg->linear.x, msg->linear.y, msg->angular.z);
            }
            catch(const std::exception& e)
            {
                RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "Can't set not monitored velocity:%s.",e.what());
                velocityControllAction_ = rpos::actions::VelocityControlMoveAction();
                return;
            }
        } 
    }

    void SlamwareRosSdkServer::msgCbMoveToGoal_(slamware_platform_t& pltfm, const geometry_msgs::msg::PoseStamped::SharedPtr msg)
    {
        const rpos::core::Location tLoc(msg->pose.position.x, msg->pose.position.y);
        const float fYaw = tf2::getYaw(msg->pose.orientation);
        
        rpos::features::motion_planner::MoveOptions optMove;
        optMove.flag = rpos::features::motion_planner::MoveOptionFlag(rpos::features::motion_planner::MoveOptionFlagMilestone
            | rpos::features::motion_planner::MoveOptionFlagPrecise
            | rpos::features::motion_planner::MoveOptionFlagWithYaw
            );
        optMove.yaw = fYaw;
        pltfm.moveTo(tLoc, optMove);
    }

    void SlamwareRosSdkServer::msgCbSyncMap_(slamware_platform_t& /*pltfm*/, const slamware_ros_sdk::msg::SyncMapRequest::SharedPtr /*msg*/)
    {
        requestSyncMap();
    }

    void SlamwareRosSdkServer::msgCbSetPose_(slamware_platform_t& pltfm, const geometry_msgs::msg::Pose::SharedPtr msg)
    {
        rpos::core::Pose robotPose;
        rosMsgToSltc(*msg, robotPose);

        pltfm.setPose(robotPose);
    }

    void SlamwareRosSdkServer::msgCbRecoverLocalization_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::RecoverLocalizationRequest::SharedPtr msg)
    {
        rpos::core::RectangleF area;
        rosMsgToSltc(msg->area, area);

        rpos::features::motion_planner::RecoverLocalizationOptions options;
        rosMsgToSltc(msg->options, options);

        pltfm.recoverLocalization(area, options);
    }

    void SlamwareRosSdkServer::msgCbClearMap_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::ClearMapRequest::SharedPtr msg)
    {
        rpos::features::location_provider::MapKind kind;
        rosMsgToSltc(msg->kind, kind);

        pltfm.clearMap(kind);
    }

    void SlamwareRosSdkServer::msgCbSetMapUpdate_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::SetMapUpdateRequest::SharedPtr msg)
    {
        rpos::features::location_provider::MapKind kind;
        rosMsgToSltc(msg->kind, kind);

        pltfm.setMapUpdate(msg->enabled, kind);
    }

    void SlamwareRosSdkServer::msgCbSetMapLocalization_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::SetMapLocalizationRequest::SharedPtr msg)
    {
        pltfm.setMapLocalization(msg->enabled);
    }

    void SlamwareRosSdkServer::msgCbMoveByDirection_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::MoveByDirectionRequest::SharedPtr msg)
    {
        rpos::core::ACTION_DIRECTION eDir;
        rosMsgToSltc(msg->direction, eDir);
        const auto tDir = rpos::core::Direction(eDir);

        rpos::features::motion_planner::MoveOptions tOpts;
        rosMsgToSltc(msg->options, tOpts);

        pltfm.moveBy(tDir, tOpts);
    }

    void SlamwareRosSdkServer::msgCbMoveByTheta_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::MoveByThetaRequest::SharedPtr msg)
    {
        rpos::features::motion_planner::MoveOptions tOpts;
        rosMsgToSltc(msg->options, tOpts);

        pltfm.moveBy(msg->theta, tOpts);
    }

    void SlamwareRosSdkServer::msgCbMoveTo_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::MoveToRequest::SharedPtr msg)
    {
        rpos::core::Location tLoc;
        rosMsgToSltc(msg->location, tLoc);

        rpos::features::motion_planner::MoveOptions tOpts;
        rosMsgToSltc(msg->options, tOpts);
        tOpts.yaw = msg->yaw;

        if (tOpts.hasFlag(rpos::features::motion_planner::MoveOptionFlagKeyPoints))
        {
            tOpts.mode = rpos::features::motion_planner::NavigationModeStrictVirtualTrack;
            if(tOpts.hasFlag(rpos::features::motion_planner::MoveOptionFlagKeyPointsWithOA))
            {
                tOpts.mode = rpos::features::motion_planner::NavigationModePriorityVirtualTrack;
            }
        }
        
        pltfm.moveTo(tLoc, tOpts);
    }

    void SlamwareRosSdkServer::msgCbMoveToLocations_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::MoveToLocationsRequest::SharedPtr msg)
    {
        std::vector<rpos::core::Location> vLocs;
        rosMsgToSltc(msg->locations, vLocs);

        rpos::features::motion_planner::MoveOptions tOpts;
        rosMsgToSltc(msg->options, tOpts);
        tOpts.yaw = msg->yaw;

        if (tOpts.hasFlag(rpos::features::motion_planner::MoveOptionFlagKeyPoints))
        {
            tOpts.mode = rpos::features::motion_planner::NavigationModeStrictVirtualTrack;
            if(tOpts.hasFlag(rpos::features::motion_planner::MoveOptionFlagKeyPointsWithOA))
            {
                tOpts.mode = rpos::features::motion_planner::NavigationModePriorityVirtualTrack;
            }
        }

        pltfm.moveTo(vLocs, tOpts);
    }

    void SlamwareRosSdkServer::msgCbRotateTo_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::RotateToRequest::SharedPtr msg)
    {
        rpos::core::Rotation orientation;
        rosMsgToSltc(msg->orientation, orientation);

        rpos::features::motion_planner::RotateActionOptions tOpts;
        //rosMsgToSltc(msg->options, tOpts);

        pltfm.rotateTo(orientation, tOpts);
    }

    void SlamwareRosSdkServer::msgCbRotate_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::RotateRequest::SharedPtr msg)
    {
        rpos::core::Rotation rotation;
        rosMsgToSltc(msg->rotation, rotation);
        rpos::features::motion_planner::RotateActionOptions tOpts;
        //rosMsgToSltc(msg->options, tOpts);

        pltfm.rotate(rotation, tOpts);
    }

    void SlamwareRosSdkServer::msgCbGoHome_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::GoHomeRequest::SharedPtr /*msg*/)
    {
        pltfm.goHome();
    }

    void SlamwareRosSdkServer::msgCbCancelAction_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::CancelActionRequest::SharedPtr /*msg*/)
    {
        auto tAct = pltfm.getCurrentAction();
        if (tAct)
            tAct.cancel();
    }

    void SlamwareRosSdkServer::msgCbAddLine_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::AddLineRequest::SharedPtr msg)
    {
        rpos::features::artifact_provider::ArtifactUsage tUsage;
        rosMsgToSltc(msg->usage, tUsage);

        rpos::core::Line tLine;
        rosMsgToSltc(msg->line, tLine);

        pltfm.addLine(tUsage, tLine);
    }

    void SlamwareRosSdkServer::msgCbAddLines_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::AddLinesRequest::SharedPtr msg)
    {
        rpos::features::artifact_provider::ArtifactUsage tUsage;
        rosMsgToSltc(msg->usage, tUsage);

        std::vector<rpos::core::Line> vLines;
        rosMsgToSltc(msg->lines, vLines);

        pltfm.addLines(tUsage, vLines);
    }

    void SlamwareRosSdkServer::msgCbRemoveLine_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::RemoveLineRequest::SharedPtr msg)
    {
        rpos::features::artifact_provider::ArtifactUsage tUsage;
        rosMsgToSltc(msg->usage, tUsage);

        pltfm.removeLineById(tUsage, msg->id);
    }

    void SlamwareRosSdkServer::msgCbClearLines_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::ClearLinesRequest::SharedPtr msg)
    {
        rpos::features::artifact_provider::ArtifactUsage tUsage;
        rosMsgToSltc(msg->usage, tUsage);

        pltfm.clearLines(tUsage);
    }

    void SlamwareRosSdkServer::msgCbMoveLine_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::MoveLineRequest::SharedPtr msg)
    {
        rpos::features::artifact_provider::ArtifactUsage tUsage;
        rosMsgToSltc(msg->usage, tUsage);

        rpos::core::Line tLine;
        rosMsgToSltc(msg->line, tLine);

        pltfm.moveLine(tUsage, tLine);
    }

    void SlamwareRosSdkServer::msgCbMoveLines_(slamware_platform_t& pltfm, const slamware_ros_sdk::msg::MoveLinesRequest::SharedPtr msg)
    {
        rpos::features::artifact_provider::ArtifactUsage tUsage;
        rosMsgToSltc(msg->usage, tUsage);

        std::vector<rpos::core::Line> vLines;
        rosMsgToSltc(msg->lines, vLines);

        pltfm.moveLines(tUsage, vLines);
    }

    //////////////////////////////////////////////////////////////////////////

    template<class SrvMsgT>
    bool SlamwareRosSdkServer::srvCbWrapperFun_T_(typename srv_cb_help_t<SrvMsgT>::srv_cb_perform_fun_t mfpCbPerform
        , const std::string& srvMsgTopic
        , typename srv_cb_help_t<SrvMsgT>::request_t req
        , typename srv_cb_help_t<SrvMsgT>::response_t resp
        )
    {
        BOOST_ASSERT(nullptr != mfpCbPerform);
        auto pltfm = safeGetSlamwarePlatform_();
        if (!pltfm)
        {
            RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "process request: %s, not connected.", srvMsgTopic.c_str());
            return false;
        }

        try
        {
            return (this->*mfpCbPerform)(pltfm, req, resp);
        }
        catch (const std::exception& excp)
        {
            RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "process request: %s, exception: %s.", srvMsgTopic.c_str(), excp.what());
        }
        catch (...)
        {
            RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "process request: %s, unknown exception.", srvMsgTopic.c_str());
        }
        return false;
    }

    template<class SrvMsgT>
    typename rclcpp::Service<SrvMsgT>::SharedPtr SlamwareRosSdkServer::advertiseService_T_(const std::string& srvMsgTopic
        , typename srv_cb_help_t<SrvMsgT>::srv_cb_perform_fun_t mfpCbPerform
        )
    {
        typedef srv_cb_help_t<SrvMsgT>     TheSrvCbHelpT;

        typename TheSrvCbHelpT::ros_cb_fun_t rosCbFun(
            boost::bind(&SlamwareRosSdkServer::srvCbWrapperFun_T_<SrvMsgT>, this, mfpCbPerform, srvMsgTopic, _1, _2)
            );
        return nh_->create_service<SrvMsgT>(srvMsgTopic, rosCbFun); 
    }

    bool SlamwareRosSdkServer::srvCbSyncGetStcm_(slamware_platform_t& pltfm, slamware_ros_sdk::srv::SyncGetStcm::Request::SharedPtr req, slamware_ros_sdk::srv::SyncGetStcm::Response::SharedPtr resp)
    {
        std::string strTmp;
        rpos::system::io::MemoryWriteStream tMemWS(1024 * 1024 * 8);
        try
        {
            const auto cmpstMap = pltfm.getCompositeMap();

            rpos::robot_platforms::objects::CompositeMapWriter tWriter;
            if (!tWriter.saveStream(strTmp, tMemWS, cmpstMap))
            {
                RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "srvCbSyncGetStcm_(), saveStream(), %s.", strTmp.c_str());
                return false;
            }
        }
        catch (const std::exception& excp)
        {
            RCLCPP_WARN(rclcpp::get_logger("slamware ros sdk server"), "srvCbSyncGetStcm_(), exception: %s.", excp.what());
            return false;
        }

        resp->raw_stcm.resize(tMemWS.size());
        std::memcpy(resp->raw_stcm.data(), tMemWS.buffer(), tMemWS.size());
        return true;
    }

    bool SlamwareRosSdkServer::srvCbSyncSetStcm_(slamware_platform_t& pltfm, slamware_ros_sdk::srv::SyncSetStcm::Request::SharedPtr req, slamware_ros_sdk::srv::SyncSetStcm::Response::SharedPtr resp)
    {
        rpos::core::Pose robotPose;
        rosMsgToSltc(req->robot_pose, robotPose);

        std::string strTmp;
        boost::shared_ptr<rpos::robot_platforms::objects::CompositeMap> spSmpstMap;
        {
            rpos::system::io::MemoryReadStream tMemRS((const void*)req->raw_stcm.data()
                , req->raw_stcm.size()
                , rpos::system::io::MemoryReadStream::MemoryReadStreamFlagBorrowBuffer
                );
            rpos::robot_platforms::objects::CompositeMapReader tReader;
            spSmpstMap = tReader.loadStream(strTmp, tMemRS);
            if (!spSmpstMap)
            {
                RCLCPP_ERROR(rclcpp::get_logger("slamware ros sdk server"), "srvCbSyncSetStcm_(), loadStream(), %s.", strTmp.c_str());
                return false;
            }
        }
        BOOST_ASSERT(spSmpstMap);

        try
        {
            pltfm.clearMap();
            auto tAct = pltfm.getCurrentAction();
            if (tAct)
                tAct.cancel();
            pltfm.setMapLocalization(false);
            pltfm.setMapUpdate(false);

            pltfm.setCompositeMap(*spSmpstMap, robotPose);

            pltfm.setMapLocalization(true);
        }
        catch (const std::exception& excp)
        {
            RCLCPP_WARN(rclcpp::get_logger("slamware ros sdk server"), "srvCbSyncSetStcm_(), exception: %s.", excp.what());
            return false;
        }

        requestSyncMap();
        return true;
    }

    rclcpp::Time SlamwareRosSdkServer::localTimestampToRosTime(uint64_t timestamp_us)
    {
        auto rosTimeus = timestamp_us + rosTimeOffset_;

        return rclcpp::Time(rosTimeus*1000);
    }

    //////////////////////////////////////////////////////////////////////////

}
