<launch>
  <arg name="ip_address" default="127.0.0.1" />
  <arg name="move_base_goal_topic" default="/move_base_simple/goal"/>
  <arg name="raw_ladar_data" default="false" />

  <node pkg="slamware_ros_sdk" exec="slamware_ros_sdk_server_node" name="slamware_ros_sdk_server_node" output="both">

  <param name="ip_address"               value="$(var ip_address)"/>
  <param name="angle_compensate"         value="true"/>
  <param name="fixed_odom_map_tf"        value="true"/>
  <param name="raw_ladar_data"           value="$(var raw_ladar_data)"/>

  <param name="robot_frame"              value="base_link"/>
  <param name="odom_frame"               value="odom"/>
  <param name="laser_frame"              value="laser"/>
  <param name="map_frame"                value="map"/>
  <param name="robot_pose_frame"         value="robot_pose"/>

  <param name="odometry_pub_period"      value="-0.05"/>
  <param name="robot_pose_pub_period"    value="0.05"/>
  <param name="scan_pub_period"          value="-0.1"/>
  <param name="map_pub_period"           value="0.5"/>
  <param name="path_pub_period"          value="-0.1"/>
  <param name="imu_raw_data_period"      value="-0.1"/>
  <param name= "virtual_walls_pub_period"   value= "-1.0"/>
  <param name= "virtual_tracks_pub_period"  value= "-1.0"/>
  <param name= "basic_sensors_values_pub_period"   value= "-1.0"/>

  <param name= "vel_control_topic"       value= "ros_sdk_cmd_vel"/>
  <param name= "ladar_data_clockwise"    value= "true"/>

  <param name = "pub_accumulate_odometry"     value = "false"/>
  <param name = "robot_pose_topic"            value = "robot_pose"/>
  <param name = "map_topic"            value = "/map"/>
   
  </node>

  <!-- <node pkg="tf2_ros" exec="static_transform_publisher" name="map2odom" args="0 0 0 0 0 0 1 slamware_map odom 100"/>
  <node pkg="tf2_ros" exec="static_transform_publisher" name="map2robotpose" args="0 0 0 0 0 0 1 slamware_map robot_pose 100"/>
  <node pkg="tf2_ros" exec="static_transform_publisher" name="base2laser" args="0 0 0 0 0 0 1 base_link laser 100"/> -->

</launch>
