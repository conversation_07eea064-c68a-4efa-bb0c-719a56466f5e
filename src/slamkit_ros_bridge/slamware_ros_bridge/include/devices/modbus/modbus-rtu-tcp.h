/*
 * Copyright © Stépha<PERSON> <<EMAIL>>
 *
 * SPDX-License-Identifier: LGPL-2.1-or-later
 */

#ifndef MODBUS_RTU_TCP_H
#define MODBUS_RTU_TCP_H

#include "modbus.h"

MODBUS_BEGIN_DECLS

#if defined(_WIN32) && !defined(__CYGWIN__)
/* Win32 with MinG<PERSON>, supplement to <errno.h> */
#include <winsock2.h>
#if !defined(ECONNRESET)
#define ECONNRESET WSAECONNRESET
#endif
#if !defined(ECONNREFUSED)
#define ECONNREFUSED WSAECONNREFUSED
#endif
#if !defined(ETIMEDOUT)
#define ETIMEDOUT WSAETIMEDOUT
#endif
#if !defined(ENOPROTOOPT)
#define ENOPROTOOPT WSAENOPROTOOPT
#endif
#if !defined(EINPROGRESS)
#define EINPROGRESS WSAEINPROGRESS
#endif
#endif

#define MODBUS_RTU_TCP_DEFAULT_PORT 502
#define MODBUS_RTU_TCP_SLAVE        0xFF

/* Modbus_Application_Protocol_V1_1b.pdf Chapter 4 Section 1 Page 5
 * TCP MODBUS ADU = 253 bytes + MBAP (7 bytes) = 260 bytes
 */

/* Modbus_Application_Protocol_V1_1b.pdf Chapter 4 Section 1 Page 5
 * RS232 / RS485 ADU = 253 bytes + slave (1 byte) + CRC (2 bytes) = 256 bytes
 */

#define MODBUS_RTU_TCP_MAX_ADU_LENGTH 256

MODBUS_API modbus_t *modbus_new_rtu_tcp(const char *ip_address, int port);
MODBUS_API int modbus_rtu_tcp_listen(modbus_t *ctx, int nb_connection);
MODBUS_API int modbus_rtu_tcp_accept(modbus_t *ctx, int *s);

MODBUS_API int modbus_send_msg_add_crc(uint8_t *req, int req_length);
MODBUS_API char * modbus_get_current_addr_in(modbus_t *ctx);

MODBUS_END_DECLS

#endif /* MODBUS_RTU_TCP_H */
