#pragma once
#include <rpos/system/util/udp_client.h>
#include <rpos/system/util/log.h>
#include <atomic>

namespace rp { namespace slamware { namespace utils {

    class RobotBasicStatus;

    class ModbusUdpClientHandler : public rpos::system::util::UdpClient<ModbusUdpClientHandler, 1024000>::EmptyUdpClientHandler
    {
    public:
        typedef rpos::system::util::UdpClient<ModbusUdpClientHandler, 1024000>::Pointer Pointer;
        virtual ~ModbusUdpClientHandler() {}

    public:
        virtual void onHostResolveFailure(Pointer client, const boost::system::error_code& ec); 
        virtual void onSendError(Pointer client, const boost::system::error_code& ec);
        virtual void onReceiveError(Pointer client, const boost::system::error_code& ec);

    private:
        static rpos::system::util::LogScope logger;
    };

    class ModbusUdpClient : public rpos::system::util::UdpClient <ModbusUdpClientHandler, 1024000> 
    {
    public:
        ModbusUdpClient(); 
        ~ModbusUdpClient();

    public:
        void connect(const std::string& host, int port);
        bool isConnected() { return connectFlag_.load(); }
        void sendRobotBasicStatusData(const RobotBasicStatus& status);

    private:
        void robotBasicStatusDataToModbusData_(const RobotBasicStatus& status, std::vector<uint8_t>& buffer);
        template<typename T>
        void addBigEndianData_(T data, int& offset, std::vector<uint8_t>& buffer);
        
    private:
        static rpos::system::util::LogScope logger;
        std::atomic<bool> connectFlag_;
    };

} } }