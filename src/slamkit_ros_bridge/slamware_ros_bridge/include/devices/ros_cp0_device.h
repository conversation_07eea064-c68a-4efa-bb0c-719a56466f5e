#pragma once

#include <rp/slamware/utils/pseudo_cp0_device.h>
#include <rpos/system/util/log.h>
#include <boost/shared_ptr.hpp>                 
#include <boost/weak_ptr.hpp>

namespace rp { namespace slamware { namespace utils {

    class DevicesManagerService;

    class RosCp0Device : public PseudoCp0Device
    {
    public:
        explicit RosCp0Device(boost::shared_ptr<DevicesManagerService> deviceManager);
        ~RosCp0Device();

    public:
        virtual void getImuAllSensorData(rpos::message::imu::ImuAllSensorData& sensorData);
 
    private:
        static rpos::system::util::LogScope logger;
        boost::weak_ptr<DevicesManagerService> deviceManager_; 
    };

}}}