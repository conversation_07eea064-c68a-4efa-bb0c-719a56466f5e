#pragma once
#include "modbus_server.h"
#include "modbus_udp_client.h"
#include "modbus_udp_server.h"
#include <rp/slamware/utils/pseudo_base_device.h>
#include <rpos/robot_platforms/slamware_core_platform.h>
#include <rpos/robot_platforms/slamware_agent_platform.h>
#include <rpos/system/util/log.h>
#include <boost/shared_ptr.hpp>                 
#include <boost/weak_ptr.hpp>
#include <thread>

namespace rp { namespace slamware { namespace utils {
 
    struct CtrlCmd{
        uint64_t timstamp;
        uint16_t localization_type;
        uint16_t map_index;
        float x;
        float y;
        float yaw;
        bool isMapChanged;
    };

    struct RobotBasicStatus{
        uint64_t timstamp;
        uint16_t state;
        uint16_t param; 
        uint32_t x;
        uint32_t y;
        uint16_t yaw;
        uint16_t map_index;
        uint16_t localization_quality;
        uint16_t omega;
        uint64_t cmd_timstamp;
    };

    enum class LocalizationState: uint16_t{
        Normal = 0,
        Mapping = 1,
        Relocalizating = 2,
        Error = 3
    };

    class ModbusBaseDevice : public PseudoBaseDevice
    {
    public:
        static const unsigned char DefaultBinaryConfig[];
        static const size_t DefaultBinaryConfigSize;

    public:
        explicit ModbusBaseDevice(const std::string& deivce_host);
        ~ModbusBaseDevice();

    public:
        virtual bool getBinaryConfig(std::uint8_t* buf, size_t* size);
        virtual bool requestMotion(const rpos::message::base::MotionRequest& request);
        virtual bool getMovementEstimation(rpos::message::Message<rpos::message::base::MovementEstimation>& estimation);
        virtual bool getExtendedSensorData(std::vector<BaseSensorData>& extendedSensorData);
        virtual bool getBaseStatus(BaseStatusData& data);
         
    private:
        bool tryConnectToPlatform();
        void initBinaryConfig_(const std::string& binaryConfigFile);
        void initDefaultBinaryConfig_();
        void worker();
        void updateRobotStatus(RobotBasicStatus& status);
        void sendStatusToClient(const RobotBasicStatus& status);
        bool isCtrlCmdUpdated(CtrlCmd& cmd);
        void onCtrlCmd(const CtrlCmd& cmd);
        bool setCurrentFloor(int mapIndex, const boost::optional<rpos::core::Pose>& pose);
        int getCurrentFloorIndex();
        void doRecoverLocalization(const rpos::core::Pose& pose, float relocalizationSize);
        void onReceiveFromUDP(int& startAddr, std::vector<uint16_t>& data);
        LocalizationState getLocalizationState();
    private:
        static rpos::system::util::LogScope logger;
        std::vector<unsigned char> binaryConfig_;
        rpos::robot_platforms::SlamwareCorePlatform core_; 
        rpos::robot_platforms::SlamwareAgentPlatform agent_;
        ModbusServer server_;
        std::thread thread_;
        bool working_;
        std::vector<rpos::robot_platforms::detail::objects::FloorIdentify> floors_;
        bool odomValid_;
        rpos::core::Pose lastPose_;
        CtrlCmd lastCtrlCmd_;
        rpos::actions::MoveAction reLocalizationAction_;
        boost::shared_ptr<ModbusUdpClient> udpClient_;
        boost::shared_ptr<ModbusUdpServer> udpServer_;
        std::string deviceHost_;
        int mapIndex_;
    };

}}}