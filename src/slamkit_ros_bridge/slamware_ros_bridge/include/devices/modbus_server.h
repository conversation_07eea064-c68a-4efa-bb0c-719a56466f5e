#pragma once
#include <thread>
#include <vector>
#include <string>
#include "modbus/modbus.h"

namespace rp { namespace slamware { namespace utils {
 
    class ModbusServer
    {
    public:
        ModbusServer();
        ~ModbusServer();

    public:
        bool start();
        void stop();
        bool readRegisters(int start_addr, int count, std::vector<uint16_t>& data);
        bool writeRegisters(int start_addr, const std::vector<uint16_t>& data);
        std::string getCurrentClient() const { return currentClient;};
    private:
        void worker();
    private:
        std::thread thread_;
        bool working_;
        modbus_mapping_t *mb_mapping;
        modbus_t *ctx;
        std::string currentClient;
    };

}}}