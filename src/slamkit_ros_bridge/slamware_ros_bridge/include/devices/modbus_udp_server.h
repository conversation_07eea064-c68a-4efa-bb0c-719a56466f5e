#pragma once
#include <rpos/system/util/udp_server.h>
#include <rpos/system/util/log.h>
#include <atomic>
#include <boost/optional.hpp>

namespace rp { namespace slamware { namespace utils {

    typedef boost::function<void(int& startAddr, std::vector<uint16_t>& data)> udpServerReceiveHandler;

    class ModbusUdpServerHandler : public rpos::system::util::UdpServer<ModbusUdpServerHandler, 1024000>::EmptyUdpServerHandler
    {
    public:
        typedef rpos::system::util::UdpServer<ModbusUdpServerHandler, 1024000>::Pointer Pointer;
        virtual ~ModbusUdpServerHandler() {}

    public:
        virtual void onReceiveError(Pointer server, const boost::system::error_code& ec, const boost::asio::ip::udp::endpoint& receiveEndpoint);
        virtual void onReceiveComplete(Pointer server, const unsigned char* buffer, size_t readBytes, const boost::asio::ip::udp::endpoint& receiveEndpoint);

    private:
        static rpos::system::util::LogScope logger;
    };

    class ModbusUdpServer : public rpos::system::util::UdpServer <ModbusUdpServerHandler, 1024000> 
    {
    public:
        friend ModbusUdpServerHandler;
        ModbusUdpServer(int port); 
        ~ModbusUdpServer();

    public:
        void registerHandler(udpServerReceiveHandler handler);
        void checkHeartbeat();
    private:
        void onResponseReceived_(const unsigned char* buffer, size_t readBytes);
        
    private:
        static rpos::system::util::LogScope logger;
        udpServerReceiveHandler receiveHandler_;
        boost::optional<uint64_t> lastRecvTime_;
    };

} } }