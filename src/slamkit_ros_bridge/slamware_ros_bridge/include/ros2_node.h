#pragma once

#include "config.h"
#include <rp/slamware/utils/pseudo_rplidar_device.h>
#include <rp/slamware/utils/pseudo_base_device.h>
#include <rpos/message/lidar_messages.h>
#include <rpos/message/base_messages.h>
#include <rpos/messaging/iceoryx_publisher.hpp>
#include <rpos/messaging/message_defines.h>
#include <rpos/core/pose.h>
#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/float64.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/vector3_stamped.hpp>
#include <tf2/LinearMath/Transform.h>
#include <mutex>
#include <boost/shared_ptr.hpp>

namespace rp { namespace slamware { namespace utils {

    class Ros2NodeBase
    {
    protected:
        Ros2NodeBase(int argc, char **argv)
        {
            rclcpp::init(argc, argv);
        }
    };

    class Ros2Node : public Ros2NodeBase
    {
    public:
        Ros2Node(int argc, char** argv, const std::string& nodeName);
        ~Ros2Node();

    public:
        void initConfig(RosNodeConfig& cfg);
        void start();
        void subscribe(std::string& msgTopic, std::uint32_t queueSize, MsgType msgType);
        void spin(bool isOnce);

        template <typename msgT>
        void advertise(std::string& msgTopic, std::uint32_t queueSize, MsgType msgType)
        {
            if (msgType == MsgTypeVelocity)
                pubVelocity_ = nh_->create_publisher<msgT>(msgTopic, queueSize);
        }

        template <typename msgT>
        void publish(const msgT& msg, MsgType msgType)
        {
            if (msgType == MsgTypeVelocity)
                pubVelocity_->publish(msg);
        }

	void clear();

    public:
        bool getLaserScan(rpos::message::lidar::LidarScan& lidarData);
        rpos::core::Vector3f getDeadReckon(uint64_t& timestamp);
        void registerLidarDevice(boost::shared_ptr<PseudoRPLidarDevice> lidarDevice){ lidarDevice_ = lidarDevice; }
        void registerBaseDevice(boost::shared_ptr<PseudoBaseDevice> baseDevice){ baseDevice_ = baseDevice; }

    private:
        void laserScanCallback_(const sensor_msgs::msg::LaserScan::ConstPtr& msg);
        void odometryCallback_(const nav_msgs::msg::Odometry::ConstPtr& msg);
        void deadReckonCallback_(const geometry_msgs::msg::Vector3Stamped::ConstPtr& msg);
        void generateTimeOffset_();
        void countOdomFrequency(const uint64_t& msgTime);
    private:
        rclcpp::Node::SharedPtr nh_;
        rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr subLaserScan_;
        rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr subOdometry_;
        rclcpp::Subscription<geometry_msgs::msg::Vector3Stamped>::SharedPtr subOdometryVector_;
        rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr pubVelocity_;

        rclcpp::Time startupROSTime_;
        uint64_t startupSteadyTime_;
        std::mutex laserDataLock_;
        rpos::message::lidar::LidarScan laserScan_;

        bool isOdometry_;
        std::mutex poseDataLock_;
        uint64_t odomTimestamp_;
        bool initEstimationFlag_;
        tf2::Transform pose_;
        tf2::Transform prePose_;
        std::vector<rpos::core::Vector3f> deadreckon_;
        int odomCount_ = 0;
        int readCount_ = 0;
        uint64_t lastCheckTimestamp_;
        int lidarCount_ = 0;
        uint64_t lastLidarTimestamp_ = 0;

        bool enable_shared_memory_;
        boost::shared_ptr<PseudoRPLidarDevice> lidarDevice_;
        boost::shared_ptr<PseudoBaseDevice> baseDevice_;
        std::shared_ptr<rpos::messaging::IceoryxPublisher<rpos::messaging::LaserScanData>> laser_pub_;
    };

}}}
