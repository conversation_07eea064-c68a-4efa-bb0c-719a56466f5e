#pragma once
#include <string>

#define ROS_DISTRO_VERSION 2

#if ROS_DISTRO_VERSION == 1
#include <ros/ros.h>
#elif ROS_DISTRO_VERSION == 2
#include <rclcpp/rclcpp.hpp>
#endif

namespace rp { namespace slamware { namespace utils {

    enum MsgType
    {
        MsgTypeScan,
        MsgTypeOdometry,
        MsgTypeDeadreckon,
        MsgTypeVelocity
    };

    struct RosNodeConfig
    {
        std::string scan_sub_topic;
        std::string odometry_sub_topic;
        std::string velocity_pub_topic;
        bool is_accumulated_odometry;
        bool enable_shared_memory_lidar;
        bool enable_modbus_base_device;
        std::string device_host;
        
        RosNodeConfig();
        void resetToDefault();
#if ROS_DISTRO_VERSION == 1
        void setBy(const ros::NodeHandle& nhRos);
#elif ROS_DISTRO_VERSION == 2
        void declare(rclcpp::Node::SharedPtr nhRos);
        void setBy(rclcpp::Node::SharedPtr nhRos); 
#endif
    };

}}}
