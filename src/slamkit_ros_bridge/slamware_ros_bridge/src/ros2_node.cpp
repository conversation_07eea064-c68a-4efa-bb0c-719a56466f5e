#include "ros2_node.h"
#include <rpos/core/angle_math.h>
#include <rpos/system/util/time_util.h>
#include <rpos/messaging/iceoryx_manager.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <cmath>
#include <boost/bind/bind.hpp>

namespace rp { namespace slamware { namespace utils {

    using namespace rpos::messaging;

    Ros2Node::Ros2Node(int argc, char** argv, const std::string& nodeName) 
        : Ros2NodeBase(argc, argv)
        , nh_(rclcpp::Node::make_shared(nodeName))
        , isOdometry_(true)
        , initEstimationFlag_(true)
        , enable_shared_memory_(false)
    {
        generateTimeOffset_();
    }

    Ros2Node::~Ros2Node()
    {
        clear();
    }
 
    void Ros2Node::clear()
    {
        subLaserScan_.reset();
        subOdometry_.reset();
        subOdometryVector_.reset();
    }
    
    void Ros2Node::initConfig(RosNodeConfig& cfg)
    {
        cfg.declare(nh_);
        cfg.setBy(nh_);
        enable_shared_memory_ = cfg.enable_shared_memory_lidar;
        RCLCPP_INFO(nh_->get_logger(), "publish velocity topic: %s", cfg.velocity_pub_topic.c_str());
        RCLCPP_INFO(nh_->get_logger(), "is modbus base device enabled: %d", cfg.enable_modbus_base_device);
        RCLCPP_INFO(nh_->get_logger(), "enable shared memory lidar: %d",  enable_shared_memory_);
        if(cfg.enable_modbus_base_device){
            RCLCPP_INFO(nh_->get_logger(), "device host: %s", cfg.device_host.c_str());
        }
    }

    void Ros2Node::start()
    {
        if(enable_shared_memory_)
        {
            laser_pub_ = IceoryxManager::createPublisher<rpos::messaging::LaserScanData>("slamware_ros_bridge","main","laser_scan");
        }
    }

    void Ros2Node::subscribe(std::string& msgTopic, std::uint32_t queueSize, MsgType msgType)
    {
        //rclcpp::QoS qos(rclcpp::QoSInitialization::from_rmw(rmw_qos_profile_default));
        //qos.transient_local().reliable();
        if (msgType == MsgTypeScan)
        {
            subLaserScan_ = nh_->create_subscription<sensor_msgs::msg::LaserScan>(msgTopic, queueSize,
            [this](const sensor_msgs::msg::LaserScan::SharedPtr msg) {
                laserScanCallback_(msg);
            });
        }
        else if (msgType == MsgTypeOdometry)
        {  
            subOdometry_ = nh_->create_subscription<nav_msgs::msg::Odometry>(msgTopic, queueSize,
            [this](const nav_msgs::msg::Odometry::SharedPtr msg) {
                odometryCallback_(msg);
            });
            isOdometry_ = true;
            RCLCPP_INFO(nh_->get_logger(), "subscribe odometry topic: %s", msgTopic.c_str());
        }
        else if ( msgType == MsgTypeDeadreckon)
        {
            subOdometryVector_ = nh_->create_subscription<geometry_msgs::msg::Vector3Stamped>(msgTopic, queueSize,
            [this](const geometry_msgs::msg::Vector3Stamped::SharedPtr msg) {
                deadReckonCallback_(msg);
            });
            isOdometry_ = false;
            RCLCPP_INFO(nh_->get_logger(), "subscribe deadreckon topic: %s", msgTopic.c_str());
        }
    }

    void Ros2Node::spin(bool isOnce)
    {
        if (isOnce)
            rclcpp::spin_some(nh_);
        else
            rclcpp::spin(nh_);
    }

    bool Ros2Node::getLaserScan(rpos::message::lidar::LidarScan& )
    {
        RCLCPP_ERROR(nh_->get_logger(), "ros rplidar is callback mode, this function should never be called");
        return false;
    }

    rpos::core::Vector3f Ros2Node::getDeadReckon(uint64_t& timestamp)
    {
        double dx = 0.0;
        double dy = 0.0;
        double dyaw = 0.0;
        if(isOdometry_)
        {
            if (initEstimationFlag_)
            {
                std::lock_guard<std::mutex> lkGuard(poseDataLock_);
                initEstimationFlag_ = false;
                prePose_ = pose_;
                timestamp = odomTimestamp_;
            }
            else
            {
                std::lock_guard<std::mutex> lkGuard(poseDataLock_);
                tf2::Transform trans = prePose_.inverseTimes(pose_);
                dx = trans.getOrigin().getX();
                dy = trans.getOrigin().getY();
                double roll, pitch;
                trans.getBasis().getRPY(roll, pitch, dyaw);
                timestamp = odomTimestamp_;
                prePose_ = pose_;
            }
        }
        else
        {
            std::vector<rpos::core::Vector3f> deadreckons;
            {
                std::lock_guard<std::mutex> lkGuard(poseDataLock_);
                deadreckons.swap(deadreckon_);
                timestamp = odomTimestamp_;
            }
            for(auto odom : deadreckons)
            { 
                double costheta = cos(dyaw);
                double sinTheta = sin(dyaw);
                dx += odom.x() * costheta - odom.y()* sinTheta;
                dy += odom.x() * sinTheta + odom.y()* costheta;
                dyaw += odom.z();
            }
        } 
        dyaw = rpos::core::constraitRadNegativePiToPi(dyaw);
        readCount_++;
        return rpos::core::Vector3f(dx,dy,dyaw);
    }

    void Ros2Node::laserScanCallback_(const sensor_msgs::msg::LaserScan::ConstPtr& msg)
    { 
        size_t count = msg->ranges.size();
        
        if(enable_shared_memory_){
            rpos::messaging::LaserScanData scanData;
            scanData.timestamp = msg->header.stamp.sec * 1000000 + msg->header.stamp.nanosec/1000;
            scanData.scan.resize(count);
            for(size_t i = 0; i< count; i++)
            { 
                bool valid = msg->ranges[i] <= msg->range_max && msg->ranges[i] >= msg->range_min;

                float degree = msg->angle_min + msg->angle_increment * i;
                float rad = rpos::core::constraitRadZeroTo2Pi(degree); //RPlidar ROS SDK offset

                rpos::messaging::LidarScanPoint& lidarPoint = scanData.scan[i];
                lidarPoint.dist = msg->ranges[i];
                lidarPoint.angle = rpos::core::rad2deg(rad);
                lidarPoint.valid = valid; 
            }
            laser_pub_->publish(scanData);
        }
        else{
            rpos::message::lidar::LidarScan laserScan; 
            laserScan.reserve(count);
            for (size_t i = 0; i < count; i++)
            {
                bool valid = msg->ranges[i] <= msg->range_max && msg->ranges[i] >= msg->range_min;

                float degree = msg->angle_min + msg->angle_increment * i;
                float rad = rpos::core::constraitRadZeroTo2Pi(degree + M_PI_2); //RPlidar ROS SDK offset

                rpos::message::lidar::LidarScanPoint lidarPoint;
                lidarPoint.dist = msg->ranges[i];
                lidarPoint.angle = rpos::core::rad2deg(rad);
                lidarPoint.valid = valid;
                lidarPoint.layer = "";
                laserScan.push_back(lidarPoint);

            }
            lidarDevice_->onScanDataReceived(std::move(laserScan));
        }
        lidarCount_++; 
        if (lidarCount_ == 400)
        {
            auto now = rpos::system::util::high_resolution_clock::get_time_in_ms();
            uint64_t diff= now- lastLidarTimestamp_;
            float hz = lidarCount_ * 1000.0f/ diff; 
            lastLidarTimestamp_ = now;
            lidarCount_ = 0; 
            RCLCPP_INFO(nh_->get_logger(), "receive lidar frequency: %.2f", hz);
        } 
    }

    void Ros2Node::odometryCallback_(const nav_msgs::msg::Odometry::ConstPtr& msg)
    {  
        auto duration = rclcpp::Time(msg->header.stamp) - startupROSTime_;
        tf2::Transform pose;
        pose.setOrigin(tf2::Vector3(msg->pose.pose.position.x, msg->pose.pose.position.y, msg->pose.pose.position.z)); 
        tf2::Quaternion quat_tf;
        tf2::fromMsg(msg->pose.pose.orientation, quat_tf);
        pose.setRotation(quat_tf);
        auto ts = startupSteadyTime_ + duration.nanoseconds()/1000000;
        countOdomFrequency(ts);   
        std::lock_guard<std::mutex> lkGuard(poseDataLock_);
        pose_ = pose;
        odomTimestamp_ = ts;
    }
    
    void Ros2Node::deadReckonCallback_(const geometry_msgs::msg::Vector3Stamped::ConstPtr& msg)
    {
        auto duration = rclcpp::Time(msg->header.stamp) - startupROSTime_;
        auto ts = startupSteadyTime_ + duration.nanoseconds()/1000000;
        countOdomFrequency(ts); 
        std::lock_guard<std::mutex> lkGuard(poseDataLock_);
        deadreckon_.emplace_back(msg->vector.x, msg->vector.y,msg->vector.z);
        odomTimestamp_ = ts;
    }

    void Ros2Node::generateTimeOffset_()
    {
        startupROSTime_ = nh_->get_clock()->now();
        startupSteadyTime_ = rpos::system::util::high_resolution_clock::get_time_in_ms();
        odomTimestamp_ = startupSteadyTime_;
        lastCheckTimestamp_ = startupSteadyTime_;
        lastLidarTimestamp_ = startupSteadyTime_;
    }

    void Ros2Node::countOdomFrequency(const uint64_t& msgTime)
    {
        if (msgTime == odomTimestamp_)
        {
            return;
        }
        auto now = rpos::system::util::high_resolution_clock::get_time_in_ms();
        int delay = static_cast<int>(now - msgTime);
        if (delay > 30)
        {
            RCLCPP_WARN(nh_->get_logger(), "odom message time delay:%d", delay);
        }
        
        odomCount_++;
        if (odomCount_ == 2000)
        {
            auto now = rpos::system::util::high_resolution_clock::get_time_in_ms();
            uint64_t diff= now- lastCheckTimestamp_;
            float hz = odomCount_ * 1000.0f/ diff;
            float readHz = readCount_ * 1000.f / diff;
            lastCheckTimestamp_ = now;
            odomCount_ = 0;
            readCount_ = 0;
            RCLCPP_INFO(nh_->get_logger(), "receive odom frequency: %.2f, read deadreck frequency: %.2f", hz, readHz);
        } 
    }
}}}

