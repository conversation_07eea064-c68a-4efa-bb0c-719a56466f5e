#include "config.h"

namespace rp { namespace slamware { namespace utils {
 
    RosNodeConfig::RosNodeConfig()
    {
        resetToDefault();
    }

    void RosNodeConfig::resetToDefault()
    {
        scan_sub_topic = "/scan";
        odometry_sub_topic = "/odom";
        is_accumulated_odometry = true;
        velocity_pub_topic = "cmd_vel";
        enable_shared_memory_lidar = false;
        enable_modbus_base_device = false;
        device_host =  "**************";
    }

#if ROS_DISTRO_VERSION == 1
    void RosNodeConfig::setBy(const ros::NodeHandle& nhRos)
    {
        nhRos.getParam("scan_sub_topic", scan_sub_topic);
        nhRos.getParam("odometry_sub_topic", odometry_sub_topic);
        nhRos.getParam("is_accumulated_odometry", is_accumulated_odometry);
        nhRos.getParam("velocity_pub_topic", velocity_pub_topic);
        nhRos.getParam("enable_shared_memory_lidar", enable_shared_memory_lidar);
        nhRos.getParam("enable_modbus_base_device", enable_modbus_base_device);
        nhRos.getParam("device_host", device_host);
    }
#elif ROS_DISTRO_VERSION == 2
    void RosNodeConfig::declare(rclcpp::Node::SharedPtr nhRos)
    {
        nhRos->declare_parameter("scan_sub_topic", "/scan");
        nhRos->declare_parameter("odometry_sub_topic", "/odom");
        nhRos->declare_parameter("is_accumulated_odometry",  true);
        nhRos->declare_parameter("velocity_pub_topic", "cmd_vel");
        nhRos->declare_parameter("enable_shared_memory_lidar", false);
        nhRos->declare_parameter("enable_modbus_base_device", false);
        nhRos->declare_parameter("device_host", "**************");
    }
    
    void RosNodeConfig::setBy(rclcpp::Node::SharedPtr nhRos)
    {
        nhRos->get_parameter("scan_sub_topic", scan_sub_topic);
        nhRos->get_parameter("odometry_sub_topic", odometry_sub_topic);
        nhRos->get_parameter("is_accumulated_odometry", is_accumulated_odometry);
        nhRos->get_parameter("velocity_pub_topic", velocity_pub_topic);
        nhRos->get_parameter("enable_shared_memory_lidar", enable_shared_memory_lidar);
        nhRos->get_parameter("enable_modbus_base_device", enable_modbus_base_device);
        nhRos->get_parameter("device_host", device_host);
    }
#endif
}}}
