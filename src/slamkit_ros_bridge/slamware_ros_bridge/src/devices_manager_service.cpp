#include "devices_manager_service.h"
#include "devices/modbus_base_device.h"
#include "devices/ros_base_device.h"
#include "devices/ros_rplidar_device.h"
#include "devices/ros_cp0_device.h"
#include <boost/make_shared.hpp>
#include <chrono>

namespace rp { namespace slamware { namespace utils {

    const int DevicesManagerService::threadDuration = 1000 / 100;

    DevicesManagerService::DevicesManagerService()
        : rosNode_("rosNode"), working_(false)
    {
        depends(&rosNode_);
    }

    DevicesManagerService::~DevicesManagerService()
    {
    }

    bool DevicesManagerService::onStart()
    {
        thread_ = std::thread(std::bind(&DevicesManagerService::workThread_, this));
        return true;
    }

    bool DevicesManagerService::onStop()
    {
        logger.info_out("devices start stop.");
        if (working_.load())
            working_.store(false);

        if (thread_.joinable())
            thread_.join();

        logger.info_out("devices end stop.");
        return true;
    }

    bool DevicesManagerService::getScanData(rpos::message::lidar::LidarScan& lidarData)
    {
        return rosNode_->getLaserScan(lidarData);
    }

    void DevicesManagerService::publishMotion(const rpos::message::base::MotionRequest& request)
    {
        rosNode_->publishMotion(request);
    }

    void DevicesManagerService::getMovementEstimation(rpos::message::Message<rpos::message::base::MovementEstimation>& estimation)
    {
        rosNode_->getMovementEstimation(estimation);
    }

    void DevicesManagerService::getImuAllSensorData(rpos::message::imu::ImuAllSensorData& sensorData)
    {
        return;
    }

    void DevicesManagerService::workThread_()
    {
        logger.info_out("device manager service thread begin.");
        working_.store(true);
        if (!initBridge_())
        {
            cleanup_();
            return;
        }

        configDevices_();

        auto res = bridge_->start();
        if (IS_FAIL(res))
        {
            logger.error_out("can not start the server: %08x.", res);
            cleanup_();
            return;
        }

        while (working_.load())
        {
            bridge_->heartBeat();
            std::this_thread::sleep_for(std::chrono::milliseconds(threadDuration));
        }

        logger.info_out("devices manager service stop bridge.");
        bridge_->stop(); 
        cleanup_();
    }

    bool DevicesManagerService::initBridge_()
    {
        bridge_ = createBridgeServer();
        if (!bridge_)
            return false;

        u_result res = bridge_->init("slamware_pseudo_device_bridge", "Slamtec Slamware Pseudo Device Bridge");
        if (IS_FAIL(res))
        {
            logger.error_out("can not initialize bridge: %08x.", res);
            return false;
        }
        logger.info_out("init tcp bridge server");
        return true;
    }

    void DevicesManagerService::configDevices_()
    {
        lidar_ = boost::make_shared<RosRPLidarDevice>(shared_from_this());
        if(rosNode_->config().enable_modbus_base_device)
        {
            logger.info_out("enable modbus base device");
            base_ = boost::make_shared<ModbusBaseDevice>(rosNode_->config().device_host);
        }
        else
        {
            base_ = boost::make_shared<RosBaseDevice>(shared_from_this());
        }
        //cp0_ = boost::make_shared<RosCp0Device>(shared_from_this());
        bridge_->addVirtualDevice("rplidar", "Lidar", lidar_.get());
        bridge_->addVirtualDevice("ctrlbus", "Control Bus", base_.get());
        //bridge_->addVirtualDevice("cp0", "CP0", cp0_.get());
        rosNode_->registerLidarDevice(lidar_);
        rosNode_->registerBaseDevice(base_);
    }

    void DevicesManagerService::cleanup_()
    {
        working_.store(false);

        if (bridge_)
        {
            releaseBridgeServer(bridge_);
            bridge_ = nullptr;
        }
    }
}}}
