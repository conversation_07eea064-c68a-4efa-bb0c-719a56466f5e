#include "ros1_node.h"
#include <rpos/core/angle_math.h>
#include <rpos/system/util/time_util.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <cmath>

namespace rp { namespace slamware { namespace utils {

    Ros1Node::Ros1Node(int argc, char** argv, const std::string& nodeName) 
        : Ros1NodeBase(argc, argv, nodeName)
        , nh_("~")
        , initEstimationFlag_(true)
        , isOdometry_(true)
        , enable_shared_memory_(false)
    {
        generateTimeOffset_();
    }

    Ros1Node::~Ros1Node()
    {
        clear();
    }
 
    void Ros1Node::clear()
    {
        subLaserScan_ = ros::Subscriber();
        subOdometry_ = ros::Subscriber();
    }
    
    void Ros1Node::initConfig(RosNodeConfig& cfg)
    {
        cfg.setBy(nh_);
        enable_shared_memory_ = cfg.enable_shared_memory_lidar;
    }

    void Ros1Node::start()
    {
        if(enable_shared_memory_)
        {
            laser_pub_ = IceoryxManager::createPublisher<rpos::messaging::LaserScanData>("slamware_ros_bridge","main","laser_scan");
        }
    }

    void Ros1Node::subscribe(std::string& msgTopic, std::uint32_t queueSize, MsgType msgType)
    {
        if (msgType == MsgTypeScan)
        {
            subLaserScan_ = nh_.subscribe(msgTopic, queueSize, &Ros1Node::laserScanCallback_, this, ros::TransportHints().tcpNoDelay());
        }
        else if (msgType == MsgTypeOdometry)
        {  
            subOdometry_ = nh_.subscribe(msgTopic, queueSize, &Ros1Node::odometryCallback_, this, ros::TransportHints().tcpNoDelay());
            isOdometry_ = true;
            ROS_INFO("subscribe odometry topic: %s", msgTopic.c_str());
        }
        else if ( msgType == MsgTypeDeadreckon)
        {
            subOdometry_ = nh_.subscribe(msgTopic, queueSize, &Ros1Node::deadReckonCallback_, this, ros::TransportHints().tcpNoDelay());
            isOdometry_ = false;
            ROS_INFO("subscribe deadreckon topic: %s", msgTopic.c_str());
        }
    }

    void Ros1Node::spin(bool isOnce)
    {
        if (isOnce)
            ros::spinOnce();
        else
            ros::spin();
    }

    bool Ros1Node::getLaserScan(rpos::message::lidar::LidarScan&)
    {
        ROS_ERROR("ros rplidar is callback mode, this function should never be called");
        return false;
    }

    rpos::core::Vector3f Ros1Node::getDeadReckon(uint64_t& timestamp)
    {
        double dx = 0.0;
        double dy = 0.0;
        double dyaw = 0.0;
        if(isOdometry_)
        {
            if (initEstimationFlag_)
            {
                std::lock_guard<std::mutex> lkGuard(poseDataLock_);
                initEstimationFlag_ = false;
                prePose_ = pose_;
                timestamp = odomTimestamp_;
            }
            else
            {
                std::lock_guard<std::mutex> lkGuard(poseDataLock_);
                tf2::Transform trans = prePose_.inverseTimes(pose_);
                dx = trans.getOrigin().getX();
                dy = trans.getOrigin().getY();
                double roll, pitch;
                trans.getBasis().getRPY(roll, pitch, dyaw);
                timestamp = odomTimestamp_;
                prePose_ = pose_;
            }
        }
        else
        {
            std::vector<rpos::core::Vector3f> deadreckons;
            {
                std::lock_guard<std::mutex> lkGuard(poseDataLock_);
                deadreckons.swap(deadreckon_);
                timestamp = odomTimestamp_;
            }
            for(auto odom : deadreckons)
            { 
                double costheta = cos(dyaw);
                double sinTheta = sin(dyaw);
                dx += odom.x() * costheta - odom.y()* sinTheta;
                dy += odom.x() * sinTheta + odom.y()* costheta;
                dyaw += odom.z();
            }
        } 
        dyaw = rpos::core::constraitRadNegativePiToPi(dyaw);
        readCount_++;
        return rpos::core::Vector3f(dx,dy,dyaw);
    }

    void Ros1Node::laserScanCallback_(const sensor_msgs::LaserScan::ConstPtr& msg)
    {
        size_t count = msg->ranges.size();
        
        if(enable_shared_memory_){
            rpos::messaging::LaserScanData scanData;
            scanData.timestamp = msg->header.stamp.sec * 1000000 + msg->header.stamp.nanosec/1000;
            scanData.scan.resize(count);
            for(size_t i = 0; i< count; i++)
            { 
                bool valid = msg->ranges[i] <= msg->range_max && msg->ranges[i] >= msg->range_min;

                float degree = msg->angle_min + msg->angle_increment * i;
                float rad = rpos::core::constraitRadZeroTo2Pi(degree + M_PI_2); //RPlidar ROS SDK offset

                rpos::messaging::LidarScanPoint& lidarPoint = scanData.scan[i];
                lidarPoint.dist = msg->ranges[i];
                lidarPoint.angle = rpos::core::rad2deg(rad);
                lidarPoint.valid = valid; 
            }
            laser_pub_->publish(scanData);
        }
        else{
            rpos::message::lidar::LidarScan laserScan; 

            for (int i = 0; i < count; i++)
            {
                bool valid = msg->ranges[i] <= msg->range_max && msg->ranges[i] >= msg->range_min;

                float degree = msg->angle_min + msg->angle_increment * i;
                float rad = rpos::core::constraitRadZeroTo2Pi(degree); //RPlidar ROS SDK offset

                rpos::message::lidar::LidarScanPoint lidarPoint;
                lidarPoint.dist = msg->ranges[i];
                lidarPoint.angle = rpos::core::rad2deg(rad);
                lidarPoint.valid = valid;
                lidarPoint.layer = "";
                laserScan.push_back(lidarPoint); 
            }
            lidarDevice_->onScanDataReceived(std::move(laserScan));
        }
    }

    void Ros1Node::odometryCallback_(const nav_msgs::Odometry::ConstPtr& msg)
    {  
        auto duration = msg->header.stamp - startupSystemTime_;
        tf2::Transform pose;
        pose.setOrigin(tf2::Vector3(msg->pose.pose.position.x, msg->pose.pose.position.y, msg->pose.pose.position.z)); 
        tf2::Quaternion quat_tf;
        tf2::fromMsg(msg->pose.pose.orientation, quat_tf);
        pose.setRotation(quat_tf);
        auto ts = startupSteadyTime_ + duration.sec*1000 + duration.nsec/1000000;
        countOdomFrequency(ts);   
        std::lock_guard<std::mutex> lkGuard(poseDataLock_);
        pose_ = pose;
        odomTimestamp_ = ts;
    }
    
    void Ros1Node::deadReckonCallback_(const geometry_msgs::Vector3Stamped::ConstPtr& msg)
    {
        auto duration = msg->header.stamp - startupSystemTime_;
        auto ts = startupSteadyTime_ + duration.sec*1000 + duration.nsec/1000000;
        countOdomFrequency(ts); 
        std::lock_guard<std::mutex> lkGuard(poseDataLock_);
        deadreckon_.emplace_back(msg->vector.x, msg->vector.y,msg->vector.z);
        odomTimestamp_ = ts;
    }

    void Ros1Node::generateTimeOffset_()
    {
        startupSystemTime_ = ros::Time::now();
        startupSteadyTime_ = rpos::system::util::high_resolution_clock::get_time_in_ms();
        odomTimestamp_ = startupSteadyTime_;
        lastCheckTimestamp_ = startupSteadyTime_;
    }

    void Ros1Node::countOdomFrequency(const uint64_t& msgTime)
    {
        //same timestamp, regard as same message, do not count
        if (msgTime == odomTimestamp_)
        {
            return;
        }
        auto now = rpos::system::util::high_resolution_clock::get_time_in_ms();
        int delay = static_cast<int>(now - msgTime);
        if (delay > 30)
        {
            ROS_WARN("odom message time delay:%d", delay);
        }
        
        odomCount_++;
        if (odomCount_ == 1000)
        {
            auto now = rpos::system::util::high_resolution_clock::get_time_in_ms();
            uint64_t diff= now- lastCheckTimestamp_;
            float hz = odomCount_ * 1000.0f/ diff;
            float readHz = readCount_ * 1000.f / diff;
            lastCheckTimestamp_ = now;
            odomCount_ = 0;
            readCount_ = 0;
            ROS_INFO("receive odom frequency: %.2f, read deadreck frequency: %.2f", hz, readHz);
        } 
    }
}}}

