#include "ros_node_service.h"
#if ROS_DISTRO_VERSION == 1 
#include "ros1_node.h"
#include <geometry_msgs/Twist.h>
#elif ROS_DISTRO_VERSION == 2 
#include "ros2_node.h"   
#include <geometry_msgs/msg/twist.hpp>
#endif  
#include <rpos/system/util/log.h>
#include <thread>
#include <chrono>
#include <cmath>

using namespace rpos::context;

namespace {
    const float kPif = 3.141592653;
}

namespace rp { namespace slamware { namespace utils {
 
    template <typename RosHandlerT>
    RosNodeService<RosHandlerT>::RosNodeService(int argc, char** argv)
        : initEstimationFlag_(true)
    {
        this->template provides<IRosNode>();
        rosNode_.reset(new RosHandlerT(argc, argv, "slamware_ros_bridge_node"));
        rosNode_->initConfig(config_);
    }

    template <typename RosHandlerT>
    RosNodeService<RosHandlerT>::~RosNodeService()
    {
        
    }

    template <typename RosHandlerT>
    bool RosNodeService<RosHandlerT>::onStart()
    {
        if (!rosNode_ )
            return false;

        logger.info_out("ros node service thread begin, lidar topic:%s, odom topic:%s, velocity command topic:%s", 
            config_.scan_sub_topic.c_str(),config_.odometry_sub_topic.c_str(), config_.velocity_pub_topic.c_str());
            
        rosNode_->start();

        rosNode_->subscribe(config_.scan_sub_topic, 1, MsgType::MsgTypeScan);
        if(config_.is_accumulated_odometry){
            rosNode_->subscribe(config_.odometry_sub_topic, 1, MsgType::MsgTypeOdometry);
        }
        else{
            rosNode_->subscribe(config_.odometry_sub_topic, 2, MsgType::MsgTypeDeadreckon);
        }
#if ROS_DISTRO_VERSION == 1 
        rosNode_->template advertise<geometry_msgs::Twist>(config_.velocity_pub_topic, 1, MsgType::MsgTypeVelocity);
#elif ROS_DISTRO_VERSION == 2 
        rosNode_->template advertise<geometry_msgs::msg::Twist>(config_.velocity_pub_topic, 1, MsgType::MsgTypeVelocity);
#endif
        return true;
    }

    template <typename RosHandlerT>
    bool RosNodeService<RosHandlerT>::onStop()
    {
        logger.info_out("ros service stop.");
        rosNode_->clear();
        return true;
    }

    template <typename RosHandlerT>
    void RosNodeService<RosHandlerT>::spin()
    {
        rosNode_->spin(false);
    }

    template <typename RosHandlerT>
    bool RosNodeService<RosHandlerT>::getLaserScan(rpos::message::lidar::LidarScan& lidarData)
    {
        return rosNode_->getLaserScan(lidarData);
    }

    template <typename RosHandlerT>
    void RosNodeService<RosHandlerT>::publishMotion(const rpos::message::base::MotionRequest& request)
    {
#if ROS_DISTRO_VERSION == 1 
        geometry_msgs::Twist msg;
        msg.linear.x = request.vx();
        msg.linear.y = request.vy();
        msg.linear.z = 0.0f;
        msg.angular.x = 0.0f;
        msg.angular.y = 0.0f;
        msg.angular.z = request.omega();
        rosNode_->template publish<geometry_msgs::Twist>(msg, MsgType::MsgTypeVelocity);
#elif ROS_DISTRO_VERSION == 2    
        geometry_msgs::msg::Twist msg;
        msg.linear.x = request.vx();
        msg.linear.y = request.vy();
        msg.linear.z = 0.0f;
        msg.angular.x = 0.0f;
        msg.angular.y = 0.0f;
        msg.angular.z = request.omega();
        rosNode_->template publish<geometry_msgs::msg::Twist>(msg, MsgType::MsgTypeVelocity);
#endif
    }

    template <typename RosHandlerT>
    void RosNodeService<RosHandlerT>::getMovementEstimation(rpos::message::Message<rpos::message::base::MovementEstimation>& estimation)
    { 
        uint64_t timestamp;
        auto odom = rosNode_->getDeadReckon(timestamp);
        estimation->positionDifference.x() = odom.x();
        estimation->positionDifference.y() = odom.y();
        estimation->angularDifference = odom.z();
        estimation.timestamp = timestamp;
    }

    template <typename RosHandlerT>
    void RosNodeService<RosHandlerT>::registerLidarDevice(boost::shared_ptr<PseudoRPLidarDevice> device)
    {
        rosNode_->registerLidarDevice(device);
    }

    template <typename RosHandlerT>
    void RosNodeService<RosHandlerT>::registerBaseDevice(boost::shared_ptr<PseudoBaseDevice> device)
    {
        rosNode_->registerBaseDevice(device);
    }

}}}

#if ROS_DISTRO_VERSION == 1 
template class rp::slamware::utils::RosNodeService<rp::slamware::utils::Ros1Node>;
#elif ROS_DISTRO_VERSION == 2  
template class rp::slamware::utils::RosNodeService<rp::slamware::utils::Ros2Node>;
#endif 

