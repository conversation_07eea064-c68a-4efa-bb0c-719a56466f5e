#include "devices_manager_service.h"
#include "ros_node_service.h"
#if ROS_DISTRO_VERSION == 1 
#include "ros1_node.h"
#elif ROS_DISTRO_VERSION == 2 
#include "ros2_node.h"
#endif  
#include <rpos/system/util/log.h>
#include <rpos/system/config/options_parser.h>
#include <rpos/context/application_context.h>
#include <rpos/messaging/iceoryx_manager.h>
#include <boost/make_shared.hpp>
#include <chrono>
#include <thread>
#include <csignal>

rpos::context::ApplicationContext context;

int main(int argc, char** argv)
{
    auto logManager = rpos::system::util::LogManager::defaultManager();
    rpos::system::util::LogConfig cfg;
    cfg.global.logLevel = rpos::system::util::LogLevelInfo;
    cfg.console.logLevel = rpos::system::util::LogLevelInfo;
    rpos::system::util::FileLogAppenderConfig fileCfg;
    fileCfg.logLevel = rpos::system::util::LogLevelInfo;
    fileCfg.filename = "/var/log/slamware/slamware_ros_bridge.log";  
    fileCfg.max_file_size_MB = 5;
    fileCfg.max_backup_index = 5;
    fileCfg.append = true;
    fileCfg.encrypt = false;
    cfg.files.push_back(fileCfg);
    logManager->configWith(cfg ,"", true); 

    boost::shared_ptr<rp::slamware::utils::IRosNode> rosNodeService;
#if ROS_DISTRO_VERSION == 1 
    rosNodeService = boost::make_shared<rp::slamware::utils::RosNodeService<rp::slamware::utils::Ros1Node>>(argc, argv);
    context.addService(boost::dynamic_pointer_cast<rp::slamware::utils::RosNodeService<rp::slamware::utils::Ros1Node>>(rosNodeService));
#elif ROS_DISTRO_VERSION == 2  
    rosNodeService = boost::make_shared<rp::slamware::utils::RosNodeService<rp::slamware::utils::Ros2Node>>(argc, argv);
    context.addService(boost::dynamic_pointer_cast<rp::slamware::utils::RosNodeService<rp::slamware::utils::Ros2Node>>(rosNodeService));
#else
    #pragma message("ROS version not defined")
#endif
    if (!rosNodeService)
    {
        printf("ROS node can not by initialized\n");
        return -1;
    }

    if(rosNodeService->config().enable_shared_memory_lidar)
    {
        printf("Initialize iceoryx runtime\n");
        rpos::messaging::IceoryxManager::initRuntime("slamware_ros_bridge");
    }
    auto deviceManagerService = boost::make_shared<rp::slamware::utils::DevicesManagerService>();
        
    context.addService(deviceManagerService);
    context.startServices();

    rosNodeService->spin();  

    context.stopServices(); 
    return 0;
}