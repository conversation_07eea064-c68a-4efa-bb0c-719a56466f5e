#include "devices/modbus_server.h"
#include <functional>
#include <rpos/system/util/log.h>

namespace rp{ namespace slamware { namespace utils {

    rpos::system::util::LogScope logger("rp.slamware.utils.modbus_server");

    ModbusServer::ModbusServer() : working_(false)
    {
    }

    ModbusServer::~ModbusServer()
    {
        modbus_mapping_free(mb_mapping);
        modbus_free(ctx);
    }

    bool ModbusServer::start()
    {
        if (working_)
        {
            return true;
        }

        ctx = modbus_new_rtu_tcp("0", 502);
        //modbus_set_slave(ctx, 17);
        //modbus_set_debug(ctx, true);

        mb_mapping = modbus_mapping_new(0, 0, 250, 0);
        if (mb_mapping == NULL)
        {
            modbus_free(ctx);
            return false;
        }

        working_ = true;
        thread_ = std::thread(std::bind(&ModbusServer::worker, this));
        return true;
    }

    void ModbusServer::stop()
    {
        working_ = false;
        if (thread_.joinable())
            thread_.join();
    }

    void ModbusServer::worker()
    {
        int s = -1;
        s = modbus_rtu_tcp_listen(ctx, 1);
        logger.info_out("modbus server start listening, register start address:%d, count:%d",
            mb_mapping->start_registers, mb_mapping->nb_registers);

        while (working_)
        {
            modbus_rtu_tcp_accept(ctx, &s);
            char *client = modbus_get_current_addr_in(ctx);
            currentClient = std::string(client);
            logger.info_out("client(%s) connected", client);
            while (working_)
            {
                uint8_t query[MODBUS_TCP_MAX_ADU_LENGTH];
                int rc;

                rc = modbus_receive(ctx, query);
                if (rc > 0)
                {
                    modbus_reply(ctx, query, rc, mb_mapping);
                }
                else if (rc == -1)
                {
                    modbus_close(ctx);
                    break;
                }
            }
            logger.info_out("client(%s) disconnected",currentClient.c_str());
            currentClient.clear();
        }
    }

    bool ModbusServer::readRegisters(int start_addr, int count, std::vector<uint16_t> &data)
    {
        data.clear();
        int lastAddr = start_addr + count;
        if (start_addr < mb_mapping->start_registers 
          || lastAddr > mb_mapping->start_registers + mb_mapping->nb_registers)
            return false;
        data.insert(data.end(),
                    &mb_mapping->tab_registers[mb_mapping->start_registers + start_addr],
                    &mb_mapping->tab_registers[mb_mapping->start_registers + start_addr + count]);
        return true;
    }

    bool ModbusServer::writeRegisters(int start_addr, const std::vector<uint16_t>& data)
    { 
        int lastAddr = start_addr + data.size();
        if (start_addr < mb_mapping->start_registers 
          || lastAddr > mb_mapping->start_registers + mb_mapping->nb_registers)
            return false;
        memcpy(&mb_mapping->tab_registers[mb_mapping->start_registers + start_addr], data.data(), data.size()*sizeof(uint16_t));
        return true;
    }
}}}