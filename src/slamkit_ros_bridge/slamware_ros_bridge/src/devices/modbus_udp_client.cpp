#include "devices/modbus_udp_client.h"
#include "devices/modbus/modbus-rtu-tcp.h"
#include "devices/modbus_base_device.h"
#include <boost/make_shared.hpp>
#include <cstring>

namespace
{
    const uint8_t kAddress = 0;
    const uint8_t kFuncCode = 0x03;
    const uint8_t kRegisterNum = 0x0e;

    std::uint16_t LittleEndianToBigEndian(std::uint16_t value) 
    {
       return ((value << 8) & 0xFF00) |
              ((value >> 8) & 0x00FF);
    }

    std::uint32_t LittleEndianToBigEndian(std::uint32_t value) 
    {
       return ((value << 24) & 0xFF000000) |
              ((value << 8) & 0x00FF0000) |
              ((value >> 8) & 0x0000FF00) |
              ((value >> 24) & 0x000000FF);
    }

    std::uint64_t LittleEndianToBigEndian(std::uint64_t value) 
    {
       return ((value << 56) & 0xFF00000000000000) |
              ((value << 40) & 0x00FF000000000000) |
              ((value << 24) & 0x0000FF0000000000) |
              ((value << 8) & 0x000000FF00000000) |
              ((value >> 8) & 0x00000000FF000000) |
              ((value >> 24) & 0x0000000000FF0000) |
              ((value >> 40) & 0x000000000000FF00) |
              ((value >> 56) & 0x00000000000000FF);
    }
}

namespace rp { namespace slamware { namespace utils {

    rpos::system::util::LogScope ModbusUdpClientHandler::logger("rp.slamware.utils.modbus_udp_client");

    void ModbusUdpClientHandler::onHostResolveFailure(Pointer client, const boost::system::error_code& ec)
    {
        logger.error_out("onHostResolveFailure:%s(%d)", ec.message().c_str(), ec.value());
    }

    void ModbusUdpClientHandler::onSendError(Pointer client, const boost::system::error_code& ec)
    {
        logger.error_out("onSendError:%s(%d)", ec.message().c_str(), ec.value());
    }

    void ModbusUdpClientHandler::onReceiveError(Pointer client, const boost::system::error_code& ec)
    {
        logger.error_out("onReceiveError:%s(%d)", ec.message().c_str(), ec.value());
    }

    rpos::system::util::LogScope ModbusUdpClient::logger("rp.slamware.utils.modbus_udp_client");

    ModbusUdpClient::ModbusUdpClient() : connectFlag_(false)
    { 
    }

    ModbusUdpClient::~ModbusUdpClient()
    {
    }

    void ModbusUdpClient::connect(const std::string& host, int port)
    {
        logger.info_out("modbus udp client connect to %s:%d", host.c_str(), port);
        UdpClient::connect(host, port);
        connectFlag_.store(true);
    }

    void ModbusUdpClient::sendRobotBasicStatusData(const RobotBasicStatus& status)
    {
        // address + fuction code + register byte count + register value + crc
        int totalByte = 3 + 2 * kRegisterNum + 2;
        std::vector<uint8_t> buffer(totalByte, 0);

        // add slave address
        buffer[0] = kAddress;
        // add function code
        buffer[1] = kFuncCode;
        // add register byte
        buffer[2] = 2 * kRegisterNum;
        // add register value
        robotBasicStatusDataToModbusData_(status, buffer);
        // add crc16
        modbus_send_msg_add_crc(&buffer[0], totalByte - 2);

        send(buffer);
    }

    void ModbusUdpClient::robotBasicStatusDataToModbusData_(const RobotBasicStatus& status, std::vector<uint8_t>& buffer)
    {
        int offset = 3;
        addBigEndianData_(status.timstamp, offset, buffer);
        addBigEndianData_(status.state, offset, buffer);
        addBigEndianData_(status.param, offset, buffer);
        addBigEndianData_(status.x, offset, buffer);
        addBigEndianData_(status.y, offset, buffer);
        addBigEndianData_(status.yaw, offset, buffer);
        addBigEndianData_(status.map_index, offset, buffer);
        addBigEndianData_(status.localization_quality, offset, buffer);
        addBigEndianData_(status.omega, offset, buffer);
        addBigEndianData_(status.cmd_timstamp, offset, buffer);
    }

    template<typename T>
    void ModbusUdpClient::addBigEndianData_(T data, int& offset, std::vector<uint8_t>& buffer)
    {
        T bigEndianData = LittleEndianToBigEndian(data);
        std::memcpy(&buffer[offset], &bigEndianData, sizeof(data));
        offset += sizeof(data);
    }

} } }
