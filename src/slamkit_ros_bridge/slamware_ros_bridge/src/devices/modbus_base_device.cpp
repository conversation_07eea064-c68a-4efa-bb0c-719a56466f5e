#include "devices/modbus_base_device.h"
#include <rpos/system/util/time_util.h>
#include <rpos/system/util/stop_watch.h>
#include <rpos/core/angle_math.h>
#include <fstream>
#include <sstream>

#if defined(BOOST_VERSION) && (BOOST_VERSION > 105600)
using namespace boost::placeholders;
#endif

namespace rp { namespace slamware { namespace utils {

    using namespace rpos::robot_platforms::detail;

    rpos::system::util::LogScope ModbusBaseDevice::logger("rp.slamware.utils.modbus_base_device");
    const unsigned char ModbusBaseDevice::DefaultBinaryConfig[] = {0x50,0x00};
    const size_t ModbusBaseDevice::DefaultBinaryConfigSize = 2;
    const int kUdpServerBindPort = 501; 

    ModbusBaseDevice::ModbusBaseDevice(const std::string& deivce_host) 
    : deviceHost_(deivce_host)
    , mapIndex_(0)
    {
        initBinaryConfig_("");
        odomValid_ = false;
        memset(&lastCtrlCmd_, 0 ,sizeof(CtrlCmd));
        udpClient_ = boost::make_shared<ModbusUdpClient>();
        udpServer_ = boost::make_shared<ModbusUdpServer>(kUdpServerBindPort);
        udpServer_->registerHandler(boost::bind(&ModbusBaseDevice::onReceiveFromUDP,
           this, _1, _2));
        udpServer_->start();
        working_ = true;
        thread_ = std::thread(std::bind(&ModbusBaseDevice::worker, this));
    }

    ModbusBaseDevice::~ModbusBaseDevice()
    {
        //
    }

    bool ModbusBaseDevice::getBinaryConfig(std::uint8_t* buf, size_t* size)
    {
        if (!buf || !size || (*size) < binaryConfig_.size())
            return false;

        (*size) = binaryConfig_.size();
        memcpy(buf, &binaryConfig_[0], binaryConfig_.size());

        return true;
    }

    bool ModbusBaseDevice::requestMotion(const rpos::message::base::MotionRequest& request)
    {
        return true;
    }

    bool ModbusBaseDevice::getMovementEstimation(rpos::message::Message<rpos::message::base::MovementEstimation>& estimation)
    {
        estimation.timestamp = rpos::system::util::high_resolution_clock::get_time_in_ms();
        estimation.payload.reset();
        std::vector<uint16_t> value;
        if(server_.readRegisters(100, 11, value))
        { 
            int64_t ts = MODBUS_GET_INT64_FROM_INT16(value.data(),0);
            if(ts == 0)
            {
                odomValid_= false;
            }
            else
            {
                estimation.timestamp = ts;
                float x = MODBUS_GET_INT32_FROM_INT16(value.data(),6) / 1000.0f;
                float y = MODBUS_GET_INT32_FROM_INT16(value.data(),8) / 1000.0f;
                float yaw = ((int16_t)value[10]) / 10000.0f;
                rpos::core::Location newLoc(x,y);
                if(odomValid_)
                {
                    rpos::core::Vector2f locInView;
                    locInView.x() = newLoc.x() - lastPose_.x();
                    locInView.y() = newLoc.y() - lastPose_.y();
                    Eigen::Rotation2Df rot(-lastPose_.yaw());
                    locInView = rot * locInView;
                    estimation->positionDifference = locInView;
                    estimation->angularDifference = rpos::core::constraitRadNegativePiToPi(yaw - lastPose_.yaw());
#if 0
                    if(fabs(locInView.x()) > 0.0001 || fabs(locInView.y()) > 0.0001 || fabs(estimation->angularDifference) > 0.0001)
                        logger.info_out("deadreck:%f,%f,dyaw:%f, origin yaw:%d", locInView.x(), locInView.y(), estimation->angularDifference, (int16_t)value[10]);
#endif
                }
                rpos::core::Rotation rot(rpos::core::constraitRadNegativePiToPi(yaw));
                lastPose_ = rpos::core::Pose(newLoc, rot);
                odomValid_ = true;
            }
        } 
        return true;
    }

    bool ModbusBaseDevice::getExtendedSensorData(std::vector<BaseSensorData>& extendedSensorData)
    {
        extendedSensorData.clear();
        return true;
    }
    
    bool ModbusBaseDevice::getBaseStatus(BaseStatusData& data)
    {
        data.isDockLocked = false;
        data.isCharging = false;
        data.battery_percentage = 100;
        return true;
    }

    void ModbusBaseDevice::initBinaryConfig_(const std::string& binaryConfigFile)
    {
        if (binaryConfigFile.empty())
        {
            logger.info_out("binary config file not specified, load default binary config");
            initDefaultBinaryConfig_();
            return;
        }

        std::ifstream file(binaryConfigFile);
        if (file.fail())
        {
            logger.warn_out("fail to open file (%s), load default binary config", binaryConfigFile.c_str());
            initDefaultBinaryConfig_();
            return;
        }
        std::stringstream ss;
        ss << file.rdbuf();
        auto  configContent = ss.str();
        binaryConfig_ = std::vector<unsigned char>(configContent.begin(), configContent.end());
        file.close();

        logger.info_out("success to load binary config file (%s), size %d", binaryConfigFile.c_str(), binaryConfig_.size());
    }
    
    bool ModbusBaseDevice::tryConnectToPlatform()
    {
        if(core_)
            return true;

        try
        {
            core_ = rpos::robot_platforms::SlamwareCorePlatform::connect("127.0.0.1", 1445);
            agent_ = rpos::robot_platforms::SlamwareAgentPlatform::connect("127.0.0.1", 1448);
            floors_ = agent_.getFloors();
            mapIndex_ = getCurrentFloorIndex();
            logger.info_out("connect to slamwared successfully, there are %d maps in agent", floors_.size());
            return true;
        }
        catch (const rpos::system::detail::ExceptionBase &e)
        {
        }
        return false;
    }

    void ModbusBaseDevice::initDefaultBinaryConfig_()
    {
        binaryConfig_ = std::vector<unsigned char>(DefaultBinaryConfig, DefaultBinaryConfig + DefaultBinaryConfigSize);
    }
    
    bool ModbusBaseDevice::isCtrlCmdUpdated(CtrlCmd& cmd)
    {
        if(!udpClient_->isConnected())
        {
            if(!deviceHost_.empty())
            {
                logger.info_out("set remote device host %s:501",deviceHost_.c_str());
                udpClient_->connect(deviceHost_, 501);
                udpClient_->start();
            }
        } 
        cmd.isMapChanged = false;
        std::vector<uint16_t> value;
        if(server_.readRegisters(200, 11, value))
        {
            cmd.timstamp = MODBUS_GET_INT64_FROM_INT16(value.data(),0);
            cmd.localization_type = value[4];
            cmd.map_index = value[5];
            cmd.x = MODBUS_GET_INT32_FROM_INT16(value.data(),6) / 1000.0f;
            cmd.y = MODBUS_GET_INT32_FROM_INT16(value.data(),8) / 1000.0f;
            cmd.yaw = (int16_t)value[10] / 10000.0f;
            if(cmd.timstamp != lastCtrlCmd_.timstamp)
            {
                if(cmd.localization_type == 0)
                {
                    logger.info_out("invalid localization type: zero");
                    return false;
                }
                if(cmd.map_index == 0)
                {
                    logger.info_out("invalid map index: zero");
                    return false;
                }
                cmd.isMapChanged = (cmd.map_index != lastCtrlCmd_.map_index);
                lastCtrlCmd_ = cmd;
                logger.info_out("control command received, localization cmd:%d, map index:%d", cmd.localization_type, cmd.map_index);
                return true;
            }
        } 
        return false;
    }
    
    bool ModbusBaseDevice::setCurrentFloor(int mapIndex, const boost::optional<rpos::core::Pose>& pose)
    {
        if(!core_)
            return false;
        auto iter = std::find_if(floors_.begin(),floors_.end(),[mapIndex](const objects::FloorIdentify& floor){
            return floor.order == (mapIndex-1);
        });
        bool bRet = false; 
        try
        { 
            if(iter != floors_.end())
            { 
                logger.info_out("set floor map to (%s/%s)", iter->building.c_str(), iter->floor.c_str());
                agent_.setCurrentFloor(iter->building,iter->floor, pose);
                bRet = true;
            }
        }
        catch (const rpos::system::detail::ExceptionBase &e)
        { 
            logger.warn_out("setCurrentFloor Exception occurred: %s", e.toString().c_str());
        }
        return bRet;
    }
     
    int ModbusBaseDevice::getCurrentFloorIndex()
    {
        int nResult = 0;
        try
        { 
            auto currentFloor = agent_.getCurrentFloor();
            auto iter = std::find_if(floors_.begin(),floors_.end(),[currentFloor](const objects::FloorIdentify& floor){
                return currentFloor.building == floor.building && currentFloor.floor == floor.floor;
            });
            if(iter != floors_.end())
            { 
                nResult = iter->order+1;
            }
        }
        catch (const rpos::system::detail::ExceptionBase &e)
        {
        }
        return nResult;
    }

    void ModbusBaseDevice::doRecoverLocalization(const rpos::core::Pose& pose, float relocalizationSize)
    {
        if(!core_)
            return;
        rpos::core::RectangleF rect(
            pose.x() - relocalizationSize/2.0f,
            pose.y() - relocalizationSize/2.0f,
            relocalizationSize,relocalizationSize
        );
        try{ 
            logger.info_out("start recover localization");
            reLocalizationAction_ = core_.recoverLocalization(rect);
        } 
        catch (const rpos::system::detail::ExceptionBase &e)
        { 
            logger.warn_out("doRecoverLocalization Exception occurred: %s", e.toString().c_str());
        }
    }

    void ModbusBaseDevice::onCtrlCmd(const CtrlCmd& cmd)
    {
        if(cmd.localization_type > 2)
        {
            logger.error_out("invalid recovery localization cmd");
            return;
        }
        rpos::core::Pose pose(rpos::core::Location(cmd.x,cmd.y), rpos::core::Rotation(cmd.yaw));
        boost::optional<rpos::core::Pose> newPose;
        if( cmd.localization_type != 0)
        {
            newPose =  pose;
        }
        if(cmd.isMapChanged)
        {
            if(!setCurrentFloor(cmd.map_index, newPose))
            {
                logger.error_out("failed to set map[%d]", cmd.map_index);
                return;
            }
        }
        try{ 
            logger.info_out("set robot pose to (%.4f,%.4f, yaw:%.4f)",pose.x(),pose.y(),pose.yaw());
            core_.setPose(pose);
        } 
        catch (const rpos::system::detail::ExceptionBase &e)
        { 
            logger.warn_out("setRobotPose Exception occurred: %s", e.toString().c_str());
        }
        if(cmd.localization_type == 2)
        {
            //relocalization
            float size = 10.0f; 
            doRecoverLocalization(pose, size); 
        }
    }

    LocalizationState ModbusBaseDevice::getLocalizationState()
    {
        if (reLocalizationAction_)
        {
            auto actionStatus = reLocalizationAction_.getStatus();
            if (actionStatus == rpos::core::ActionStatusRunning)
            {
                return LocalizationState::Relocalizating;
            }
            else if( actionStatus == rpos::core::ActionStatusFinished)
            {
                reLocalizationAction_ = rpos::actions::MoveAction();
                return LocalizationState::Normal;
            }
            else
            {
                reLocalizationAction_ = rpos::actions::MoveAction();
                return LocalizationState::Error;
            }
        }
        if(core_.getMapUpdate())
        {
            return LocalizationState::Mapping;
        }
        auto health = core_.getRobotHealth();
        if(health.hasError || health.hasFatal)
        {
            return LocalizationState::Error; 
        }
        return LocalizationState::Normal;
    }

    void ModbusBaseDevice::updateRobotStatus(RobotBasicStatus& status)
    {
        memset(&status, 0 ,sizeof(RobotBasicStatus));
        if(!core_)
            return;
        try
        {             
            status.state = (uint16_t)getLocalizationState();
            auto pose = core_.getPose();
            auto quality = core_.getLocalizationQuality(); 
            auto speed = core_.getSpeed();
            status.timstamp = rpos::system::util::high_resolution_clock::get_time_in_ms();
            status.param = 0;
            status.x = int32_t(pose.x() * 1000);
            status.y = int32_t(pose.y() * 1000);
            status.yaw = int16_t(pose.yaw() * 10000);
            status.map_index = mapIndex_;
            status.localization_quality = quality; 
            status.omega = int16_t(speed.angularSpeed * 10000);
            status.cmd_timstamp = lastCtrlCmd_.timstamp;
        }
        catch (const rpos::system::detail::ExceptionBase &e)
        { 
            logger.warn_out("updateRobotStatus Exception occurred: %s", e.toString().c_str());
            core_ = rpos::robot_platforms::SlamwareCorePlatform();
        }
    }

    void  ModbusBaseDevice::sendStatusToClient(const RobotBasicStatus& status)
    {  
        if(udpClient_->isConnected())
        {
            udpClient_->sendRobotBasicStatusData(status);
        }
    }

    void ModbusBaseDevice::worker()
    {
        if(!server_.start())
        {
            logger.error_out("failed to start modbus server");
            return;
        }
        constexpr int kIntervalMS = 20;
        while(working_)
        {
            rpos::system::util::StopWatch watcher;
            if(!tryConnectToPlatform())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(3000));
                continue;
            }
            RobotBasicStatus status;
            updateRobotStatus(status);
            sendStatusToClient(status);

            CtrlCmd cmd;
            if (isCtrlCmdUpdated(cmd))
            {
                onCtrlCmd(cmd);
            } 
            udpServer_->checkHeartbeat();
            int ts = int(watcher.msSinceStart());
            if (ts < kIntervalMS)
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(kIntervalMS - ts));
            }
            else if (ts > 50)
            {
                logger.info_out("iteration time cost:%d",ts);
            }
        }
        server_.stop();
    }
    
    void ModbusBaseDevice::onReceiveFromUDP(int& startAddr, std::vector<uint16_t>& data)
    { 
        if(!server_.writeRegisters(startAddr, data))
        {
            logger.error_out("failed to write registers, start address:%d, count:%d", startAddr, data.size());
        }
    }
}}}