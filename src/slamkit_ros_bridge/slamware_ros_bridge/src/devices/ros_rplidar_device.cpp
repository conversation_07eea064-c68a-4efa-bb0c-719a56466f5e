#include "devices/ros_rplidar_device.h"
#include "devices_manager_service.h"

namespace rp { namespace slamware { namespace utils {

    RosRPLidarDevice::RosRPLidarDevice(boost::shared_ptr<DevicesManagerService> deviceManager)
        : deviceManager_(deviceManager)
    {
        //
    }

    RosRPLidarDevice::~RosRPLidarDevice()
    {
        //
    }

    bool RosRPLidarDevice::getScanData(rpos::message::lidar::LidarScan& lidarData)
    {    
        if (auto devMgr = deviceManager_.lock())
        {
            return devMgr->getScanData(lidarData);
        }
        return false;
    }

}}}
