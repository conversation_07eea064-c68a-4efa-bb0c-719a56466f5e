#include "devices/modbus_udp_server.h"
#include "devices/modbus/modbus.h"
#include <boost/make_shared.hpp>
#include <rpos/system/util/time_util.h>

namespace
{
    std::uint16_t BigEndianToLittleEndian(std::uint16_t value) 
    {
       return ((value << 8) & 0xFF00) |
              ((value >> 8) & 0x00FF);
    }
}

namespace rp { namespace slamware { namespace utils {

    static boost::shared_ptr<ModbusUdpServer> getConnection(ModbusUdpServerHandler::Pointer pointer)
    {
        return boost::dynamic_pointer_cast<ModbusUdpServer>(pointer);
    }

#define GET_CONNECTION \
    auto conn = getConnection(server); \
    if (!conn) return;

    rpos::system::util::LogScope ModbusUdpServerHandler::logger("rp.slamware.utils.modbus_udp_server");

    void ModbusUdpServerHandler::onReceiveError(Pointer server, const boost::system::error_code& ec, const boost::asio::ip::udp::endpoint& receiveEndpoint)
    {
        std::stringstream ss;
        ss << receiveEndpoint.address().to_string() << ":" << receiveEndpoint.port();
        logger.error_out("%s onReceiveError:%s(%d)", ss.str().c_str(), ec.message().c_str(), ec.value());
    }

    void ModbusUdpServerHandler::onReceiveComplete(Pointer server, const unsigned char* buffer, size_t readBytes, const boost::asio::ip::udp::endpoint& receiveEndpoint)
    {
        std::stringstream ss;
        ss << receiveEndpoint.address().to_string() << ":" << receiveEndpoint.port();
        logger.debug_out("%s onReceiveComplete: size: %d", ss.str().c_str(), readBytes);
        GET_CONNECTION;
        conn->onResponseReceived_(buffer, readBytes);
    }

    rpos::system::util::LogScope ModbusUdpServer::logger("rp.slamware.utils.modbus_udp_server");

    ModbusUdpServer::ModbusUdpServer(int port)
    : rpos::system::util::UdpServer<ModbusUdpServerHandler, 1024000>(boost::asio::ip::udp::endpoint(boost::asio::ip::udp::v4(), port))
    { 
    }

    ModbusUdpServer::~ModbusUdpServer()
    {
    }

    void ModbusUdpServer::registerHandler(udpServerReceiveHandler handler)
    {
        receiveHandler_ = handler;
    }

    void ModbusUdpServer::checkHeartbeat()
    {
        if(!lastRecvTime_)
            return;
        auto now = rpos::system::util::high_resolution_clock::get_time_in_ms();
        if(now - *lastRecvTime_ > 2000)
        {
            logger.info_out("no odom received for more than 2 second");
            lastRecvTime_.reset();
            return;
        }
    }

    void ModbusUdpServer::onResponseReceived_(const unsigned char* buffer, size_t readBytes)
    {
        if (!receiveHandler_)
            return;

        // address(1byte) + function code(1byte) + start address(2bytes) + register quantity(2bytes) + byte count(1byte) + register value(nbytes) + crc16(2bytes)
        if (readBytes < 11)
        {
            logger.info_out("receive udp read bytes: %d smaller than 10", readBytes);
            return;
        }
        else if (buffer[1] != 0x10)
        {
            logger.info_out("receive invalid function code: %d", buffer[1]);
            return;
        }

        // start address
        std::uint16_t tmpStartAddr = 0;
        std::memcpy(&tmpStartAddr, &buffer[2], 2 * sizeof(unsigned char));
        int startAddr = static_cast<int>(BigEndianToLittleEndian(tmpStartAddr));
        // byte count
        std::uint8_t byteCount = buffer[6];
        if (readBytes < byteCount + 9)
        {
            logger.info_out("receive udp read bytes: %d smaller than %d", readBytes, byteCount + 9);
            return;
        }
        // register value
        std::vector<std::uint16_t> data(byteCount / 2, 0);
        int offset = 7;
        for (std::uint8_t i = 0; i < byteCount / 2; ++i)
        { 
            data[i] = MODBUS_GET_INT16_FROM_INT8(buffer, offset);
            offset += 2 * sizeof(unsigned char);
        }
        if(!lastRecvTime_)
        {
            logger.info_out("start to receive odom");
        }
        lastRecvTime_ = rpos::system::util::high_resolution_clock::get_time_in_ms();
        receiveHandler_(startAddr, data);
    }

} } }
