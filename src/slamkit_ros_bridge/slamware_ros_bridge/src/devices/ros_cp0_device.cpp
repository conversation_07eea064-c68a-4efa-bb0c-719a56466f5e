#include "devices/ros_cp0_device.h"
#include "devices_manager_service.h"

#include <fstream>
#include <sstream>

namespace rp { namespace slamware { namespace utils {

    rpos::system::util::LogScope RosCp0Device::logger("rp.slamware.utils.ros_cp0_device"); 

    RosCp0Device::RosCp0Device(boost::shared_ptr<DevicesManagerService> deviceManager)
        : deviceManager_(deviceManager)
    {
    }

    RosCp0Device::~RosCp0Device()
    {
        //
    }   

    void RosCp0Device::getImuAllSensorData(rpos::message::imu::ImuAllSensorData& sensorData)
    {
        if (auto devMgr = deviceManager_.lock())
        {
            devMgr->getImuAllSensorData(sensorData);
        }
    } 
}}}