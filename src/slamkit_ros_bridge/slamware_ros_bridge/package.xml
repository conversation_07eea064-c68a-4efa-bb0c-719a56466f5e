<?xml version="1.0"?>
<package format="3">
  <name>slamware_ros_bridge</name>
  <version>2.0.0</version>
  <description>The Slamware ROS2 bridge package</description>

  <maintainer email="<EMAIL>">Slamtec Ros Maintainer</maintainer>
  <license>BSD</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>nav_msgs</build_depend>
  <build_depend>rclcpp</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_ros</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>tf2_msgs</build_depend>


  <build_depend>rosidl_default_generators</build_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>
  <member_of_group>rosidl_interface_packages</member_of_group>


  <build_export_depend>nav_msgs</build_export_depend>
  <build_export_depend>sensor_msgs</build_export_depend>
  <build_export_depend>std_msgs</build_export_depend>
  <build_export_depend>tf2</build_export_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>rclcpp</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>tf2</exec_depend>
  <exec_depend>message_runtime</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
