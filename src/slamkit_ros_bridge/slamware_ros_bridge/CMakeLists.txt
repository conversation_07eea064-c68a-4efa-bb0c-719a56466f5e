cmake_minimum_required(VERSION 3.5)
project(slamware_ros_bridge)

# Search for Slamware SDK
find_path(slamware_sdk_INCLUDE_DIR rpos/rpos.h ${PROJECT_SOURCE_DIR}/../slamware_sdk/include)
find_path(slamware_sdk_LIBRARY librpos_framework.a ${PROJECT_SOURCE_DIR}/../slamware_sdk/lib)
if(slamware_sdk_INCLUDE_DIR AND slamware_sdk_LIBRARY)
  set(SLTC_SDK_INC_DIR "${slamware_sdk_INCLUDE_DIR}")
  set(SLTC_SDK_LIB_DIR "${slamware_sdk_LIBRARY}")
else()
  message(FATAL_ERROR "Slamware SDK directory not found")
endif()

if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

set(CMAKE_POSITION_INDEPENDENT_CODE OFF)
add_link_options(-no-pie)

# Find dependencies
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclpy REQUIRED)

find_package(nav_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(tf2_msgs REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)

find_package(Threads) 

include_directories(include)

# Create executable
add_executable(slamware_ros_bridge_node
  src/devices_manager_service.cpp
  src/devices/modbus_server.cpp
  src/devices/modbus_udp_client.cpp
  src/devices/modbus_udp_server.cpp
  src/devices/modbus_base_device.cpp
  src/devices/ros_base_device.cpp
  src/devices/ros_rplidar_device.cpp
  src/devices/ros_cp0_device.cpp
  src/config.cpp
  src/ros2_node.cpp
  src/ros_node_service.cpp
  src/main.cpp
)

ament_target_dependencies(slamware_ros_bridge_node rclcpp std_msgs tf2_ros sensor_msgs nav_msgs geometry_msgs tf2_geometry_msgs)

# Include directories
target_include_directories(slamware_ros_bridge_node
  PRIVATE ${SLTC_SDK_INC_DIR}
)

# Compiler options
target_compile_options(slamware_ros_bridge_node
  PRIVATE -Wno-deprecated-declarations
)

# Link libraries
target_link_libraries(slamware_ros_bridge_node
  ${SLTC_SDK_LIB_DIR}/libpseudo_device.a
  ${SLTC_SDK_LIB_DIR}/liblibany2tcp_bridge.a
  ${SLTC_SDK_LIB_DIR}/libslamware_sdp_driver.a
  ${SLTC_SDK_LIB_DIR}/libserial_tcp_client.a
  ${SLTC_SDK_LIB_DIR}/libinfra.a
  ${SLTC_SDK_LIB_DIR}/librpos_robotplatforms_rpslamware.a
  ${SLTC_SDK_LIB_DIR}/librpos_framework.a
  ${SLTC_SDK_LIB_DIR}/librpos_messaging.a
  ${SLTC_SDK_LIB_DIR}/libbase64.a
  ${SLTC_SDK_LIB_DIR}/librlelib.a
  ${SLTC_SDK_LIB_DIR}/libjsoncpp.a
  ${SLTC_SDK_LIB_DIR}/libcurl.a
  ${SLTC_SDK_LIB_DIR}/libcares.a
  ${SLTC_SDK_LIB_DIR}/libssl.a
  ${SLTC_SDK_LIB_DIR}/libcrypto.a
  ${SLTC_SDK_LIB_DIR}/libboost_atomic.a
  ${SLTC_SDK_LIB_DIR}/libboost_chrono.a
  ${SLTC_SDK_LIB_DIR}/libboost_date_time.a
  ${SLTC_SDK_LIB_DIR}/libboost_regex.a 
  ${SLTC_SDK_LIB_DIR}/libboost_filesystem.a
  ${SLTC_SDK_LIB_DIR}/libboost_system.a
  ${SLTC_SDK_LIB_DIR}/libboost_thread.a
  ${SLTC_SDK_LIB_DIR}/libboost_random.a
  ${SLTC_SDK_LIB_DIR}/libusb-1.0.a
  ${SLTC_SDK_LIB_DIR}/libz.a
  ${SLTC_SDK_LIB_DIR}/libmodbus.a
  ${SLTC_SDK_LIB_DIR}/libiceoryx_posh.a
  ${SLTC_SDK_LIB_DIR}/libiceoryx_hoofs.a
  ${SLTC_SDK_LIB_DIR}/libiceoryx_platform.a
  ${rclcpp_LIBRARIES}
  pthread
  dl
  rt
)

# Install targets
install(TARGETS slamware_ros_bridge_node
  DESTINATION lib/${PROJECT_NAME}
)

install(DIRECTORY include/${PROJECT_NAME}/
  DESTINATION include/${PROJECT_NAME}
  FILES_MATCHING PATTERN "*.h"
  PATTERN ".svn" EXCLUDE
)
ament_export_include_directories(include/${PROJECT_NAME})

install(FILES
  launch/slamware_ros_bridge.xml
  DESTINATION share/${PROJECT_NAME}/launch
)

# Install launch files.
install(DIRECTORY
  launch
  DESTINATION share/${PROJECT_NAME}/
)

ament_package()