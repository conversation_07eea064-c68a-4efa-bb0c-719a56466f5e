<launch>
  <node name="slamware_ros_bridge_node" pkg="slamware_ros_bridge" exec="slamware_ros_bridge_node" output="screen">
    <param name = "scan_sub_topic"             value = "/fusion_scan"/>
    <param name = "odometry_sub_topic"         value = "/odom"/>
    <param name = "is_accumulated_odometry"    value = "true"/>
    <param name = "velocity_pub_topic"         value = "slamware_cmd_vel"/>
    <param name = "enable_modbus_base_device"  value = "false"/> 
    <param name = "enable_shared_memory_lidar" value = "true"/> 
    <param name = "device_host"                value = "**************"/> 
  </node>
</launch>
