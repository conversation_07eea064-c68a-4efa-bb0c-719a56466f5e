cmake_minimum_required(VERSION 3.8)
project(rpos_common)

if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(Eigen3 REQUIRED)

# Build jsoncpp as static library dependency from rslamware/3rdparty
message(STATUS "Building jsoncpp as static library dependency from rslamware/3rdparty")
# Navigate to the project root directory (rslamware) from src/rpos_common
get_filename_component(PROJECT_ROOT_DIR ${CMAKE_SOURCE_DIR}/../.. ABSOLUTE)
set(JSONCPP_SOURCE_DIR ${PROJECT_ROOT_DIR}/3rdparty/jsoncpp)

# Check if jsoncpp directory exists
if(NOT EXISTS ${JSONCPP_SOURCE_DIR})
  message(FATAL_ERROR "jsoncpp source directory not found: ${JSONCPP_SOURCE_DIR}")
endif()

add_subdirectory(${JSONCPP_SOURCE_DIR} jsoncpp_build)

add_library(${PROJECT_NAME} STATIC
  src/rle.cpp
  src/core/metadata.cpp
  src/core/pose.cpp
  src/core/geometry.cpp
  src/serialization/core_objects_serialization.cpp
  src/serialization/buffer_stream_adaptor.cpp
  src/io/file_stream.cpp
  src/io/i_stream_io.cpp
  src/io/memory_read_stream.cpp
  src/io/memory_write_stream.cpp
  src/system/io_utils.cpp
  src/system/string_utils.cpp
  src/system/exception.cpp
  src/stcm/map_layer.cpp
  src/stcm/grid_map_layer.cpp
  src/stcm/line_map_layer.cpp
  src/stcm/points_map_layer.cpp
  src/stcm/pose_map_layer.cpp
  src/stcm/rectangle_area_map_layer.cpp
  src/stcm/polygon_area_map_layer.cpp 
  src/stcm/image_features_map_layer.cpp
  src/stcm/composite_map.cpp
  src/stcm/composite_map_rw_impl_base.cpp
  src/stcm/composite_map_reader_impl.cpp
  src/stcm/composite_map_writer_impl.cpp
  src/stcm/composite_map_reader.cpp
  src/stcm/composite_map_writer.cpp
)

target_include_directories(${PROJECT_NAME} PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
  ${EIGEN3_INCLUDE_DIRS}
)

# Add jsoncpp include directories privately (not exported)
target_include_directories(${PROJECT_NAME} PRIVATE
  ${JSONCPP_SOURCE_DIR}/include
)

set_target_properties(${PROJECT_NAME} PROPERTIES
  POSITION_INDEPENDENT_CODE ON
)

ament_target_dependencies(${PROJECT_NAME}
  rclcpp
  std_msgs
  Eigen3
)

# Link jsoncpp static library after ament_target_dependencies
target_link_libraries(${PROJECT_NAME} jsoncpp_static)

install(DIRECTORY include/
  DESTINATION include
)

# Don't install jsoncpp headers - they are used internally only

# Don't install jsoncpp separately - it's statically linked into rpos_common

install(TARGETS ${PROJECT_NAME}
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

ament_export_include_directories(include)
ament_export_libraries(${PROJECT_NAME})

ament_export_dependencies(
  rclcpp
  std_msgs
  Eigen3
)

# jsoncpp is statically linked, no need to export targets

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
