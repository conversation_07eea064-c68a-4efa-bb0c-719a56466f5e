cmake_minimum_required(VERSION 3.8)
project(rpos_common)

if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(Eigen3 REQUIRED)

# Use precompiled Boost.JSON library from slamware_sdk
get_filename_component(SLAMWARE_SDK_DIR ${CMAKE_SOURCE_DIR}/../../src/slamkit_ros_bridge/slamware_sdk ABSOLUTE)
set(JSON_INCLUDE_DIR ${SLAMWARE_SDK_DIR}/include)
set(JSON_LIB ${SLAMWARE_SDK_DIR}/lib/libjsoncpp.a)

# Check if <PERSON>SO<PERSON> exists
if(NOT EXISTS ${JSON_INCLUDE_DIR}/json/json.h)
  message(FATAL_ERROR "JSON include directory not found: ${JSON_INCLUDE_DIR}")
endif()
if(NOT EXISTS ${JSON_LIB})
  message(FATAL_ERROR "JSON library not found: ${JSON_LIB}")
endif()

add_library(${PROJECT_NAME} STATIC
  src/rle.cpp
  src/core/metadata.cpp
  src/core/pose.cpp
  src/core/geometry.cpp
  src/serialization/core_objects_serialization.cpp
  src/serialization/buffer_stream_adaptor.cpp
  src/io/file_stream.cpp
  src/io/i_stream_io.cpp
  src/io/memory_read_stream.cpp
  src/io/memory_write_stream.cpp
  src/system/io_utils.cpp
  src/system/string_utils.cpp
  src/system/exception.cpp
  src/stcm/map_layer.cpp
  src/stcm/grid_map_layer.cpp
  src/stcm/line_map_layer.cpp
  src/stcm/points_map_layer.cpp
  src/stcm/pose_map_layer.cpp
  src/stcm/rectangle_area_map_layer.cpp
  src/stcm/polygon_area_map_layer.cpp 
  src/stcm/image_features_map_layer.cpp
  src/stcm/composite_map.cpp
  src/stcm/composite_map_rw_impl_base.cpp
  src/stcm/composite_map_reader_impl.cpp
  src/stcm/composite_map_writer_impl.cpp
  src/stcm/composite_map_reader.cpp
  src/stcm/composite_map_writer.cpp
  src/algorithm/aperture.cpp
  src/algorithm/grid_cell_utils.cpp
)

target_include_directories(${PROJECT_NAME} PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
  ${EIGEN3_INCLUDE_DIRS}
)

# Add Boost.JSON include directories privately (not exported)
target_include_directories(${PROJECT_NAME} PRIVATE
  ${JSON_INCLUDE_DIR}
)

set_target_properties(${PROJECT_NAME} PROPERTIES
  POSITION_INDEPENDENT_CODE ON
)

ament_target_dependencies(${PROJECT_NAME}
  rclcpp
  std_msgs
  Eigen3
)

# Link Boost.JSON static library
target_link_libraries(${PROJECT_NAME} ${JSON_LIB})

install(DIRECTORY include/
  DESTINATION include
)

# Don't install jsoncpp headers - they are used internally only

# Don't install jsoncpp separately - it's statically linked into rpos_common

install(TARGETS ${PROJECT_NAME}
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

ament_export_include_directories(include)
ament_export_libraries(${PROJECT_NAME})

ament_export_dependencies(
  rclcpp
  std_msgs
  Eigen3
)

# jsoncpp is statically linked, no need to export targets

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
