#pragma once

#include <string>
#include <vector>
#include <cstdint>
#include <stdio.h>

namespace rpos_common { namespace system { 

    /**
    * \brief Check if a file exists
    */
     bool file_exists(const std::string& filename);

    /**
    * \brief Check if a directory exists
    */
     bool dir_exists(const std::string& filename);

    /**
    * \brief Create a directory
    */
     bool dir_create(const std::string& path, bool createParentDirectoryIfNotExist = true);

	/**
	* \brief Find all files and directories in a directory
	*/
	 bool dir_scan(const std::string& path, std::vector<std::string>& outFiles);

    /**
    * \brief Copy file
    */
     bool file_copy(const std::string& from, const std::string& to);

    /**
    * \brief Move file
    */
     bool file_move(const std::string& from, const std::string& to);

    /**
    * \brief Delete file
    */
     bool file_del(const std::string& path);

    /**
    * \brief Open file by wstring
    */
     ::FILE* file_open(const std::wstring& path, const std::wstring& mode);

    /**
    * \brief Get temp directory
    */
     std::string temp_dir();

    /**
    * \brief Get the size of particular file
    */
     std::uint64_t file_size(const std::string& filename);

    /**
    * \brief Read all content of the file
    */
     std::string read_all_text(const std::string& filename);

    /**
    * \brief Read all lines of a file
    */
     std::vector<std::string> read_all_lines(const std::string& filename);

    /**
    * \brief Write text to a file
    */
     bool write_all_text(const std::string& filename, const std::string& content);

    /**
    * \brief Read all bytes of the file
    */
     std::vector<unsigned char> read_all_bytes(const std::string& filename);

    /**
    * \brief Write all bytes to the file
    */
     bool write_all_bytes(const std::string& filename, const std::vector<unsigned char>& buffer);

    /**
    * \brief Write all bytes to the file
    */
     bool write_all_bytes(const std::string& filename, const unsigned char* buffer, size_t size);

} }
