#pragma once

#include <string>
#include <vector>
#include <cstdint>
#include <cstring>
#include <cwchar>

#define RPOS_STRING_NPOS        ((size_t)-1)

namespace rpos_common { namespace system {

    /**
    * \brief Convert value to string representation
    */
    std::string to_string(std::int32_t value);

    /**
    * \brief Convert value to string representation
    */
    std::string to_string(std::int64_t value);

    /**
    * \brief Convert value to string representation
    */
    std::string to_string(std::uint32_t value);

	/**
	* \brief Convert value to string representation
	*/
	std::string to_string(std::uint64_t value);

	/**
	* \brief Convert value to string representation
	*/
	std::string to_string(float value);

	/**
	* \brief Convert value to string representation
	*/
	std::string to_string(double value);

    /**
    * \brief Generate hex string
    */
    std::string to_hex_string(const std::uint8_t* buffer, size_t size, bool upperCase = false);

    /**
    * \brief Generate uuid string from byte array(16 bytes)
    */
    std::string to_uuid_string(const std::uint8_t* buffer, bool upperCase = false);

    /**
    * \brief Parse value from string
    */
    bool try_parse(const std::string& s, int& v, int base = 10);

    /**
    * \brief Parse value from string
    */
    bool try_parse(const std::string& s, long& v, int base = 10);

    /**
    * \brief Parse value from string
    */
    bool try_parse(const std::string& s, long long& v, int base = 10);

    /**
    * \brief Parse value from string
    */
    bool try_parse(const std::string& s, std::uint64_t& v, int base = 10);

    /**
    * \brief Parse value from string
    */
    bool try_parse(const std::string& s, float& v);

    /**
    * \brief Parse value from string
    */
    bool try_parse(const std::string& s, double& v);

    /**
    * \brief Parse value from string
    */
    bool try_parse_uuid(const std::string& s, std::vector<std::uint8_t>& data);

    /**
    * \brief Remove leading spaces (including space, tab char) from a string
    */
    std::string trim_left(const std::string& that);

    /**
    * \brief Remove tailing spaces from a string
    */
    std::string trim_right(const std::string& that);

    /**
    * \brief Remove leading and tailing spaces from a string
    */
    std::string trim(const std::string& that);

    /**
    * \brief Replace all string parts matches `match` with `replace` in string `s`
    */
    std::string replace(const std::string& s, const std::string& match, const std::string& replace);

    /**
    * \brief Replace all string parts matches `match` with `replace` in string `s`
    */
    std::string replace(const std::string& s, char match, const std::string& replace);

    /**
    * \brief Replace all string parts matches `match` with `replace` in string `s`
    */
    std::string replace(const std::string& s, char match, char replace);

    /**
    * \brief Remove all string parts matches `match` in string `s`
    */
    std::string remove(const std::string& s, const std::string& match);

    /**
    * \brief Remove all string parts matches `match` in string `s`
    */
    std::string remove(const std::string& s, char match);

    /**
    * \brief Check if string `that` is pure spaces
    */
    bool is_whitespace(const std::string& that);

    /**
    * \brief Check if string `text` starts with string `match`
    */
    bool starts_with(const std::string& text, const std::string& match);

    /**
    * \brief Check if string `text` starts with character `match`
    */
    bool starts_with(const std::string& text, char match);

    /**
    * \brief Check if string `text` ends with string `match`
    */
    bool ends_with(const std::string& text, const std::string& match);

    /**
    * \brief Check if string `text` ends with string `match`
    */
    bool ends_with(const std::string& text, char match);

    /**
    * \brief Check if string `text` constains string `match`
    */
    bool contains(const std::string& text, const std::string& match);

    /**
    * \brief Check if string `text` constains character `match`
    */
    bool contains(const std::string& text, char match);

    /**
    * \brief Lookup string `match` in `text`
    *
    * \return The position of the found string, string::npos for not found
    */
    std::string::size_type find(const std::string& text, const std::string& match, std::string::size_type start = 0);

    /**
    * \brief Lookup string `match` in `text`
    *
    * \return The position of the found string, string::npos for not found
    */
    std::string::size_type find(const std::string& text, char match, std::string::size_type start = 0);

    /**
    * \brief Split string with `splitter`
    */
    std::vector<std::string> split(const std::string& that, char splitter, size_t max_parts = 0, bool merge_continuous_splitters = false);

    /**
    * \brief Split string with `splitter`, and trim the splitted parts
    */
    std::vector<std::string> split_trim(const std::string& that, char splitter, size_t max_parts = 0, bool merge_continuous_splitters = false);

    /**
    * \brief Concat strings with connector
    */
    std::string join(const std::vector<std::string>& strings, const std::string& connector);

    /**
    * \brief Concat strings with connector
    */
    std::string join(const std::vector<std::string>& strings, char connector);

    /**
    * \brief Concat strings
    */
    std::string join(const std::vector<std::string>& strings);

    /**
    * \brief Repeat string
    *
    * For instance: string("hello") * 5 will produce a string "hellohellohellohellohello"
    */
    std::string operator*(const std::string& a, int b);

    /**
    * \brief Repeat string
    *
    * For instance: 5 * string("hello") will produce a string "hellohellohellohellohello"
    */
    std::string operator*(int a, const std::string& b);
    
    /**
    * \brief Transform string to its hex representation in textual format.  "ab" to "6162"
    */
    std::string stringToHexString(const std::string& s);

    /**
    * \brief Transform string's hex representation in textual format back to string.  "6162" to "ab"
    */
    std::string hexStringToString(const std::string& s);

    /**
    * \brief Transform all character to lower case
    */
    std::string lowerCase(const std::string& s);

    /**
    * \brief Transform all character to upper case
    */
    std::string upperCase(const std::string& s);

    bool utf8_to_wcs(::std::wstring& rDest, const char* pcSrc, size_t szLen);

    bool utf8_to_wcs(::std::wstring& rDest, const ::std::string& rcSrc);

    inline ::std::wstring utf8_to_wcs(const ::std::string& rcSrc)
    {
        ::std::wstring wstrRet;
        utf8_to_wcs(wstrRet, rcSrc);
        return wstrRet;
    }

    bool wcs_to_utf8(::std::string& rDest, const wchar_t* pcSrc, size_t szLen);

    bool wcs_to_utf8(::std::string& rDest, const ::std::wstring& rcSrc);

    inline ::std::string wcs_to_utf8(const ::std::wstring& rcSrc)
    {
        ::std::string strRet;
        wcs_to_utf8(strRet, rcSrc);
        return strRet;
    }

} } 
