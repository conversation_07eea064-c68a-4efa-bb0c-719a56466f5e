﻿#pragma once
#include <functional>

namespace rpos_common { namespace system { 

    class DestructHelper
    {
    public:
        typedef std::function< void() >           destruct_fun_t;

    public:
        DestructHelper()
        {
        }
        DestructHelper(const DestructHelper&) = delete;
        DestructHelper& operator=(const DestructHelper&) = delete;

        explicit DestructHelper(const destruct_fun_t& destructFun)
            : destructFun_(destructFun)
        {
        }

        ~DestructHelper()
        {
            if (destructFun_)
                destructFun_();
        }

        void resetDestructFun()
        {
            destructFun_ = destruct_fun_t();
        }

        void setDestructFun(const destruct_fun_t& destructFun)
        {
            destructFun_ = destructFun;
        }

    private:
        destruct_fun_t destructFun_;
    };

}}
