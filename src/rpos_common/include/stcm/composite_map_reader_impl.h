#pragma once

#include <stcm/composite_map_reader.h>
#include <stcm/composite_map_rw_impl_base.h>
#include <stcm/grid_map_layer.h>
#include <stcm/line_map_layer.h>
#include <stcm/pose_map_layer.h>
#include <stcm/points_map_layer.h>
#include <stcm/image_features_map_layer.h>
#include <stcm/rectangle_area_map_layer.h>
#include <stcm/polygon_area_map_layer.h>
#include <sstream>
#include <stdexcept>
#include <unordered_map>


namespace rpos_common { namespace stcm {

    // Helper function to replace boost::lexical_cast
    template<typename T>
    T string_cast(const std::string& str) {
        std::istringstream iss(str);
        T result;
        if (!(iss >> result) || !iss.eof()) {
            throw std::invalid_argument("string_cast: conversion failed for \"" + str + "\"");
        }
        return result;
    }

    // Specialization for std::string (no conversion needed)
    template<>
    inline std::string string_cast<std::string>(const std::string& str) {
        return str;
    }

    class CompositeMapReaderImpl : public CompositeMapRwImplBase
    {
    private:
        static const uint16_t C_READER_VERSION = 0x0002U;

    public:
        CompositeMapReaderImpl(void);
        ~CompositeMapReaderImpl(void);

    public:
        std::shared_ptr<CompositeMap> loadFromStream(rpos_common::io::IStream& inStream);

    private:
        const void* deserializeMetadataFromMem(core::Metadata& rMetadata, const void* pcSrcBegin, const void* pcSrcEnd);
        void deserializeMetadataFromStream(core::Metadata& rMetadata, rpos_common::io::IStream& inStream);

    private:
        template<class _ValT>
        bool tryGetValInMetadata(_ValT& rDest, const core::Metadata& rcMetadata, const std::string& rcKey) const
        {
            auto citTmp = rcMetadata.dict().find(rcKey);
            if (rcMetadata.dict().cend() != citTmp)
            {
                try {
                    rDest = string_cast<_ValT>(citTmp->second);
                    return true;
                } catch (const std::exception&) {
                    return false;
                }
            }
            return false;
        }
        template<class _ValT>
        bool tryGetValInMetadata(_ValT& rDest, const core::Metadata& rcMetadata, const std::string& rcKey, const _ValT& rcDefVal) const
        {
            auto citTmp = rcMetadata.dict().find(rcKey);
            if (rcMetadata.dict().cend() != citTmp)
            {
                try {
                    rDest = string_cast<_ValT>(citTmp->second);
                    return true;
                } catch (const std::exception&) {
                    rDest = rcDefVal;
                    return false;
                }
            }
            rDest = rcDefVal;
            return false;
        }

    private:
        class MapLayerFactoryBase
        {
        public:
            explicit MapLayerFactoryBase(CompositeMapReaderImpl* pCmrImpl);
            virtual ~MapLayerFactoryBase(void) {}
        public:
            virtual MapLayerSharedPtr createMapLayerByMem(core::Metadata& rMetadata, const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType) = 0;
        protected:
            void refreshMapLayerProperties_(MapLayerSharedPtr pMapLayer);
            const ubyte_buf_type& procBodyInFileMaybeDecompress_(const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType);
        protected:
            CompositeMapReaderImpl* const m_pCmrImpl;
        };

        class UnknownMapLayerFactory : public MapLayerFactoryBase
        {
        public:
            explicit UnknownMapLayerFactory(CompositeMapReaderImpl* pCmrImpl): MapLayerFactoryBase(pCmrImpl) {}
            virtual ~UnknownMapLayerFactory(void) {}
        public:
            virtual MapLayerSharedPtr createMapLayerByMem(core::Metadata& rMetadata, const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType);
        };

        class GridMapLayerFactory : public MapLayerFactoryBase
        {
        public:
            explicit GridMapLayerFactory(CompositeMapReaderImpl* pCmrImpl): MapLayerFactoryBase(pCmrImpl) {}
            virtual ~GridMapLayerFactory(void) {}
        public:
            virtual MapLayerSharedPtr createMapLayerByMem(core::Metadata& rMetadata, const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType);
        };

        class LineMapLayerFactory : public MapLayerFactoryBase
        {
        public:
            explicit LineMapLayerFactory(CompositeMapReaderImpl* pCmrImpl): MapLayerFactoryBase(pCmrImpl) {}
            virtual ~LineMapLayerFactory(void) {}
        public:
            virtual MapLayerSharedPtr createMapLayerByMem(core::Metadata& rMetadata, const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType);
        };

        class PoseMapLayerFactory : public MapLayerFactoryBase
        {
        public:
            explicit PoseMapLayerFactory(CompositeMapReaderImpl* pCmrImpl): MapLayerFactoryBase(pCmrImpl) {}
            virtual ~PoseMapLayerFactory(void) {}
        public:
            virtual MapLayerSharedPtr createMapLayerByMem(core::Metadata& rMetadata, const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType);
        };

        class PointsMapLayerFactory : public MapLayerFactoryBase
        {
        public:
            explicit PointsMapLayerFactory(CompositeMapReaderImpl* pCmrImpl): MapLayerFactoryBase(pCmrImpl) {}
            virtual ~PointsMapLayerFactory(void) {}
        public:
            virtual MapLayerSharedPtr createMapLayerByMem(core::Metadata& rMetadata, const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType);
        };

        class ImageFeaturesMapLayerFactory : public MapLayerFactoryBase
        {
        public:
            explicit ImageFeaturesMapLayerFactory(CompositeMapReaderImpl* pCmrImpl) : MapLayerFactoryBase(pCmrImpl) {}
            virtual ~ImageFeaturesMapLayerFactory(void) {}
        public:
            virtual MapLayerSharedPtr createMapLayerByMem(core::Metadata& rMetadata, const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType);
        };

        class RectangleAreaLayerFactory : public MapLayerFactoryBase
        {
        public:
            static const char* const LegacyType; //compatible with v2.8, remove it later
            explicit RectangleAreaLayerFactory(CompositeMapReaderImpl* pCmrImpl): MapLayerFactoryBase(pCmrImpl) {}
            virtual ~RectangleAreaLayerFactory(void) {}
        public:
            virtual MapLayerSharedPtr createMapLayerByMem(core::Metadata& rMetadata, const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType);
        };

        class PolygonAreaLayerFactory : public MapLayerFactoryBase
        {
        public:
            explicit PolygonAreaLayerFactory(CompositeMapReaderImpl* pCmrImpl): MapLayerFactoryBase(pCmrImpl) {}
            virtual ~PolygonAreaLayerFactory(void) {}
        public:
            virtual MapLayerSharedPtr createMapLayerByMem(core::Metadata& rMetadata, const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType);
        };

        typedef std::unordered_map< std::string, MapLayerFactoryBase* >           MapLayerTypeToFactoryHashMap;

    private:
        void doLoadFileHeader_(StcmFileHeader& rFileHeader, CompositeMap& rCmpstMap, rpos_common::io::IStream& inStream);
        MapLayerFactoryBase* prepareMapLayerFactory_(CompressType& rCmprsType, const core::Metadata& rcMetadata);
        MapLayerSharedPtr doLoadOneMapSection_(rpos_common::io::IStream& inStream);

    private:
        UnknownMapLayerFactory m_tUnknownMlFactory;
        GridMapLayerFactory m_tGridMlFactory;
        LineMapLayerFactory m_tLineMlFactory;
        PoseMapLayerFactory m_tPoseMlFactory;
        PointsMapLayerFactory m_tPointsMlFactory;
        ImageFeaturesMapLayerFactory m_tImageFeaturesMlFactory;
        RectangleAreaLayerFactory m_tRectangleAreaMlFactory;
        PolygonAreaLayerFactory m_tPolygonAreaMlFactory;
        MapLayerTypeToFactoryHashMap m_hmTypeToFactory;
        //
        ubyte_buf_type m_tMetadataBuf;
        ubyte_buf_type m_tMapBodyBuf;
        ubyte_buf_type m_tDcmprsdMapBodyBuf;
    };
}} // namespace rpos_common::stcm
