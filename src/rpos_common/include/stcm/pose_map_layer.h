#pragma once

#include <stcm/map_layer.h>
#include <core/pose.h>
#include <core/metadata.h>
#include <core/pose_entry.h>
#include <map>
#include <string>
#include <vector>

namespace rpos_common { namespace stcm {

    class PoseMapLayer : public MapLayer
    {
    public:
        static const char* const Type;

    public:
        virtual void clear(void);

    public:
        const core::PoseEntryMap& poses() const;
        core::PoseEntryMap& poses();

    private:
    core::PoseEntryMap poses_;
    };

}}
