#pragma once

#include <stcm/composite_map_writer.h>
#include <stcm/composite_map_rw_impl_base.h>

#include <stcm/grid_map_layer.h>
#include <stcm/line_map_layer.h>
#include <stcm/pose_map_layer.h>
#include <stcm/points_map_layer.h>
#include <stcm/image_features_map_layer.h>
#include <stcm/rectangle_area_map_layer.h>
#include <stcm/polygon_area_map_layer.h>

namespace rpos_common { namespace stcm {

    class CompositeMapWriterImpl : public CompositeMapRwImplBase
    {
    private:
        static const uint16_t C_WRITER_VERSION = 0x0001U;
        static const uint16_t C_MULTI_FLOOR_WRITER_VERSION = 0x0002U;

    public:
        CompositeMapWriterImpl(void);
        ~CompositeMapWriterImpl(void);

    public:
        void saveToStream(rpos_common::io::IStream& outStream, const CompositeMap& rcCmpstMap);

    private:
        void serializeMetadataAppToMem(ubyte_buf_type& rDest, const core::Metadata& rcMetadata);
        void serializeMetadataWithOverwriteKvAppToMem(ubyte_buf_type& rDest, const core::Metadata& rcMetadata, const std::string& rcOverwriteKey, const std::string& rcOverwriteVal);

    private:
        class MapSectionWriterBase
        {
        public:
            explicit MapSectionWriterBase(CompositeMapWriterImpl* pCmwImpl);
            virtual ~MapSectionWriterBase(void) {}
        public:
            virtual void writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer) = 0;
        protected:
            void doWriteMapSection_(rpos_common::io::IStream& outStream, const ubyte_buf_type& rcMetadataBuf, const ubyte_buf_type& rcBodyBuf);
            const ubyte_buf_type& procBodyInMemMaybeCompress_(const ubyte_buf_type& rcBodyInMem, const core::Metadata& rcMetadata);
        protected:
            CompositeMapWriterImpl* const m_pCmwImpl;
        };

        class UnknownMapSectionWriter : public MapSectionWriterBase
        {
        public:
            explicit UnknownMapSectionWriter(CompositeMapWriterImpl* pCmwImpl): MapSectionWriterBase(pCmwImpl) {}
            virtual ~UnknownMapSectionWriter(void) {}
        public:
            virtual void writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer);
        };

        class GridMapSectionWriter : public MapSectionWriterBase
        {
        public:
            explicit GridMapSectionWriter(CompositeMapWriterImpl* pCmwImpl): MapSectionWriterBase(pCmwImpl) {}
            virtual ~GridMapSectionWriter(void) {}
        public:
            virtual void writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer);
        };

        class LineMapSectionWriter : public MapSectionWriterBase
        {
        public:
            explicit LineMapSectionWriter(CompositeMapWriterImpl* pCmwImpl): MapSectionWriterBase(pCmwImpl) {}
            virtual ~LineMapSectionWriter(void) {}
        public:
           virtual void writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer);
        };

        class PoseMapSectionWriter : public MapSectionWriterBase
        {
        public:
            explicit PoseMapSectionWriter(CompositeMapWriterImpl* pCmwImpl): MapSectionWriterBase(pCmwImpl) {}
            virtual ~PoseMapSectionWriter(void) {}
        public:
            virtual void writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer);
        };

        class PointsMapSectionWriter : public MapSectionWriterBase
        {
        public:
            explicit PointsMapSectionWriter(CompositeMapWriterImpl* pCmwImpl): MapSectionWriterBase(pCmwImpl) {}
            virtual ~PointsMapSectionWriter(void) {}
        public:
            virtual void writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer);
        };

        class ImageFeaturesMapSectionWriter : public MapSectionWriterBase
        {
        public:
            explicit ImageFeaturesMapSectionWriter(CompositeMapWriterImpl* pCmwImpl) : MapSectionWriterBase(pCmwImpl) {}
            virtual ~ImageFeaturesMapSectionWriter(void) {}
        public:
            virtual void writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer);
        };

        class RectangleAreaMapSectionWriter : public MapSectionWriterBase
        {
        public:
            explicit RectangleAreaMapSectionWriter(CompositeMapWriterImpl* pCmwImpl): MapSectionWriterBase(pCmwImpl) {}
            virtual ~RectangleAreaMapSectionWriter(void) {}
        public:
            virtual void writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer);
        };

        class PolygonAreaLayerSectionWriter : public MapSectionWriterBase
        {
        public:
            explicit PolygonAreaLayerSectionWriter(CompositeMapWriterImpl* pCmwImpl) : MapSectionWriterBase(pCmwImpl) {}
            virtual ~PolygonAreaLayerSectionWriter(void) {}
        public:
            virtual void writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer);
        };

        typedef std::unordered_map< std::string, MapSectionWriterBase* >         MapTypeToWriterHashMap;

    private:
        void doSaveFileHeader_(rpos_common::io::IStream& outStream, const CompositeMap& rcCmpstMap);
        MapSectionWriterBase* prepareMapSectionWriter_(const core::Metadata& rcMetadata);
        void doSaveMapSection_(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer);

    private:
        UnknownMapSectionWriter m_tUnknowMlWr;
        GridMapSectionWriter m_tGridMlWr;
        LineMapSectionWriter m_tLineMlWr;
        PoseMapSectionWriter m_tPoseMlWr;
        PointsMapSectionWriter m_tPointsMlWr;
        ImageFeaturesMapSectionWriter m_tImageFeaturesMlWr;
        RectangleAreaMapSectionWriter m_tRectangleAreaMlWr;
        PolygonAreaLayerSectionWriter m_tPolygonAreaMlWr;
        MapTypeToWriterHashMap m_hmMlTypeToWr;
        //
        ubyte_buf_type m_tMetadataBuf;
        ubyte_buf_type m_tMapBodyBuf;
        ubyte_buf_type m_tCmprsdMapBodyBuf;
    };

}}
