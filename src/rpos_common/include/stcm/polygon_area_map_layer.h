#pragma once

#include <core/pose.h>
#include <core/geometry.h>
#include <stcm/map_layer.h>
#include <queue>
#include <vector>

namespace rpos_common { namespace stcm {

    struct polygonArea
    {
        rpos_common::core::SegmentID id;
        uint count;
        std::deque<core::Location> locations;
        core::Metadata metadata;
    };

    class PolygonAreaMapLayer : public MapLayer
    {
    public:
        static const char* const Type;

        PolygonAreaMapLayer();
        virtual ~PolygonAreaMapLayer();

    public:
        virtual void clear(void);

    public:
        const std::vector<polygonArea> & areas() const;
        std::vector<polygonArea> & areas();

    private:
        std::vector<polygonArea> areas_;
    };

}}
