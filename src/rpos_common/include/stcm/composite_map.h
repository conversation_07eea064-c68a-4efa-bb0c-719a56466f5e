#pragma once

#include <core/metadata.h>
#include <core/pose.h>
#include <stcm/map_layer.h>
#include <vector>
#include <memory>

namespace rpos_common { namespace stcm {

    class CompositeMap
    {
    public:
        CompositeMap();
        CompositeMap(const CompositeMap&);
        CompositeMap(core::Metadata metadata, std::vector< std::shared_ptr<MapLayer> > maps);

    public:
        const core::Metadata& metadata() const;
        core::Metadata& metadata();

        const std::vector< std::shared_ptr<MapLayer> >& maps() const;
        std::vector< std::shared_ptr<MapLayer> >& maps(); 

        bool isMultiFloorMap() const;
        bool isMultiFloorMap(std::string& defaultMap) const;

        //get map of specific floor
        std::vector<std::shared_ptr<MapLayer>> filterMaps(const std::map<std::string,std::string>& criteria) const;
    private:
        core::Metadata metadata_;
        std::vector< std::shared_ptr<MapLayer> > maps_;
    };

}}