#pragma once

#include <io/i_stream.h>
#include <stcm/composite_map.h>

namespace rpos_common { namespace stcm {

    class CompositeMapWriterImpl;

    class CompositeMapWriter
    {
    public:
        CompositeMapWriter(void);
        ~CompositeMapWriter(void);

    public:
        // throw exception if error occurs

        void saveFile(const std::string& rcFilePath, const CompositeMap& rcCmpstMap);
        void saveFile(const std::wstring& rcFilePath, const CompositeMap& rcCmpstMap);

        void saveStream(rpos_common::io::IStream& outStream, const CompositeMap& rcCmpstMap);

    public:
        // returns true if succeed, and "rErrMsg" will be empty;
        // returns false if error occurs, and error message will be in "rErrMsg".

        bool saveFile(std::string& rErrMsg, const std::string& rcFilePath, const CompositeMap& rcCmpstMap);
        bool saveFile(std::string& rErrMsg, const std::wstring& rcFilePath, const CompositeMap& rcCmpstMap);

        bool saveStream(std::string& rErrMsg, rpos_common::io::IStream& outStream, const CompositeMap& rcCmpstMap);

    private:
        bool doSaveToStream_(std::string& rErrMsg, rpos_common::io::IStream& outStream, const CompositeMap& rcCmpstMap);

    private:
        CompositeMapWriterImpl* m_pImpl;
    };

}}
