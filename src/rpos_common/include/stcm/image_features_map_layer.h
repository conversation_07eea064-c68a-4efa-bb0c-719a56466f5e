#pragma once

#include <core/pose.h>
#include <core/metadata.h>
#include <stcm/map_layer.h>

#include <vector>

namespace rpos_common { namespace stcm {
    enum FeatureType
    {
        FeatureTypeUnknown = 0,
        FeatureTypeDBowORB,
        FeatureTypeVSlamMarker,
        FeatureTypeSnapshot, 
        FeatureTypeVSlamKeyframe,
        FeatureTypeCartographer,
        //Add new type here
        FeatureTypeMax
    };

    struct ImageFeaturesObservation
    {
        unsigned int ID;
        core::Pose cameraPose;
        std::vector<uint8_t> features; 

        ImageFeaturesObservation(): ID(0)
        {}
    };

    class ImageFeaturesMapLayer : public MapLayer
    {
    public:
        static const char* const Type;

        ImageFeaturesMapLayer();
        ~ImageFeaturesMapLayer();

    public:
        virtual void clear(void);

        FeatureType getFeatureType() const;
        void setFeatureType(FeatureType type);
    public:
        const std::vector<ImageFeaturesObservation>& featureObs() const;
        std::vector<ImageFeaturesObservation>& featureObs();

    private:
        std::vector<ImageFeaturesObservation> featureObs_;
        FeatureType type_;
    };

}}