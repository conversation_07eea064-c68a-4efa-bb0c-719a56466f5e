#pragma once

#include <core/pose.h>
#include <core/metadata.h>
#include <stcm/map_layer.h>
#include <string>

namespace rpos_common { namespace stcm {

    struct Line
    {
        std::string name;
        core::Location start, end;
        core::Metadata metadata;
    };

    class LineMapLayer : public MapLayer
    {
    public:
        static const char* const Type;

    public:
        virtual void clear(void);

    public:
        const std::map<std::string, Line>& lines() const;
        std::map<std::string, Line>& lines();

    private:
        std::map<std::string, Line> lines_;
    };

}}