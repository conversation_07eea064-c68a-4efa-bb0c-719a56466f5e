#pragma once

#include <stcm/map_layer.h>
#include <core/points_map.h>
#include <core/metadata.h>

namespace rpos_common { namespace stcm {

    class PointsMapLayer : public MapLayer
    {
    public:
        static const char* const Type;

        PointsMapLayer();
        ~PointsMapLayer();

    public:
        virtual void clear(void);

    public:
        const std::vector<core::PointPDF>& points() const;
        std::vector<core::PointPDF>& points();

    private:
        std::vector<core::PointPDF> points_;
    };

}}