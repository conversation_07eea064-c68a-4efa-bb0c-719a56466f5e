#pragma once

#include <io/i_stream.h>
#include <stcm/composite_map.h>


namespace rpos_common { namespace stcm {

    class CompositeMapReaderImpl;

    class CompositeMapReader
    {
    public:
        CompositeMapReader(void);
        ~CompositeMapReader(void);

    public:
        // throw exception if error occurs

        std::shared_ptr<CompositeMap> loadFile(const std::string& rcFilePath);
        std::shared_ptr<CompositeMap> loadFile(const std::wstring& rcFilePath);

        std::shared_ptr<CompositeMap> loadStream(rpos_common::io::IStream& inStream);

    public:
        // returns valid pointer if succeed, and "rErrMsg" will be empty;
        // returns invalid pointer if error occurs, and error message will be in "rErrMsg".

        std::shared_ptr<CompositeMap> loadFile(std::string& rErrMsg, const std::string& rcFilePath);
        std::shared_ptr<CompositeMap> loadFile(std::string& rErrMsg, const std::wstring& rcFilePath);

        std::shared_ptr<CompositeMap> loadStream(std::string& rErrMsg, rpos_common::io::IStream& inStream);

    public:
        //merge composite maps
        std::shared_ptr<CompositeMap> mergeFiles(std::string& rErrMsg, std::vector<MapDescription>& maps);

    private:
        std::shared_ptr<CompositeMap> doLoadFromStream_(std::string& rErrMsg, rpos_common::io::IStream& inStream);

    private:
        CompositeMapReaderImpl* m_pImpl;
    };

}}
