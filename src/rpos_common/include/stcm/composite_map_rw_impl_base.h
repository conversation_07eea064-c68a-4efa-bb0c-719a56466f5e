#pragma once

#include <io/i_stream.h>
#include <stcm/map_layer.h>
#include <stcm/composite_map_defs.h>
#include <system/byte_order.h>

#include <cstdio>
#include <string.h>
#include <memory>

#define BOOST_UNLIKELY(x) __builtin_expect(!!(x), 0)

namespace rpos_common { namespace stcm {

    class CompositeMapRwImplBase
    {
    public:
        typedef uint16_t       stcm_file_str_len_type;
        typedef std::vector<unsigned char>      ubyte_buf_type;
        typedef std::shared_ptr<MapLayer>     MapLayerSharedPtr;
        //
        enum CompressType
        {
            CMPRS_TYPE_UNKNOWN = -1
            , CMPRS_TYPE_NONE = 0
            , CMPRS_TYPE_RLE
        };
        //
        static const uint16_t      C_METADATA_MAX_ENTRY_CNT = 0xFFFFU;
        static const stcm_file_str_len_type         C_MAX_UTF8_STRING_LEN = 0xFFFFU;
        static const uint32_t      C_MAPSECTION_MAX_CNT = 0xFFFFFFFFU;
        static const uint16_t      C_MAX_STCM_FILE_HEADER_SIZE = 0xFFFFU;
        static const uint32_t      C_MAX_MAPSECTION_SIZE = 0xFFFFFFFFU;
        static const uint8_t       C_MAX_TAG_CNT_IN_POSE_ENTRY = 0xFFU;
        static const uint8_t       C_MAX_TAG_CNT_IN_POINTPDF_TAG_ENTRY = 0xFFU;
        static const uint8_t       C_MAX_TAG_CNT_IN_RECTANGLEAREADESCRIPTOR_TAG_ENTRY = 0xFFU;

    public:
        static const size_t C_STCM_FILE_SIGNATURE_SIZE = 4;
        static const uint8_t       s_tStcmSignature[C_STCM_FILE_SIGNATURE_SIZE];
        //
    #pragma pack(push, 1)
        struct StcmFileHeader
        {
            uint8_t        tSignature[C_STCM_FILE_SIGNATURE_SIZE]; // four chars which are 'STCM'
            uint16_t       u16HeaderSize; // the size of the whole header (including the 4bytes signature)
            uint16_t       u16Ver; // the version of current file
            uint16_t       u16MinReaderVer; // The lowest supported reader version
            uint16_t       u16MinWriterVer; // The lowest supported writer version
            uint32_t       uSectionCnt; // the amount of map sections
            // ... // a metadata section
        };
    #pragma pack(pop)

    public:
		template < typename T >
		static inline void* cpuToLeWriteToBuffer(void* ptr, const T& src)
		{
			T nv = rpos_common::system::cpu_to_le<T>(src);
			::memcpy(ptr, &nv, sizeof(T));
			return static_cast< unsigned char* >(ptr) + sizeof(T);
		}
		template < typename T >
		static inline const void* leToCpuReadFromBuffer(T& dest, const void* ptr)
		{
			T v;
			::memcpy(&v, ptr, sizeof(T));
			dest = rpos_common::system::le_to_cpu<T>(v);
			return static_cast< const unsigned char* >(ptr) + sizeof(T);
		}

        static void exactRead(void* pDest, size_t szExactLen, rpos_common::io::IStream& inStream, const char* pcErrMsg);
        static void exactWrite(rpos_common::io::IStream& outStream, const void* pcSrc, size_t szExactLen, const char* pcErrMsg);
    
    protected:
        void initStcmFileHeader(StcmFileHeader& rHeader);
        bool checkStcmSignature(const uint8_t arrSignature[C_STCM_FILE_SIGNATURE_SIZE]);

        CompressType getCompressType(const core::Metadata& rcMetadata);
        void doCompressAppend(CompressType eCmprsType, ubyte_buf_type& rDest, const void* pcSrc, size_t szSrcLen);
        void doDecompressAppend(CompressType eCmprsType, ubyte_buf_type& rDest, const void* pcSrc, size_t szSrcLen);

        void serializeStrAppToMem(ubyte_buf_type& rDest, const std::string& rcSrc);
        const void* deserializeStrFromMem(std::string& rDest, const void* pcSrcBegin, const void* pcSrcEnd);
        void deserializeStrFromStream(std::string& rDest, rpos_common::io::IStream& inStream);

        template<class NumValT>
        const void* deserializeNumValFromMem_T(NumValT& rDest, const void* pcSrcBegin, const void* pcSrcEnd, const char* pcErrMsg)
        {
            const char* pcTmp = (const char*)pcSrcBegin;
            if (pcTmp + sizeof(NumValT) <= ((const char*)pcSrcEnd))
            {
				pcTmp = (const char*)leToCpuReadFromBuffer<NumValT>(rDest, pcTmp);
            }
            else
            {
                RPOS_COMPOSITEMAP_THROW_EXCEPTION(pcErrMsg);
            }
            return pcTmp;
        }
    };

}}
