/*
* geometry.h
* Geometry objects
*
* Created by <PERSON><PERSON> (<EMAIL>) at 2014-12-26
* Copyright 2014 (c) www.robopeak.com
*/

#pragma once
#include "metadata.h"
#include "geometry_matrix.h"
#include "geometry_rectangle.h"

namespace rpos_common {
    namespace core {
        typedef uint32_t SegmentID;

        class Point {
        public:
            Point();
            Point(float x, float y);
            Point(const Point&);
            ~Point();

        public:
            Point& operator=(const Point&);

        public:
            float x() const;
            float& x();

            float y() const;
            float& y();

        private:
            float x_, y_;
        };

        class Line {
        public:
            Line();
            Line(const Point &startP, const Point &endP);
            Line(const Point &startP, const Point &endP, int id);
            Line(const Line&);
            ~Line();

        public:
            Line& operator=(const Line&);

        public:
            bool isLineSegmentCross(const Line&);

        public:
            Point& startP();
            const Point& startP() const;
            Point& endP();
            const Point& endP() const;
            SegmentID& id();
            const SegmentID& id() const;
            Metadata& metadata();
            const Metadata& metadata() const;
            bool isBezierCurve(rpos_common::core::Vector2f& p1, rpos_common::core::Vector2f& p2) const;
            bool isBezierCurve() const;
            bool isBidirectional() const;
            void setBidirection(bool isBidirectional);
            void reverseDirection();
        private:
            Point startP_;
            Point endP_;
            SegmentID id_;
            Metadata metadata_;
        };
    }
}