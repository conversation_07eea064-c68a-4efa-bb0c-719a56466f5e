#pragma once 
#include <core/metadata.h>
#include <core/geometry.h>

namespace rpos_common { namespace core { 

    enum ArtifactUsage
    {
        /**
         * Virtual walls works just like walls, they blocks robot move through
         */
        ArtifactUsageVirtualWall = 0,

        /**
         * Virtual tracks are used to guide robot's movement. Robot will try its best to move along with virtual tracks
         */
        ArtifactUsageVirtualTrack,

        /**
         * Schedule areas are used to achieve multi-robot collaborative behavior
         */
        ArtifactUsageScheduleArea,
        
        /**
         * Maintenance area has mutable environment, slamware will automatically update map of this area
         */
        ArtifactUsageMaintenanceArea,
        
        /**
         * Forbidden area means robot should not enter this area, but if accidentally entered, it can escape
         */
        ArtifactUsageForbiddenArea,

        /**
         * Robot will disable designated sensor when entering sensor disable area
         */
        ArtifactUsageSensorDisableArea,
        
        /**
         * Elevator area is used to describe the area associated with the elevator, mainly including the sill border, the inner border and outer border.
         */
        ArtifactUsageElevatorArea,

        /**
         * When in dangerous area some limits will be enabled, such as max_speed, max_acceleration.
         */
        ArtifactUsageDangerousArea,

        /**
         * Points of interest
         */
        ArtifactUsagePoi,

        /**
         * Coverage area for sweeping and disinfecting
         */
        ArtifactUsageCoverageArea,

        /**
         * Restricted area for multi-robot collaborative behavior
         */
        ArtifactUsageRestrictedArea,

        /**
         * Special marks detected by lidar or camera
         */
        ArtifactUsageLandmark,

        /**
         * If robot is not on dock,
         * it should be powered on in a startup area and recover pose through recoverLocalizationByStartupArea();
         */
        ArtifactUsageStartupArea,
    };

    enum DangerousAreaType
    {
        SlopeArea = 0,
        NarrowCorridorArea = 1,
        OAStrategyStopAndWait = 2,
        OAStrategyWaitBeforeObstacleAvoidance = 3,
        GateArea = 4
    };

    struct RectangleArea {
        ArtifactUsage usage;
        rpos_common::core::SegmentID id;
        core::ORectangleF area;
        core::Metadata metadata;
        RectangleArea() {}
        RectangleArea(ArtifactUsage usage, const core::ORectangleF& rect, uint32_t id)
            : usage(usage), id(id), area(rect) {}
    };
 
    // elevator area
    static constexpr const char* MetaDataKey_ElevatorID = "elevator_id";
    static constexpr const char* MetaDataKey_ElevatorSillWidth = "elevator_sill_width";
    static constexpr const char* MetaDataKey_ElevatorSchedulingPointDist = "elevator_scheduling_point_dist";
    static constexpr const char* MetaDataKey_ElevatorDisableDefaultSchedulingPoint = "elevator_disable_default_scheduling_point";
    static constexpr const char* MetaDataKey_ElevatorExtraFrontSchedulingPoints = "elevator_extra_front_scheduling_points";
    static constexpr const char* MetaDataKey_ElevatorExtraRearSchedulingPoints = "elevator_extra_rear_scheduling_points";
    static constexpr const char* MetaDataKey_ElevatorDoorType = "elevator_door_type";
    static constexpr const char* MetaDataKey_ElevatorDepthValidHalfAngle = "elevator_depth_valid_half_angle";
    // dangerous area
    static constexpr const char* MetaDataKey_DangerousAreaType = "dangerous_area_type";
    static constexpr const char* MetaDataKey_MaxLineSpeed = "max_line_speed";
    static constexpr const char* MetaDataKey_GateID = "gate_id";
    // restricted area
    static constexpr const char* MetaDataKey_RestrictedSchedulingPoints = "restricted_scheduling_points";
    static constexpr const char* MetaDataKey_RestrictedRobotsNumberLimit = "restricted_robots_number_limit";
    // sensor disable area
    static constexpr const char* MetaDataKey_SensorType = "sensor_type";

}}