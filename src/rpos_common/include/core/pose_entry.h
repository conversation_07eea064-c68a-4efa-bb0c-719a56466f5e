#pragma once
#include <core/metadata.h>
#include <core/pose.h>
#include <map>

namespace rpos_common { namespace core { 
    struct PoseEntry
    {
        std::string id;
        core::Pose pose;
        uint8_t flags;
        Metadata metadata;

        bool operator==(const PoseEntry& that) const
        {
            return (id == that.id && pose == that.pose && metadata == that.metadata);
        }
        PoseEntry(){}
        PoseEntry(const core::Pose& p) : pose(p) {}
    };

    typedef std::map<std::string, PoseEntry> PoseEntryMap;
    typedef std::pair<std::string, PoseEntry> PoseEntryPair;
}}