#pragma once
#include <Eigen/Core>

namespace rpos_common { namespace core {

    class Location {
    public:
        Location();
        Location(double x, double y, double z = 0);
        Location(const Location&);
        ~Location();

    public:
        Location& operator=(const Location&);
        bool operator==(const Location&) const;

    public:
        double x() const;
        double& x();

        double y() const;
        double& y();

        double z() const;
        double& z();

        double distanceTo(const Location& that) const;

    private:
        double x_, y_, z_;
    };

    class Rotation {
    public:
        Rotation();
        Rotation(double yaw, double pitch = 0, double roll = 0);
        Rotation(const Rotation&);
        explicit Rotation(const Eigen::Matrix3d&);
        ~Rotation();

    public:
        Rotation& operator=(const Rotation&);
        bool operator==(const Rotation&) const;
        operator Eigen::Matrix3d() const;

    public:
        double yaw() const;
        double& yaw();

        double pitch() const;
        double& pitch();

        double roll() const;
        double& roll();

    private:
        double yaw_, pitch_, roll_;
    };

    class Pose {
    public:
        Pose();
        Pose(const Location&, const Rotation&);
        Pose(const Location&);
        Pose(const Rotation&);
        Pose(const Pose&);
        explicit Pose(const Eigen::Matrix4d&);
        ~Pose();

    public:
        Pose& operator=(const Pose&);
        bool operator==(const Pose&) const;
        bool operator<(const Pose&) const;
        operator Eigen::Matrix4d() const;

    public:
        const Location& location() const;
        Location& location();

        const Rotation& rotation() const;
        Rotation& rotation();

        double x() const;
        double& x();

        double y() const;
        double& y();

        double z() const;
        double& z();

        double yaw() const;
        double& yaw();

        double pitch() const;
        double& pitch();

        double roll() const;
        double& roll();

    private:
        Location location_;
        Rotation rotation_;
    };

} }
