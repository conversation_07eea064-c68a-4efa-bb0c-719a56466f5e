﻿#pragma once

#include <core/geometry.h>

#include <cmath>
#include <vector>


namespace rpos_common { namespace algorithm {
    
    class GridCellHelper
    {
    public:
        static const size_t C_VERTEX_COUNT_OF_RECTANGLE = 4;

        typedef ::rpos_common::core::Vector2i          Vector2i;
        typedef ::rpos_common::core::Vector2f          Vector2f;

        class CoordinateFrame;

        // inner classes for collision detection
        class AABB2D;           // 2D axis aligned bounding box
        class BoundingCircle ;
        class OBB2D;            // 2D oriented bounding box

        class OrientedCircleEndsBoundingArea;

        static void sfGetMinMaxOf4(float f0, float f1, float f2, float f3, float& rMin, float& rMax);
        static inline bool sfAreSegmentsContacted(float fAMin, float fAMax, float fBMin, float fBMax)
        {
            assert(fAMin <= fAMax);
            assert(fBMin <= fBMax);
            return (fBMin <= fAMax && fAMin <= fBMax);
        }

        class I2DBoundingArea
        {
        public:
            virtual ~I2DBoundingArea(){}
            virtual const AABB2D& getAABB() const = 0;
            virtual bool contains(const Vector2f& tPoint) const = 0;
            virtual bool collidesWith(const AABB2D& tOthAabb) const = 0;
        };

        // convenient for coordinate transformation
        class CoordinateFrame
        {
        public:
            CoordinateFrame() { reset(); }
            CoordinateFrame(float x, float y, float theta) { reset(x, y, theta); }
            CoordinateFrame(const Vector2f& tPosition, float theta) { reset(tPosition, theta); }

            void reset();
            void reset(float x, float y, float theta);
            void reset(const Vector2f& tPosition, float theta) { reset(tPosition.x(), tPosition.y(), theta); }

            // translate the position by an offset vector in the parent frame
            CoordinateFrame& translate(const Vector2f& tOffset) { m_tPosition += tOffset; return *this; }
            CoordinateFrame& translate(float offsetX, float offsetY) { return translate(Vector2f(offsetX, offsetY)); }

            const Vector2f& getPosition() const { return m_tPosition; }
            float getCosTheta() const { return m_fCosTheta; }
            float getSinTheta() const { return m_fSinTheta; }

            float getTheta() const { return m_fTheta; }

            // transform direction vector of child axis into the parent frame, the length always equals to 1.
            Vector2f axisXOfChildToParent() const { return Vector2f(m_fCosTheta, m_fSinTheta); }
            Vector2f axisYOfChildToParent() const { return Vector2f(-m_fSinTheta, m_fCosTheta); }
            // transform direction vector of parent axis into the child frame, the length always equals to 1.
            Vector2f axisXOfParentToChild() const { return Vector2f(m_fCosTheta, -m_fSinTheta); }
            Vector2f axisYOfParentToChild() const { return Vector2f(m_fSinTheta, m_fCosTheta); }

            Vector2f childToParent(const Vector2f& tChild) const;
            Vector2f parentToChild(const Vector2f& tParent) const;

        public:
            void inplaceChildToParent(Vector2f* pInOutBuf, size_t szCnt) const;
            void inplaceParentToChild(Vector2f* pInOutBuf, size_t szCnt) const;
            
            void transformChildToParent(const Vector2f* pcIn, size_t szCnt, Vector2f* pOut) const;
            void transformParentToChild(const Vector2f* pcIn, size_t szCnt, Vector2f* pOut) const;

        private:
            Vector2f m_tPosition;
            float m_fCosTheta;
            float m_fSinTheta;
            
            // redundant data:
            float m_fTheta;
        };

        //////////////////////////////////////////////////////////////////////////

        // 2D axis aligned bounding box
        class AABB2D : public I2DBoundingArea
        {
        public:
            AABB2D(): m_fXMin(0.0f), m_fYMin(0.0f), m_fXMax(-FLT_EPSILON), m_fYMax(-FLT_EPSILON) {}
            AABB2D(float fXMin, float fYMin, float fXMax, float fYMax): m_fXMin(fXMin), m_fYMin(fYMin), m_fXMax(fXMax), m_fYMax(fYMax) {}
            AABB2D(const Vector2f& p0, const Vector2f& p1);
            AABB2D(const Vector2f& p0, const Vector2f& p1, const Vector2f& p2, const Vector2f& p3);

            // x and y here are coordinates in child frame "coordFrame", they will be transformed into parent frame.
            AABB2D(const CoordinateFrame& coordFrame, float fXMin, float fYMin, float fXMax, float fYMax);

            float getXMin() const { return m_fXMin; }
            float getYMin() const { return m_fYMin; }
            float getXMax() const { return m_fXMax; }
            float getYMax() const { return m_fYMax; }

            void setXMin(float fXMin) { m_fXMin = fXMin; }
            void setYMin(float fYMin) { m_fYMin = fYMin; }
            void setXMax(float fXMax) { m_fXMax = fXMax; }
            void setYMax(float fYMax) { m_fYMax = fYMax; }

            bool empty() const { return m_fXMax <= m_fXMin || m_fYMax <= m_fYMin; }
            bool isSameWith(const AABB2D& tOth) const { return m_fXMin == tOth.m_fXMin && m_fYMin == tOth.m_fYMin && m_fXMax == tOth.m_fXMax && m_fYMax == tOth.m_fYMax; }

            // puts 4 vertexes into destination (even if it is empty).
            // the indexes of 4 vertexes are:
            //   1 ----- 0
            //   |       |
            //   |   C   |
            //   |       |
            //   2 ----- 3
            void calcVertexes(Vector2f destVertexes[C_VERTEX_COUNT_OF_RECTANGLE]) const;
            void calcVertexes(std::vector<Vector2f>& destVertexes) const;

            virtual bool contains(const Vector2f& tPoint) const { return m_fXMin <= tPoint.x() && tPoint.x() <= m_fXMax && m_fYMin <= tPoint.y() && tPoint.y() <= m_fYMax; }
            virtual bool collidesWith(const AABB2D& tOthAabb) const;
            virtual const AABB2D& getAABB() const { return *this; }

            AABB2D& mergesWith(const AABB2D& tOth);
            void intersectionOf(const rpos_common::core::RectangleF& dest);
            bool collidesWith(const BoundingCircle& tCircle) const;
            bool contains(const BoundingCircle& tCircle) const;
        private:
            float m_fXMin;
            float m_fYMin;
            float m_fXMax;
            float m_fYMax;
        };

        class BoundingCircle : public I2DBoundingArea
        {
        public:
            BoundingCircle (const Vector2f& tCenter, float fRadius);
            const Vector2f& getCenterPoint() const { return m_tCenterPoint; }
            float getRadius() const { return m_fRadius; }

            virtual const AABB2D& getAABB() const { return m_tAabb; }

            virtual bool contains(const Vector2f& tPoint) const;
            virtual bool collidesWith(const AABB2D& tOthAabb) const;

        private:
            Vector2f m_tCenterPoint;
            float m_fRadius;

            // redundant data:
            float m_fRadiusSquare;
            AABB2D m_tAabb;
        };

        // 2D oriented bounding box
        class OBB2D : public I2DBoundingArea
        {
        public:
            OBB2D() { reset(); }
            OBB2D(const Vector2f& tStartPoint, const Vector2f& tEndPoint, float fHalfWidth) { reset(tStartPoint, tEndPoint, fHalfWidth); }
            OBB2D(const CoordinateFrame& coordFrame, float fXMin, float fYMin, float fXMax, float fYMax) { reset(coordFrame, fXMin, fYMin, fXMax, fYMax); }

            void reset();
            // the local origin point will be (tStartPoint + tEndPoint) / 2;
            void reset(const Vector2f& tStartPoint, const Vector2f& tEndPoint, float fHalfWidth);
            // x and y here are coordinates in child frame "coordFrame".
            void reset(const CoordinateFrame& coordFrame, float fXMin, float fYMin, float fXMax, float fYMax);
            // rotate the OBB around its center point
            void rotate(float yawDiff);

            const CoordinateFrame& getCoordinateFrame() const { return m_tCoordinateFrame; }
            const AABB2D& getLocalAABB() const { return m_tLocalAabb; }
            const Vector2f* getVertexes() const { return m_tVertexes; }
            void getVertexes(std::vector<Vector2f>& vertexes) const;

            Vector2f localToWorld(const Vector2f& tLocal) const { return m_tCoordinateFrame.childToParent(tLocal); }
            Vector2f worldToLocal(const Vector2f& tWorld) const { return m_tCoordinateFrame.parentToChild(tWorld); }

            bool empty() const { return m_tLocalAabb.empty() || m_tAabb.empty(); }

            virtual const AABB2D& getAABB() const { return m_tAabb; }
            virtual bool contains(const Vector2f& tPoint) const;
            virtual bool collidesWith(const AABB2D& tOthAabb) const;
            virtual bool collidesWith(const OBB2D& tOthObb) const;
            bool contains(const OBB2D& tOthObb) const;
            bool contains(const BoundingCircle& tCircle) const;
            bool collidesWith(const BoundingCircle& tCircle) const;
            bool localContains(const Vector2f& tPoint) const;
            Vector2f getCenterPoint() const;
            float getLength() const { return m_length; }
            float getWidth() const { return m_width; }
        private:
            void doCalcWorldDataByLocalData_();

        private:
            CoordinateFrame m_tCoordinateFrame;
            AABB2D m_tLocalAabb;
            float m_length;
            float m_width;
            // redundant data:
            //
            // coordinates of vertexes in the world frame.
            Vector2f m_tVertexes[C_VERTEX_COUNT_OF_RECTANGLE];
            // the min and max value of the vertexes' coordinates in the world frame
            AABB2D m_tAabb;
        };

        //////////////////////////////////////////////////////////////////////////

        class OrientedCircleEndsBoundingArea : public I2DBoundingArea
        {
        public:
            OrientedCircleEndsBoundingArea(const Vector2f& tStartPoint, const Vector2f& tEndPoint, float fRadius);

            const BoundingCircle & getBoundingCircleOfStartPoint() const { return m_tBoundingCircleOfStartPoint; }
            const BoundingCircle & getBoundingCircleOfEndPoint() const { return m_tBoundingCircleOfEndPoint; }
            const OBB2D& getObbOfCenterRect() const { return m_tObbOfCenterRect; }

            virtual const AABB2D& getAABB() const { return m_tAabb; }

            virtual bool contains(const Vector2f& tPoint) const;
            virtual bool collidesWith(const AABB2D& tOthAabb) const;

        private:
            BoundingCircle  m_tBoundingCircleOfStartPoint;
            BoundingCircle  m_tBoundingCircleOfEndPoint;
            OBB2D m_tObbOfCenterRect;
            
            AABB2D m_tAabb;
        };

        //////////////////////////////////////////////////////////////////////////

        struct IndexRange
        {
            int iBegin;
            int iEnd;

            IndexRange(): iBegin(0), iEnd(0) {}
            IndexRange(int iB, int iE): iBegin(iB), iEnd(iE) {}

            bool empty() const { return iEnd <= iBegin; }
        };
        struct RangeOnY
        {
            int iY;
            IndexRange tXRange;

            RangeOnY(): iY(0) {}
            RangeOnY(int iPosY, int iXBegin, int iXEnd): iY(iPosY), tXRange(iXBegin, iXEnd) {}

            bool empty() const { return tXRange.empty(); }
        };
        typedef ::std::vector<RangeOnY>     RangeOnY_Vector;

    public:
        GridCellHelper();
        explicit GridCellHelper(float fResolustion, float fXMin, float fYMin, int iDimX, int iDimY);

        void reset(float fResolustion, float fXMin, float fYMin, int iDimX, int iDimY);

        float getResolution() const { return m_fResolution; }
        Vector2i getDimension() const { return m_tDimesion; }
        float getHalfResolution() const { return m_fHalfResolution; }

    public:
        Vector2i calcCellIndex(float fX, float fY) const
        {
            return Vector2i(static_cast<int>(::floor((fX - m_tMinPoint.x()) / m_fResolution))
                , static_cast<int>(::floor((fY - m_tMinPoint.y()) / m_fResolution))
                );
        }
        Vector2i calcCellIndex(const Vector2f& tPos) const { return calcCellIndex(tPos.x(), tPos.y()); }

        Vector2f calcCenterOfCell(int iCellIndexX, int iCellIndexY) const
        {
            return Vector2f((m_tMinPoint.x() + iCellIndexX * m_fResolution + m_fHalfResolution)
                , (m_tMinPoint.y() + iCellIndexY * m_fResolution + m_fHalfResolution)
                );
        }
        Vector2f calcCenterOfCell(const Vector2i& tCellIndex) const { return calcCenterOfCell(tCellIndex.x(), tCellIndex.y()); }

        bool isPointInside(float fX, float fY) const
        {
            return m_tMinPoint.x() <= fX && fX < m_tMaxPoint.x()
                && m_tMinPoint.y() <= fY && fY < m_tMaxPoint.y();
        }
        bool isPointInside(const Vector2f& tPos) const { return isPointInside(tPos.x(), tPos.y()); }

        bool isCellIndexInside(int iCellIndexX, int iCellIndexY) const
        {
            return 0 <= iCellIndexX && iCellIndexX < m_tDimesion.x()
                && 0 <= iCellIndexY && iCellIndexY < m_tDimesion.y();
        }
        bool isCellIndexInside(const Vector2i& tCellIndex) const { return isCellIndexInside(tCellIndex.x(), tCellIndex.y()); }

    public:
        void calcPotentialCellIndexRange(const AABB2D& tOthAabb, int& rCellXMin, int& rCellYMin, int& rCellXMax, int& rCellYMax) const;

        // calculates cells that collided with the path
        void calcCollidedCells(const I2DBoundingArea& boundingArea, RangeOnY_Vector& result) const;
        // calculates cells of which center point collided with the path
        void calcCenterOfCellCollidedCells(const I2DBoundingArea& boundingArea, RangeOnY_Vector& result) const;
		// calculates cells that collided with the line
		void calcLineCollidedCells(const Vector2f& tStartPoint, const Vector2f& tEndPoint, RangeOnY_Vector& result) const;
        // divide an OBB2D rect area to several equal sub rect areas.
        void divideOBB2DIntoSubsets(const OBB2D& area, int widthCnt, int heightCnt, std::vector<OBB2D>& subsets) const;
        void divideOBB2DIntoSubsets(const OBB2D& area, int widthCnt, int heightCnt, std::vector<rpos_common::core::ORectangleF>& subsets) const;

    private:
        float m_fResolution;
        Vector2f m_tMinPoint;
        Vector2i m_tDimesion;

        // redundant data:
        float m_fHalfResolution;
        Vector2f m_tMaxPoint;
    };

}}
