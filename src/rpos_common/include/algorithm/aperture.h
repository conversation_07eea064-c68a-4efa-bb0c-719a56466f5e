/*
* aperture.h
* Aperture class contains defination and some filter functions to filter laser scan data according to aperture settings
*
* Created by <PERSON> (<EMAIL>) at 2017-1-4
* Copyright 2017 (c) Shanghai Slamtec Co., Ltd.
*/

/*
* Why this class should be thread safe?
* This class is used to store the usable range of lidar Aperture which should be config entry of lidar.aperture
* But our system has a config reload system which is not thread safe, in order to make sure this function working well
* we have to make it thread safe. When we enabled the more powerful and strong config reload infrastructure, this thread-safe
* feature of this class can be removed.
*
* Why this class is placed in the robot_config library?
* We don't have the powerful and strong config reload infrasturture yet. As a result, we have to use this class instead of
* raw config data (std::vector<SlamwareLidarApertureRange>) as the type of config entry to reduce the cost.
* So this class will be a dependency of robot_config library. In stead of making another library, we just put it into the
* robot_config library for short.
*/

#pragma once

#include <vector>
#include <list>
#include <mutex>

namespace rpos_common { namespace algorithm {

    struct ApertureRange
    {
        double start;
        double size;
    };

    class Aperture {
    public:
        Aperture();
        Aperture(const ApertureRange& aperture);
        Aperture(const std::vector<ApertureRange>& aperture);
        Aperture(const Aperture&);
        Aperture(Aperture&&);
        ~Aperture();

    public:
        Aperture& operator=(const Aperture&);
        Aperture& operator=(Aperture&&);

    public:
        void copy(const Aperture&);
        void swap(Aperture&);

    public:
        double radSize() const;
        double degSize() const;

    public:
        std::list<ApertureRange>::iterator begin();
        std::list<ApertureRange>::const_iterator begin() const;
        std::list<ApertureRange>::iterator end();
        std::list<ApertureRange>::const_iterator end() const;

    public:
        bool radInRange(double rad) const;
        bool degInRange(double deg) const;

    public:
        void setRange(const ApertureRange& range);
        void setRange(const std::vector<ApertureRange>& ranges);
        void setRange(double start, double size);

        void addRange(const ApertureRange& range);
        void addRange(double start, double size);
        void clear();

        void addFilterRange(double start, double size);

    private:
        void addRange_(const ApertureRange& range);
        void addRange_(double start, double size);
        void addRangeNoCheck_(double start, double size);
        void recalculateSize_();
        void addFilterRangeNoCheck_(double start, double size);

    private:
        mutable std::mutex lock_;
        std::list<ApertureRange> ranges_;
        double sizeInRad_;
    };

} }
