#pragma once

#include "json_serialization.h"
#include <core/pose.h>
#include <core/geometry.h>
#include <core/sensor_type.h>

namespace rpos_common { namespace serialization { namespace json {

    template <>
	struct Serializer < core::Pose >
    {
        static Json::Value serialize(const core::Pose& v);
        static core::Pose deserialize(const Json::Value& v);
    };

    template <>
    struct Serializer < core::Location >
    {
        static Json::Value serialize(const core::Location& v);
        static core::Location deserialize(const Json::Value& v);
    };

    template <>
	struct Serializer < core::RectangleF >
    {
        static Json::Value serialize(const core::RectangleF& v);
        static core::RectangleF deserialize(const Json::Value& v);
    };

    template <>
    struct Serializer < core::RectangleI >
    {
        static Json::Value serialize(const core::RectangleI& v);
        static core::RectangleI deserialize(const Json::Value& v);
    };

    template <>
    struct Serializer < core::ORectangleF >
    {
        static Json::Value serialize(const core::ORectangleF& v);
        static core::ORectangleF deserialize(const Json::Value& v);
    };

    template <>
	struct Serializer < core::Vector2i >
    {
        static Json::Value serialize(const core::Vector2i& v);
        static core::Vector2i deserialize(const Json::Value& v);
    };

    template <>
	struct Serializer < core::Vector3i >
    {
        static Json::Value serialize(const core::Vector3i& v);
        static core::Vector3i deserialize(const Json::Value& v);
    };

    template <>
	struct Serializer < core::Vector4i >
    {
        static Json::Value serialize(const core::Vector4i& v);
        static core::Vector4i deserialize(const Json::Value& v);
    };

    template <>
	struct Serializer < core::Vector2f >
    {
        static Json::Value serialize(const core::Vector2f& v);
        static core::Vector2f deserialize(const Json::Value& v);
    };

    template <>
	struct Serializer < core::Vector3f >
    {
        static Json::Value serialize(const core::Vector3f& v);
        static core::Vector3f deserialize(const Json::Value& v);
    };

    template <>
	struct Serializer < core::Vector4f >
    {
        static Json::Value serialize(const core::Vector4f& v);
        static core::Vector4f deserialize(const Json::Value& v);
    };
    
    template <>
    struct Serializer < core::SensorType > {
        static Json::Value serialize(const core::SensorType& tVal);
        static core::SensorType deserialize(const Json::Value& jsnVal);
    };

    
} } }
