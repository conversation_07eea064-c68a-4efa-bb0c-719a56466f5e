#pragma once
#include <vector>
#include <io/i_stream.h>

namespace rpos_common {  namespace serialization {

    class BufferStreamAdaptor : public rpos_common::io::IStream
    {
    public:
        explicit BufferStreamAdaptor(std::vector<uint8_t> *buf);
        virtual ~BufferStreamAdaptor();

        virtual bool isOpen();
        virtual bool canRead();
        virtual bool canWrite();
        virtual bool canSeek();

        virtual void close();

        virtual bool endOfStream();

        virtual int read(void *buffer, size_t count);
        virtual int write(const void *buffer, size_t count);
        virtual size_t tell();
        virtual void seek(rpos_common::io::SeekType type, int offset);

    private:
        std::vector<uint8_t> *buf_;
        size_t head_;
    };

} } 
