/*
* json_serialization.h
* RPOS JSON Serialization Utilities
*
* Created By <PERSON> (<EMAIL>) at 2014-12-11
* Copyright 2014 (c) Shanghai Slamtec Co., Ltd.
*/

#pragma once
#include <core/pose.h>
#include <json/json.h>
#include <vector>
#include <list>
#include <set>
#include <map>
#include <cstdint> 
#include <optional>
#include <memory> 

namespace rpos_common { namespace serialization { namespace json {

    template <class T>
    struct Serializer
    {
        static Json::Value serialize(const T& v);
        static T deserialize(const Json::Value& jsonValue);
    };

    template <class T>
    Json::Value serialize(const T& v)
    {
        return Serializer<T>::serialize(v);
    }

    template <class T>
    std::optional<Json::Value> try_serialize(const T& v)
    {
        try
        {
            return std::optional<Json::Value>(serialize<T>(v));
        }
        catch (...)
        {
            return std::optional<Json::Value>();
        }
    }

    template <class T>
    T deserialize(const Json::Value& v)
    {
        return Serializer<T>::deserialize(v);
    }

    template <class T>
    std::optional<T> try_deserialize(const Json::Value& v)
    {
        try
        {
            return std::optional<T>(deserialize<T>(v));
        }
        catch (...)
        {
            return std::optional<T>();
        }
    }

    template <>
    struct Serializer < std::nullptr_t >
    {
        static Json::Value serialize(const std::nullptr_t& )
        {
            return Json::Value();
        }

        static std::nullptr_t deserialize(const Json::Value& )
        {
            return nullptr;
        }
    };

    template <>
    struct Serializer < Json::Value >
    {
        static Json::Value serialize(const Json::Value& v)
        {
            return v;
        }

        static Json::Value deserialize(const Json::Value& v)
        {
            return v;
        }
    };

    template <>
    struct Serializer < std::string >
    {
        static Json::Value serialize(const std::string& v)
        {
            return Json::Value(v);
        }

        static std::string deserialize(const Json::Value& v)
        {
            return v.asString();
        }
    };

    template <>
    struct Serializer < int >
    {
        static Json::Value serialize(const int& v)
        {
            return Json::Value(Json::Int(v));
        }

        static int deserialize(const Json::Value& v)
        {
            return v.asInt();
        }
    };

    template <>
    struct Serializer < long >
    {
        static Json::Value serialize(const long& v)
        {
            return Json::Value(Json::Int(v));
        }

        static long deserialize(const Json::Value& v)
        {
            return v.asInt();
        }
    };

    template <>
    struct Serializer < long long >
    {
        static Json::Value serialize(const long long& v)
        {
            return Json::Value(Json::Int(v));
        }

        static long long deserialize(const Json::Value& v)
        {
            return v.asInt();
        }
    };

    template <>
    struct Serializer < unsigned int >
    {
        static Json::Value serialize(const unsigned int& v)
        {
            return Json::Value(Json::UInt(v));
        }

        static unsigned int deserialize(const Json::Value& v)
        {
            return v.asUInt();
        }
    };

    template <>
    struct Serializer < unsigned long >
    {
        static Json::Value serialize(const unsigned long& v)
        {
            return Json::Value(Json::UInt(v));
        }

        static unsigned long deserialize(const Json::Value& v)
        {
            return v.asUInt();
        }
    };

    template <>
    struct Serializer < unsigned long long >
    {
        static Json::Value serialize(const unsigned long long& v)
        {
            return Json::Value(Json::UInt(v));
        }

        static unsigned long long deserialize(const Json::Value& v)
        {
            return v.asUInt();
        }
    };

    template <>
    struct Serializer < bool >
    {
        static Json::Value serialize(const bool& v)
        {
            return Json::Value(v);
        }

        static bool deserialize(const Json::Value& v)
        {
            return v.asBool();
        }
    };

    template <>
    struct Serializer < float >
    {
        static Json::Value serialize(const float& v)
        {
            return Json::Value(v);
        }

        static float deserialize(const Json::Value& v)
        {
            return (float)v.asDouble();
        }
    };

    template <>
    struct Serializer < double >
    {
        static Json::Value serialize(const double& v)
        {
            return Json::Value(v);
        }

        static double deserialize(const Json::Value& v)
        {
            return (double)v.asDouble();
        }
    };

    template <class T>
    struct Serializer < std::map<std::string, T> >
    {
        static Json::Value serialize(const std::map<std::string, T>& v)
        {
            Json::Value output;

            for (auto iter = v.begin(); iter != v.end(); iter++)
            {
                output[iter->first] = json::serialize(iter->second);
            }
            return output;
        }

        static std::map<std::string, T> deserialize(const Json::Value& v)
        {
            std::map<std::string, T> output;

            std::vector<std::string> memberNames = v.getMemberNames();

            for (auto iter = memberNames.begin(); iter != memberNames.end(); iter++)
            {
                output[*iter] = json::deserialize<T>(v[*iter]);
            }

            return output;
        }
    };

    template <class T>
    struct Serializer < std::vector<T> >
    {
        static Json::Value serialize(const std::vector<T>& v)
        {
            Json::Value output(Json::arrayValue);

            for (auto iter = v.begin(); iter != v.end(); iter++)
            {
                output.append(json::serialize(*iter));
            }

            return output;
        }

        static std::vector<T> deserialize(const Json::Value& v)
        {
            std::vector<T> output;

            for (uint32_t i = 0; i < v.size(); i++)
            {
                output.push_back(json::deserialize<T>(v[i]));
            }

            return output;
        }
    };

    template <class T, class U>
    struct Serializer < std::pair<T, U> >
    {
        static Json::Value serialize(const std::pair<T, U>& v)
        {
            Json::Value output;

            output["first"] = json::serialize(v.first);
            output["second"] = json::serialize(v.second);
            return output;
        }

        static std::pair<T, U> deserialize(const Json::Value& v)
        {
            std::pair<T, U> output;

            output.first = json::deserialize<T>(v["first"]);
            output.second = json::deserialize<T>(v["second"]);

            return output;
        }
    };

    template<class ValT>
    struct Serializer< std::shared_ptr< std::vector<ValT> > >
    {
    public:
        typedef ValT                                value_t;
        typedef std::vector<value_t>                vector_t;
        typedef std::shared_ptr<vector_t>           vector_shared_ptr;
        typedef std::shared_ptr<const vector_t>     const_vector_shared_ptr;

    public:
        static Json::Value serialize(const const_vector_shared_ptr& vals)
        {
            if (vals)
            {
                Json::Value jsnVal(Json::arrayValue);
                serializeHelp_(*vals, jsnVal);
                return jsnVal;
            }
            return Json::Value(Json::nullValue);
        }
        static Json::Value serialize(const vector_shared_ptr& vals)
        {
            if (vals)
            {
                Json::Value jsnVal(Json::arrayValue);
                serializeHelp_(*vals, jsnVal);
                return jsnVal;
            }
            return Json::Value(Json::nullValue);
        }

        static vector_shared_ptr deserialize(const Json::Value& jsnVal)
        {
            if (jsnVal.isNull())
                return nullptr;

            auto vals = std::make_shared<vector_t>();
            const auto cnt = jsnVal.size();
            vals->reserve(cnt);
            for (Json::Value::UInt u = 0; u < cnt; ++u)
            {
                vals->push_back(json::deserialize<value_t>(jsnVal[u]));
            }
            return vals;
        }

    private:
        static void serializeHelp_(const vector_t& vals, Json::Value& jsnDest)
        {
            for (auto cit = vals.begin(), citEnd = vals.end(); citEnd != cit; ++cit)
            {
                jsnDest.append(json::serialize(*cit));
            }
        }
    };

    template <class T>
    struct Serializer < std::list<T> >
    {
        static Json::Value serialize(const std::list<T>& v)
        {
            Json::Value output(Json::arrayValue);

            for (auto iter = v.begin(); iter != v.end(); iter++)
            {
                output.append(json::serialize(*iter));
            }

            return output;
        }

        static std::list<T> deserialize(const Json::Value& v)
        {
            std::list<T> output;

            for (uint32_t i = 0; i < v.size(); i++)
            {
                output.push_back(json::deserialize<T>(v[i]));
            }

            return output;
        }
    };

    template <class T>
    struct Serializer < std::set<T> >
    {
        static Json::Value serialize(const std::set<T>& v)
        {
            Json::Value output(Json::arrayValue);

            for (auto iter = v.begin(); iter != v.end(); iter++)
            {
                output.append(json::serialize(*iter));
            }

            return output;
        }

        static std::set<T> deserialize(const Json::Value& v)
        {
            std::set<T> output;

            for (uint32_t i = 0; i < v.size(); i++)
            {
                output.insert(json::deserialize<T>(v[i]));
            }

            return output;
        }
    };

    template <class T>
    struct Serializer < std::optional<T> >
    {
        static Json::Value serialize(const std::optional<T>& v)
        {
            Json::Value output;

            if (v)
            {
                output = json::serialize(*v);
            }
            if (!output.isArray())
            {
                output["optional"] = bool(v);
            }

            return output;
        }

        static std::optional<T> deserialize(const Json::Value& v)
        {
            std::optional<T> output;
            bool optional = true;

            if(v.isObject() && v.isMember("optional"))
            {
                optional = v["optional"].asBool();
            }

            if(optional)
            {
                output = json::deserialize<T>(v);
            }

            return output;
        }
    };

    template <>
    struct Serializer < std::vector < rpos_common::core::Location > >
    {
        static Json::Value serialize(const std::vector< rpos_common::core::Location >& v);
        static std::vector< rpos_common::core::Location > deserialize(const Json::Value& v);
    };


    template <>
    struct Serializer < std::vector < std::uint8_t > >
    {
        static Json::Value serialize(const std::vector< std::uint8_t >& v);
        static std::vector< std::uint8_t > deserialize(const Json::Value& v);
    }; 
}}}
