/*
* memory_read_stream.cpp
* MemoryReadStream use a buffer in memory to read from
*
* Created by <PERSON> (<EMAIL>) at 2017-4-13
* Copyright 2017 (c) Shanghai Slamtec Co., Ltd.
*/

#include <io/memory_read_stream.h>
#include <string.h>

namespace rpos_common { namespace io {

    MemoryReadStream::MemoryReadStream(const void* buffer, size_t size, MemoryReadStreamFlag flags)
        : flag_(flags), offset_(0)
    {
        if (flags == MemoryReadStreamFlagCopyBuffer)
        {
            if (size)
            {
                vectorHolder_.resize(size);
                memcpy(&vectorHolder_[0], buffer, size);
            }

            bufferInVector_ = true;
            buffer_ = size ? &vectorHolder_[0] : 0;
            bufferSize_ = size;
        }
        else
        {
            bufferInVector_ = false;
            buffer_ = (const std::uint8_t*)buffer;
            bufferSize_ = size;
        }
    }

    MemoryReadStream::MemoryReadStream(const std::vector<std::uint8_t>& buffer, MemoryReadStreamFlag flags)
        : flag_(flags), offset_(0)
    {
        if (flags == MemoryReadStreamFlagCopyBuffer)
        {
            vectorHolder_ = buffer;

            bufferInVector_ = true;
            buffer_ = &vectorHolder_[0];
            bufferSize_ = buffer.size();
        }
        else
        {
            bufferInVector_ = false;
            buffer_ = &buffer[0];
            bufferSize_ = buffer.size();
        }
    }

    MemoryReadStream::MemoryReadStream(std::vector<std::uint8_t>&& buffer)
        : flag_(MemoryReadStreamFlagCopyBuffer)
        , bufferInVector_(true)
        , bufferSize_(buffer.size())
        , vectorHolder_(buffer)
        , offset_(0)
    {
        buffer_ = &vectorHolder_[0];
    }

    MemoryReadStream::~MemoryReadStream()
    {
        if (bufferInVector_ || bufferSize_ == 0)
            return;

        if (flag_ == MemoryReadStreamFlagFreeAfterUse)
        {
            free(const_cast<std::uint8_t*>(buffer_));
            buffer_ = nullptr;
        }
        else if (flag_ == MemoryReadStreamFlagDeleteArrayAfterUse)
        {
            delete[] const_cast<std::uint8_t*>(buffer_);
            buffer_ = nullptr;
        }
    }

    bool MemoryReadStream::isOpen()
    {
        return true;
    }

    bool MemoryReadStream::canRead()
    {
        return true;
    }

    bool MemoryReadStream::canWrite()
    {
        return false;
    }

    bool MemoryReadStream::canSeek()
    {
        return true;
    }

    void MemoryReadStream::close()
    {}

    bool MemoryReadStream::endOfStream()
    {
        return offset_ >= bufferSize_;
    }

    int MemoryReadStream::read(void* buffer, size_t size)
    {
        size_t bytesLeft = bufferSize_ - offset_;
        size_t bytesRead = std::min(bytesLeft, size);

        if (bytesRead)
            memcpy(buffer, &buffer_[offset_], bytesRead);

        offset_ += bytesRead;

        return (int)bytesRead;
    }

    int MemoryReadStream::write(const void* , size_t )
    {
        return -1;
    }

    size_t MemoryReadStream::tell()
    {
        return offset_;
    }

    void MemoryReadStream::seek(SeekType type, int offset)
    {
        int newPose = (int)offset_;
        switch (type)
        {
        case SeekTypeSet:
            newPose = offset;
            break;
        case SeekTypeEnd:
            newPose = (int)bufferSize_ - offset;
            break;
        case SeekTypeOffset:
            newPose += offset;
            break;
        }

        if (newPose < 0)
            offset_ = 0;
        else if ((size_t)newPose > bufferSize_)
            offset_ = bufferSize_;
        else
            offset_ = (size_t)newPose;
    }

    size_t MemoryReadStream::size() const
    {
        return bufferSize_;
    }

    const std::uint8_t* MemoryReadStream::buffer() const
    {
        return buffer_;
    }

} }
