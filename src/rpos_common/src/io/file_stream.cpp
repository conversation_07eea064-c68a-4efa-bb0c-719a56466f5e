/*
* file_stream.cpp
* FileStream implements the IStream interface and is used to manipulate files
*
* Created by <PERSON> (<EMAIL>) at 2016-6-13
* Copyright 2016 (c) Shanghai Slamtec Co., Ltd.
*/

#include <io/file_stream.h>
#include <system/io_utils.h>
#include <stdexcept>

namespace rpos_common { namespace io {

    FileStream::FileStream()
        : open_(false)
        , canRead_(false)
        , canWrite_(false)
        , fileSize_(0)
        , file_(0)
    {}

    FileStream::~FileStream()
    {
        if (file_)
        {
            fclose(file_);
            file_ = 0;
        }
    }

    bool FileStream::open(const std::string& filename, OpenFileMode openFileMode)
    {
        close();

        filename_ = filename;
        
        if (openFileMode == OpenFileModeRead)
        {
            std::error_code ec;
            fileSize_ = std::filesystem::file_size(std::filesystem::path(filename), ec);
#ifdef _WIN32
            errno_t err = fopen_s(&file_, filename.c_str(), "rb");
            if (err != 0) {
                return false;
            }
#else
            file_ = fopen(filename.c_str(), "rb");
            if (!file_) {
                return false;
            }
#endif
            open_ = true;
            canRead_ = true;
            canWrite_ = false;
        }
        else if (openFileMode == OpenFileModeWrite)
        {
#ifdef _WIN32
        errno_t err = fopen_s(&file_, filename.c_str(), "wb");
        if(err != 0)
            return false;
#else
            file_ = fopen(filename.c_str(), "wb");
            if (!file_)
                return false;
#endif
            open_ = true;
            canRead_ = false;
            canWrite_ = true;
            fileSize_ = 0;
        }
        else if (openFileMode == OpenFileModeReadWrite)
        {
            std::error_code ec;
            fileSize_ = std::filesystem::file_size(std::filesystem::path(filename), ec);
#ifdef _WIN32
            errno_t err = fopen_s(&file_, filename.c_str(), "rb+");
            if(err != 0)
                return false;
#else
            file_ = fopen(filename.c_str(), "rb+");
            if (!file_)
                return false;
#endif
            open_ = true;
            canRead_ = true;
            canWrite_ = true;
        }
        else if (openFileMode == OpenFileModeAppendWrite)
        {
            std::error_code ec;
            fileSize_ = std::filesystem::file_size(std::filesystem::path(filename),ec);
#ifdef _WIN32
            errno_t err = fopen_s(&file_, filename.c_str(), "ab");
            if (err != 0)
                return false;
#else
            file_ = fopen(filename.c_str(), "ab");
            if (!file_)
                return false;
#endif
            open_ = true;
            canRead_ = false;
            canWrite_ = true;
        }
        else
        {
            return false;
        }

        return true;
    }

    bool FileStream::open(const std::wstring& filename, OpenFileMode openFileMode)
    {
        close();

        filename_ = filename;

        if (openFileMode == OpenFileModeRead)
        {
            std::error_code ec;
            fileSize_ = std::filesystem::file_size(std::filesystem::path(filename), ec);
            file_ = rpos_common::system::file_open(filename, L"rb");
            if (!file_)
                return false;

            open_ = true;
            canRead_ = true;
            canWrite_ = false;
        }
        else if (openFileMode == OpenFileModeWrite)
        {
            file_ = rpos_common::system::file_open(filename, L"wb");
            if (!file_)
                return false;

            open_ = true;
            canRead_ = false;
            canWrite_ = true;
            fileSize_ = 0;
        }
        else if (openFileMode == OpenFileModeReadWrite)
        {
            std::error_code ec;
            fileSize_ = std::filesystem::file_size(std::filesystem::path(filename), ec);
            file_ = rpos_common::system::file_open(filename, L"rb+");
            if (!file_)
                return false;

            open_ = true;
            canRead_ = true;
            canWrite_ = true;
        }
        else if (openFileMode == OpenFileModeAppendWrite)
        {
            std::error_code ec;
            fileSize_ = std::filesystem::file_size(std::filesystem::path(filename), ec);
            file_ = rpos_common::system::file_open(filename, L"ab");
            if (!file_)
                return false;

            open_ = true;
            canRead_ = false;
            canWrite_ = true;
        }
        else
        {
            return false;
        }

        return true;
    }

    bool FileStream::isOpen()
    {
        return open_;
    }

    bool FileStream::canRead()
    {
        return canRead_;
    }

    bool FileStream::canWrite()
    {
        return canWrite_;
    }

    bool FileStream::canSeek()
    {
        return open_;
    }

    void FileStream::close()
    {
        if (file_)
        {
            fclose(file_);
            file_ = 0;

            open_ = false;
            canRead_ = false;
            canWrite_ = false;
            fileSize_ = 0;
        }
    }

    bool FileStream::endOfStream()
    {
        if (!canRead_) // OpenFileModeWrite
            return true; // ?

        const uint64_t currPos = ftell(file_);

        if (!canWrite_) // OpenFileModeRead
            return fileSize_ <= currPos;

        // OpenFileModeReadWrite
        fseek(file_, 0, SEEK_END);
        fileSize_ = ftell(file_);
        fseek(file_, currPos, SEEK_SET);
        return fileSize_ <= currPos;
    }

    int FileStream::read(void* buffer, size_t size)
    {
        if (!canRead_)
            return -1;

        size_t read_bytes = 0;
        while (read_bytes < size)
        {
            int this_read = fread((unsigned char*)buffer + read_bytes, 1, size - read_bytes, file_);
            if (this_read < 0)
            {
                return this_read;
            }
            else if (this_read == 0)
            {
                break;
            }

            read_bytes += this_read;
        }

        return static_cast<int>(read_bytes);
    }

    int FileStream::write(const void* buffer, size_t size)
    {
        if (!canWrite_)
            return -1;

        size_t written = 0;
        while (written < size)
        {
            int this_written = fwrite((unsigned char*)buffer + written, 1, size - written, file_);
            if (this_written <= 0)
            {
                return this_written;
            }

            written += this_written;
        }

        fileSize_ += written;
        return (int)written;
    }

    size_t FileStream::tell()
    {
        if (!canSeek())
            throw std::runtime_error("Stream can not seek");

        return (size_t)ftell(file_);
    }

    void FileStream::seek(SeekType type, int offset)
    {
        if (!canSeek())
            throw std::runtime_error("Stream can not seek");

        int origin;

        switch (type)
        {
        case SeekTypeSet:
            origin = SEEK_SET;
            break;
        case SeekTypeOffset:
            origin = SEEK_CUR;
            break;
        case SeekTypeEnd:
            origin = SEEK_END;
            break;
        default:
            throw std::invalid_argument("type");
        }

        fseek(file_, offset, origin);
    }

    void FileStream::flush()
    {
        if (!canWrite())
            throw std::runtime_error("Stream can not write");

        fflush(file_);
    }

    uint64_t FileStream::size() const
    {
        return fileSize_;
    }

    std::filesystem::path FileStream::filename() const
    {
        return filename_;
    }

} } 
