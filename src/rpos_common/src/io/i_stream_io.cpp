/*
* i_stream.cpp
* IStream is the abstract interface of a byte stream
*
* Created by <PERSON> (<EMAIL>) at 2016-6-13
* Copyright 2016 (c) Shanghai Slamtec Co., Ltd.
*/

#include <stdexcept>
#include <cassert>
#include <limits>
#include <io/i_stream.h>
#include <system/byte_order.h>

namespace rpos_common { namespace io {

	IStream::IStream() {}
	IStream::~IStream() {}

    void IStream::exactRead(void* buffer, size_t size)
    {
        if (0 == size)
            return;
        if (NULL == buffer)
            throw std::runtime_error("can not exactly read stream to NULL");

        assert(size <= static_cast<size_t>((std::numeric_limits<int>::max)()));
        const int actuallyRead = read(buffer, size);
        if (static_cast<int>(size) != actuallyRead)
            throw std::runtime_error("failed to exactly read stream to buffer");
    }

    void IStream::exactWrite(const void* buffer, size_t size)
    {
        if (0 == size)
            return;
        if (NULL == buffer)
            throw std::runtime_error("can not exactly write NULL to stream");

        assert(size <= static_cast<size_t>((std::numeric_limits<int>::max)()));
        const int actuallyWritten = write(buffer, size);
        if (static_cast<int>(size) != actuallyWritten)
            throw std::runtime_error("failed to exactly write buffer to stream");
    }

#ifdef RPOS_TARGET_BE
#define IMPLEMENT_ISTREAM_READ_WRITE( T ) \
    IStream& operator<<(IStream&out, const T &a) \
    { \
        T b = cpu_to_le(a);\
        out.exactWrite( (const void*)&b, sizeof(b) ); \
        return out; \
    } \
    IStream& operator>>(IStream&in, T &a) \
    { \
        T b;\
        in.exactRead( (void*)&b, sizeof(b) ); \
        a = le_to_cpu(b);\
        return in; \
    }
#else
#define IMPLEMENT_ISTREAM_READ_WRITE( T ) \
    IStream& operator<<(IStream&out, const T &a) \
    { \
        out.exactWrite( (const void*)&a, sizeof(a) ); \
        return out; \
    } \
    IStream& operator>>(IStream&in, T &a) \
    { \
        in.exactRead( (void*)&a, sizeof(a) ); \
        return in; \
    }
#endif

    IMPLEMENT_ISTREAM_READ_WRITE(bool)
    IMPLEMENT_ISTREAM_READ_WRITE(char)
    IMPLEMENT_ISTREAM_READ_WRITE(uint8_t)
    IMPLEMENT_ISTREAM_READ_WRITE(int8_t)
    IMPLEMENT_ISTREAM_READ_WRITE(uint16_t)
    IMPLEMENT_ISTREAM_READ_WRITE(int16_t)
    IMPLEMENT_ISTREAM_READ_WRITE(uint32_t)
    IMPLEMENT_ISTREAM_READ_WRITE(int32_t)
    IMPLEMENT_ISTREAM_READ_WRITE(uint64_t)
    IMPLEMENT_ISTREAM_READ_WRITE(int64_t)
    IMPLEMENT_ISTREAM_READ_WRITE(float)
    IMPLEMENT_ISTREAM_READ_WRITE(double)

    IStream& operator<<(IStream&out, const std::vector<uint8_t> & buf)
    {
        out << static_cast<std::uint32_t>(buf.size());
        if (!buf.empty())
            out.exactWrite(&buf[0], buf.size());
        return out;
    }
    IStream& operator>>(IStream&in, std::vector<uint8_t> & buf)
    {
        std::uint32_t size;
        in >> size;
        buf.resize(size);
        if (0 != size)
            in.exactRead(&buf[0], size);
        return in;
    }

    IStream& operator<<(IStream&out, const std::string& str)
    {
        assert(str.length() <= std::numeric_limits<std::uint32_t>::max());
        out << static_cast<std::uint32_t>(str.length());
        if (!str.empty())
            out.exactWrite(str.c_str(), str.length());
        return out;
    }
    IStream& operator>>(IStream&in, std::string& str)
    {
        std::uint32_t len;
        in >> len;
        str.resize(len);
        if (0 != len)
            in.exactRead(&str[0], len);
        return in;
    }

} }
