/*
* memory_stream.cpp
* MemoryStream use memory to load or store data in memory
*
* Created by <PERSON> (<EMAIL>) at 2016-6-13
* Copyright 2016 (c) Shanghai Slamtec Co., Ltd.
*/

#include <io/memory_write_stream.h>
#include <string.h>
#include <stdexcept>

namespace rpos_common { namespace io {

    MemoryWriteStream::MemoryWriteStream(size_t initialCapacity)
        : buffer_(initialCapacity)
        , size_(0)
    {}

    MemoryWriteStream::~MemoryWriteStream()
    {}

    bool MemoryWriteStream::isOpen()
    {
        return true;
    }

    bool MemoryWriteStream::canRead()
    {
        return false;
    }

    bool MemoryWriteStream::canWrite()
    {
        return true;
    }

    bool MemoryWriteStream::canSeek()
    {
        return false;
    }

    void MemoryWriteStream::close()
    {
    }

    bool MemoryWriteStream::endOfStream()
    {
        return true;
    }

    int MemoryWriteStream::read(void* , size_t )
    {
        return -1;
    }

    int MemoryWriteStream::write(const void* buffer, size_t size)
    {
        if (!size)
            return size;
        size_t expectedSize = size_ + size;

        if (expectedSize > buffer_.size())
        {
            size_t newSize = std::max(buffer_.size(), DefaultMemoryStreamInitialCapacity);
            while (newSize < expectedSize)
            {
                newSize += (newSize >> 1);
            }
            try {
                buffer_.resize(newSize);
            }
            catch (const std::bad_alloc &)
            {
                buffer_.resize(expectedSize + DefaultMemoryStreamInitialCapacity);
            }
        }

        memcpy(&buffer_[size_], buffer, size);
        size_ += size;

        return size;
    }

    size_t MemoryWriteStream::tell()
    {
        return size_;
    }
    
    void MemoryWriteStream::seek(SeekType , int )
    {
        throw std::runtime_error("not supported");
    }

    size_t MemoryWriteStream::size() const
    {
        return size_;
    }

    const std::uint8_t* MemoryWriteStream::buffer() const
    {
        return &buffer_[0];
    }

    const std::vector<std::uint8_t>& MemoryWriteStream::asByteVector() const
    {
        return buffer_;
    }


    void MemoryWriteStream::writeTo(IStream& target) const
    {
        target.write(&buffer_[0], size_);
    }

    void MemoryWriteStream::writeToFile(const std::string& filename) const
    {
        FILE* file;
#ifdef _WIN32
        errno_t err = fopen_s(&file, filename.c_str(), "wb");
        if(err != 0)
            return;
#else
        file = fopen(filename.c_str(), "wb");
        if (!file)
            return;
#endif

        size_t written = 0;
        while (written < size_)
        {
            int this_write = fwrite(&buffer_[written], 1, size_ - written, file);
            if (this_write <= 0)
            { 
                break;
            }

            written += this_write;
        }

        fclose(file);
    }

} }
