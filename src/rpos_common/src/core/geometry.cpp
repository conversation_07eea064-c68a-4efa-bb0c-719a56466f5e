/**
* geometry.cpp
* Geometry
*
* Created By <PERSON><PERSON> @ 2014-12-26
* Copyright (c) 2014 Shanghai SlamTec Co., Ltd.
*/

#include <core/geometry.h>
#include <core/metadata_internal.h>

namespace rpos_common {
    namespace core {

        // ==============
        // class Point
        // ==============

        // Constructors
        Point::Point()
            : x_(0), y_(0)
        {}

        Point::Point(float x, float y)
            : x_(x), y_(y)
        {}

        Point::Point(const Point& that)
            : x_(that.x_), y_(that.y_)
        {}

        Point::~Point() {}

        // Operators
        Point& Point::operator=(const Point& that) {
            x_ = that.x_;
            y_ = that.y_;
            return *this;
        }

        // Accessors
        float Point::x() const {
            return x_;
        }

        float& Point::x() {
            return x_;
        }

        float Point::y() const {
            return y_;
        }

        float& Point::y() {
            return y_;
        }

        // ==============
        // class Line
        // ==============

        // Constructors
        Line::Line()
            : startP_(0, 0), endP_(0, 0), id_(0)
        {}

        Line::Line(const Point &startP, const Point &endP)
            : startP_(startP), endP_(endP), id_(0)
        {}

        Line::Line(const Point &startP, const Point &endP, int id)
            : startP_(startP), endP_(endP), id_(id)
        {}

        Line::Line(const Line& that)
            : startP_(that.startP_), endP_(that.endP_), id_(that.id_), metadata_(that.metadata_)
        {}

        Line::~Line() {}

        // Operators
        Line& Line::operator=(const Line& that) {
            startP_ = that.startP_;
            endP_   = that.endP_;
            id_     = that.id_;
            metadata_ = that.metadata_;
            return *this;
        }

        static float mult(const Point& a,const Point& b, const Point& c)
        {
            return (a.x()-c.x())*(b.y()-c.y())-(b.x()-c.x())*(a.y()-c.y());
        }

        bool Line::isLineSegmentCross(const Line& line)
        {
            if ( std::max(startP_.x(), endP_.x())<std::min(line.startP_.x(), line.endP_.x()) )
            {
                return false;
            }
            if ( std::max(startP_.y(), endP_.y())<std::min(line.startP_.y(), line.endP_.y()) )
            {
                return false;
            }
            if ( std::max(line.startP_.x(), line.endP_.x())<std::min(startP_.x(), endP_.x()) )
            {
                return false;
            }
            if ( std::max(line.startP_.y(), line.endP_.y())<std::min(startP_.y(), endP_.y()) )
            {
                return false;
            }
            if ( mult(line.startP_, endP_, startP_)*mult(endP_, line.endP_, startP_)<0 )
            {
                return false;
            }
            if ( mult(startP_, line.endP_, line.startP_)*mult(line.endP_, endP_, line.startP_)<0 )
            {
                return false;
            }
            return true;
        }

        // Accessors
        Point& Line::startP() {
            return startP_;
        }

        const Point& Line::startP() const {
            return startP_;
        }

        Point& Line::endP() {
            return endP_;
        }

        const Point& Line::endP() const {
            return endP_;
        }

        SegmentID& Line::id() {
            return id_;
        }

        const SegmentID& Line::id() const {
            return id_;
        }

        rpos_common::core::Metadata& Line::metadata()
        {
            return metadata_;
        }

        const rpos_common::core::Metadata& Line::metadata() const
        {
            return metadata_;
        }

        bool Line::isBezierCurve(rpos_common::core::Vector2f& p1, rpos_common::core::Vector2f& p2) const
        {
            if (metadata_.tryGet<rpos_common::core::Vector2f>("control_point1",p1)
                && metadata_.tryGet<rpos_common::core::Vector2f>("control_point2",p2))
            {
                return true;
            }
            return false;
        }

        bool Line::isBezierCurve() const
        {
            rpos_common::core::Vector2f p1,p2;
            if (metadata_.tryGet<rpos_common::core::Vector2f>("control_point1",p1)
                && metadata_.tryGet<rpos_common::core::Vector2f>("control_point2",p2))
            {
                return true;
            }
            return false;
        }

        void Line::setBidirection(bool isBidirectional)
        {
            metadata_.set<bool>("bidirection", isBidirectional);
        }

        void Line::reverseDirection()
        {
            Point temp = startP_;
            startP_ = endP_;
            endP_ = temp;
        }

        bool Line::isBidirectional() const
        {
            bool isBiDirectional = false;
            if (metadata_.tryGet<bool>("bidirection", isBiDirectional))
            {
                return isBiDirectional;
            }
            return false;
        }
    }
}
