#include <core/metadata.h>

namespace rpos_common { namespace core { 

    void Metadata::set(const std::string& key, const std::string& value)
    {
        dict_[key] = value;
    }

    const std::string& Metadata::get(const std::string& key) const
    {
        return dict_.find(key)->second;
    }

    bool Metadata::tryGet(const std::string& key, std::string& outValue) const
    {
        auto itr = dict_.find(key);
        if(itr != dict_.end())
        {
            outValue = itr->second;
            return true;
        }
        else
        {
            return false;
        }
    }

    bool Metadata::operator==(const Metadata& that) const
    {
        return dict_ == that.dict_;
    }

    const std::map<std::string, std::string>& Metadata::dict() const
    {
        return dict_;
    }

    std::map<std::string, std::string>& Metadata::dict()
    {
        return dict_;
    }
}}
