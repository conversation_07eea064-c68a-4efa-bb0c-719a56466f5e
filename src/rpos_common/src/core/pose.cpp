#include <core/pose.h>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace rpos_common { namespace core {
 
    Location::Location()
        : x_(0), y_(0), z_(0)
    {}

    Location::Location(double x, double y, double z)
        : x_(x), y_(y), z_(z)
    {}

    Location::Location(const Location& that)
        : x_(that.x_), y_(that.y_), z_(that.z_)
    {}

    Location::~Location() {}

    // Operators
    Location& Location::operator=(const Location& that) {
        x_ = that.x_;
        y_ = that.y_;
        z_ = that.z_;
        return *this;
    }

    bool Location::operator==(const Location& that) const
    {
        return x_ == that.x_ && y_ == that.y_ && z_ == that.z_;
    }

    // Accessors
    double Location::x() const {
        return x_;
    }

    double& Location::x() {
        return x_;
    }

    double Location::y() const {
        return y_;
    }

    double& Location::y() {
        return y_;
    }

    double Location::z() const {
        return z_;
    }

    double& Location::z() {
        return z_;
    }

    double Location::distanceTo(const Location& that) const {
        double dx = that.x_ - x_;
        double dy = that.y_ - y_;
        double dz = that.z_ - z_;

        return std::sqrt(dx*dx + dy*dy + dz*dz);
    }

    // ==============
    // class Rotation
    // ==============

    // Constructors
    Rotation::Rotation()
        : yaw_(0), pitch_(0), roll_(0)
    {}

    Rotation::Rotation(double yaw, double pitch, double roll)
        : yaw_(yaw), pitch_(pitch), roll_(roll)
    {}

    Rotation::Rotation(const Rotation& that)
        : yaw_(that.yaw_), pitch_(that.pitch_), roll_(that.roll_)
    {}

    Rotation::Rotation(const Eigen::Matrix3d& rot) {
        const Eigen::Vector3d& n = rot.col(0);
        const Eigen::Vector3d& o = rot.col(1);
        const Eigen::Vector3d& a = rot.col(2);

        yaw_ = std::atan2(n(1), n(0));
        pitch_ = std::atan2(-n(2), n(0) * std::cos(yaw_) + n(1) * std::sin(yaw_));
        roll_ = std::atan2(a(0) * std::sin(yaw_) - a(1) * std::cos(yaw_), -o(0) * std::sin(yaw_) + o(1) * std::cos(yaw_));
    }

    Rotation::~Rotation() {}

    // Operators
    Rotation& Rotation::operator=(const Rotation& that) {
        yaw_ = that.yaw_;
        pitch_ = that.pitch_;
        roll_ = that.roll_;
        return *this;
    }

    bool Rotation::operator==(const Rotation& that) const
    {
        return yaw_ == that.yaw_ && pitch_ == that.pitch_ && roll_ == that.roll_;
    }

    Rotation::operator Eigen::Matrix3d() const 
    {
        Eigen::Matrix3d Rz;
        Rz << std::cos(yaw_), -std::sin(yaw_), 0,
            std::sin(yaw_), std::cos(yaw_), 0,
            0, 0, 1;

        Eigen::Matrix3d Ry;
        Ry << std::cos(pitch_), 0., std::sin(pitch_),
            0., 1., 0.,
            -std::sin(pitch_), 0., std::cos(pitch_);

        Eigen::Matrix3d Rx;
        Rx << 1., 0., 0.,
            0., std::cos(roll_), -std::sin(roll_),
            0., std::sin(roll_), std::cos(roll_);

        return Rz * Ry * Rx;
    }

    // Accessors
    double Rotation::yaw() const {
        return yaw_;
    }

    double& Rotation::yaw() {
        return yaw_;
    }

    double Rotation::pitch() const {
        return pitch_;
    }

    double& Rotation::pitch() {
        return pitch_;
    }

    double Rotation::roll() const {
        return roll_;
    }

    double& Rotation::roll() {
        return roll_;
    }
 
    // ==========
    // class Pose
    // ==========

    // Constructors
    Pose::Pose() {}
    
    Pose::Pose(const Location& location, const Rotation& rotation)
        : location_(location), rotation_(rotation)
    {}

    Pose::Pose(const Location& location)
        : location_(location), rotation_()
    {}

    Pose::Pose(const Rotation& rotation)
        : location_(), rotation_(rotation)
    {}

    Pose::Pose(const Pose& pose)
        : location_(pose.location_), rotation_(pose.rotation_)
    {}

    Pose::Pose(const Eigen::Matrix4d& transform) {
        Eigen::Vector3d location = transform.block<3, 1>(0, 3); 
        location_ = Location(location.x(), location.y(), location.z());
        rotation_ = Rotation(transform.block<3, 3>(0, 0));
    }

    Pose::~Pose() {}

    // Assign Operator
    Pose& Pose::operator=(const Pose& that) {
        location_ = that.location_;
        rotation_ = that.rotation_;
        return *this;
    }

    bool Pose::operator==(const Pose& that) const
    {
        return location_ == that.location_ && rotation_ == that.rotation_;
    }

    bool Pose::operator<(const Pose& that) const
    {
        if (y() > that.y())
        {
            return false;
        }
        else if (y() == that.y())
        {
            return x() < that.x();
        }
        else
            return true;
    }
    
    Pose::operator Eigen::Matrix4d() const {
        Eigen::Matrix4d result = Eigen::Matrix4d::Identity();
        result.block<3, 3>(0, 0) = Eigen::Matrix3d(rotation());
        result.block<3, 1>(0, 3) = Eigen::Vector3d(x(),y(),z());
        return result;
    }

    // Accessors
    const Location& Pose::location() const {
        return location_;
    }

    Location& Pose::location() {
        return location_;
    }

    const Rotation& Pose::rotation() const {
        return rotation_;
    }

    Rotation& Pose::rotation() {
        return rotation_;
    }

    double Pose::x() const {
        return location_.x();
    }

    double& Pose::x() {
        return location_.x();
    }

    double Pose::y() const {
        return location_.y();
    }

    double& Pose::y() {
        return location_.y();
    }

    double Pose::z() const {
        return location_.z();
    }

    double& Pose::z() {
        return location_.z();
    }

    double Pose::yaw() const {
        return rotation_.yaw();
    }

    double& Pose::yaw() {
        return rotation_.yaw();
    }

    double Pose::pitch() const {
        return rotation_.pitch();
    }

    double& Pose::pitch() {
        return rotation_.pitch();
    }

    double Pose::roll() const {
        return rotation_.roll();
    }

    double& Pose::roll() {
        return rotation_.roll();
    }
} }
