/*
* aperture.cpp
* Aperture class contains defination and some filter functions to filter laser scan data according to aperture settings
*
* Created by <PERSON> (<EMAIL>) at 2017-1-4
* Copyright 2017 (c) Shanghai Slamtec Co., Ltd.
*/

/*
* Design: the ranges in ranges_ field should be ordered ascending by start field, and never overlap with each other (if overlap occurred, it will be merged)
*/

#include <algorithm/aperture.h> 
#include <core/angle_math.h>

namespace rpos_common { namespace algorithm {

    using namespace rpos_common::core;

    Aperture::Aperture()
        : sizeInRad_(0)
    {}

    Aperture::Aperture(const ApertureRange& aperture)
        : sizeInRad_(0)
    {
        addRange(aperture);
    }

    Aperture::Aperture(const std::vector<ApertureRange>& aperture)
        : sizeInRad_(0)
    {
        setRange(aperture);
    }

    Aperture::Aperture(const Aperture& that)
        : sizeInRad_(0)
    {
        copy(that);
    }

    Aperture::Aperture(Aperture&& that)
        : sizeInRad_(0)
    {
        swap(that);
    }

    Aperture::~Aperture()
    {}

    Aperture& Aperture::operator=(const Aperture& that)
    {
        if (this != &that)
        {
            copy(that);
        }

        return *this;
    }

    Aperture& Aperture::operator=(Aperture&& that)
    {
        if (this != &that)
        {
            swap(that);
        }

        return *this;
    }

    void Aperture::copy(const Aperture& that)
    {
        std::lock_guard<std::mutex> guard(lock_);
        std::lock_guard<std::mutex> thatGuard(that.lock_);

        ranges_ = that.ranges_;
        sizeInRad_ = that.sizeInRad_;
    }

    void Aperture::swap(Aperture& that)
    {
        std::lock_guard<std::mutex> guard(lock_);
        std::lock_guard<std::mutex> thatGuard(that.lock_);

        std::swap(ranges_, that.ranges_);
        std::swap(sizeInRad_, that.sizeInRad_);
    }

    double Aperture::radSize() const
    {
        std::lock_guard<std::mutex> guard(lock_);
        return sizeInRad_;
    }

    double Aperture::degSize() const
    {
        return rad2deg(radSize());
    }

    std::list<ApertureRange>::iterator Aperture::begin()
    {
        std::lock_guard<std::mutex> guard(lock_);
        return ranges_.begin();
    }

    std::list<ApertureRange>::const_iterator Aperture::begin() const
    {
        std::lock_guard<std::mutex> guard(lock_);
        return ranges_.begin();
    }

    std::list<ApertureRange>::iterator Aperture::end()
    {
        std::lock_guard<std::mutex> guard(lock_);
        return ranges_.end();
    }

    std::list<ApertureRange>::const_iterator Aperture::end() const
    {
        std::lock_guard<std::mutex> guard(lock_);
        return ranges_.end();
    }

    bool Aperture::radInRange(double rad) const
    {
        std::lock_guard<std::mutex> guard(lock_);

        rad = constraitRadNegativePiToPi(rad);

        for (auto iter = ranges_.begin(); iter != ranges_.end(); iter++)
        {
            auto& range = *iter;

            if (range.start > rad)
                return false;

            if ((rad - range.start) <= range.size)
                return true;
        }

        return false;
    }

    bool Aperture::degInRange(double deg) const
    {
        return radInRange(deg2rad(deg));
    }

    void Aperture::setRange(const ApertureRange& range)
    {
        std::lock_guard<std::mutex> guard(lock_);
        ranges_.clear();
        sizeInRad_ = 0;
        addRange_(range);
    }

    void Aperture::setRange(const std::vector<ApertureRange>& range)
    {
        std::lock_guard<std::mutex> guard(lock_);
        ranges_.clear();
        sizeInRad_ = 0;
        for (auto iter = range.begin(); iter != range.end(); iter++)
            addRange_(*iter);
    }

    void Aperture::setRange(double start, double size)
    {
        std::lock_guard<std::mutex> guard(lock_);
        ranges_.clear();
        sizeInRad_ = 0;
        addRange_(start, size);
    }

    void Aperture::addRange(const ApertureRange& range)
    {
        std::lock_guard<std::mutex> guard(lock_);
        addRange_(range);
    }

    void Aperture::addRange(double start, double size)
    {
        std::lock_guard<std::mutex> guard(lock_);
        addRange_(start, size);
    }

    void Aperture::addRange_(const ApertureRange& range)
    {
        addRange_(range.start, range.size);
    }

    enum RangeCompareResult
    {
        // #####
        //       ||||
        RangeCompareResultNoContactBefore,

        //         #####
        // |||||
        RangeCompareResultNoContactAfter,

        //   ####
        // ||||
        RangeCompareResultExtendHead,

        // ####
        //   ||||
        RangeCompareResultExtendTail,

        // ####
        //  ||
        RangeCompareResultContains,

        //  ##
        // ||||
        RangeCompareResultContained
    };

    static inline RangeCompareResult compareRange(double start1, double size1, double start2, double size2)
    {
        double end1 = start1 + size1;
        double end2 = start2 + size2;

        if (start2 > end1)
            return RangeCompareResultNoContactBefore;

        if (start1 > end2)
            return RangeCompareResultNoContactAfter;

        if (start2 < start1)
        {
            if (end2 >= end1)
                return RangeCompareResultContained;
            else
                return RangeCompareResultExtendHead;
        }
        else if (start1 < start2)
        {
            if (end1 >= end2)
                return RangeCompareResultContains;
            else
                return RangeCompareResultExtendTail;
        }
        else
        {
            if (end1 >= end2)
                return RangeCompareResultContains;
            else
                return RangeCompareResultContained;
        }
    }

    void Aperture::addRange_(double start, double size)
    {
        // in this function, we have following issue to deal with:
        // 1) trim start to [-PI, PI)
        // 2) if size if negative, flip it to make sure it's positive
        // 3) if size is greater than or equal to 2PI, just remove all elements in range and use [-PI, PI)
        // 4) if the range is completely covered by other ranges, just ignore it
        // 5) if the range cross the -PI and PI edge, split it into two ranges
        // 6) if the range is overlapped with other ranges, merge ranges
        // 6.1)   if the range extends the tail of a range, merge it and check if later range should be merged either
        // 6.2)   if the range extends the head of a range, it's ok to not to do the check

        if (size < 0)
        {
            start += size;
            size = -size;
        }

        start = constraitRadNegativePiToPi(start);

        if ((M_PI - start) < size)
        {
            // the range crosses the -PI,PI border, split to two parts
            addRangeNoCheck_(start, M_PI - start);
            addRangeNoCheck_(-M_PI, size - M_PI + start);
        }
        else
        {
            addRangeNoCheck_(start, size);
        }

        recalculateSize_();
    }

    void Aperture::addFilterRange(double start, double size)
    {
        std::lock_guard<std::mutex> guard(lock_);

        if (size < 0)
        {
            start += size;
            size = -size;
        }

        start = constraitRadNegativePiToPi(start);

        if ((M_PI - start) < size)
        {
            addFilterRangeNoCheck_(start, M_PI - start);
            addFilterRangeNoCheck_(-M_PI, size - M_PI + start);
        }
        else
        {
            addFilterRangeNoCheck_(start, size);
        }

        recalculateSize_();
    }

    void Aperture::addFilterRangeNoCheck_(double start, double size)
    {
        for (auto iter = ranges_.begin(); iter != ranges_.end(); iter++)
        {
            auto compareResult = compareRange(iter->start, iter->size, start, size);

            if (compareResult == RangeCompareResultContains)
            {
                auto first_start = iter->start;
                auto first_size = start - iter->start;
                auto second_start = start + size;
                auto second_size = iter->start + iter->size - second_start;
                ranges_.erase(iter);
                addRangeNoCheck_(first_start, first_size);
                addRangeNoCheck_(second_start, second_size);
                break;
            }

            if (compareResult == RangeCompareResultContained)
            {
                ranges_.erase(iter);
                break;
            }

            if (compareResult == RangeCompareResultNoContactBefore)
                continue;

            if (compareResult == RangeCompareResultNoContactAfter)
            {
                continue;
            }

            if (compareResult == RangeCompareResultExtendHead)
            {
                auto first_start = start + size;
                auto first_size = iter->start + iter->size - first_start;
                ranges_.erase(iter);
                addRangeNoCheck_(first_start, first_size);
                break;
            }

            if (compareResult == RangeCompareResultExtendTail)
            {
                auto first_start = iter->start;
                auto first_size = start - iter->start;
                ranges_.erase(iter);
                addRangeNoCheck_(first_start, first_size);
                break;
            }
        }
    }

    void Aperture::clear()
    {
        std::lock_guard<std::mutex> guard(lock_);
        ranges_.clear();
        sizeInRad_ = 0;
    }

    void Aperture::addRangeNoCheck_(double start, double size)
    {
        bool inserted = false;

        // do the insertion first, do the merge later
        for (auto iter = ranges_.begin(); iter != ranges_.end(); iter++)
        {
            auto compareResult = compareRange(iter->start, iter->size, start, size);

            if (compareResult == RangeCompareResultContains)
            {
                // the range already contains the range
                return;
            }

            if (compareResult == RangeCompareResultContained)
            {
                iter->start = start;
                iter->size = size;
                inserted = true;
                break;
            }

            if (compareResult == RangeCompareResultNoContactBefore)
                continue;

            if (compareResult == RangeCompareResultNoContactAfter)
            {
                ApertureRange range;
                range.start = start;
                range.size = size;
                ranges_.insert(iter, range);
                return;
            }

            if (compareResult == RangeCompareResultExtendHead)
            {
                iter->size = iter->start + iter->size - start;
                iter->start = start;
                inserted = true;
                break;
            }

            if (compareResult == RangeCompareResultExtendTail)
            {
                iter->size = start + size - iter->start;
                inserted = true;
                break;
            }
        }

        if (!inserted)
        {
            ApertureRange range;
            range.start = start;
            range.size = size;
            ranges_.push_back(range);
        }

        // do the merge
        bool needMerge = true;
        while (needMerge)
        {
            auto slow = ranges_.begin();
            if (slow == ranges_.end())
                break;

            auto fast = ranges_.begin();
            fast++;

            needMerge = false;
            for (;fast != ranges_.end();slow++, fast++)
            {
                auto compareResult = compareRange(slow->start, slow->size, fast->start, fast->size);

                if (compareResult == RangeCompareResultContains)
                {
                    ranges_.erase(fast);
                    needMerge = true;
                    break;
                }
                else if (compareResult == RangeCompareResultContained)
                {
                    ranges_.erase(slow);
                    needMerge = true;
                    break;
                }
                else if (compareResult == RangeCompareResultNoContactAfter || compareResult == RangeCompareResultNoContactBefore)
                {
                    continue;
                }
                else if (compareResult == RangeCompareResultExtendHead)
                {
                    fast->size = slow->start + slow->size - fast->start;
                    ranges_.erase(slow);
                    needMerge = true;
                    break;
                }
                else if (compareResult == RangeCompareResultExtendTail)
                {
                    slow->size = fast->start + fast->size - slow->start;
                    ranges_.erase(fast);
                    needMerge = true;
                    break;
                }
            }
        }
    }

    void Aperture::recalculateSize_()
    {
        double size = 0;

        for (auto iter = ranges_.begin(); iter != ranges_.end(); iter++)
        {
            size += iter->size;
        }

        sizeInRad_ = size;
    }

} } 
