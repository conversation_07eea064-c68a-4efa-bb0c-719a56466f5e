#include <algorithm/grid_cell_utils.h>

namespace rpos_common { namespace algorithm {

    void GridCellHelper::sfGetMinMaxOf4(float f0, float f1, float f2, float f3, float& rMin, float& rMax)
    {
        float fMin = f0;
        float fMax = f0;
        
        if (f1 < fMin)
            fMin = f1;
        else if (fMax < f1)
            fMax = f1;

        if (f2 < fMin)
            fMin = f2;
        else if (fMax < f2)
            fMax = f2;

        if (f3 < fMin)
            fMin = f3;
        else if (fMax < f3)
            fMax = f3;

        rMin = fMin;
        rMax = fMax;
    }

    void GridCellHelper::CoordinateFrame::reset()
    {
        m_tPosition = Vector2f(0.0f, 0.0f);
        m_fCosTheta = 1.0f;
        m_fSinTheta = 0.0f;

        m_fTheta = 0.0f;
    }

    void GridCellHelper::CoordinateFrame::reset(float x, float y, float theta)
    {
        m_tPosition = Vector2f(x, y);
        m_fCosTheta = std::cos(theta);
        m_fSinTheta = std::sin(theta);

        m_fTheta = theta;
    }

    rpos_common::core::Vector2f GridCellHelper::CoordinateFrame::childToParent(const Vector2f& tChild) const
    {
        Vector2f tParent(m_fCosTheta * tChild.x() - m_fSinTheta * tChild.y(), m_fSinTheta * tChild.x() + m_fCosTheta * tChild.y());
        tParent += m_tPosition;
        return tParent;
    }

    rpos_common::core::Vector2f GridCellHelper::CoordinateFrame::parentToChild(const Vector2f& tParent) const
    {
        Vector2f tTmp(tParent - m_tPosition);
        Vector2f tChild(m_fCosTheta * tTmp.x() + m_fSinTheta * tTmp.y(), -m_fSinTheta * tTmp.x() + m_fCosTheta * tTmp.y());
        return tChild;
    }

    void GridCellHelper::CoordinateFrame::inplaceChildToParent(Vector2f* pInOutBuf, size_t szCnt) const
    {
        assert(NULL != pInOutBuf || 0 == szCnt);
        for (size_t t = 0; t < szCnt; ++t)
            pInOutBuf[t] = childToParent(pInOutBuf[t]);
    }

    void GridCellHelper::CoordinateFrame::inplaceParentToChild(Vector2f* pInOutBuf, size_t szCnt) const
    {
        assert(NULL != pInOutBuf || 0 == szCnt);
        for (size_t t = 0; t < szCnt; ++t)
            pInOutBuf[t] = parentToChild(pInOutBuf[t]);
    }

    void GridCellHelper::CoordinateFrame::transformChildToParent(const Vector2f* pcIn, size_t szCnt, Vector2f* pOut) const
    {
        assert((NULL != pcIn && NULL != pOut) || 0 == szCnt);
        for (size_t t = 0; t < szCnt; ++t)
            pOut[t] = childToParent(pcIn[t]);
    }

    void GridCellHelper::CoordinateFrame::transformParentToChild(const Vector2f* pcIn, size_t szCnt, Vector2f* pOut) const
    {
        assert((NULL != pcIn && NULL != pOut) || 0 == szCnt);
        for (size_t t = 0; t < szCnt; ++t)
            pOut[t] = parentToChild(pcIn[t]);
    }

    //////////////////////////////////////////////////////////////////////////

    GridCellHelper::AABB2D::AABB2D(const Vector2f& p0, const Vector2f& p1)
    {
        if (p0.x() < p1.x())
        {
            m_fXMin = p0.x();
            m_fXMax = p1.x();
        }
        else
        {
            m_fXMin = p1.x();
            m_fXMax = p0.x();
        }

        if (p0.y() < p1.y())
        {
            m_fYMin = p0.y();
            m_fYMax = p1.y();
        }
        else
        {
            m_fYMin = p1.y();
            m_fYMax = p0.y();
        }
    }

    GridCellHelper::AABB2D::AABB2D(const Vector2f& p0, const Vector2f& p1, const Vector2f& p2, const Vector2f& p3)
    {
        sfGetMinMaxOf4(p0.x(), p1.x(), p2.x(), p3.x(), m_fXMin, m_fXMax);
        sfGetMinMaxOf4(p0.y(), p1.y(), p2.y(), p3.y(), m_fYMin, m_fYMax);
    }

    GridCellHelper::AABB2D::AABB2D(const CoordinateFrame& coordFrame, float fXMin, float fYMin, float fXMax, float fYMax)
    {
        const Vector2f p0 = coordFrame.childToParent(Vector2f(fXMax, fYMax));
        const Vector2f p1 = coordFrame.childToParent(Vector2f(fXMin, fYMax));
        const Vector2f p2 = coordFrame.childToParent(Vector2f(fXMin, fYMin));
        const Vector2f p3 = coordFrame.childToParent(Vector2f(fXMax, fYMin));
        sfGetMinMaxOf4(p0.x(), p1.x(), p2.x(), p3.x(), m_fXMin, m_fXMax);
        sfGetMinMaxOf4(p0.y(), p1.y(), p2.y(), p3.y(), m_fYMin, m_fYMax);
    }

    void GridCellHelper::AABB2D::calcVertexes(Vector2f destVertexes[C_VERTEX_COUNT_OF_RECTANGLE]) const
    {
        destVertexes[0] = Vector2f(m_fXMax, m_fYMax);
        destVertexes[1] = Vector2f(m_fXMin, m_fYMax);
        destVertexes[2] = Vector2f(m_fXMin, m_fYMin);
        destVertexes[3] = Vector2f(m_fXMax, m_fYMin);
    }

    void GridCellHelper::AABB2D::calcVertexes(std::vector<Vector2f>& destVertexes) const
    {
        destVertexes.clear();
        destVertexes.resize(C_VERTEX_COUNT_OF_RECTANGLE);
        destVertexes[0] = Vector2f(m_fXMax, m_fYMax);
        destVertexes[1] = Vector2f(m_fXMin, m_fYMax);
        destVertexes[2] = Vector2f(m_fXMin, m_fYMin);
        destVertexes[3] = Vector2f(m_fXMax, m_fYMin);
    }

    bool GridCellHelper::AABB2D::collidesWith(const AABB2D & tOthAabb) const
    {
        return !empty() && !tOthAabb.empty()
            && sfAreSegmentsContacted(m_fXMin, m_fXMax, tOthAabb.m_fXMin, tOthAabb.m_fXMax)
            && sfAreSegmentsContacted(m_fYMin, m_fYMax, tOthAabb.m_fYMin, tOthAabb.m_fYMax);
    }

    GridCellHelper::AABB2D& GridCellHelper::AABB2D::mergesWith(const AABB2D& tOth)
    {
        if (this != (&tOth) && !tOth.empty())
        {
            if (!empty())
            {
                m_fXMin = std::min<float>(m_fXMin, tOth.m_fXMin);
                m_fXMax = std::max<float>(m_fXMax, tOth.m_fXMax);
                m_fYMin = std::min<float>(m_fYMin, tOth.m_fYMin);
                m_fYMax = std::max<float>(m_fYMax, tOth.m_fYMax);
            }
            else
            {
                m_fXMin = tOth.m_fXMin;
                m_fXMax = tOth.m_fXMax;
                m_fYMin = tOth.m_fYMin;
                m_fYMax = tOth.m_fYMax;
            }
        }
        return *this;
    } 

    void GridCellHelper::AABB2D::intersectionOf(const rpos_common::core::RectangleF& dest)
    { 
        if (empty()) 
            return;

        float newXMin = std::max(dest.left(), getXMin());
        float newYMin = std::max(dest.top(), getYMin());

        float newXMax = std::min(dest.right(), getXMax());
        float newYMax = std::min(dest.bottom(), getYMax());

        if (newXMax <= newXMin || newYMax <= newYMin)
        {
            m_fXMin = 0;
            m_fYMin = 0;
            m_fXMax = 0;
            m_fYMax = 0;
        }
        else 
        {
            m_fXMin = newXMin;
            m_fYMin = newYMin;
            m_fXMax = newXMax;
            m_fYMax = newYMax;
        }
    }

    bool GridCellHelper::AABB2D::contains(const BoundingCircle& tCircle) const
    {
        const auto& cirCenter = tCircle.getCenterPoint();
        const float radius = tCircle.getRadius();
       
        return ((m_fXMax - cirCenter.x()) >= radius && (cirCenter.x() - m_fXMin) >= radius
                && (m_fYMax - cirCenter.y()) >= radius && (cirCenter.y() - m_fYMin) >= radius);
    }

    bool GridCellHelper::AABB2D::collidesWith(const BoundingCircle& tCircle) const
    {
        //find the closest point from a circle to a rectangle
        Vector2f aabbCenter((m_fXMin + m_fXMax) / 2.f, (m_fYMin + m_fYMax) / 2.f);
        Vector2f rectCenterToCirCenter = (tCircle.getCenterPoint() - aabbCenter).cwiseAbs();
        Vector2f rectHalfLength = Vector2f(m_fXMax, m_fYMax) - aabbCenter;
        Vector2f nearestRectPoint = rectCenterToCirCenter - rectHalfLength;
        nearestRectPoint.x() = std::max<float>(nearestRectPoint.x(), 0.f);
        nearestRectPoint.y() = std::max<float>(nearestRectPoint.y(), 0.f);
        return nearestRectPoint.dot(nearestRectPoint) <= tCircle.getRadius() * tCircle.getRadius();
    }

    GridCellHelper::BoundingCircle::BoundingCircle(const Vector2f& tCenter, float fRadius)
        : m_tCenterPoint(tCenter)
        , m_fRadius(fRadius)
        , m_fRadiusSquare(fRadius * fRadius)
        , m_tAabb(tCenter.x() - fRadius, tCenter.y() - fRadius, tCenter.x() + fRadius, tCenter.y() + fRadius)
    {
        assert(0.0f <= m_fRadius);
    }

    bool GridCellHelper::BoundingCircle ::contains(const Vector2f& tPoint) const
    {
        const Vector2f tDiff(tPoint - m_tCenterPoint);
        if (
            -m_fRadius <= tDiff.x() && tDiff.x() <= m_fRadius
            && -m_fRadius <= tDiff.y() && tDiff.y() <= m_fRadius
            )
        {
            const float fDistanceSquare = tDiff.squaredNorm();
            return fDistanceSquare <= m_fRadiusSquare;
        }
        return false;
    }

    bool GridCellHelper::BoundingCircle ::collidesWith(const AABB2D& tOthAabb) const
    {
        if (tOthAabb.empty())
            return false;

        const float fXLocalMin = tOthAabb.getXMin() - m_tCenterPoint.x();
        const float fXLocalMax = tOthAabb.getXMax() - m_tCenterPoint.x();
        const float fYLocalMin = tOthAabb.getYMin() - m_tCenterPoint.y();
        const float fYLocalMax = tOthAabb.getYMax() - m_tCenterPoint.y();

        const float fExtXLocalMin = fXLocalMin - m_fRadius;
        const float fExtXLocalMax = fXLocalMax + m_fRadius;
        const float fExtYLocalMin = fYLocalMin - m_fRadius;
        const float fExtYLocalMax = fYLocalMax + m_fRadius;

        // check if the center of circle is in the XY-extended AABB
        if (
            0.0f < fExtXLocalMin || fExtXLocalMax < 0.0f
            || 0.0f < fExtYLocalMin || fExtYLocalMax < 0.0f
            )
        {
            return false;
        }

        // check if the center of circle is in the X-extended AABB
        if (fYLocalMin <= 0.0f && 0.0f <= fYLocalMax)
        {
            return true;
        }

        // check if the center of circle is in the Y-extended AABB
        if (fXLocalMin <= 0.0f && 0.0f <= fXLocalMax)
        {
            return true;
        }

        // check the distance between the center of circle and vertexes of the other AABB
        const float fTmpX = std::min<float>(std::abs(fXLocalMin), std::abs(fXLocalMax));
        const float fTmpY = std::min<float>(std::abs(fYLocalMin), std::abs(fYLocalMax));
        const float fTmpDistanceSqaure = (fTmpX * fTmpX + fTmpY * fTmpY);
        return (fTmpDistanceSqaure <= m_fRadiusSquare);
    }

    void GridCellHelper::OBB2D::reset()
    {
        m_tCoordinateFrame.reset();
        m_tLocalAabb = AABB2D();
        m_tVertexes[0] = m_tVertexes[1] = m_tVertexes[2] = m_tVertexes[3] = Vector2f(0.0f, 0.0f);
        m_tAabb = AABB2D();
        m_length = 0.0f;
        m_width = 0.0f;
    }

    void GridCellHelper::OBB2D::reset(const Vector2f& tStartPoint, const Vector2f& tEndPoint, float fHalfWidth)
    {
        const Vector2f tDiff = tEndPoint - tStartPoint;
        const float fTheta = std::atan2(tDiff.y(), tDiff.x());
        const float fDistance = tDiff.norm();

        const Vector2f tCenterPoint = ((tStartPoint + tEndPoint) / 2);
        m_tCoordinateFrame.reset(tCenterPoint, fTheta);
        
        const float fXHalfLen = (fDistance / 2);
        assert(0.0f <= fXHalfLen);
        assert(0.0f <= fHalfWidth);
        m_tLocalAabb = AABB2D(-fXHalfLen, -fHalfWidth, fXHalfLen, fHalfWidth);
        m_length = fDistance;
        m_width = fHalfWidth * 2.0f;

        doCalcWorldDataByLocalData_();
    }

    void GridCellHelper::OBB2D::reset(const CoordinateFrame& coordFrame, float fXMin, float fYMin, float fXMax, float fYMax)
    {
        m_tCoordinateFrame = coordFrame;
        m_tLocalAabb = AABB2D(fXMin, fYMin, fXMax, fYMax);
        m_length = fXMax - fXMin;
        m_width = fYMax - fYMin;

        doCalcWorldDataByLocalData_();
    }

    void  GridCellHelper::OBB2D::rotate(float yawDiff)
    {
        m_tCoordinateFrame.reset(m_tCoordinateFrame.getPosition().x(), m_tCoordinateFrame.getPosition().y(), m_tCoordinateFrame.getTheta() + yawDiff);
        doCalcWorldDataByLocalData_();
    }

    void GridCellHelper::OBB2D::doCalcWorldDataByLocalData_()
    {
        m_tLocalAabb.calcVertexes(m_tVertexes);
        m_tCoordinateFrame.inplaceChildToParent(m_tVertexes, C_VERTEX_COUNT_OF_RECTANGLE);
        m_tAabb = AABB2D(m_tVertexes[0], m_tVertexes[1], m_tVertexes[2], m_tVertexes[3]);
    }

    void GridCellHelper::OBB2D::getVertexes(std::vector<Vector2f>& vertexes) const
    {
        assert(NULL != m_tVertexes);
        vertexes.clear();
        vertexes.reserve(C_VERTEX_COUNT_OF_RECTANGLE);
        for (size_t t = 0; t < C_VERTEX_COUNT_OF_RECTANGLE; ++t)
            vertexes.push_back(m_tVertexes[t]);
    }

    rpos_common::core::Vector2f GridCellHelper::OBB2D::getCenterPoint() const
    {
        assert(NULL != m_tVertexes);
        Vector2f centerPoint(0.f, 0.f);
        for (size_t t = 0; t < C_VERTEX_COUNT_OF_RECTANGLE; ++t)
        {
            centerPoint += m_tVertexes[t];
        }
        return (centerPoint / C_VERTEX_COUNT_OF_RECTANGLE);
    }

    bool GridCellHelper::OBB2D::contains(const Vector2f& tPoint) const
    {
        if (m_tAabb.contains(tPoint))
        {
            const Vector2f tLocal = worldToLocal(tPoint);
            return m_tLocalAabb.contains(tLocal);
        }
        return false;
    }

    bool GridCellHelper::OBB2D::localContains(const Vector2f& tPoint) const
    {
        return m_tAabb.contains(tPoint);
    }

    bool GridCellHelper::OBB2D::collidesWith(const AABB2D& tOthAabb) const
    {
        if (empty() || tOthAabb.empty())
            return false;

        // Separating Axis, x of other AABB
        if (!sfAreSegmentsContacted(m_tAabb.getXMin(), m_tAabb.getXMax(), tOthAabb.getXMin(), tOthAabb.getXMax()))
            return false;
        // Separating Axis, y of other AABB
        if (!sfAreSegmentsContacted(m_tAabb.getYMin(), m_tAabb.getYMax(), tOthAabb.getYMin(), tOthAabb.getYMax()))
            return false;

        Vector2f tOthVertexesLocal[C_VERTEX_COUNT_OF_RECTANGLE];
        tOthAabb.calcVertexes(tOthVertexesLocal);
        m_tCoordinateFrame.inplaceParentToChild(tOthVertexesLocal, C_VERTEX_COUNT_OF_RECTANGLE);

        float fTmpMin;
        float fTmpMax;
        // Separating Axis, x of this OBB
        sfGetMinMaxOf4(tOthVertexesLocal[0].x(), tOthVertexesLocal[1].x(), tOthVertexesLocal[2].x(), tOthVertexesLocal[3].x(), fTmpMin, fTmpMax);
        if (!sfAreSegmentsContacted(m_tLocalAabb.getXMin(), m_tLocalAabb.getXMax(), fTmpMin, fTmpMax))
            return false;
        // Separating Axis, y of this OBB
        sfGetMinMaxOf4(tOthVertexesLocal[0].y(), tOthVertexesLocal[1].y(), tOthVertexesLocal[2].y(), tOthVertexesLocal[3].y(), fTmpMin, fTmpMax);
        if (!sfAreSegmentsContacted(m_tLocalAabb.getYMin(), m_tLocalAabb.getYMax(), fTmpMin, fTmpMax))
            return false;

        return true;
    }

    bool GridCellHelper::OBB2D::collidesWith(const OBB2D& tOthObb) const
    {
        if (!m_tAabb.collidesWith(tOthObb.m_tAabb))
            return false;
        if (empty() || tOthObb.empty())
            return false;

        Vector2f tmpVertexesLocal[C_VERTEX_COUNT_OF_RECTANGLE];
        m_tCoordinateFrame.transformParentToChild(tOthObb.m_tVertexes, C_VERTEX_COUNT_OF_RECTANGLE, tmpVertexesLocal);
        float fTmpMin;
        float fTmpMax;
        // Separating Axis, x of this OBB
        sfGetMinMaxOf4(tmpVertexesLocal[0].x(), tmpVertexesLocal[1].x(), tmpVertexesLocal[2].x(), tmpVertexesLocal[3].x(), fTmpMin, fTmpMax);
        if (!sfAreSegmentsContacted(m_tLocalAabb.getXMin(), m_tLocalAabb.getXMax(), fTmpMin, fTmpMax))
            return false;
        // Separating Axis, y of this OBB
        sfGetMinMaxOf4(tmpVertexesLocal[0].y(), tmpVertexesLocal[1].y(), tmpVertexesLocal[2].y(), tmpVertexesLocal[3].y(), fTmpMin, fTmpMax);
        if (!sfAreSegmentsContacted(m_tLocalAabb.getYMin(), m_tLocalAabb.getYMax(), fTmpMin, fTmpMax))
            return false;

        tOthObb.m_tCoordinateFrame.transformParentToChild(m_tVertexes, C_VERTEX_COUNT_OF_RECTANGLE, tmpVertexesLocal);
        // Separating Axis, x of other OBB
        sfGetMinMaxOf4(tmpVertexesLocal[0].x(), tmpVertexesLocal[1].x(), tmpVertexesLocal[2].x(), tmpVertexesLocal[3].x(), fTmpMin, fTmpMax);
        if (!sfAreSegmentsContacted(tOthObb.m_tLocalAabb.getXMin(), tOthObb.m_tLocalAabb.getXMax(), fTmpMin, fTmpMax))
            return false;
        // Separating Axis, y of other OBB
        sfGetMinMaxOf4(tmpVertexesLocal[0].y(), tmpVertexesLocal[1].y(), tmpVertexesLocal[2].y(), tmpVertexesLocal[3].y(), fTmpMin, fTmpMax);
        if (!sfAreSegmentsContacted(tOthObb.m_tLocalAabb.getYMin(), tOthObb.m_tLocalAabb.getYMax(), fTmpMin, fTmpMax))
            return false;

        return true;
    }

    bool GridCellHelper::OBB2D::contains(const OBB2D& tOthObb) const
    {
        if (!m_tAabb.collidesWith(tOthObb.m_tAabb))
            return false;
        if (empty() || tOthObb.empty())
            return false;

        std::vector<Vector2f> tOthVertexes;
        tOthObb.getVertexes(tOthVertexes);
        for (auto it = tOthVertexes.begin(); it != tOthVertexes.end(); it++)
        {
            if (!contains(*it))
                return false;
        }
        return true;
    }

    bool GridCellHelper::OBB2D::contains(const BoundingCircle& tCircle) const
    {
        if(!contains(tCircle.getCenterPoint()))
            return false;
        Vector2f tCenterPoint = m_tCoordinateFrame.parentToChild(tCircle.getCenterPoint());
        auto translatedCircle = BoundingCircle(tCenterPoint, tCircle.getRadius());
        return m_tLocalAabb.contains(translatedCircle);
    }

    bool GridCellHelper::OBB2D::collidesWith(const BoundingCircle& tCircle) const
    {
        Vector2f tCenterPoint = m_tCoordinateFrame.parentToChild(tCircle.getCenterPoint());
        auto translatedCircle = BoundingCircle(tCenterPoint, tCircle.getRadius());
        return m_tLocalAabb.collidesWith(translatedCircle);
    }

    //////////////////////////////////////////////////////////////////////////

    GridCellHelper::OrientedCircleEndsBoundingArea::OrientedCircleEndsBoundingArea(const Vector2f& tStartPoint, const Vector2f& tEndPoint, float fRadius)
        : m_tBoundingCircleOfStartPoint(tStartPoint, fRadius)
        , m_tBoundingCircleOfEndPoint(tEndPoint, fRadius)
        , m_tObbOfCenterRect(tStartPoint, tEndPoint, fRadius)
    {
        m_tAabb = m_tBoundingCircleOfStartPoint.getAABB();
        m_tAabb.mergesWith(m_tBoundingCircleOfEndPoint.getAABB());
        m_tAabb.mergesWith(m_tObbOfCenterRect.getAABB());
    }

    bool GridCellHelper::OrientedCircleEndsBoundingArea::contains(const Vector2f& tPoint) const
    {
        return m_tBoundingCircleOfStartPoint.contains(tPoint)
            || m_tBoundingCircleOfEndPoint.contains(tPoint)
            || m_tObbOfCenterRect.contains(tPoint);
    }

    bool GridCellHelper::OrientedCircleEndsBoundingArea::collidesWith(const AABB2D& tOthAabb) const
    {
        return m_tBoundingCircleOfStartPoint.collidesWith(tOthAabb)
            || m_tBoundingCircleOfEndPoint.collidesWith(tOthAabb)
            || m_tObbOfCenterRect.collidesWith(tOthAabb);
    }

    //////////////////////////////////////////////////////////////////////////

    GridCellHelper::GridCellHelper()
        : m_fResolution(0.0f)
        , m_tMinPoint(0.0f, 0.0f)
        , m_tDimesion(0, 0)
        , m_fHalfResolution(0.0f)
        , m_tMaxPoint(0.0f, 0.0f)
    {
        //
    }

    GridCellHelper::GridCellHelper(float fResolustion, float fXMin, float fYMin, int iDimX, int iDimY)
    {
        reset(fResolustion, fXMin, fYMin, iDimX, iDimY);
    }

    void GridCellHelper::reset(float fResolustion, float fXMin, float fYMin, int iDimX, int iDimY)
    {
        m_fResolution = fResolustion;
        assert(FLT_EPSILON < m_fResolution);
        m_tMinPoint = Vector2f(fXMin, fYMin);
        m_tDimesion = Vector2i(iDimX, iDimY);
        assert(0 <= m_tDimesion.x());
        assert(0 <= m_tDimesion.y());

        m_fHalfResolution = (m_fResolution / 2);
        m_tMaxPoint = Vector2f((m_tMinPoint.x() + m_fResolution * m_tDimesion.x()), (m_tMinPoint.y() + m_fResolution * m_tDimesion.y()));
    }

    void GridCellHelper::calcPotentialCellIndexRange(const AABB2D& tOthAabb, int& rCellXMin, int& rCellYMin, int& rCellXMax, int& rCellYMax) const
    {
        if (!tOthAabb.empty())
        {
            const Vector2i tPotentialCellIndexMin = calcCellIndex(tOthAabb.getXMin(), tOthAabb.getYMin());
            rCellXMin = std::max<int>(tPotentialCellIndexMin.x(), 0);
            rCellYMin = std::max<int>(tPotentialCellIndexMin.y(), 0);
            const Vector2i tPotentialCellIndexMax = calcCellIndex(tOthAabb.getXMax(), tOthAabb.getYMax());
            rCellXMax = std::min<int>(tPotentialCellIndexMax.x(), m_tDimesion.x() - 1);
            rCellYMax = std::min<int>(tPotentialCellIndexMax.y(), m_tDimesion.y() - 1);
        }
        else
        {
            rCellXMin = 0;
            rCellYMin = 0;
            rCellXMax = -1;
            rCellYMax = -1;
        }
    }

    void GridCellHelper::calcCollidedCells(const I2DBoundingArea& boundingArea, RangeOnY_Vector& result) const
    {
        result.clear();
        
        Vector2i tPotentialCellIndexMin, tPotentialCellIndexMax;
        calcPotentialCellIndexRange(boundingArea.getAABB(), tPotentialCellIndexMin.x(), tPotentialCellIndexMin.y(), tPotentialCellIndexMax.x(), tPotentialCellIndexMax.y());

        if (tPotentialCellIndexMax.x() < tPotentialCellIndexMin.x())
            return;
        for (int y = tPotentialCellIndexMin.y(); y <= tPotentialCellIndexMax.y(); ++y)
        {
            AABB2D tTmpCellAabb;
            tTmpCellAabb.setYMin(m_tMinPoint.y() + y * m_fResolution);
            tTmpCellAabb.setYMax(tTmpCellAabb.getYMin() + m_fResolution);

            int iXBegin = tPotentialCellIndexMin.x();
            tTmpCellAabb.setXMin(m_tMinPoint.x() + iXBegin * m_fResolution);
            tTmpCellAabb.setXMax(tTmpCellAabb.getXMin() + m_fResolution);
            for (; iXBegin <= tPotentialCellIndexMax.x(); ++iXBegin)
            {
                if (boundingArea.collidesWith(tTmpCellAabb))
                {
                    break;
                }
                tTmpCellAabb.setXMin(tTmpCellAabb.getXMax());
                tTmpCellAabb.setXMax(tTmpCellAabb.getXMin() + m_fResolution);
            }

            if (tPotentialCellIndexMax.x() < iXBegin)
                continue;
            int iXEnd = iXBegin + 1;
            tTmpCellAabb.setXMin(tTmpCellAabb.getXMax());
            tTmpCellAabb.setXMax(tTmpCellAabb.getXMin() + m_fResolution);
            for (; iXEnd <= tPotentialCellIndexMax.x(); ++iXEnd)
            {
                if (boundingArea.collidesWith(tTmpCellAabb))
                {
                    tTmpCellAabb.setXMin(tTmpCellAabb.getXMax());
                    tTmpCellAabb.setXMax(tTmpCellAabb.getXMin() + m_fResolution);
                }
                else
                {
                    break;
                }
            }

            assert(iXBegin < iXEnd);
            assert(iXEnd <= tPotentialCellIndexMax.x() + 1);
            result.push_back(RangeOnY(y, iXBegin, iXEnd));
        }
    }

    void GridCellHelper::calcCenterOfCellCollidedCells(const I2DBoundingArea& boundingArea, RangeOnY_Vector& result) const
    {
        result.clear();

        Vector2i tPotentialCellIndexMin, tPotentialCellIndexMax;
        calcPotentialCellIndexRange(boundingArea.getAABB(), tPotentialCellIndexMin.x(), tPotentialCellIndexMin.y(), tPotentialCellIndexMax.x(), tPotentialCellIndexMax.y());

        if (tPotentialCellIndexMax.x() < tPotentialCellIndexMin.x())
            return;
        for (int y = tPotentialCellIndexMin.y(); y <= tPotentialCellIndexMax.y(); ++y)
        {
            int iXBegin = tPotentialCellIndexMin.x();
            Vector2f tTmpCenterOfCell = calcCenterOfCell(iXBegin, y);
            for (; iXBegin <= tPotentialCellIndexMax.x(); ++iXBegin)
            {
                if (boundingArea.contains(tTmpCenterOfCell))
                {
                    break;
                }
                tTmpCenterOfCell.x() += m_fResolution;
            }

            if (tPotentialCellIndexMax.x() < iXBegin)
                continue;
            int iXEnd = iXBegin + 1;
            tTmpCenterOfCell.x() += m_fResolution;
            for (; iXEnd <= tPotentialCellIndexMax.x(); ++iXEnd)
            {
                if (boundingArea.contains(tTmpCenterOfCell))
                {
                    tTmpCenterOfCell.x() += m_fResolution;
                }
                else
                {
                    break;
                }
            }

            assert(iXBegin < iXEnd);
            assert(iXEnd <= tPotentialCellIndexMax.x() + 1);
            result.push_back(RangeOnY(y, iXBegin, iXEnd));
        }
    }

	void GridCellHelper::calcLineCollidedCells(const Vector2f& tStartPoint, const Vector2f& tEndPoint, RangeOnY_Vector& result) const
	{
		result.clear();
		Vector2i tStartPointIndex = calcCellIndex(tStartPoint);
		Vector2i tEndPointIndex = calcCellIndex(tEndPoint);
		assert(tStartPointIndex.x() >= 0 && tStartPointIndex.x() <= m_tDimesion.x() - 1);
		assert(tEndPointIndex.x() >= 0 && tEndPointIndex.x() <= m_tDimesion.x() - 1);
		assert(tStartPointIndex.y() >= 0 && tStartPointIndex.y() <= m_tDimesion.y() - 1);
		assert(tEndPointIndex.y() >= 0 && tEndPointIndex.y() <= m_tDimesion.y() - 1);
		int iXBegin, iXEnd;
		int iCurrY, iCurrX, iLastY, iLastX;
		if (tStartPointIndex.y() == tEndPointIndex.y())
		{
			iXBegin = std::min<int>(tStartPointIndex.x(), tEndPointIndex.x());
			iXEnd = std::max<int>(tStartPointIndex.x(), tEndPointIndex.x());
			iXEnd = iXEnd + 1;
			assert(iXBegin < iXEnd);
			assert(iXEnd <= m_tDimesion.x());
			result.push_back(RangeOnY(tStartPointIndex.y(), iXBegin, iXEnd));
			return;
		}
		else if (tStartPointIndex.y() < tEndPointIndex.y())
		{
			iCurrY = tStartPointIndex.y();
			iCurrX = tStartPointIndex.x();
			iLastY = tEndPointIndex.y();
			iLastX = tEndPointIndex.x();
		}
		else
		{
			iCurrY = tEndPointIndex.y();
			iCurrX = tEndPointIndex.x();
			iLastY = tStartPointIndex.y();
			iLastX = tStartPointIndex.x();
		}
		float k = (tStartPoint.x() - tEndPoint.x()) / (tStartPoint.y() - tEndPoint.y());
		float b = tStartPoint.x() - k * tStartPoint.y();
		while (iCurrY <= iLastY)
		{
			float fNextY = static_cast<float>((iCurrY + 1) * m_fResolution + m_tMinPoint.y());
			float fNextX = k * fNextY + b;
			int iTmpX = static_cast<int>((fNextX - m_tMinPoint.x()) / m_fResolution);
			int iNextX = (iCurrY == iLastY) ? iLastX : iTmpX;
			iXBegin = std::min<int>(iCurrX, iNextX);
			iXEnd = std::max<int>(iCurrX, iNextX);
			iXEnd = iXEnd + 1;
			assert(iXBegin < iXEnd);
			assert(iXEnd <= m_tDimesion.x());
			result.push_back(RangeOnY(iCurrY, iXBegin, iXEnd));
			iCurrY++;
			iCurrX = iNextX;
		}
	}

    void GridCellHelper::divideOBB2DIntoSubsets(const OBB2D& area, int widthCnt, int heightCnt, std::vector<OBB2D>& subsets) const
    {
        subsets.clear();
        if (area.empty())
            return;

        if (widthCnt <= 0 || heightCnt <= 0 || (widthCnt == 1 && heightCnt == 1))
        {
            subsets.push_back(area);
            return;
        }
        std::vector<Vector2f> vertexes;
        area.getLocalAABB().calcVertexes(vertexes);
        if (vertexes.size() != C_VERTEX_COUNT_OF_RECTANGLE)
        {
            return;
        }

        float local_xmax = area.getLocalAABB().getXMax();
        float local_xmin = area.getLocalAABB().getXMin();
        float local_ymax = area.getLocalAABB().getYMax();
        float local_ymin = area.getLocalAABB().getYMin();
        float width = local_ymax - local_ymin;
        float height = local_xmax - local_xmin;

        if (widthCnt == 1)
        {
            subsets.reserve(heightCnt);
            float step = height / heightCnt;
            float fixed_y = (local_ymax + local_ymin) / 2;
            float x_start = local_xmax - step;
            Vector2f repeat_start(local_xmax, fixed_y);
            Vector2f repeat_end(x_start, fixed_y);
            subsets.push_back(OBB2D(area.localToWorld(repeat_start), area.localToWorld(repeat_end), width / 2));
            int cnt = 1;
            while (cnt < heightCnt)
            {
                repeat_start = repeat_end;
                x_start -= step;
                repeat_end = Vector2f(x_start, fixed_y);
                subsets.push_back(OBB2D(area.localToWorld(repeat_start), area.localToWorld(repeat_end), width / 2));
                cnt++;
            }
            return;
        }
        else if (heightCnt == 1)
        {
            subsets.reserve(widthCnt);
            float step = width / widthCnt;
            float fixed_x = (local_xmax + local_xmin) / 2;
            float y_start = local_ymax - step;
            Vector2f repeat_start(fixed_x, local_ymax);
            Vector2f repeat_end(fixed_x, y_start);
            subsets.push_back(OBB2D(area.localToWorld(repeat_start), area.localToWorld(repeat_end), height / 2));
            int cnt = 1;
            while (cnt < widthCnt)
            {
                repeat_start = repeat_end;
                y_start -= step;
                repeat_end = Vector2f(fixed_x, y_start);
                subsets.push_back(OBB2D(area.localToWorld(repeat_start), area.localToWorld(repeat_end), height / 2));
                cnt++;
            }
            return;
        }
        else
        {
            subsets.reserve(widthCnt * heightCnt);
            float step_in_y = width / widthCnt;
            float step_in_x = height / heightCnt;
            float sub_rect_half_width = step_in_y / 2;
            float y_start = local_ymax - step_in_y / 2;
            float x_start;
            for (int i = 0; i < widthCnt; i++)
            {
                float fixed_y = y_start - i * step_in_y;
                x_start = local_xmax - step_in_x;
                Vector2f repeat_start(local_xmax, fixed_y);
                Vector2f repeat_end(x_start, fixed_y);
                subsets.push_back(OBB2D(area.localToWorld(repeat_start), area.localToWorld(repeat_end), sub_rect_half_width));
                int cnt = 1;
                while (cnt < heightCnt)
                {
                    repeat_start = repeat_end;
                    x_start -= step_in_x;
                    repeat_end = Vector2f(x_start, fixed_y);
                    subsets.push_back(OBB2D(area.localToWorld(repeat_start), area.localToWorld(repeat_end), sub_rect_half_width));
                    cnt++;
                }
            }
            return;
        }
    }

    void GridCellHelper::divideOBB2DIntoSubsets(const OBB2D& area, int widthCnt, int heightCnt, std::vector<rpos_common::core::ORectangleF>& subsets) const
    {
        subsets.clear();
        if (area.empty())
            return;
       
        std::vector<Vector2f> vertexes;
        area.getLocalAABB().calcVertexes(vertexes);
        if (vertexes.size() != C_VERTEX_COUNT_OF_RECTANGLE)
        {
            return;
        }
        float local_xmax = area.getLocalAABB().getXMax();
        float local_xmin = area.getLocalAABB().getXMin();
        float local_ymax = area.getLocalAABB().getYMax();
        float local_ymin = area.getLocalAABB().getYMin();
        float width = local_ymax - local_ymin;
        float height = local_xmax - local_xmin;

        if (widthCnt <= 0 || heightCnt <= 0 || (widthCnt == 1 && heightCnt == 1))
        {
            float fixed_y = (local_ymax + local_ymin) / 2;
            Vector2f repeat_start(local_xmax, fixed_y);
            Vector2f repeat_end(local_xmin, fixed_y);
            subsets.push_back(rpos_common::core::ORectangleF(area.localToWorld(repeat_start), area.localToWorld(repeat_end), width / 2));
            return;
        }

        if (widthCnt == 1)
        {
            subsets.reserve(heightCnt);
            float step = height / heightCnt;
            float fixed_y = (local_ymax + local_ymin) / 2;
            float x_start = local_xmax - step;
            Vector2f repeat_start(local_xmax, fixed_y);
            Vector2f repeat_end(x_start, fixed_y);
            subsets.push_back(rpos_common::core::ORectangleF(area.localToWorld(repeat_start), area.localToWorld(repeat_end), width / 2));
            int cnt = 1;
            while (cnt < heightCnt)
            {
                repeat_start = repeat_end;
                x_start -= step;
                repeat_end = Vector2f(x_start, fixed_y);
                subsets.push_back(rpos_common::core::ORectangleF(area.localToWorld(repeat_start), area.localToWorld(repeat_end), width / 2));
                cnt++;
            }
            return;
        }
        else if (heightCnt == 1)
        {
            subsets.reserve(widthCnt);
            float step = width / widthCnt;                    
            float fixed_x = (local_xmax + local_xmin) / 2;
            float y_start = local_ymax - step;
            Vector2f repeat_start(fixed_x, local_ymax);
            Vector2f repeat_end(fixed_x, y_start);
            subsets.push_back(rpos_common::core::ORectangleF(area.localToWorld(repeat_start), area.localToWorld(repeat_end), height / 2));
            int cnt = 1;
            while (cnt < widthCnt)
            {
                repeat_start = repeat_end;
                y_start -= step;
                repeat_end = Vector2f(fixed_x, y_start);
                subsets.push_back(rpos_common::core::ORectangleF(area.localToWorld(repeat_start), area.localToWorld(repeat_end), height / 2));
                cnt++;
            }
            return;
        }
        else
        {        
            subsets.reserve(widthCnt * heightCnt);
            float step_in_y = width / widthCnt;
            float step_in_x = height / heightCnt;
            float sub_rect_half_width = step_in_y / 2;
            float y_start = local_ymax - step_in_y / 2;
            float x_start;
            for (int i = 0; i < widthCnt; i++)
            {
                float fixed_y = y_start - i * step_in_y;
                x_start = local_xmax - step_in_x;
                Vector2f repeat_start(local_xmax, fixed_y);
                Vector2f repeat_end(x_start, fixed_y);
                subsets.push_back(rpos_common::core::ORectangleF(area.localToWorld(repeat_start), area.localToWorld(repeat_end), sub_rect_half_width));
                int cnt = 1;
                while (cnt < heightCnt)
                {
                    repeat_start = repeat_end;
                    x_start -= step_in_x;
                    repeat_end = Vector2f(x_start, fixed_y);
                    subsets.push_back(rpos_common::core::ORectangleF(area.localToWorld(repeat_start), area.localToWorld(repeat_end), sub_rect_half_width));
                    cnt++;
                }
            }
            return;
        }
    }
}}
