#include <system/target_info.h>
#include <system/string_utils.h>
#include <stdio.h>
#include <stdlib.h>
#include <algorithm>
#include <limits>
#include <cassert>
#include <clocale>

#ifdef RPOS_TARGET_WINDOWS
    #ifndef NOMINMAX
        #define NOMINMAX
    #endif
    #include <Windows.h>
#endif // RPOS_TARGET_WINDOWS

#ifdef _WIN32
#    define snprintf sprintf_s
#endif

using namespace std;

namespace rpos_common { namespace system {
    static char hexCharArray[] = "0123456789abcdef";

    string to_string(std::int32_t value)
    {
        char buffer[32];
        snprintf(buffer, 32, "%d", value);

        return buffer;
    }

    string to_string(std::int64_t value)
    {
        char buffer[64];
        snprintf(buffer, 64, "%ld", value);

        return buffer;
    }

    string to_string(std::uint32_t value)
    {
        char buffer[32];
        snprintf(buffer, 32, "%u", value);

        return buffer;
    }

    string to_string(std::uint64_t value)
    {
        char buffer[64];
        snprintf(buffer, 64, "%lu", value);

        return buffer;
    }

    string to_string(float value)
    {
        return to_string(double(value));
    }

    string to_string(double value)
    {
        char buffer[64];
        snprintf(buffer, 64, "%f", value);

        return buffer;
    }

    std::string to_hex_string(const std::uint8_t* buffer, size_t size, bool upperCase)
    {
        char* output = (char*)malloc(size * 2 + 1);

        if (!output)
            throw bad_alloc();

        for (size_t i = 0; i < size; i++)
        {
            snprintf(output + i * 2, 3, upperCase ? "%02X" : "%02x", buffer[i]);
        }
        output[size*2] = '\0';

        std::string strOutput(output);
        free(output);

        return strOutput;;
    }

    std::string to_uuid_string(const std::uint8_t* buffer, bool upperCase /*= false*/)
    {
        char output[33] = { '\0' };
        for (size_t i = 0; i < 16; i++)
        {
            snprintf(output + 2 * i, 3, upperCase ? "%02X" : "%02x", buffer[i]);
        }
        std::string result(output);
        result.insert(20, "-");
        result.insert(16, "-");
        result.insert(12, "-");
        result.insert(8, "-");
        return result;
    }

    bool try_parse(const std::string& s, int& v, int base)
    {
        long long lv;

        if (!try_parse(s, lv, base))
            return false;

        v = (int)lv;

        return true;
    }

    bool try_parse(const std::string& s, long& v, int base)
    {
        long long lv;

        if (!try_parse(s, lv, base))
            return false;

        v = (long)lv;

        return true;
    }

    bool try_parse(const std::string& s, long long& v, int base)
    {
        const char* begin = s.c_str();
        const char* expectedEnd = begin + s.size();
        char* end;

#ifdef _WIN32
        v = _strtoi64(begin, &end, base);
#else
        v = strtol(begin, &end, base);
#endif

        return end == expectedEnd;
    }
    
    bool try_parse(const std::string& s, std::uint64_t& v, int base)
    {
        const char* begin = s.c_str();
        const char* expectedEnd = begin + s.size();
        char* end;

#ifdef _WIN32
        v = _strtoui64(begin, &end, base);
#else
        v = strtoul(begin, &end, base);
#endif

        return end == expectedEnd;
    }

    bool try_parse(const std::string& s, float& v)
    {
        const char* begin = s.c_str();
        const char* expectedEnd = begin + s.size();
        char* end;

        v = (float)strtod(begin, &end);

        return end == expectedEnd;
    }

    bool try_parse(const std::string& s, double& v)
    {
        const char* begin = s.c_str();
        const char* expectedEnd = begin + s.size();
        char* end;

        v = strtod(begin, &end);

        return end == expectedEnd;
    }

    bool try_parse_uuid(const std::string& s, std::vector<std::uint8_t>& data)
    { 
        std::string tmpstr = s;
        tmpstr.erase(std::remove(tmpstr.begin(), tmpstr.end(), '-'), tmpstr.end());
        if (tmpstr.length() != 32)
        {
            return false;
        }
        std::transform(tmpstr.begin(), tmpstr.end(), tmpstr.begin(), ::tolower);
        data.resize(16);
        const char* begin = tmpstr.c_str();
        char tmp[3] = { '\0' };
        for (int i = 0; i < 16; i++, begin+=2)
        {     
            tmp[0] = begin[0];
            tmp[1] = begin[1];
            const char* expectedEnd = &tmp[2];
            char* end; 
#ifdef _WIN32
            data[i] = (std::uint8_t)_strtoi64(tmp, &end, 16);
#else
            data[i] = (std::uint8_t)strtol(tmp, &end, 16);
#endif
            if (end != expectedEnd)
            {
                return false;
            }
        }
        return true;
    }

    std::string trim_left(const std::string& that)
    {
        size_t p = 0;
        for (; p < that.size(); p++)
        {
            if (!isspace((unsigned char)that[p]))
                break;
        }

        return that.substr(p);
    }

    std::string trim_right(const std::string& that)
    {
        int p = (int)(that.size() - 1);
        for (; p >= 0; p--)
        {
            if (!isspace((unsigned char)that[p]))
                break;
        }

        return that.substr(0, p + 1);
    }

    std::string trim(const std::string& that)
    {
        return trim_right(trim_left(that));
    }

    std::string replace(const std::string& s, const std::string& match, const std::string& r)
    {
        if (match.empty() || s.size() < match.size())
            return s;

        if (match.size() == 1)
            return replace(s, match[0], r);

        string output;
        for (size_t i = 0; i < s.size();)
        {
            auto next_match = find(s, match, i);

            if (next_match == string::npos)
            {
                // no match
                return i ? output + s.substr(i) : s;
            }

            output += s.substr(i, next_match - i);
            if (!r.empty())
                output += r;
            i = next_match + match.size();
        }

        return output;
    }

    std::string replace(const std::string& s, char match, const std::string& r)
    {
        if (s.empty())
            return s;

        if (r.size() == 1)
            return replace(s, match, r[0]);

        string output;
        for (size_t i = 0; i < s.size(); i++)
        {
            if (match == s[i])
            {
                if (!r.empty())
                    output += r;
            }
            else
            {
                output.push_back(s[i]);
            }
        }

        return output;
    }

    std::string replace(const std::string& s, char match, char replace)
    {
        string output = s;

        for (size_t i = 0; i < s.size(); i++)
        {
            if (output[i] == match)
                output[i] = replace;
        }

        return output;
    }

    std::string remove(const std::string& s, const std::string& match)
    {
        return replace(s, match, string());
    }

    std::string remove(const std::string& s, char match)
    {
        return replace(s, match, string());
    }

    bool is_whitespace(const std::string& that)
    {
        for (size_t i = 0; i < that.size(); i++)
        {
            if (!isspace((unsigned char)that[i]))
                return false;
        }

        return true;
    }

    bool starts_with(const std::string& text, const std::string& match)
    {
        if (text.size() < match.size())
        {
            return false;
        }
        else if (text.size() == match.size())
        {
            return text == match;
        }
        else
        {
            return text.substr(0, match.size()) == match;
        }
    }

    bool starts_with(const std::string& text, char match)
    {
        if (text.empty())
            return false;
        else
            return text[0] == match;
    }

    bool ends_with(const std::string& text, const std::string& match)
    {
        if (text.size() < match.size())
        {
            return false;
        }
        else if (text.size() == match.size())
        {
            return text == match;
        }
        else
        {
            return text.substr(text.size() - match.size(), match.size()) == match;
        }
    }

    bool ends_with(const std::string& text, char match)
    {
        if (text.empty())
            return false;
        else
            return text[text.size() - 1] == match;
    }

    bool contains(const std::string& text, const std::string& match)
    {
        return match.empty() || find(text, match) != string::npos;
    }

    bool contains(const std::string& text, char match)
    {
        return text.find(match) != string::npos;
    }

    std::string::size_type find(const std::string& text, const std::string& match, std::string::size_type start)
    {
        if (text.size() < match.size())
            return string::npos;

        if (match.empty())
            return string::npos;

        if (match.size() == 1)
            return find(text, match[0], start);

        for (size_t p = start; p < text.size();)
        {
            size_t match_start = text.find(match[0], p);

            if (match_start == string::npos || (match_start + match.size()) > text.size())
            {
                return string::npos;
            }

            size_t match_length = 1;
            for (size_t i = 1; i < match.size(); i++)
            {
                if (text[match_start + i] != match[i])
                    break;
                match_length++;
            }

            if (match_length == match.size())
                return match_start;

            p = match_start + match_length;
        }

        return string::npos;
    }

    std::string::size_type find(const std::string& text, char match, std::string::size_type start)
    {
        return text.find(match, start);
    }

    std::vector<std::string> split(const std::string& that, char splitter, size_t max_parts, bool merge_continuous_splitters)
    {
        vector<string> output;

        if (that.empty())
            return output;

        for (size_t p = 0; p < that.size();)
        {
            if (output.size() + 1 == max_parts)
            {
                output.push_back(that.substr(p));
                return output;
            }

            size_t next_splitter = that.find(splitter, p);
            size_t end_splitter = next_splitter;

            if (end_splitter != string::npos && merge_continuous_splitters)
            {
                while (end_splitter < that.size() - 1 && that[end_splitter + 1] == splitter)
                    end_splitter++;
            }

            if (next_splitter == string::npos)
            {
                output.push_back(that.substr(p));
                return output;
            }
            else
            {
                output.push_back(that.substr(p, next_splitter - p));
                p = end_splitter + 1;
            }
        }

        return output;
    }

    std::vector<std::string> split_trim(const std::string& that, char splitter, size_t max_parts, bool merge_continuous_splitters)
    {
        vector<string> output = split(that, splitter, max_parts, merge_continuous_splitters);

        for (auto iter = output.begin(); iter != output.end(); iter++)
            (*iter) = trim(*iter);

        return output;
    }

    size_t joined_string_size_(const std::vector<std::string>& strings, size_t connector_size)
    {
        if (strings.empty())
            return 0;

        size_t total_size = 0;
        for (auto iter = strings.begin(); iter != strings.end(); iter++)
            total_size += iter->size();

        total_size += connector_size * (strings.size() - 1);

        return total_size;
    }

    std::string join(const std::vector<std::string>& strings, const std::string& connector)
    {
        if (strings.empty())
        {
            return string();
        }
        else if (connector.size() == 0)
        {
            return join(strings);
        }
        else if (connector.size() == 1)
        {
            return join(strings, connector[0]);
        }
        else
        {
            size_t joined_size = joined_string_size_(strings, connector.size());
            size_t pos = 0;

            string output(joined_size, 0);

            for (auto iter = strings.begin(); iter != strings.end(); iter++)
            {
                output.replace(pos, iter->size(), *iter);
                pos += iter->size();

                if (pos < joined_size)
                {
                    output.replace(pos, connector.size(), connector);
                    pos += connector.size();
                }
            }

            return output;
        }
    }

    std::string join(const std::vector<std::string>& strings, char connector)
    {
        if (strings.empty())
        {
            return string();
        }
        else
        {
            size_t joined_size = joined_string_size_(strings, 1);
            size_t pos = 0;

            string output(joined_size, 0);

            for (auto iter = strings.begin(); iter != strings.end(); iter++)
            {
                output.replace(pos, iter->size(), *iter);
                pos += iter->size();

                if (pos < joined_size)
                {
                    output[pos] = connector;
                    pos++;
                }
            }

            return output;
        }
    }

    std::string join(const std::vector<std::string>& strings)
    {
        if (strings.empty())
        {
            return string();
        }
        else
        {
            size_t joined_size = joined_string_size_(strings, 0);
            size_t pos = 0;

            string output(joined_size, 0);

            for (auto iter = strings.begin(); iter != strings.end(); iter++)
            {
                output.replace(pos, iter->size(), *iter);
                pos += iter->size();
            }

            return output;
        }
    }

    std::string operator*(const std::string& a, int b)
    {
        if (b <= 0)
            return string();
        else if (b == 1)
            return a;
        else
            return a + a * (b - 1);
    }

    std::string operator*(int a, const std::string& b)
    {
        return b * a;
    }

    std::string stringToHexString(const std::string& s)
    {
        std::string hexString;
        hexString.reserve(s.size());
        for (auto it = s.begin(); it != s.end(); ++it)
        {
            int index = (*it >> 4) & 0xf;
            hexString.push_back(hexCharArray[index]);
            index = *it & 0xf;
            hexString.push_back(hexCharArray[index]);
        }
        return hexString;
    }

    std::string hexStringToString(const std::string& hexString)
    {
        std::string s;
        if(hexString.size()&0x1)
            return s;

        s.reserve(hexString.size() / 2);
        for (size_t pos = 0; pos < hexString.size();)
        {
            int hIndex = std::find(hexCharArray, hexCharArray + 16, tolower(hexString[pos++])) - hexCharArray;
            int lIndex = std::find(hexCharArray, hexCharArray + 16, tolower(hexString[pos++])) - hexCharArray;
            if (hIndex == 16 || lIndex == 16)
            {
                //invalid format, return empty string
                return std::string();
            }
            s.push_back(hIndex << 4 | lIndex);
        }
        return s;
    }

    string lowerCase(const string & s)
    {
        string outStr(s);

        transform(
            outStr.begin(), outStr.end(),		// In
            outStr.begin(),			// Out
            (int(*)(int)) tolower);
        return outStr;
    }

    std::string upperCase(const std::string & s)
    {
        string outStr(s);

        transform(
            outStr.begin(), outStr.end(),		// In
            outStr.begin(),			// Out
            (int(*)(int)) toupper);
        return outStr;
    }

    bool utf8_to_wcs(::std::wstring& rDest, const char* pcSrc, size_t szLen)
    { 
        std::setlocale(LC_ALL, "en_US.UTF-8"); // 设置 UTF-8 locale
        size_t len = std::mbstowcs(nullptr, pcSrc, 0);
        if (len == static_cast<size_t>(-1))
        {
            return false;
        } 
        std::wstring wstr(len, L'\0');
        std::mbstowcs(&wstr[0], pcSrc, szLen); 
        rDest = wstr;
        return true;
    }

    bool utf8_to_wcs(::std::wstring& rDest, const ::std::string& rcSrc)
    {
        return utf8_to_wcs(rDest, rcSrc.c_str(), rcSrc.size());
    }

    bool wcs_to_utf8(::std::string& rDest, const wchar_t* pcSrc, size_t szLen)
    {
        std::setlocale(LC_ALL, "en_US.UTF-8");
        size_t len = std::wcstombs(nullptr, pcSrc, 0);
        if (len == static_cast<size_t>(-1)) 
        {
            return false;
        } 
        std::string str(len, '\0');
        std::wcstombs(&str[0], pcSrc, szLen);
        rDest = str; 
        return true;
    }

    bool wcs_to_utf8(::std::string& rDest, const ::std::wstring& rcSrc)
    {
        return wcs_to_utf8(rDest, rcSrc.c_str(), rcSrc.size());
    }

} }
