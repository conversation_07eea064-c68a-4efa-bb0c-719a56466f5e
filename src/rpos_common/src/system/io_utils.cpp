#ifdef _WIN32
#   define WIN32_DEAN_AND_LEAN
#	ifndef _CRT_SECURE_NO_WARNINGS
#		define _CRT_SECURE_NO_WARNINGS
#	endif
#   include <Windows.h>
#else
#   include <unistd.h>
#endif

#include <system/io_utils.h>
#include <system/string_utils.h>
#include <stdio.h>

namespace rpos_common { namespace system { 
 
    ::FILE* file_open(const std::wstring& path, const std::wstring& mode)
    {
#ifdef _WIN32
        return ::_wfopen(path.c_str(), mode.c_str());
#else
        const std::string utf8Path = rpos_common::system::wcs_to_utf8(path);
        const std::string utf8Mode = rpos_common::system::wcs_to_utf8(mode);
        return ::fopen(utf8Path.c_str(), utf8Mode.c_str());
#endif
    }

} }
