#include "serialization/core_objects_serialization.h"
#include <sstream>
#include <stdexcept>

namespace rpos_common { namespace serialization { namespace json {
    using namespace rpos_common::core;

    // Helper function to replace boost::lexical_cast
    template<typename T>
    T string_cast(const std::string& str) {
        std::istringstream iss(str);
        T result;
        if (!(iss >> result) || !iss.eof()) {
            throw std::invalid_argument("string_cast: conversion failed for \"" + str + "\"");
        }
        return result;
    }

    // Specialization for std::string (no conversion needed)
    template<>
    std::string string_cast<std::string>(const std::string& str) {
        return str;
    }

    // Helper function for converting from numeric types to string
    template<typename T>
    std::string to_string_cast(const T& value) {
        std::ostringstream oss;
        oss << value;
        return oss.str();
    }
    
    Json::Value Serializer < Pose >::serialize(const Pose& value)
    {
        Json::Value output;
        output["x"] = json::serialize(value.x());
        output["y"] = json::serialize(value.y());
        output["z"] = json::serialize(value.z());
        output["yaw"] = json::serialize(value.yaw());
        output["pitch"] = json::serialize(value.pitch());
        output["roll"] = json::serialize(value.roll());
        return output;
    }

    Pose Serializer < Pose >::deserialize(const Json::Value& value)
    {
        Pose output;
        output.x() = json::deserialize<double>(value["x"]);
        output.y() = json::deserialize<double>(value["y"]);
        output.z() = json::deserialize<double>(value["z"]);
        output.yaw() = json::deserialize<double>(value["yaw"]);
        output.pitch() = json::deserialize<double>(value["pitch"]);
        output.roll() = json::deserialize<double>(value["roll"]);
        return output;
    }

    Json::Value Serializer < Location >::serialize(const Location& value)
    {
        Json::Value output;
        output["x"] = json::serialize(value.x());
        output["y"] = json::serialize(value.y());
        output["z"] = json::serialize(value.z());
        return output;
    }

    Location Serializer < Location >::deserialize(const Json::Value& v)
    {
        Location output;
        output.x() = json::deserialize<double>(v["x"]);
        output.y() = json::deserialize<double>(v["y"]);
        output.z() = json::deserialize<double>(v["z"]);
        return output;
    }

    Json::Value Serializer < RectangleF >::serialize(const RectangleF& value)
    {
        Json::Value output;
        output["x"] = json::serialize(value.x());
        output["y"] = json::serialize(value.y());
        output["width"] = json::serialize(value.width());
        output["height"] = json::serialize(value.height());
        return output;
    }

    RectangleF Serializer < RectangleF >::deserialize(const Json::Value& v)
    {
        RectangleF output;

        if (!v["x"].empty())
        {
            output.x() = json::deserialize<float>(v["x"]);
            output.y() = json::deserialize<float>(v["y"]);
            output.width() = json::deserialize<float>(v["width"]);
            output.height() = json::deserialize<float>(v["height"]);
        }
        else
        {
            float minX = json::deserialize<float>(v["min_x"]);
            float minY = json::deserialize<float>(v["min_y"]);
            float maxX = json::deserialize<float>(v["max_x"]);
            float maxY = json::deserialize<float>(v["max_y"]);

            output.x() = minX;
            output.y() = minY;
            output.width() = maxX - minX;
            output.height() = maxY - minY;
        }

        return output;
    }

    Json::Value Serializer < RectangleI >::serialize(const RectangleI& value)
    {
        Json::Value output;
        output["x"] = json::serialize(value.x());
        output["y"] = json::serialize(value.y());
        output["width"] = json::serialize(value.width());
        output["height"] = json::serialize(value.height());
        return output;
    }

    RectangleI Serializer < RectangleI >::deserialize(const Json::Value& v)
    {
        RectangleI output;

        if (!v["x"].empty())
        {
            output.x() = json::deserialize<int>(v["x"]);
            output.y() = json::deserialize<int>(v["y"]);
            output.width() = json::deserialize<int>(v["width"]);
            output.height() = json::deserialize<int>(v["height"]);
        }
        else
        {
            int minX = json::deserialize<int>(v["min_x"]);
            int minY = json::deserialize<int>(v["min_y"]);
            int maxX = json::deserialize<int>(v["max_x"]);
            int maxY = json::deserialize<int>(v["max_y"]);

            output.x() = minX;
            output.y() = minY;
            output.width() = maxX - minX;
            output.height() = maxY - minY;
        }

        return output;
    }

    Json::Value Serializer < ORectangleF >::serialize(const ORectangleF& value)
    {
        Json::Value output;
        output["sx"] = json::serialize(value.start().x());
        output["sy"] = json::serialize(value.start().y());
        output["ex"] = json::serialize(value.end().x());
        output["ey"] = json::serialize(value.end().y());
        output["halfwidth"] = json::serialize(value.halfWidth());
        return output;
    }

    ORectangleF Serializer < ORectangleF >::deserialize(const Json::Value& v)
    {
        ORectangleF output;
        if (v.isMember("start") && v.isMember("end")) //be compatible with slamwared and agent
        {
            output.start() = json::deserialize<Vector2f>(v["start"]);
            output.end()= json::deserialize<Vector2f>(v["end"]);
            output.halfWidth() = json::deserialize<float>(v["half_width"]);
        }
        else
        {
            output.start().x() = json::deserialize<float>(v["sx"]);
            output.start().y() = json::deserialize<float>(v["sy"]);
            output.end().x() = json::deserialize<float>(v["ex"]);
            output.end().y() = json::deserialize<float>(v["ey"]); 
            output.halfWidth() = json::deserialize<float>(v["halfwidth"]);
        }
        return output;
    }

    Json::Value Serializer < Vector2i >::serialize(const Vector2i& value)
    {
        Json::Value output;
        output["x"] = json::serialize(value.x());
        output["y"] = json::serialize(value.y());
        return output;
    }

    Vector2i Serializer < Vector2i >::deserialize(const Json::Value& value)
    {
        Vector2i output;
        output.x() = json::deserialize<int>(value["x"]);
        output.y() = json::deserialize<int>(value["y"]);
        return output;
    }

    Json::Value Serializer < Vector3i >::serialize(const Vector3i& value)
    {
        Json::Value output;
        output["x"] = json::serialize(value.x());
        output["y"] = json::serialize(value.y());
        output["z"] = json::serialize(value.z());
        return output;
    }

    Vector3i Serializer < Vector3i >::deserialize(const Json::Value& value)
    {
        Vector3i output;
        output.x() = json::deserialize<int>(value["x"]);
        output.y() = json::deserialize<int>(value["y"]);
        output.z() = json::deserialize<int>(value["z"]);
        return output;
    }

    Json::Value Serializer < Vector4i >::serialize(const Vector4i& value)
    {
        Json::Value output;
        output["x"] = json::serialize(value.x());
        output["y"] = json::serialize(value.y());
        output["z"] = json::serialize(value.z());
        output["w"] = json::serialize(value.w());
        return output;
    }

    Vector4i Serializer < Vector4i >::deserialize(const Json::Value& value)
    {
        Vector4i output;
        output.x() = json::deserialize<int>(value["x"]);
        output.y() = json::deserialize<int>(value["y"]);
        output.z() = json::deserialize<int>(value["z"]);
        output.w() = json::deserialize<int>(value["w"]);
        return output;
    }

    Json::Value Serializer < Vector2f >::serialize(const Vector2f& value)
    {
        Json::Value output;
        output["x"] = json::serialize(value.x());
        output["y"] = json::serialize(value.y());
        return output;
    }

    Vector2f Serializer < Vector2f >::deserialize(const Json::Value& value)
    {
        Vector2f output;
        output.x() = json::deserialize<float>(value["x"]);
        output.y() = json::deserialize<float>(value["y"]);
        return output;
    }

    Json::Value Serializer < Vector3f >::serialize(const Vector3f& value)
    {
        Json::Value output;
        output["x"] = json::serialize(value.x());
        output["y"] = json::serialize(value.y());
        output["z"] = json::serialize(value.z());
        return output;
    }

    Vector3f Serializer < Vector3f >::deserialize(const Json::Value& value)
    {
        Vector3f output;
        output.x() = json::deserialize<float>(value["x"]);
        output.y() = json::deserialize<float>(value["y"]);
        output.z() = json::deserialize<float>(value["z"]);
        return output;
    }

    Json::Value Serializer < Vector4f >::serialize(const Vector4f& value)
    {
        Json::Value output;
        output["x"] = json::serialize(value.x());
        output["y"] = json::serialize(value.y());
        output["z"] = json::serialize(value.z());
        output["w"] = json::serialize(value.w());
        return output;
    }

    Vector4f Serializer < Vector4f >::deserialize(const Json::Value& value)
    {
        Vector4f output;
        output.x() = json::deserialize<float>(value["x"]);
        output.y() = json::deserialize<float>(value["y"]);
        output.z() = json::deserialize<float>(value["z"]);
        output.w() = json::deserialize<float>(value["w"]);
        return output;
    }

    Json::Value Serializer<core::SensorType>::serialize(const core::SensorType& tVal)
    {
        Json::Value jsnVal;
        switch (tVal)
        {
        case core::SensorTypeBumper:
            jsnVal = "bumper";
            break;
        case core::SensorTypeCliff:
            jsnVal = "cliff";
            break;
        case core::SensorTypeSonar:
            jsnVal = "sonar";
            break;
        case core::SensorTypeDepthCamera:
            jsnVal = "depth_camera";
            break;
        case core::SensorTypeWallSensor:
            jsnVal = "wall_sensor";
            break;
        case core::SensorTypeMagTapeDetector:
            jsnVal = "magnetic_tape_detector";
            break;
        case core::SensorTypeTofCliff:
            jsnVal = "tof_cliff";
            break;
        default:
            jsnVal = "unknown";
            break;
        }
        return jsnVal;
    }

    core::SensorType Serializer<core::SensorType>::deserialize(const Json::Value& jsnVal)
    {
        core::SensorType tVal = core::SensorTypeUnknown;
        if (jsnVal.isString())
        {
            const std::string strTmp = jsnVal.asString();
            if (strTmp == "bumper")
                tVal = core::SensorTypeBumper;
            else if (strTmp == "cliff")
                tVal = core::SensorTypeCliff;
            else if (strTmp == "sonar")
                tVal = core::SensorTypeSonar;
            else if (strTmp == "depth_camera")
                tVal = core::SensorTypeDepthCamera;
            else if (strTmp == "wall_sensor")
                tVal = core::SensorTypeWallSensor;
            else if (strTmp == "magnetic_tape_detector")
                tVal = core::SensorTypeMagTapeDetector;
            else if(strTmp == "tof_cliff")
                tVal = core::SensorTypeTofCliff;
        }
        return tVal;
    }





}}} // namespace rpos_common::serialization::json
