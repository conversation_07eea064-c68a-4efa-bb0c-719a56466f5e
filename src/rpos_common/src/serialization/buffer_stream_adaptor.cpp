#include <cstring> 
#include <stdexcept>
#include "serialization/buffer_stream_adaptor.h"

namespace rpos_common { namespace serialization {

    BufferStreamAdaptor::BufferStreamAdaptor(std::vector<uint8_t> *buf) : buf_(buf), head_(0)
    {
    }

    BufferStreamAdaptor::~BufferStreamAdaptor() {}

    bool BufferStreamAdaptor::isOpen()
    {
        return true;
    }

    bool BufferStreamAdaptor::canRead()
    {
        return true;
    }

    bool BufferStreamAdaptor::canWrite()
    {
        return true;
    }

    bool BufferStreamAdaptor::canSeek()
    {
        return false;
    }

    void BufferStreamAdaptor::close()
    {
        // do nothing here
    }

    bool BufferStreamAdaptor::endOfStream()
    {
        return buf_->size() <= head_;
    }

    int BufferStreamAdaptor::read(void *buffer, size_t count)
    {
        if (count > buf_->size() - head_)
            return 0;
        auto pDst = static_cast<uint8_t*>(buffer);
        auto pSrc = static_cast<uint8_t*>(&(buf_->at(head_)));
        memcpy(pDst, pSrc, count);
        head_ += count;
        return static_cast<int>(count);
    }

    int BufferStreamAdaptor::write(const void *buffer, size_t count)
    {
        size_t buf_size = buf_->size();
        buf_->resize(buf_size + count);
        memcpy(&buf_->back() + 1 - count, buffer, count);
        return static_cast<int>(count);
    }

    size_t BufferStreamAdaptor::tell()
    {
        return head_;
    }

    void BufferStreamAdaptor::seek(rpos_common::io::SeekType, int)
    {
        throw std::runtime_error("not supported");
    }

} } 
