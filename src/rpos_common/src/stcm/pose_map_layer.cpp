#include <stcm/pose_map_layer.h>

namespace rpos_common { namespace stcm {

    const char* const PoseMapLayer::Type = "vnd.slamtec.map-layer/vnd.pose-map+binary";

    void PoseMapLayer::clear(void)
    {
        poses_.clear();
        this->MapLayer::clear();
    }

    const core::PoseEntryMap& PoseMapLayer::poses() const
    {
        return poses_;
    }

    core::PoseEntryMap& PoseMapLayer::poses()
    {
        return poses_;
    }

}}
