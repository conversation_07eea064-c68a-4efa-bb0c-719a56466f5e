#include <stcm/composite_map_reader_impl.h> 
#include <serialization/buffer_stream_adaptor.h>
#include <core/metadata_internal.h>
#include <system/types.h>

namespace rpos_common { namespace stcm {

    using namespace rpos_common::system;
    using namespace rpos_common::system::types;

    const char* const CompositeMapReaderImpl::RectangleAreaLayerFactory::LegacyType = "vnd.slamtec.map-layer/vnd.rectangle-area-map+binary";

    CompositeMapReaderImpl::MapLayerFactoryBase::MapLayerFactoryBase(CompositeMapReaderImpl* pCmrImpl)
        : m_pCmrImpl(pCmrImpl)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != m_pCmrImpl);
    }

#ifdef _WIN32
#   pragma warning(push)
#   pragma warning(disable: 4355)
#endif

    CompositeMapReaderImpl::CompositeMapReaderImpl(void)
        : m_tUnknownMlFactory(this), m_tGridMlFactory(this), m_tLineMlFactory(this), m_tPoseMlFactory(this), m_tPointsMlFactory(this)
        , m_tImageFeaturesMlFactory(this)
        , m_tRectangleAreaMlFactory(this)
        , m_tPolygonAreaMlFactory(this)
    {
        m_hmTypeToFactory[GridMapLayer::Type] = &m_tGridMlFactory;
        m_hmTypeToFactory[LineMapLayer::Type] = &m_tLineMlFactory;
        m_hmTypeToFactory[PoseMapLayer::Type] = &m_tPoseMlFactory;
        m_hmTypeToFactory[PointsMapLayer::Type] = &m_tPointsMlFactory;
        m_hmTypeToFactory[ImageFeaturesMapLayer::Type] = &m_tImageFeaturesMlFactory;
        m_hmTypeToFactory[RectangleAreaMapLayer::Type] = &m_tRectangleAreaMlFactory;
        m_hmTypeToFactory[RectangleAreaLayerFactory::LegacyType] = &m_tRectangleAreaMlFactory;
        m_hmTypeToFactory[PolygonAreaMapLayer::Type] = &m_tPolygonAreaMlFactory;
        //
        m_tMetadataBuf.reserve(1024);
        m_tMapBodyBuf.reserve(1024 * 8);
        m_tDcmprsdMapBodyBuf.reserve(1024 * 8);
    }

#ifdef _WIN32
#   pragma warning(pop)
#endif

    CompositeMapReaderImpl::~CompositeMapReaderImpl(void)
    {
        //
    }
    
    std::shared_ptr<CompositeMap> CompositeMapReaderImpl::loadFromStream(rpos_common::io::IStream& inStream)
    {
        if (!inStream.canRead())
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("StreamNotReadable");

        std::shared_ptr<CompositeMap> spRet(std::make_shared<CompositeMap>());
        StcmFileHeader tFileHeader;
        doLoadFileHeader_(tFileHeader, *spRet, inStream);
        spRet->maps().reserve(tFileHeader.uSectionCnt);
        for (_u32 u = 0; u < tFileHeader.uSectionCnt; ++u)
        {
            MapLayerSharedPtr spMapLayer(doLoadOneMapSection_(inStream));
            RPOS_COMPOSITEMAP_ASSERT(spMapLayer);
            spRet->maps().emplace_back(spMapLayer);
        }
        return spRet;
    }

    const void* CompositeMapReaderImpl::deserializeMetadataFromMem(core::Metadata& rMetadata, const void* pcSrcBegin, const void* pcSrcEnd)
    {
        std::map<std::string, std::string> & rDict = rMetadata.dict();
        RPOS_COMPOSITEMAP_ASSERT(rDict.empty());
        RPOS_COMPOSITEMAP_ASSERT(pcSrcBegin <= pcSrcEnd);
        const char* pcTmp = (const char*)pcSrcBegin;
        const char* const pcEnd = (const char*)pcSrcEnd;
        if (pcTmp + sizeof(_u16) <= pcEnd)
        {
			_u16 u16Tmp;
			pcTmp = (const char*)leToCpuReadFromBuffer<_u16>(u16Tmp, pcTmp);
            RPOS_COMPOSITEMAP_ASSERT(u16Tmp <= C_METADATA_MAX_ENTRY_CNT);
            std::string strKey;
            std::string strVal;
            for (_u16 u = 0; u < u16Tmp; ++u)
            {
                pcTmp = (const char*)deserializeStrFromMem(strKey, pcTmp, pcEnd);
                pcTmp = (const char*)deserializeStrFromMem(strVal, pcTmp, pcEnd);
                auto prTmpRet = rDict.insert(std::map<std::string, std::string>::value_type(strKey, strVal));
                RPOS_COMPOSITEMAP_NOT_USED(prTmpRet);
                RPOS_COMPOSITEMAP_ASSERT(prTmpRet.second);
            }
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("ReadMetadataEntryCntFailed");
        }
        return pcTmp;
    }
    void CompositeMapReaderImpl::deserializeMetadataFromStream(core::Metadata& rMetadata, rpos_common::io::IStream& inStream)
    {
        std::map<std::string, std::string> & rDict = rMetadata.dict();
        RPOS_COMPOSITEMAP_ASSERT(rDict.empty());
        _u16 u16Tmp;
        exactRead(&u16Tmp, sizeof(u16Tmp), inStream, "ReadMetadataEntryCntFailed");
        u16Tmp = le_to_cpu(u16Tmp);
        RPOS_COMPOSITEMAP_ASSERT(u16Tmp <= C_METADATA_MAX_ENTRY_CNT);
        std::string strKey;
        std::string strVal;
        for (_u16 u = 0; u < u16Tmp; ++u)
        {
            deserializeStrFromStream(strKey, inStream);
            deserializeStrFromStream(strVal, inStream);
            auto prTmpRet = rDict.insert(std::map<std::string, std::string>::value_type(strKey, strVal));
            RPOS_COMPOSITEMAP_NOT_USED(prTmpRet);
            RPOS_COMPOSITEMAP_ASSERT(prTmpRet.second);
        }
    }

    void CompositeMapReaderImpl::doLoadFileHeader_(StcmFileHeader& rFileHeader, CompositeMap& rCmpstMap, rpos_common::io::IStream& inStream)
    {
        const size_t szOldPos = inStream.tell();
        exactRead(&rFileHeader, sizeof(rFileHeader), inStream, "ReadStcmFileHeaderFailed");
        rFileHeader.u16HeaderSize = rpos_common::system::le_to_cpu(rFileHeader.u16HeaderSize);
        rFileHeader.u16Ver = le_to_cpu(rFileHeader.u16Ver);
        rFileHeader.u16MinReaderVer = le_to_cpu(rFileHeader.u16MinReaderVer);
        rFileHeader.u16MinWriterVer = le_to_cpu(rFileHeader.u16MinWriterVer);
        rFileHeader.uSectionCnt = le_to_cpu(rFileHeader.uSectionCnt);
        if (checkStcmSignature(rFileHeader.tSignature))
        {
            if (rFileHeader.u16MinReaderVer <= C_READER_VERSION)
            {
                deserializeMetadataFromStream(rCmpstMap.metadata(), inStream);
                const size_t szNewPos = inStream.tell();
                if (BOOST_UNLIKELY(szNewPos < szOldPos || (szNewPos - szOldPos) != rFileHeader.u16HeaderSize))
                {
                    RPOS_COMPOSITEMAP_THROW_EXCEPTION("StcmFileHeaderSizeNotConsistent");
                }
            }
            else
            {
                RPOS_COMPOSITEMAP_THROW_EXCEPTION("StcmFileReaderVersionTooLow");
            }
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("StcmFileHeaderWrongSignature");
        }
    }

    CompositeMapReaderImpl::MapLayerFactoryBase* CompositeMapReaderImpl::prepareMapLayerFactory_(CompressType& rCmprsType, const core::Metadata& rcMetadata)
    {
        rCmprsType = getCompressType(rcMetadata);
        if (CMPRS_TYPE_UNKNOWN != rCmprsType)
        {
            auto citType = rcMetadata.dict().find(RPOS_COMPOSITEMAP_METADATA_KEY_TYPE);
            if (rcMetadata.dict().cend() != citType)
            {
                auto citFactory = m_hmTypeToFactory.find(citType->second);
                if (m_hmTypeToFactory.cend() != citFactory)
                {
                    return citFactory->second;
                }
            }
        }
        return &m_tUnknownMlFactory;
    }
    CompositeMapReaderImpl::MapLayerSharedPtr CompositeMapReaderImpl::doLoadOneMapSection_(rpos_common::io::IStream& inStream)
    {
        // Section Size
        const size_t szPosBegin = inStream.tell();
        _u32 uSectionSize;
        exactRead(&uSectionSize, sizeof(uSectionSize), inStream, "ReadMapSectionSizeFailed");
        uSectionSize = le_to_cpu(uSectionSize);
        // Metadata
        core::Metadata tTmpMetadata;
        deserializeMetadataFromStream(tTmpMetadata, inStream);
        // Body
        const size_t szPosBody = inStream.tell();
        RPOS_COMPOSITEMAP_ASSERT(szPosBegin < szPosBody);
        size_t szBodySizeInFile = szPosBody - szPosBegin; // the size of ("SectionSize" + "Metadata")
        if (szBodySizeInFile <= uSectionSize)
        {
            szBodySizeInFile = uSectionSize - szBodySizeInFile;
            m_tMapBodyBuf.resize(szBodySizeInFile);
            exactRead(m_tMapBodyBuf.data(), m_tMapBodyBuf.size(), inStream, "ReadMapBodyFailed");
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("CalcMapBodySizeInFileWrong");
        }
        // Prepare Factory
        CompressType eCmprsType;
        MapLayerFactoryBase* pMlFactory = prepareMapLayerFactory_(eCmprsType, tTmpMetadata);
        RPOS_COMPOSITEMAP_ASSERT(NULL != pMlFactory);
        return pMlFactory->createMapLayerByMem(tTmpMetadata, m_tMapBodyBuf, eCmprsType);
    }

    //////////////////////////////////////////////////////////////////////////

    void CompositeMapReaderImpl::MapLayerFactoryBase::refreshMapLayerProperties_(MapLayerSharedPtr pMapLayer)
    {
        RPOS_COMPOSITEMAP_ASSERT(pMapLayer);
        std::string strTmp;
        if (pMapLayer->metadata().tryGet(RPOS_COMPOSITEMAP_METADATA_KEY_NAME, strTmp))
        {
            pMapLayer->setName(strTmp);
        }
        if (pMapLayer->metadata().tryGet(RPOS_COMPOSITEMAP_METADATA_KEY_USAGE, strTmp))
        {
            pMapLayer->setUsage(strTmp);
        }
        if (pMapLayer->metadata().tryGet(RPOS_COMPOSITEMAP_METADATA_KEY_TYPE, strTmp))
        {
            pMapLayer->setType(strTmp);
        }
    }

    const CompositeMapReaderImpl::ubyte_buf_type& 
        CompositeMapReaderImpl::MapLayerFactoryBase::procBodyInFileMaybeDecompress_(const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType)
    {
        if (CMPRS_TYPE_NONE != eCmprsType)
        {
            m_pCmrImpl->m_tDcmprsdMapBodyBuf.clear();
            m_pCmrImpl->doDecompressAppend(eCmprsType, m_pCmrImpl->m_tDcmprsdMapBodyBuf, rcBodyInFile.data(), rcBodyInFile.size());
            return m_pCmrImpl->m_tDcmprsdMapBodyBuf;
        }
        return rcBodyInFile;
    }

    CompositeMapReaderImpl::MapLayerSharedPtr 
        CompositeMapReaderImpl::UnknownMapLayerFactory::createMapLayerByMem(core::Metadata& rMetadata
        , const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType)
    {
        RPOS_COMPOSITEMAP_NOT_USED(eCmprsType);
        std::shared_ptr<UnknownMapLayer> pUnknownMl(std::make_shared<UnknownMapLayer>());
        pUnknownMl->metadata().swap(rMetadata);
        refreshMapLayerProperties_(pUnknownMl);
        pUnknownMl->rawBody() = rcBodyInFile;
        return pUnknownMl;
    }

    CompositeMapReaderImpl::MapLayerSharedPtr 
        CompositeMapReaderImpl::GridMapLayerFactory::createMapLayerByMem(core::Metadata& rMetadata
        , const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType)
    {
        std::shared_ptr<GridMapLayer> pGridMl(std::make_shared<GridMapLayer>());
        pGridMl->metadata().swap(rMetadata);
        refreshMapLayerProperties_(pGridMl);
        const core::Metadata& rcMd = pGridMl->metadata();
        {
            core::Location tOrigin;
            m_pCmrImpl->tryGetValInMetadata(tOrigin.x(), rcMd, RPOS_COMPOSITEMAP_METADATA_KEY_ORIGIN_X);
            m_pCmrImpl->tryGetValInMetadata(tOrigin.y(), rcMd, RPOS_COMPOSITEMAP_METADATA_KEY_ORIGIN_Y);
            pGridMl->setOrigin(tOrigin);
            core::Vector2i tDimension;
            m_pCmrImpl->tryGetValInMetadata(tDimension.x(), rcMd, RPOS_COMPOSITEMAP_METADATA_KEY_DIMENSION_WIDTH, 0);
            m_pCmrImpl->tryGetValInMetadata(tDimension.y(), rcMd, RPOS_COMPOSITEMAP_METADATA_KEY_DIMENSION_HEIGHT, 0);
            pGridMl->setDimension(tDimension);
            core::Vector2f tResolution;
            m_pCmrImpl->tryGetValInMetadata(tResolution.x(), rcMd, RPOS_COMPOSITEMAP_METADATA_KEY_RESOLUTION_X, 0.0f);
            m_pCmrImpl->tryGetValInMetadata(tResolution.y(), rcMd, RPOS_COMPOSITEMAP_METADATA_KEY_RESOLUTION_Y, 0.0f);
            pGridMl->setResolution(tResolution);
        }
        {
            const ubyte_buf_type& rcBody = procBodyInFileMaybeDecompress_(rcBodyInFile, eCmprsType);
            std::vector<uint8_t>& rDest = pGridMl->mapData();
            rDest.resize(rcBody.size());
			if (!rDest.empty())
				::memcpy(rDest.data(), rcBody.data(), rDest.size());
        }
        return pGridMl;
    }

    CompositeMapReaderImpl::MapLayerSharedPtr 
        CompositeMapReaderImpl::LineMapLayerFactory::createMapLayerByMem(core::Metadata& rMetadata
        , const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType)
    {
        std::shared_ptr<LineMapLayer> pLineMl(std::make_shared<LineMapLayer>());
        pLineMl->metadata().swap(rMetadata);
        refreshMapLayerProperties_(pLineMl);
        _u64 u64LineCnt;
        if (m_pCmrImpl->tryGetValInMetadata(u64LineCnt, pLineMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT))
        {
            if (u64LineCnt <= (std::numeric_limits<size_t>::max)())
            {
                pLineMl->metadata().dict().erase(RPOS_COMPOSITEMAP_METADATA_KEY_COUNT);
                //
                const ubyte_buf_type& rcBody = procBodyInFileMaybeDecompress_(rcBodyInFile, eCmprsType);
                const char* pcTmp = (const char*)rcBody.data();
                const char* const pcEnd = pcTmp + rcBody.size();
                std::string strName;
                float fTmpX;
                float fTmpY;
                std::map<std::string, Line>& rLines = pLineMl->lines();
                for (size_t t = 0; t < static_cast<size_t>(u64LineCnt); ++t)
                {
                    pcTmp = (const char*)m_pCmrImpl->deserializeStrFromMem(strName, pcTmp, pcEnd);
                    auto tPrTmpRet = rLines.insert(std::map<std::string, Line>::value_type(strName, Line()));
                    if (tPrTmpRet.second)
                    {
                        Line& rLine = tPrTmpRet.first->second;
                        rLine.name = strName;
                        pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpX, pcTmp, pcEnd, "ReadStartXOfLineFailed");
                        pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpY, pcTmp, pcEnd, "ReadStartYOfLineFailed");
                        rLine.start.x() = fTmpX;
                        rLine.start.y() = fTmpY;
                        pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpX, pcTmp, pcEnd, "ReadEndXOfLineFailed");
                        pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpY, pcTmp, pcEnd, "ReadEndYOfLineFailed");
                        rLine.end.x() = fTmpX;
                        rLine.end.y() = fTmpY;
                        pcTmp = (const char*)m_pCmrImpl->deserializeMetadataFromMem(rLine.metadata, pcTmp, pcEnd);
                    }
                    else
                    {
                        RPOS_COMPOSITEMAP_THROW_EXCEPTION("LineNameInLineMapDuplicated");
                    }
                }
            }
            else
            {
                RPOS_COMPOSITEMAP_THROW_EXCEPTION("LinesCountOfLineMapTooLargeForSizeT");
            }
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("LinesCountOfLineMapNotFoundInMetadata");
        }
        return pLineMl;
    }

    CompositeMapReaderImpl::MapLayerSharedPtr 
        CompositeMapReaderImpl::PoseMapLayerFactory::createMapLayerByMem(core::Metadata& rMetadata
        , const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType)
    {
        std::shared_ptr<PoseMapLayer> pPoseMl(std::make_shared<PoseMapLayer>());
        pPoseMl->metadata().swap(rMetadata);
        refreshMapLayerProperties_(pPoseMl);
        _u64 u64PoseEntryCnt;
        if (m_pCmrImpl->tryGetValInMetadata(u64PoseEntryCnt, pPoseMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT))
        {
            if (u64PoseEntryCnt <= (std::numeric_limits<size_t>::max)())
            {
                pPoseMl->metadata().dict().erase(RPOS_COMPOSITEMAP_METADATA_KEY_COUNT);
                //
                const ubyte_buf_type& rcBody = procBodyInFileMaybeDecompress_(rcBodyInFile, eCmprsType);
                const char* pcTmp = (const char*)rcBody.data();
                const char* const pcEnd = pcTmp + rcBody.size();
                std::string strName;
                _u8 u8TagCnt;
                float fTmpX;
                float fTmpY;
                float fTmpYaw;
                std::map<std::string, rpos_common::core::PoseEntry>& rPoses = pPoseMl->poses();
                for (size_t t = 0; t < static_cast<size_t>(u64PoseEntryCnt); ++t)
                {
                    pcTmp = (const char*)m_pCmrImpl->deserializeStrFromMem(strName, pcTmp, pcEnd);
                    auto tPrTmpRet = rPoses.insert(std::map<std::string, rpos_common::core::PoseEntry>::value_type(strName, rpos_common::core::PoseEntry()));
                    if (tPrTmpRet.second)
                    {
                        rpos_common::core::PoseEntry& rPoseEntry = tPrTmpRet.first->second;
                        rPoseEntry.id = strName;
                        pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(u8TagCnt, pcTmp, pcEnd, "ReadTagCntOfPoseEntryFailed");
                        RPOS_COMPOSITEMAP_ASSERT(u8TagCnt <= CompositeMapRwImplBase::C_MAX_TAG_CNT_IN_POSE_ENTRY);
                         
                        pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpX, pcTmp, pcEnd, "ReadPoseXInPoseEntryFailed");
                        pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpY, pcTmp, pcEnd, "ReadPoseYInPoseEntryFailed");
                        pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpYaw, pcTmp, pcEnd, "ReadPoseYawInPoseEntryFailed");
                        rPoseEntry.pose.x() = fTmpX;
                        rPoseEntry.pose.y() = fTmpY;
                        rPoseEntry.pose.yaw() = fTmpYaw;
                        pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T<_u8>(rPoseEntry.flags, pcTmp, pcEnd, "ReadFlagsOfPoseEntryFailed");
                        pcTmp = (const char*)m_pCmrImpl->deserializeMetadataFromMem(rPoseEntry.metadata, pcTmp, pcEnd);
                    }
                    else
                    {
                        RPOS_COMPOSITEMAP_THROW_EXCEPTION("PoseEntryNameInPoseMapDuplicated");
                    }
                }
            }
            else
            {
                RPOS_COMPOSITEMAP_THROW_EXCEPTION("PoseEntryCountOfPoseMapTooLargeForSizeT");
            }
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("PoseEntryCountOfPoseMapNotFoundInMetadata");
        }
        return pPoseMl;
    }

    CompositeMapReaderImpl::MapLayerSharedPtr 
        CompositeMapReaderImpl::PointsMapLayerFactory::createMapLayerByMem(rpos_common::core::Metadata& rMetadata
        , const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType)
    {
        std::shared_ptr<PointsMapLayer> pPointsMl(std::make_shared<PointsMapLayer>());
        pPointsMl->metadata().swap(rMetadata);
        refreshMapLayerProperties_(pPointsMl);
        _u64 u64PointsEntryCnt;
        if (m_pCmrImpl->tryGetValInMetadata(u64PointsEntryCnt, pPointsMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT))
        {
            if (u64PointsEntryCnt <= (std::numeric_limits<size_t>::max)())
            {
                pPointsMl->metadata().dict().erase(RPOS_COMPOSITEMAP_METADATA_KEY_COUNT);
                //
                const ubyte_buf_type& rcBody = procBodyInFileMaybeDecompress_(rcBodyInFile, eCmprsType);
                const char* pcTmp = (const char*)rcBody.data();
                const char* const pcEnd = pcTmp + rcBody.size();

                _u32 u32PointsId;
                float flocationX;
                float fLocationY;
                float fCircularErrorProbability;
                _u8 u8TagCnt;

                std::vector<rpos_common::core::PointPDF>& rPoints = pPointsMl->points();
                rPoints.resize(static_cast<size_t>(u64PointsEntryCnt));
                for (size_t t = 0; t < static_cast<size_t>(u64PointsEntryCnt); ++t)
                {
                    rpos_common::core::PointPDF& pointPdf = rPoints[t];

                    pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(u32PointsId, pcTmp, pcEnd, "ReadPointIdOfPointEntryFailed");
                    pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(flocationX, pcTmp, pcEnd, "ReadPointXInPointEntryFailed");
                    pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fLocationY, pcTmp, pcEnd, "ReadPointYInPointEntryFailed");
                    pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fCircularErrorProbability, pcTmp, pcEnd, "ReadPointCircularErrorProbabilityInPointEntryFailed");
                    pointPdf.id = u32PointsId;
                    pointPdf.location.x() = flocationX;
                    pointPdf.location.y() =  fLocationY;
                    pointPdf.circular_error_probability = fCircularErrorProbability;

                    pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(u8TagCnt, pcTmp, pcEnd, "ReadTagCntOfPointEntryFailed");
                    RPOS_COMPOSITEMAP_ASSERT(u8TagCnt <= CompositeMapRwImplBase::C_MAX_TAG_CNT_IN_POINTPDF_TAG_ENTRY);
                    pointPdf.tags.resize(u8TagCnt);
                    for (_u8 u = 0; u < u8TagCnt; ++u)
                    {
                        pcTmp = (const char*)m_pCmrImpl->deserializeStrFromMem(pointPdf.tags[u], pcTmp, pcEnd);
                    }
                }
            }
            else
            {
                RPOS_COMPOSITEMAP_THROW_EXCEPTION("PointEntryCountOfPointMapTooLargeForSizeT");
            }
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("PointEntryCountOfPointMapNotFoundInMetadata");
        }
        return pPointsMl;
    }

    CompositeMapReaderImpl::MapLayerSharedPtr
        CompositeMapReaderImpl::ImageFeaturesMapLayerFactory::createMapLayerByMem(core::Metadata& rMetadata
            , const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType)
    {
        std::shared_ptr<ImageFeaturesMapLayer> pImageFeaturesMl(std::make_shared<ImageFeaturesMapLayer>());
        pImageFeaturesMl->metadata().swap(rMetadata);
        refreshMapLayerProperties_(pImageFeaturesMl);
        const core::Metadata& rcMd = pImageFeaturesMl->metadata();
        
        uint32_t type; 
        if(m_pCmrImpl->tryGetValInMetadata(type, rcMd, RPOS_COMPOSITEMAP_METADATA_KEY_FEATURE_TYPE))
        { 
            pImageFeaturesMl->setFeatureType((rpos_common::stcm::FeatureType)type);
        }
        else
        {   
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("LegacyImageFeaturesMapNotSupported");
        }

        _u64 u64FeaturesObsEntryCnt;
        if (!m_pCmrImpl->tryGetValInMetadata(u64FeaturesObsEntryCnt, pImageFeaturesMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("CountOfImageFeaturesMapNotFoundInMetadata"); 
            return pImageFeaturesMl;
        }
        if (u64FeaturesObsEntryCnt > std::numeric_limits<size_t>::max())
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("FeaturesObsEntryCountOfImageFeaturesMapTooLargeForSizeT");
            return pImageFeaturesMl;
        } 

        pImageFeaturesMl->metadata().dict().erase(RPOS_COMPOSITEMAP_METADATA_KEY_COUNT);
        //
        const ubyte_buf_type& rcBody = procBodyInFileMaybeDecompress_(rcBodyInFile, eCmprsType);
        const char* pcTmp = (const char*)rcBody.data();
        const char* const pcEnd = pcTmp + rcBody.size();

        int id;
        float fTmpX;
        float fTmpY;
        float fTmpZ;
        float fTmpYaw;
        float fTmpPitch;
        float fTmpRoll;
        _u32 u32FeaturesCnt;

        std::vector<rpos_common::stcm::ImageFeaturesObservation>& rImageFeatures = pImageFeaturesMl->featureObs();
        rImageFeatures.resize(static_cast<size_t>(u64FeaturesObsEntryCnt));
        for (size_t t = 0; t < static_cast<size_t>(u64FeaturesObsEntryCnt); ++t)
        {
            rpos_common::stcm::ImageFeaturesObservation& obs = rImageFeatures[t];

            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(id, pcTmp, pcEnd, "ReadEntryIDOfFeaturesObsEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpX, pcTmp, pcEnd, "ReadCameraPoseXOfFeaturesObsEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpY, pcTmp, pcEnd, "ReadCameraPoseYOfFeaturesObsEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpZ, pcTmp, pcEnd, "ReadCameraPoseZOfFeaturesObsEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpYaw, pcTmp, pcEnd, "ReadCameraPoseYawOfFeaturesObsEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpPitch, pcTmp, pcEnd, "ReadCameraPosePitchOfFeaturesObsEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(fTmpRoll, pcTmp, pcEnd, "ReadCameraPoseRollOfFeaturesObsEntryFailed");
            obs.ID = id;
            obs.cameraPose.x() = fTmpX;
            obs.cameraPose.y() = fTmpY;
            obs.cameraPose.z() = fTmpZ;
            obs.cameraPose.yaw() = fTmpYaw;
            obs.cameraPose.pitch() = fTmpPitch;
            obs.cameraPose.roll() = fTmpRoll;

            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(u32FeaturesCnt, pcTmp, pcEnd, "ReadFeaturesCountFailed");
            RPOS_COMPOSITEMAP_ASSERT(u32FeaturesCnt <= (std::numeric_limits<_u32>::max)()); 
            
            obs.features.resize(u32FeaturesCnt);
            memcpy(&obs.features[0],pcTmp,u32FeaturesCnt); 
            pcTmp += u32FeaturesCnt; 
        }  
        return pImageFeaturesMl;
    }

    CompositeMapReaderImpl::MapLayerSharedPtr 
        CompositeMapReaderImpl::RectangleAreaLayerFactory::createMapLayerByMem(core::Metadata& rMetadata
        , const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType)
    {
        std::shared_ptr<RectangleAreaMapLayer> pAreasMl(std::make_shared<RectangleAreaMapLayer>());
        pAreasMl->metadata().swap(rMetadata);
        refreshMapLayerProperties_(pAreasMl);

        std::string id;
        m_pCmrImpl->tryGetValInMetadata(id, pAreasMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_ID);
        pAreasMl->setId(id);
        int ver;
        bool isNewVersion = pAreasMl->metadata().tryGet<int>(RPOS_COMPOSITEMAP_METADATA_KEY_VERSION,ver);  
        if (!isNewVersion)
        {
            pAreasMl->setType(RectangleAreaMapLayer::Type);
        }
        else if (ver > RectangleAreaMapLayer::Version)
        {
            //RectangleAreaMapLayer from future SDK, ignore this layer
            return pAreasMl;
        }   
        _u64 u64AreasEntryCnt;
        if (!m_pCmrImpl->tryGetValInMetadata(u64AreasEntryCnt, pAreasMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("AreaEntryCountOfRectangleAreaMapNotFoundInMetadata"); 
            return pAreasMl;
        }
        if (u64AreasEntryCnt > std::numeric_limits<size_t>::max())
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("AreaEntryCountOfRectangleAreaMapTooLargeForSizeT");
            return pAreasMl;
        } 
        pAreasMl->metadata().dict().erase(RPOS_COMPOSITEMAP_METADATA_KEY_COUNT);

        //
        const ubyte_buf_type& rcBody = procBodyInFileMaybeDecompress_(rcBodyInFile, eCmprsType);
        const char* pcTmp = (const char*)rcBody.data();
        const char* const pcEnd = pcTmp + rcBody.size();

        _u32 u32AreaId;
        float sX, sY, eX, eY, hW;

        auto& rAreas = pAreasMl->areas();
        rAreas.resize(static_cast<size_t>(u64AreasEntryCnt));
        for (size_t t = 0; t < static_cast<size_t>(u64AreasEntryCnt); ++t)
        {
            auto& rectArea = rAreas[t];

            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(u32AreaId, pcTmp, pcEnd, "ReadAreaIdOfRectangleAreaEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(sX, pcTmp, pcEnd, "ReadAreaXInRectangleAreaEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(sY, pcTmp, pcEnd, "ReadAreaYInRectangleAreaEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(eX, pcTmp, pcEnd, "ReadAreaWInRectangleAreaEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(eY, pcTmp, pcEnd, "ReadAreaHInRectangleAreaEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(hW, pcTmp, pcEnd, "ReadAreaHInRectangleAreaEntryFailed");

            rectArea.id = u32AreaId;
            rectArea.area = rpos_common::core::ORectangleF(rpos_common::core::Vector2f(sX, sY), rpos_common::core::Vector2f(eX, eY), hW);
            if (isNewVersion)
            {
                if (ver == RectangleAreaMapLayer::Version)
                {
                    _u32 usage;
                    pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(usage, pcTmp, pcEnd, "ReadTypeOfRectangleAreaEntryFailed");
                    rectArea.usage = (rpos_common::core::ArtifactUsage)usage;
                    pcTmp = (const char*)m_pCmrImpl->deserializeMetadataFromMem(rectArea.metadata, pcTmp, pcEnd);
                }
            }
            else //for slamware 2.8.1 and biguiyuan
            {
                RPOS_COMPOSITEMAP_THROW_EXCEPTION("LegacyRectangleAreaMapUnsupported");
            }
        } 
        return pAreasMl;
    }
 
    CompositeMapReaderImpl::MapLayerSharedPtr 
        CompositeMapReaderImpl::PolygonAreaLayerFactory::createMapLayerByMem(core::Metadata& rMetadata
        , const ubyte_buf_type& rcBodyInFile, CompressType eCmprsType)
    {
        std::shared_ptr<PolygonAreaMapLayer> pAreasMl(std::make_shared<PolygonAreaMapLayer>());
        pAreasMl->metadata().swap(rMetadata);
        refreshMapLayerProperties_(pAreasMl);

        _u64 u64AreasEntryCnt;
        if (!m_pCmrImpl->tryGetValInMetadata(u64AreasEntryCnt, pAreasMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("AreaEntryCountOfPolygonAreaMapNotFoundInMetadata"); 
            return pAreasMl;
        }
        if (u64AreasEntryCnt > std::numeric_limits<size_t>::max())
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("AreaEntryCountOfPolygonAreaMapTooLargeForSizeT");
            return pAreasMl;
        } 
        pAreasMl->metadata().dict().erase(RPOS_COMPOSITEMAP_METADATA_KEY_COUNT);

        const ubyte_buf_type& rcBody = procBodyInFileMaybeDecompress_(rcBodyInFile, eCmprsType);
        const char* pcTmp = (const char*)rcBody.data();
        const char* const pcEnd = pcTmp + rcBody.size();

        _u32 u32AreaId;
        _u8 u8Count;
        float pX, pY;

        core::Location tmpLocation;
        auto& rAreas = pAreasMl->areas();
        rAreas.resize(static_cast<size_t>(u64AreasEntryCnt));

        for (size_t t = 0; t < static_cast<size_t>(u64AreasEntryCnt); ++t)
        {
            auto& rectArea = rAreas[t];

            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(u32AreaId, pcTmp, pcEnd, "ReadAreaIdOfPolygonAreaEntryFailed");
            pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(u8Count, pcTmp, pcEnd, "ReadAreaCountInPolygonAreaEntryFailed");
            rectArea.id = u32AreaId;
            rectArea.count = u8Count;
            for ( _u8 c = 0; c < static_cast<_u8>(u8Count); c++ )
            {
                pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(pX, pcTmp, pcEnd, "ReadAreaPxInPolygonAreaEntryFailed");
                pcTmp = (const char*)m_pCmrImpl->deserializeNumValFromMem_T(pY, pcTmp, pcEnd, "ReadAreaPyInPolygonAreaEntryFailed");
                tmpLocation.x() = pX;
                tmpLocation.y() = pY;
                rectArea.locations.push_back(tmpLocation);
            }
            pcTmp = (const char*)m_pCmrImpl->deserializeMetadataFromMem(rectArea.metadata, pcTmp, pcEnd);
        }
        return pAreasMl;
    }
}}
