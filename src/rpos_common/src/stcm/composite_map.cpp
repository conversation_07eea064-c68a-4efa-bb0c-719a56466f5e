#include <stcm/composite_map.h>
#include <serialization/core_objects_serialization.h>
#include <core/metadata_internal.h>

namespace rpos_common { namespace stcm {

    CompositeMap::CompositeMap()
        : metadata_()
        , maps_()
    {}

    CompositeMap::CompositeMap(const CompositeMap& map)
        : metadata_(map.metadata())
        , maps_(map.maps())
    {}

    CompositeMap::CompositeMap(core::Metadata metadata, std::vector< std::shared_ptr<MapLayer> > maps)
        : metadata_(metadata)
        , maps_(maps)
    {}

    const core::Metadata& CompositeMap::metadata() const
    {
        return metadata_;
    }

    core::Metadata& CompositeMap::metadata()
    {
        return metadata_;
    }

    const std::vector< std::shared_ptr<MapLayer> >& CompositeMap::maps() const
    {
        return maps_;
    }

    std::vector< std::shared_ptr<MapLayer> >& CompositeMap::maps()
    {
        return maps_;
    }  

    bool CompositeMap::isMultiFloorMap() const
    { 
        std::string defaultMap;
        return metadata_.tryGet(RPOS_COMPOSITEMAP_METADATA_KEY_DEFAULT,defaultMap); 
    }

    bool CompositeMap::isMultiFloorMap(std::string& defaultMap) const
    {
        return metadata_.tryGet(RPOS_COMPOSITEMAP_METADATA_KEY_DEFAULT, defaultMap);
    }
    
    static bool isMapLayerMatched(std::shared_ptr<MapLayer> mapLayer, const std::map<std::string,std::string>& criteria)
    { 
        for (auto iter = criteria.begin(); iter != criteria.end(); ++iter)
        {
            std::string key = iter->first;
            std::string value;
            if (!mapLayer->metadata().tryGet(key,value) || value != iter->second)
            {
                return false;
            } 
        }
        return true;
    }

    std::vector< std::shared_ptr<rpos_common::stcm::MapLayer> > CompositeMap::filterMaps(const std::map<std::string,std::string>& criteria) const
    { 
        std::string defaultOrder;
        bool hasDefault = metadata_.tryGet(RPOS_COMPOSITEMAP_METADATA_KEY_DEFAULT, defaultOrder);  
        if (!hasDefault && criteria.empty()) //single floor map
        {
            return maps_;
        }

        std::vector<std::shared_ptr<rpos_common::stcm::MapLayer>> layers;
        for (auto iter = maps_.begin(); iter != maps_.end(); ++iter)
        {
            if (criteria.empty())
            {
                //no criteria, get default map
                std::string order;
                (*iter)->metadata().tryGet(RPOS_COMPOSITEMAP_METADATA_KEY_ORDER,order);
                if(defaultOrder == order)
                    layers.push_back(*iter);
            }
            else if (isMapLayerMatched(*iter,criteria))
            {
                layers.push_back(*iter); 
            }
        }
        return layers;
    }

}}