#include <stcm/composite_map_reader_impl.h>
#include <io/file_stream.h>
#include <system/string_utils.h> 
#include <core/metadata_internal.h> 
#include <set>

namespace rpos_common { namespace stcm {

    CompositeMapReader::CompositeMapReader(void)
        : m_pImpl(new CompositeMapReaderImpl())
    {
        //
    }
    CompositeMapReader::~CompositeMapReader(void)
    {
        delete m_pImpl;
        m_pImpl = NULL;
    }

    std::shared_ptr<CompositeMap> CompositeMapReader::loadFile(const std::string& rcFilePath)
    {
        auto ifs = std::make_shared<rpos_common::io::FileStream>();
        if (!ifs->open(rcFilePath, rpos_common::io::OpenFileModeRead))
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("failed to open input file");

        return m_pImpl->loadFromStream(*ifs);
    }
    std::shared_ptr<CompositeMap> CompositeMapReader::loadFile(const std::wstring& rcFilePath)
    {
        auto ifs = std::make_shared<rpos_common::io::FileStream>();
        if (!ifs->open(rcFilePath, rpos_common::io::OpenFileModeRead))
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("failed to open input file");

        return m_pImpl->loadFromStream(*ifs);
    }

    std::shared_ptr<CompositeMap> CompositeMapReader::loadStream(rpos_common::io::IStream& inStream)
    {
        return m_pImpl->loadFromStream(inStream);
    }  

    std::shared_ptr<CompositeMap> CompositeMapReader::mergeFiles(std::string& rErrMsg, std::vector<MapDescription>& maps)
    {
        std::shared_ptr<CompositeMap> result = std::make_shared<CompositeMap>();
        bool hasDefaultFloor = false;
        std::set<std::string> floors; 
        int order = 0;
        for (auto iter = maps.begin(); iter != maps.end(); ++iter, ++order)
        {
            std::string strOrder = rpos_common::system::to_string(order);
            if ((*iter).floor.empty())
            {
                rErrMsg = "Floor can not by empty";
                return nullptr;
            }
            std::string floorName =  (*iter).building + (*iter).floor;
            if (floors.find(floorName) != floors.end())
            {
                rErrMsg = "Duplicate floor number";
                return nullptr;
            }
            floors.insert(floorName);

            auto map = loadFile(rErrMsg,(*iter).filePath);
            if (!map)
            {
                return nullptr;
            }  
            if ((*iter).isDefaultFloor)
            {
                if (hasDefaultFloor)
                {
                    rErrMsg = "More than one default floor";
                    return nullptr;
                }
                hasDefaultFloor = true; 
                result->metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_DEFAULT, strOrder);   
            }
            for (auto layerIt = map->maps().begin(); layerIt!= map->maps().end(); ++layerIt)
            {
                auto mapLayer = *layerIt;
                mapLayer->metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_FLOOR,(*iter).floor);
                mapLayer->metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_BUILDING, (*iter).building); 
                mapLayer->metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_ORDER, strOrder); 
                if (!(*iter).isDefaultFloor && mapLayer->getUsage() == RPOS_COMPOSITEMAP_USAGE_EXPLORE)
                {
                    mapLayer->metadata().set<rpos_common::core::Vector3f>(RPOS_COMPOSITEMAP_METADATA_KEY_TRANSFORM,(*iter).transform);
                } 
                result->maps().push_back(mapLayer);
            }
        }
        if (!hasDefaultFloor)
        {
            rErrMsg = "There is no default floor"; 
            return nullptr;
        }  
        return result;
    } 

    std::shared_ptr<CompositeMap> CompositeMapReader::doLoadFromStream_(std::string& rErrMsg, rpos_common::io::IStream& inStream)
    {
        std::shared_ptr<CompositeMap> spRet;
        try
        {
            spRet = m_pImpl->loadFromStream(inStream);
            RPOS_COMPOSITEMAP_ASSERT(spRet);
            rErrMsg.clear();
        }
        catch (const rpos_common::system::ExceptionBase& rcRposExcp)
        {
            rErrMsg = rcRposExcp.toString();
        }
        catch (const std::exception& rcExcp)
        {
            rErrMsg = rcExcp.what();
        }
        catch (...)
        {
            rErrMsg = "Unknown Exception.";
        }
        return spRet;
    }

    std::shared_ptr<CompositeMap> CompositeMapReader::loadFile(std::string& rErrMsg, const std::string& rcFilePath)
    {
        auto ifs = std::make_shared<rpos_common::io::FileStream>();
        if (!ifs->open(rcFilePath, rpos_common::io::OpenFileModeRead))
        {
            rErrMsg = "failed to open input file";
            return nullptr;
        }
        return doLoadFromStream_(rErrMsg, *ifs);
    }
    std::shared_ptr<CompositeMap> CompositeMapReader::loadFile(std::string& rErrMsg, const std::wstring& rcFilePath)
    {
        auto ifs = std::make_shared<rpos_common::io::FileStream>();
        if (!ifs->open(rcFilePath, rpos_common::io::OpenFileModeRead))
        {
            rErrMsg = "failed to open input file";
            return nullptr;
        }
        return doLoadFromStream_(rErrMsg, *ifs);
    }
    
    std::shared_ptr<CompositeMap> CompositeMapReader::loadStream(std::string& rErrMsg, rpos_common::io::IStream& inStream)
    {
        return doLoadFromStream_(rErrMsg, inStream);
    }

}}
