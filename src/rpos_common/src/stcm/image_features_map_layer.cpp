#include <stcm/image_features_map_layer.h> 
#include <system/string_utils.h>

namespace rpos_common { namespace stcm {

    const char* const ImageFeaturesMapLayer::Type = "vnd.slamtec.map-layer/vnd.image-features-map+binary";

    ImageFeaturesMapLayer::ImageFeaturesMapLayer()
    {
        setFeatureType(FeatureTypeUnknown);
    }

    ImageFeaturesMapLayer::~ImageFeaturesMapLayer()
    {}

    void ImageFeaturesMapLayer::clear(void)
    {
        featureObs_.clear();
        this->MapLayer::clear();
    }

    FeatureType ImageFeaturesMapLayer::getFeatureType() const
    {
        return type_;
    }

    void ImageFeaturesMapLayer::setFeatureType(FeatureType type)
    {
        metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_FEATURE_TYPE, rpos_common::system::to_string((uint32_t)type)); 
        type_ = type;  
    }

    const std::vector<ImageFeaturesObservation>& ImageFeaturesMapLayer::featureObs() const
    {
        return featureObs_;
    }

    std::vector<ImageFeaturesObservation>& ImageFeaturesMapLayer::featureObs()
    {
        return featureObs_;
    }

}}