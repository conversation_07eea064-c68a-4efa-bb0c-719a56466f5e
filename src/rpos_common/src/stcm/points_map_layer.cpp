#include <stcm/points_map_layer.h>

namespace rpos_common { namespace stcm {

    const char* const PointsMapLayer::Type = "vnd.slamtec.map-layer/vnd.points-map+binary";

    PointsMapLayer::PointsMapLayer()
    {}

    PointsMapLayer::~PointsMapLayer()
    {}

    void PointsMapLayer::clear(void)
    {
        points_.clear();
        this->MapLayer::clear();
    }

    const std::vector<core::PointPDF>& PointsMapLayer::points() const
    {
        return points_;
    }

    std::vector<core::PointPDF>& PointsMapLayer::points()
    {
        return points_;
    }

}}