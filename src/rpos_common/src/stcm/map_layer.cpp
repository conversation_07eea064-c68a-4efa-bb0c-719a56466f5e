#include <stcm/map_layer.h>

namespace rpos_common { namespace stcm {

    MapLayer::~MapLayer()
    {
        //
    }

    void MapLayer::clear(void)
    {
        metadata_.clear();
        name_.clear();
        usage_.clear();
        type_.clear();
    }

    const core::Metadata& MapLayer::metadata() const
    {
        return metadata_;
    }

    core::Metadata& MapLayer::metadata()
    {
        return metadata_;
    }

    const std::string& MapLayer::getName() const
    {
        return name_;
    }
    void MapLayer::setName(const std::string& value)
    {
        metadata_.set(RPOS_COMPOSITEMAP_METADATA_KEY_NAME, value);
        name_ = value;
    }

    const std::string& MapLayer::getUsage() const
    {
        return usage_;
    }
    
    void MapLayer::setUsage(const std::string& value)
    {
        metadata_.set(RPOS_COMPOSITEMAP_METADATA_KEY_USAGE, value);
        usage_ = value;
    }
    
    const std::string& MapLayer::getType() const
    {
        return type_;
    }
    void MapLayer::setType(const std::string& value)
    {
        metadata_.set(RPOS_COMPOSITEMAP_METADATA_KEY_TYPE, value);
        type_ = value;
    }

    //////////////////////////////////////////////////////////////////////////

    UnknownMapLayer::~UnknownMapLayer()
    {
        //
    }

    void UnknownMapLayer::clear(void)
    {
        raw_body_.clear();
        this->MapLayer::clear();
    }

}}
