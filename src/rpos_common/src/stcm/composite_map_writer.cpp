#include <stcm/composite_map_writer_impl.h>
#include <io/file_stream.h>

namespace rpos_common { namespace stcm {

    CompositeMapWriter::CompositeMapWriter(void)
        : m_pImpl(new CompositeMapWriterImpl())
    {
        //
    }
    CompositeMapWriter::~CompositeMapWriter(void)
    {
        delete m_pImpl;
        m_pImpl = NULL;
    }

    void CompositeMapWriter::saveFile(const std::string& rcFilePath, const CompositeMap& rcCmpstMap)
    {
        auto ofs = std::make_shared<rpos_common::io::FileStream>();
        if (!ofs->open(rcFilePath, rpos_common::io::OpenFileModeWrite))
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("failed to open output file");

        m_pImpl->saveToStream(*ofs, rcCmpstMap);
    }
    void CompositeMapWriter::saveFile(const std::wstring& rcFilePath, const CompositeMap& rcCmpstMap)
    {
        auto ofs = std::make_shared<rpos_common::io::FileStream>();
        if (!ofs->open(rcFilePath, rpos_common::io::OpenFileModeWrite))
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("failed to open output file");

        m_pImpl->saveToStream(*ofs, rcCmpstMap);
    }

    void CompositeMapWriter::saveStream(rpos_common::io::IStream& outStream, const CompositeMap& rcCmpstMap)
    {
        m_pImpl->saveToStream(outStream, rcCmpstMap);
    }

    bool CompositeMapWriter::doSaveToStream_(std::string& rErrMsg, rpos_common::io::IStream& outStream, const CompositeMap& rcCmpstMap)
    {
        bool bRet = false;
        try
        {
            m_pImpl->saveToStream(outStream, rcCmpstMap);
            rErrMsg.clear();
            bRet = true;
        }
        catch (const rpos_common::system::ExceptionBase& rcRposExcp)
        {
            rErrMsg = rcRposExcp.toString();
        }
        catch (const std::exception& rcExcp)
        {
            rErrMsg = rcExcp.what();
        }
        catch (...)
        {
            rErrMsg = "Unknown Exception.";
        }
        return bRet;
    }

    bool CompositeMapWriter::saveFile(std::string& rErrMsg, const std::string& rcFilePath, const CompositeMap& rcCmpstMap)
    {
        auto ofs = std::make_shared<rpos_common::io::FileStream>();
        if (!ofs->open(rcFilePath, rpos_common::io::OpenFileModeWrite))
        {
            rErrMsg = "failed to open output file";
            return false;
        }
        return doSaveToStream_(rErrMsg, *ofs, rcCmpstMap);
    }
    
    bool CompositeMapWriter::saveFile(std::string& rErrMsg, const std::wstring& rcFilePath, const CompositeMap& rcCmpstMap)
    {
        auto ofs = std::make_shared<rpos_common::io::FileStream>();
        if (!ofs->open(rcFilePath, rpos_common::io::OpenFileModeWrite))
        {
            rErrMsg = "failed to open output file";
            return false;
        }
        return doSaveToStream_(rErrMsg, *ofs, rcCmpstMap);
    }

    bool CompositeMapWriter::saveStream(std::string& rErrMsg, rpos_common::io::IStream& outStream, const CompositeMap& rcCmpstMap)
    {
        return doSaveToStream_(rErrMsg, outStream, rcCmpstMap);
    }

}}
