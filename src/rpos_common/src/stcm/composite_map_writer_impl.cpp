#include <stcm/composite_map_writer_impl.h>
#include <core/rectangle_area.h>
#include <system/string_utils.h>
#include <system/types.h>
#include <sstream>

namespace rpos_common { namespace stcm {

    using namespace rpos_common::system;
    using namespace rpos_common::system::types;

    CompositeMapWriterImpl::MapSectionWriterBase::MapSectionWriterBase(CompositeMapWriterImpl* pCmwImpl)
        : m_pCmwImpl(pCmwImpl)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != m_pCmwImpl);
    }

#ifdef _WIN32
#   pragma warning(push)
#   pragma warning(disable: 4355)
#endif

    CompositeMapWriterImpl::CompositeMapWriterImpl(void)
        : m_tUnknowMlWr(this), m_tGridMlWr(this), m_tLineMlWr(this), m_tPoseMlWr(this), m_tPointsMlWr(this)
        , m_tImageFeaturesMlWr(this)
        , m_tRectangleAreaMlWr(this)
        , m_tPolygonAreaMlWr(this)
    {
        m_hmMlTypeToWr[GridMapLayer::Type] = &m_tGridMlWr;
        m_hmMlTypeToWr[LineMapLayer::Type] = &m_tLineMlWr;
        m_hmMlTypeToWr[PoseMapLayer::Type] = &m_tPoseMlWr;
        m_hmMlTypeToWr[PointsMapLayer::Type] = &m_tPointsMlWr;
        m_hmMlTypeToWr[ImageFeaturesMapLayer::Type] = &m_tImageFeaturesMlWr;
        m_hmMlTypeToWr[RectangleAreaMapLayer::Type] = &m_tRectangleAreaMlWr;
        m_hmMlTypeToWr[PolygonAreaMapLayer::Type] = &m_tPolygonAreaMlWr;
        //
        m_tMetadataBuf.reserve(1024);
        m_tMapBodyBuf.reserve(1024 * 8);
        m_tCmprsdMapBodyBuf.reserve(1024 * 8);
    }

#ifdef _WIN32
#   pragma warning(pop)
#endif

    CompositeMapWriterImpl::~CompositeMapWriterImpl(void)
    {
        //
    }
    
    void CompositeMapWriterImpl::saveToStream(rpos_common::io::IStream& outStream, const CompositeMap& rcCmpstMap)
    {
        if (!outStream.canWrite())
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("StreamNotWriteable");

        // FileHeader
        doSaveFileHeader_(outStream, rcCmpstMap);
        // MapSection
        const std::vector<MapLayerSharedPtr>& rcvpMapLayers = rcCmpstMap.maps();
        auto citMapLayerSpEnd = rcvpMapLayers.cend();
        auto citMapLayerSp = rcvpMapLayers.cbegin();
        for (; citMapLayerSpEnd != citMapLayerSp; ++citMapLayerSp)
        {
            doSaveMapSection_(outStream, (*citMapLayerSp).get());
        }
    }

    void CompositeMapWriterImpl::serializeMetadataAppToMem(ubyte_buf_type& rDest, const core::Metadata& rcMetadata)
    {
        const std::map<std::string, std::string> & rcDict = rcMetadata.dict();
        size_t szEntryCnt = rcDict.size();
        if (szEntryCnt <= C_METADATA_MAX_ENTRY_CNT)
        {
            {
                const size_t szOrigSize = rDest.size();
                rDest.resize(szOrigSize + sizeof(uint16_t));
                void* pTmp = rDest.data() + szOrigSize;
                pTmp = cpuToLeWriteToBuffer<uint16_t>(pTmp, static_cast<uint16_t>(szEntryCnt));
            }
            auto citEntryEnd = rcDict.cend();
            auto citEntry = rcDict.cbegin();
            for (; citEntryEnd != citEntry; ++citEntry)
            {
                serializeStrAppToMem(rDest, citEntry->first);
                serializeStrAppToMem(rDest, citEntry->second);
            }
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("MetadataTooManyEntries");
        }
    }
    void CompositeMapWriterImpl::serializeMetadataWithOverwriteKvAppToMem(ubyte_buf_type& rDest, const core::Metadata& rcMetadata, const std::string& rcOverwriteKey, const std::string& rcOverwriteVal)
    {
        const std::map<std::string, std::string> & rcDict = rcMetadata.dict();
        size_t szEntryCnt = rcDict.size();
        auto citEntryEnd = rcDict.cend();
        auto citSrcOverwriteKv = rcDict.find(rcOverwriteKey);
        if (citEntryEnd == citSrcOverwriteKv)
        {
            ++szEntryCnt;
        }
        //
        if (szEntryCnt <= C_METADATA_MAX_ENTRY_CNT)
        {
            {
                const size_t szOrigSize = rDest.size();
                rDest.resize(szOrigSize + sizeof(_u16));
                void* pTmp = rDest.data() + szOrigSize;
                pTmp = cpuToLeWriteToBuffer<_u16>(pTmp, static_cast<_u16>(szEntryCnt));
            }
            auto citEntry = rcDict.cbegin();
            if (citEntryEnd == citSrcOverwriteKv)
            {
                serializeStrAppToMem(rDest, rcOverwriteKey);
                serializeStrAppToMem(rDest, rcOverwriteVal);
                for (; citEntryEnd != citEntry; ++citEntry)
                {
                    serializeStrAppToMem(rDest, citEntry->first);
                    serializeStrAppToMem(rDest, citEntry->second);
                }
            }
            else
            {
                for (; citEntryEnd != citEntry; ++citEntry)
                {
                    if (citSrcOverwriteKv != citEntry)
                    {
                        serializeStrAppToMem(rDest, citEntry->first);
                        serializeStrAppToMem(rDest, citEntry->second);
                    }
                    else
                    {
                        serializeStrAppToMem(rDest, rcOverwriteKey);
                        serializeStrAppToMem(rDest, rcOverwriteVal);
                    }
                }
            }
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("MetadataTooManyEntries");
        }
    }

    void CompositeMapWriterImpl::doSaveFileHeader_(rpos_common::io::IStream& outStream, const CompositeMap& rcCmpstMap)
    {
        StcmFileHeader tFileHeader;
        initStcmFileHeader(tFileHeader); 
        tFileHeader.u16Ver = tFileHeader.u16MinReaderVer = tFileHeader.u16MinWriterVer = cpu_to_le(rcCmpstMap.isMultiFloorMap() ? C_MULTI_FLOOR_WRITER_VERSION : C_WRITER_VERSION);
        const size_t szSectionCnt = rcCmpstMap.maps().size();
        if (szSectionCnt <= C_MAPSECTION_MAX_CNT)
        {
            tFileHeader.uSectionCnt = cpu_to_le(static_cast<uint32_t>(szSectionCnt));
            m_tMetadataBuf.clear();
            serializeMetadataAppToMem(m_tMetadataBuf, rcCmpstMap.metadata());
            const size_t szHeaderSize = sizeof(tFileHeader) + m_tMetadataBuf.size();
            if (szHeaderSize <= C_MAX_STCM_FILE_HEADER_SIZE)
            {
                tFileHeader.u16HeaderSize = cpu_to_le(static_cast<uint16_t>(szHeaderSize));
                exactWrite(outStream, &tFileHeader, sizeof(tFileHeader), "WriteStcmFileHeaderFailed");
                exactWrite(outStream, m_tMetadataBuf.data(), m_tMetadataBuf.size(), "WriteStcmFileHeadMetadataFailed");
            }
            else
            {
                RPOS_COMPOSITEMAP_THROW_EXCEPTION("StcmFileHeaderSizeTooLarge");
            }
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("TooManyMapSections");
        }
    }

    CompositeMapWriterImpl::MapSectionWriterBase* CompositeMapWriterImpl::prepareMapSectionWriter_(const core::Metadata& rcMetadata)
    {
        auto citType = rcMetadata.dict().find(RPOS_COMPOSITEMAP_METADATA_KEY_TYPE);
        if (rcMetadata.dict().cend() != citType)
        {
            auto citWrHelp = m_hmMlTypeToWr.find(citType->second);
            if (m_hmMlTypeToWr.cend() != citWrHelp)
            {
                return citWrHelp->second;
            }
        }
        return &m_tUnknowMlWr;
    }
    void CompositeMapWriterImpl::doSaveMapSection_(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcMapLayer);
        MapSectionWriterBase* pSectionWr = prepareMapSectionWriter_(pcMapLayer->metadata());
        RPOS_COMPOSITEMAP_ASSERT(NULL != pSectionWr);
        pSectionWr->writeMapSection(outStream, pcMapLayer);
    }

    //////////////////////////////////////////////////////////////////////////

    void CompositeMapWriterImpl::MapSectionWriterBase::doWriteMapSection_(rpos_common::io::IStream& outStream
        , const ubyte_buf_type& rcMetadataBuf, const ubyte_buf_type& rcBodyBuf)
    {
        const size_t szMapSectionSize = sizeof(uint32_t) + rcMetadataBuf.size() + rcBodyBuf.size();
        if (szMapSectionSize <= C_MAX_MAPSECTION_SIZE)
        {
            uint32_t uTmp = cpu_to_le(static_cast<uint32_t>(szMapSectionSize));
            exactWrite(outStream, &uTmp, sizeof(uTmp), "WriteMapSectionSizeFailed");
            exactWrite(outStream, rcMetadataBuf.data(), rcMetadataBuf.size(), "WriteMapSectionMetadataFailed");
            exactWrite(outStream, rcBodyBuf.data(), rcBodyBuf.size(), "WriteMapBodyFailed");
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("MapSectionSizeTooLarge");
        }
    }

    const CompositeMapWriterImpl::ubyte_buf_type& 
        CompositeMapWriterImpl::MapSectionWriterBase::procBodyInMemMaybeCompress_(const ubyte_buf_type& rcBodyInMem, const core::Metadata& rcMetadata)
    {
        CompressType eCmprsType = m_pCmwImpl->getCompressType(rcMetadata);
        if (CMPRS_TYPE_NONE != eCmprsType)
        {
            m_pCmwImpl->m_tCmprsdMapBodyBuf.clear();
            m_pCmwImpl->doCompressAppend(eCmprsType, m_pCmwImpl->m_tCmprsdMapBodyBuf, rcBodyInMem.data(), rcBodyInMem.size());
            return m_pCmwImpl->m_tCmprsdMapBodyBuf;
        }
        return rcBodyInMem;
    }

    void CompositeMapWriterImpl::UnknownMapSectionWriter::writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcMapLayer);
        const UnknownMapLayer* pcUnknownMl = dynamic_cast< const UnknownMapLayer* >(pcMapLayer);
        if (BOOST_UNLIKELY(NULL == pcUnknownMl))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("DownCastToUnknownMapLayerFailed");
        }
        // Metadata
        m_pCmwImpl->m_tMetadataBuf.clear();
        m_pCmwImpl->serializeMetadataAppToMem(m_pCmwImpl->m_tMetadataBuf, pcUnknownMl->metadata());
        // Body
        const ubyte_buf_type& rcUnknownBody = pcUnknownMl->rawBody();
        // Write
        doWriteMapSection_(outStream, m_pCmwImpl->m_tMetadataBuf, rcUnknownBody);
    }

    void CompositeMapWriterImpl::GridMapSectionWriter::writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcMapLayer);
        const GridMapLayer* pcGridMl = dynamic_cast< const GridMapLayer* >(pcMapLayer);
        if (BOOST_UNLIKELY(NULL == pcGridMl))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("DownCastToGridMapLayerFailed");
        }
        // Metadata
        m_pCmwImpl->m_tMetadataBuf.clear();
        m_pCmwImpl->serializeMetadataAppToMem(m_pCmwImpl->m_tMetadataBuf, pcGridMl->metadata());
        // Body
        const ubyte_buf_type& rcBody = procBodyInMemMaybeCompress_(pcGridMl->mapData(), pcGridMl->metadata());
        // Write
        doWriteMapSection_(outStream, m_pCmwImpl->m_tMetadataBuf, rcBody);
    }

    void CompositeMapWriterImpl::LineMapSectionWriter::writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcMapLayer);
        const LineMapLayer* pcLineMl = dynamic_cast< const LineMapLayer* >(pcMapLayer);
        if (BOOST_UNLIKELY(NULL == pcLineMl))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("DownCastToLineMapLayerFailed");
        }
        // Metadata
        std::ostringstream oss;
        oss << pcLineMl->lines().size();
        std::string strCount = oss.str();
        m_pCmwImpl->m_tMetadataBuf.clear();
        m_pCmwImpl->serializeMetadataWithOverwriteKvAppToMem(m_pCmwImpl->m_tMetadataBuf, pcLineMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT, strCount);
        // Body
        m_pCmwImpl->m_tMapBodyBuf.clear();
        auto citLnEnd = pcLineMl->lines().cend();
        auto citLn = pcLineMl->lines().cbegin();
        for (; citLnEnd != citLn; ++citLn)
        {
            RPOS_COMPOSITEMAP_ASSERT(citLn->first == citLn->second.name);
            m_pCmwImpl->serializeStrAppToMem(m_pCmwImpl->m_tMapBodyBuf, citLn->second.name);
            {
                // double --> float ?
                size_t szTmpPos = m_pCmwImpl->m_tMapBodyBuf.size();
                assert(4 == sizeof(float));
                m_pCmwImpl->m_tMapBodyBuf.resize(szTmpPos + sizeof(float) * 4);
                void* pTmp = &m_pCmwImpl->m_tMapBodyBuf[szTmpPos];
                pTmp = cpuToLeWriteToBuffer<float>(pTmp, static_cast<float>(citLn->second.start.x()));
                pTmp = cpuToLeWriteToBuffer<float>(pTmp, static_cast<float>(citLn->second.start.y()));
                pTmp = cpuToLeWriteToBuffer<float>(pTmp, static_cast<float>(citLn->second.end.x()));
                pTmp = cpuToLeWriteToBuffer<float>(pTmp, static_cast<float>(citLn->second.end.y()));
            }
            m_pCmwImpl->serializeMetadataAppToMem(m_pCmwImpl->m_tMapBodyBuf, citLn->second.metadata);
        }
        const ubyte_buf_type& rcBody = procBodyInMemMaybeCompress_(m_pCmwImpl->m_tMapBodyBuf, pcLineMl->metadata());
        // Write
        doWriteMapSection_(outStream, m_pCmwImpl->m_tMetadataBuf, rcBody);
    }

    void CompositeMapWriterImpl::PoseMapSectionWriter::writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcMapLayer);
        const PoseMapLayer* pcPoseMl = dynamic_cast< const PoseMapLayer* >(pcMapLayer);
        if (BOOST_UNLIKELY(NULL == pcPoseMl))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("DownCastToPoseMapLayerFailed");
        }
        // Metadata
        std::string strCount = system::to_string(pcPoseMl->poses().size());
        m_pCmwImpl->m_tMetadataBuf.clear();
        m_pCmwImpl->serializeMetadataWithOverwriteKvAppToMem(m_pCmwImpl->m_tMetadataBuf, pcPoseMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT, strCount);
        // Body
        m_pCmwImpl->m_tMapBodyBuf.clear();
        auto citPoseEnd = pcPoseMl->poses().cend();
        auto citPose = pcPoseMl->poses().cbegin();
        for (; citPoseEnd != citPose; ++citPose)
        {
            RPOS_COMPOSITEMAP_ASSERT(citPose->first == citPose->second.name);
            m_pCmwImpl->serializeStrAppToMem(m_pCmwImpl->m_tMapBodyBuf, citPose->second.id);
            uint8_t u8TagCnt = 0; //for compatibility
            m_pCmwImpl->m_tMapBodyBuf.push_back(u8TagCnt); 
            {
                size_t szTmpPos = m_pCmwImpl->m_tMapBodyBuf.size();
                m_pCmwImpl->m_tMapBodyBuf.resize(szTmpPos + sizeof(float) * 3);
                void *pTmpPose = &m_pCmwImpl->m_tMapBodyBuf[szTmpPos];
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citPose->second.pose.x()));
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citPose->second.pose.y()));
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citPose->second.pose.yaw()));
            }
            m_pCmwImpl->m_tMapBodyBuf.push_back(citPose->second.flags);
            m_pCmwImpl->serializeMetadataAppToMem(m_pCmwImpl->m_tMapBodyBuf, citPose->second.metadata);
        }
        const ubyte_buf_type& rcBody = procBodyInMemMaybeCompress_(m_pCmwImpl->m_tMapBodyBuf, pcPoseMl->metadata());
        // Write
        doWriteMapSection_(outStream, m_pCmwImpl->m_tMetadataBuf, rcBody);
    }

    void CompositeMapWriterImpl::PointsMapSectionWriter::writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcMapLayer);
        const PointsMapLayer* pcPointsMl = dynamic_cast< const PointsMapLayer* >(pcMapLayer);
        if (BOOST_UNLIKELY(NULL == pcPointsMl))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("DownCastToPointsMapLayerFailed");
        }
        // Metadata
        std::string strCount = system::to_string(pcPointsMl->points().size());
        m_pCmwImpl->m_tMetadataBuf.clear();
        m_pCmwImpl->serializeMetadataWithOverwriteKvAppToMem(m_pCmwImpl->m_tMetadataBuf, pcPointsMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT, strCount);
        // Body
        m_pCmwImpl->m_tMapBodyBuf.clear();
        auto citPointsEnd = pcPointsMl->points().cend();
        auto citPoint = pcPointsMl->points().cbegin();
        for (; citPointsEnd != citPoint; ++citPoint)
        {
            { //id
                size_t szTmpPos = m_pCmwImpl->m_tMapBodyBuf.size();
                m_pCmwImpl->m_tMapBodyBuf.resize(szTmpPos + sizeof(uint32_t));
                void* pTmpPose = &m_pCmwImpl->m_tMapBodyBuf[szTmpPos];
                pTmpPose = cpuToLeWriteToBuffer<uint32_t>(pTmpPose, static_cast<uint32_t>(citPoint->id));
            }
            { //location, circular_error_probability
                size_t szTmpPos = m_pCmwImpl->m_tMapBodyBuf.size();
                m_pCmwImpl->m_tMapBodyBuf.resize(szTmpPos + sizeof(float) * 3);
                void* pTmpPose = &m_pCmwImpl->m_tMapBodyBuf[szTmpPos];
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citPoint->location.x()));
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citPoint->location.y()));
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citPoint->circular_error_probability));
            }
            {// std::vector<std::string> tags;
                if (citPoint->tags.size() <= CompositeMapRwImplBase::C_MAX_TAG_CNT_IN_POINTPDF_TAG_ENTRY)
                {
                    uint8_t u8TagCnt = static_cast<uint8_t>(citPoint->tags.size());
                    m_pCmwImpl->m_tMapBodyBuf.push_back(u8TagCnt);

                    auto citTagEnd = citPoint->tags.cend();
                    auto citTag = citPoint->tags.cbegin();
                    for (; citTagEnd != citTag; ++citTag)
                    {
                        m_pCmwImpl->serializeStrAppToMem(m_pCmwImpl->m_tMapBodyBuf, *citTag);
                    }
                }
                else
                {
                    RPOS_COMPOSITEMAP_THROW_EXCEPTION("TooManyTagsInOnePointPdfEntry");
                }
            }
        }
        const ubyte_buf_type& rcBody = procBodyInMemMaybeCompress_(m_pCmwImpl->m_tMapBodyBuf, pcPointsMl->metadata());
        // Write
        doWriteMapSection_(outStream, m_pCmwImpl->m_tMetadataBuf, rcBody);
    }

    void CompositeMapWriterImpl::ImageFeaturesMapSectionWriter::writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcMapLayer);
        const ImageFeaturesMapLayer* pcImageFeaturesMl = dynamic_cast<const ImageFeaturesMapLayer*>(pcMapLayer);
        if (BOOST_UNLIKELY(NULL == pcImageFeaturesMl))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("DownCastToPointsMapLayerFailed");
        }
        // Metadata
        std::string strCount = system::to_string(pcImageFeaturesMl->featureObs().size());
        m_pCmwImpl->m_tMetadataBuf.clear();
        m_pCmwImpl->serializeMetadataWithOverwriteKvAppToMem(m_pCmwImpl->m_tMetadataBuf, pcImageFeaturesMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT, strCount);
        // Body
        m_pCmwImpl->m_tMapBodyBuf.clear();
        auto citFeatureObsEnd = pcImageFeaturesMl->featureObs().cend();
        auto citFeatureObs = pcImageFeaturesMl->featureObs().cbegin();
        for (; citFeatureObsEnd != citFeatureObs; ++citFeatureObs)
        {
            { //camera pose
                size_t szTmpPos = m_pCmwImpl->m_tMapBodyBuf.size();
                m_pCmwImpl->m_tMapBodyBuf.resize(szTmpPos + sizeof(float) * 6 + sizeof(int));
                void* pTmpPose = &m_pCmwImpl->m_tMapBodyBuf[szTmpPos];
                pTmpPose = cpuToLeWriteToBuffer<int>(pTmpPose, citFeatureObs->ID);
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citFeatureObs->cameraPose.x()));
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citFeatureObs->cameraPose.y()));
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citFeatureObs->cameraPose.z()));
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citFeatureObs->cameraPose.yaw()));
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citFeatureObs->cameraPose.pitch()));
                pTmpPose = cpuToLeWriteToBuffer<float>(pTmpPose, static_cast<float>(citFeatureObs->cameraPose.roll()));
            }

            {// features;
                if (citFeatureObs->features.size() <= std::numeric_limits<uint32_t>::max())
                {
                    uint32_t u32FeaturesCnt = static_cast<uint32_t>(citFeatureObs->features.size());
                    size_t featureTotalSize = sizeof(uint32_t) + u32FeaturesCnt;

                    size_t szTmpPos = m_pCmwImpl->m_tMapBodyBuf.size();
                    m_pCmwImpl->m_tMapBodyBuf.resize(szTmpPos + featureTotalSize);
                    void* pTmpPose = &m_pCmwImpl->m_tMapBodyBuf[szTmpPos];

                    pTmpPose = cpuToLeWriteToBuffer<uint32_t>(pTmpPose, u32FeaturesCnt);
                    memcpy(pTmpPose,&citFeatureObs->features[0],u32FeaturesCnt);
                    pTmpPose = static_cast<char*>(pTmpPose) + u32FeaturesCnt;
                }
                else
                {
                    RPOS_COMPOSITEMAP_THROW_EXCEPTION("TooManyFeaturesInOneFeaturesObservationEntry");
                }
            }
        }
        const ubyte_buf_type& rcBody = procBodyInMemMaybeCompress_(m_pCmwImpl->m_tMapBodyBuf, pcImageFeaturesMl->metadata());
        // Write
        doWriteMapSection_(outStream, m_pCmwImpl->m_tMetadataBuf, rcBody);
    }

    void CompositeMapWriterImpl::RectangleAreaMapSectionWriter::writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcMapLayer);
        const RectangleAreaMapLayer* pcAreasMl = dynamic_cast< const RectangleAreaMapLayer* >(pcMapLayer);
        if (BOOST_UNLIKELY(NULL == pcAreasMl))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("DownCastToRectangleAreaMapLayerFailed");
        }
        // Metadata
        std::string strCount = system::to_string(pcAreasMl->areas().size());
        m_pCmwImpl->m_tMetadataBuf.clear();
        m_pCmwImpl->serializeMetadataWithOverwriteKvAppToMem(m_pCmwImpl->m_tMetadataBuf, pcAreasMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT, strCount);
        // Body
        m_pCmwImpl->m_tMapBodyBuf.clear();
        auto citAreaEnd = pcAreasMl->areas().cend();
        auto citArea = pcAreasMl->areas().cbegin();
        for (; citAreaEnd != citArea; ++citArea)
        {
            const core::RectangleArea& rectArea = *citArea;
           
            size_t szTmpPos = m_pCmwImpl->m_tMapBodyBuf.size();
            m_pCmwImpl->m_tMapBodyBuf.resize(szTmpPos + sizeof(float) * 5 + sizeof(uint32_t)*2); 
            void* pTmpPos = &m_pCmwImpl->m_tMapBodyBuf[szTmpPos];
            //id
            pTmpPos = cpuToLeWriteToBuffer<uint32_t>(pTmpPos, static_cast<uint32_t>(rectArea.id));  
            //sx, sy, ex, ey, halfwidth, usage
            pTmpPos = cpuToLeWriteToBuffer<float>(pTmpPos, static_cast<float>(rectArea.area.start().x()));
            pTmpPos = cpuToLeWriteToBuffer<float>(pTmpPos, static_cast<float>(rectArea.area.start().y()));
            pTmpPos = cpuToLeWriteToBuffer<float>(pTmpPos, static_cast<float>(rectArea.area.end().x()));
            pTmpPos = cpuToLeWriteToBuffer<float>(pTmpPos, static_cast<float>(rectArea.area.end().y()));
            pTmpPos = cpuToLeWriteToBuffer<float>(pTmpPos, static_cast<float>(rectArea.area.halfWidth()));
            pTmpPos = cpuToLeWriteToBuffer<uint32_t>(pTmpPos, static_cast<uint32_t>(rectArea.usage)); 

            m_pCmwImpl->serializeMetadataAppToMem(m_pCmwImpl->m_tMapBodyBuf, rectArea.metadata); 
        }
        const ubyte_buf_type& rcBody = procBodyInMemMaybeCompress_(m_pCmwImpl->m_tMapBodyBuf, pcAreasMl->metadata());
        // Write
        doWriteMapSection_(outStream, m_pCmwImpl->m_tMetadataBuf, rcBody);
    }

    void CompositeMapWriterImpl::PolygonAreaLayerSectionWriter::writeMapSection(rpos_common::io::IStream& outStream, const MapLayer* pcMapLayer)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcMapLayer);
        const PolygonAreaMapLayer* pcAreasMl = dynamic_cast<const PolygonAreaMapLayer*>(pcMapLayer);
        if (BOOST_UNLIKELY(NULL == pcAreasMl))
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("DownCastToPolygonAreaMapLayerFailed");
        }
        // Metadata
        std::string strCount = system::to_string(pcAreasMl->areas().size());
        m_pCmwImpl->m_tMetadataBuf.clear();
        m_pCmwImpl->serializeMetadataWithOverwriteKvAppToMem(m_pCmwImpl->m_tMetadataBuf, pcAreasMl->metadata(), RPOS_COMPOSITEMAP_METADATA_KEY_COUNT, strCount);
        // Body
        m_pCmwImpl->m_tMapBodyBuf.clear();
        auto citAreaEnd = pcAreasMl->areas().cend();
        auto citArea = pcAreasMl->areas().cbegin();
        for (; citAreaEnd != citArea; ++citArea)
        {
            const auto& rectArea = *citArea;

            size_t szTmpPos = m_pCmwImpl->m_tMapBodyBuf.size();
            m_pCmwImpl->m_tMapBodyBuf.resize(szTmpPos + sizeof(float) * rectArea.locations.size() * 2 + sizeof(uint32_t) + sizeof(uint8_t));
            void* pTmpPos = &m_pCmwImpl->m_tMapBodyBuf[szTmpPos];
            //id
            pTmpPos = cpuToLeWriteToBuffer<uint32_t>(pTmpPos, static_cast<uint32_t>(rectArea.id));
            pTmpPos = cpuToLeWriteToBuffer<uint8_t>(pTmpPos, static_cast<uint8_t>(rectArea.count));
            for (uint8_t c = 0; c < static_cast<uint8_t>(rectArea.count); c++) {
                const auto& location = rectArea.locations[c];
                pTmpPos = cpuToLeWriteToBuffer<float>(pTmpPos, static_cast<float>(location.x()));
                pTmpPos = cpuToLeWriteToBuffer<float>(pTmpPos, static_cast<float>(location.y()));
            }

            m_pCmwImpl->serializeMetadataAppToMem(m_pCmwImpl->m_tMapBodyBuf, rectArea.metadata);
        }
        const ubyte_buf_type& rcBody = procBodyInMemMaybeCompress_(m_pCmwImpl->m_tMapBodyBuf, pcAreasMl->metadata());
        // Write
        doWriteMapSection_(outStream, m_pCmwImpl->m_tMetadataBuf, rcBody);
    }

}}
