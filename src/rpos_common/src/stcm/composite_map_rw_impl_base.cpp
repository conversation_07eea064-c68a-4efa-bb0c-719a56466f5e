
#include <limits>
#include <stcm/composite_map_rw_impl_base.h>
#include <system/types.h>
#include <rle.h>

using namespace rpos_common::system;
using namespace rpos_common::system::types;

namespace rpos_common { namespace stcm {

    const _u8 CompositeMapRwImplBase::s_tStcmSignature[C_STCM_FILE_SIGNATURE_SIZE] = {'S', 'T', 'C', 'M'};

    void CompositeMapRwImplBase::exactRead(void* pDest, size_t szExactLen, rpos_common::io::IStream& inStream, const char* pcErrMsg)
    {
        if (0 != szExactLen)
        {
            assert(szExactLen <= static_cast<size_t>((std::numeric_limits<int>::max)()));
            const int readBytes = inStream.read(pDest, szExactLen);
            if (static_cast<int>(szExactLen) != readBytes)
                RPOS_COMPOSITEMAP_THROW_EXCEPTION(pcErrMsg);
        }
    }

    void CompositeMapRwImplBase::exactWrite(rpos_common::io::IStream& outStream, const void* pcSrc, size_t szExactLen, const char* pcErrMsg)
    {
        if (0 != szExactLen)
        {
            assert(szExactLen <= static_cast<size_t>((std::numeric_limits<int>::max)()));
            const int writtenBytes = outStream.write(pcSrc, szExactLen);
            if (static_cast<int>(szExactLen) != writtenBytes)
                RPOS_COMPOSITEMAP_THROW_EXCEPTION(pcErrMsg);
        }
    }

    void CompositeMapRwImplBase::initStcmFileHeader(StcmFileHeader& rHeader)
    {
        ::memset(&rHeader, 0, sizeof(rHeader));
        ::memcpy(rHeader.tSignature, s_tStcmSignature, C_STCM_FILE_SIGNATURE_SIZE);
    }
    bool CompositeMapRwImplBase::checkStcmSignature(const rpos_common::system::types::_u8 arrSignature[C_STCM_FILE_SIGNATURE_SIZE])
    {
        return 0 == ::memcmp(s_tStcmSignature, arrSignature, C_STCM_FILE_SIGNATURE_SIZE);
    }

    CompositeMapRwImplBase::CompressType CompositeMapRwImplBase::getCompressType(const core::Metadata& rcMetadata)
    {
        const std::map<std::string, std::string>& rcDict = rcMetadata.dict();
        auto citTmp = rcDict.find(RPOS_COMPOSITEMAP_METADATA_KEY_COMPRESSION);
        if (rcDict.cend() != citTmp)
        {
            const std::string& rcStrCmprsType = citTmp->second;
            if (rcStrCmprsType == "no_compression" || rcStrCmprsType == "")
            {
                return CMPRS_TYPE_NONE;
            }
            else if (rcStrCmprsType == "rle")
            {
                return CMPRS_TYPE_RLE;
            }
            return CMPRS_TYPE_UNKNOWN;
        }
        return CMPRS_TYPE_NONE;
    }
    void CompositeMapRwImplBase::doCompressAppend(CompressType eCmprsType, ubyte_buf_type& rDest, const void* pcSrc, size_t szSrcLen)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcSrc);
        switch (eCmprsType)
        {
        case CMPRS_TYPE_RLE:
            {
                RPOS_COMPOSITEMAP_ASSERT(szSrcLen + 16U < (std::numeric_limits<_u32>::max)());
                const size_t szDestOrigSize = rDest.size();
                _u32 uAppLen = static_cast<_u32>(szSrcLen) + 16;
                rDest.resize(szDestOrigSize + uAppLen);
                _u32 i = 0, j = 0;
                // What does "129, 127" mean?
                unsigned char sentinel1 = 129, sentinel2 = 127;
                int iRleRet = -1;
                while (-1 == (iRleRet = ::RLEEncode((unsigned char*)pcSrc, static_cast<_u32>(szSrcLen), &rDest[szDestOrigSize], &uAppLen, sentinel1, sentinel2, i, j)))
                {
                    uAppLen += 64;
                    rDest.resize(szDestOrigSize + uAppLen);
                }
                if (0 == iRleRet)
                    rDest.resize(szDestOrigSize + uAppLen);
                else
                    RPOS_COMPOSITEMAP_THROW_EXCEPTION("failed to RLEEncode()");
            }
            break;
        default:
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("CompressTypeNotSupported");
            break;
        }
    }
    void CompositeMapRwImplBase::doDecompressAppend(CompressType eCmprsType, ubyte_buf_type& rDest, const void* pcSrc, size_t szSrcLen)
    {
        RPOS_COMPOSITEMAP_ASSERT(NULL != pcSrc);
        switch (eCmprsType)
        {
        case CMPRS_TYPE_RLE:
            {
                RPOS_COMPOSITEMAP_ASSERT(szSrcLen < ((std::numeric_limits<_u32>::max)() / 2));
                const size_t szDestOrigSize = rDest.size();
                _u32 uAppLen = (std::max)(static_cast<_u32>(szSrcLen * 2), 16U);
                rDest.resize(szDestOrigSize + uAppLen);
                int iRleRet = -1;
                while (1 == (iRleRet = ::RLEDecode((unsigned char*)pcSrc, static_cast<_u32>(szSrcLen), &rDest[szDestOrigSize], &uAppLen)))
                {
                    rDest.resize(szDestOrigSize + uAppLen);
                }
                if (0 == iRleRet)
                    rDest.resize(szDestOrigSize + uAppLen);
                else
                    RPOS_COMPOSITEMAP_THROW_EXCEPTION("failed to RLEDecode()");
            }
            break;
        default:
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("DecompressTypeNotSupported");
            break;
        }
    }

    void CompositeMapRwImplBase::serializeStrAppToMem(ubyte_buf_type& rDest, const std::string& rcSrc)
    {
        if (rcSrc.length() <= C_MAX_UTF8_STRING_LEN)
        {
            const size_t szOrigSize = rDest.size();
            rDest.resize(szOrigSize + sizeof(stcm_file_str_len_type) + rcSrc.length());
            void* pTmp = rDest.data() + szOrigSize;
			pTmp = cpuToLeWriteToBuffer<stcm_file_str_len_type>(pTmp, static_cast<stcm_file_str_len_type>(rcSrc.length()));
			::memcpy(pTmp, rcSrc.c_str(), rcSrc.length());
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("Utf8StringTooLong");
        }
    }
    const void* CompositeMapRwImplBase::deserializeStrFromMem(std::string& rDest, const void* pcSrcBegin, const void* pcSrcEnd)
    {
        RPOS_COMPOSITEMAP_ASSERT(pcSrcBegin <= pcSrcEnd);
        const char* pcTmp = (const char*)pcSrcBegin;
        const char* const pcEnd = (const char*)pcSrcEnd;
        if (pcTmp + sizeof(stcm_file_str_len_type) <= pcEnd)
        {
            stcm_file_str_len_type tTmpLen;
			pcTmp = (const char*)leToCpuReadFromBuffer<stcm_file_str_len_type>(tTmpLen, pcTmp);
            RPOS_COMPOSITEMAP_ASSERT(tTmpLen <= C_MAX_UTF8_STRING_LEN);
            if (pcTmp + tTmpLen <= pcEnd)
            {
                rDest.assign(pcTmp, tTmpLen);
                pcTmp += tTmpLen;
            }
            else
            {
                RPOS_COMPOSITEMAP_THROW_EXCEPTION("ReadUtf8StringContentFailed");
            }
        }
        else
        {
            RPOS_COMPOSITEMAP_THROW_EXCEPTION("ReadUtf8StringLenFailed");
        }
        return pcTmp;
    }

    void CompositeMapRwImplBase::deserializeStrFromStream(std::string& rDest, rpos_common::io::IStream& inStream)
    {
        stcm_file_str_len_type tTmpLen;
        exactRead(&tTmpLen, sizeof(tTmpLen), inStream, "ReadUtf8StringLenFailed");
        tTmpLen = le_to_cpu(tTmpLen);
        RPOS_COMPOSITEMAP_ASSERT(tTmpLen <= C_MAX_UTF8_STRING_LEN);
        rDest.resize(tTmpLen);
        if (0 != tTmpLen)
            exactRead(&rDest[0], tTmpLen, inStream, "ReadUtf8StringContentFailed");
    }

}}
