#include <stcm/line_map_layer.h>

namespace rpos_common { namespace stcm {

    const char* const LineMapLayer::Type = "vnd.slamtec.map-layer/vnd.line-map+binary";

    void LineMapLayer::clear(void)
    {
        lines_.clear();
        this->MapLayer::clear();
    }

    const std::map<std::string, Line>& LineMapLayer::lines() const
    {
        return lines_;
    }

    std::map<std::string, Line>& LineMapLayer::lines()
    {
        return lines_;
    }

}}
