#include <stcm/grid_map_layer.h>
#include <system/string_utils.h>
 
namespace rpos_common { namespace stcm {

    const char* const GridMapLayer::Type = "vnd.slamtec.map-layer/vnd.grid-map+binary";

    void GridMapLayer::clear(void)
    {
        origin_ = core::Location();
        dimension_ = core::Vector2i();
        resolution_ = core::Vector2f();
        mapData_.clear();
        this->MapLayer::clear();
    }

    const core::Location& GridMapLayer::getOrigin() const
    {
        return origin_;
    }
    void GridMapLayer::setOrigin(const core::Location& rcSrc)
    {
        metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_ORIGIN_X, rpos_common::system::to_string(rcSrc.x()));
        metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_ORIGIN_Y, rpos_common::system::to_string(rcSrc.y()));
        // what about "origin_z" ?
        origin_ = rcSrc;
    }

    const core::Vector2i& GridMapLayer::getDimension() const
    {
        return dimension_;
    }
    void GridMapLayer::setDimension(const core::Vector2i& rcSrc)
    {
        metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_DIMENSION_WIDTH, rpos_common::system::to_string(rcSrc.x()));
        metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_DIMENSION_HEIGHT, rpos_common::system::to_string(rcSrc.y()));
        dimension_ = rcSrc;
    }

    const core::Vector2f& GridMapLayer::getResolution() const
    {
        return resolution_;
    }
    void GridMapLayer::setResolution(const core::Vector2f& rcSrc)
    {
        metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_RESOLUTION_X, rpos_common::system::to_string(rcSrc.x()));
        metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_RESOLUTION_Y, rpos_common::system::to_string(rcSrc.y()));
        resolution_ = rcSrc;
    }

    const std::vector<uint8_t>& GridMapLayer::mapData() const
    {
        return mapData_;
    }

    std::vector<uint8_t>& GridMapLayer::mapData()
    {
        return mapData_;
    }

}}
