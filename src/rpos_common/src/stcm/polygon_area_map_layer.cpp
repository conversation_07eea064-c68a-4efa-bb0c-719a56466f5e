
#include <stcm/polygon_area_map_layer.h>

namespace rpos_common { namespace stcm {

    const char* const PolygonAreaMapLayer::Type = "vnd.slamtec.map-layer/vnd.polygon-map+binary";

    PolygonAreaMapLayer::PolygonAreaMapLayer()
    {
    }

    PolygonAreaMapLayer::~PolygonAreaMapLayer()
    {
        //
    }

    void PolygonAreaMapLayer::clear(void)
    {
        areas_.clear();
        this->MapLayer::clear();
    }

    const std::vector<polygonArea> & PolygonAreaMapLayer::areas() const
    {
        return areas_;
    }

    std::vector<polygonArea> & PolygonAreaMapLayer::areas()
    {
        return areas_;
    }

}}
