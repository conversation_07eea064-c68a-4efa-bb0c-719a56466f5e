# 充电桩可视化功能说明

## 功能概述

修改后的 `home_detect_node` 现在支持发布充电桩的简化可视化，使用 `visualization_msgs::msg::MarkerArray` 在 RViz 中显示充电桩的关键信息。

## 新增功能

### 1. 可视化发布者

- **Topic**: `/dock_visualization`
- **消息类型**: `visualization_msgs::msg::MarkerArray`
- **坐标系**: `map` 坐标系

### 2. 可视化内容

充电桩可视化包含以下元素：

1. **homeLine** (红色线条)
   - 显示充电桩检测到的前边缘线
   - 连接 `home.homeLine.line.start` 和 `home.homeLine.line.end`

2. **中心点** (蓝色球体)
   - 显示充电桩的中心位置
   - 用于定位参考

3. **朝向箭头** (黄色箭头)
   - 显示充电桩的朝向
   - 指示充电桩的正面方向

## 使用方法

### 1. 启动节点

```bash
# 启动 home_detect_node
ros2 run algorithm_utils home_detect_node
```

### 2. 在 RViz 中查看

1. 启动 RViz
2. 添加 MarkerArray 显示类型
3. 设置 Topic 为 `/dock_visualization`
4. 确保坐标系设置为 `map`

### 3. 触发检测

使用 action client 发送检测请求：

```bash
# 使用 Python 测试脚本
python3 detect_home_test.py
```

或者使用命令行：

```bash
# 发送检测请求
ros2 action send_goal /detect_home interfaces/action/DetectHome "{expected_home_pose: {position: {x: 1.0, y: 0.0, z: 0.0}, orientation: {x: 0.0, y: 0.0, z: 0.9903, w: 0.1392}}, timeout_ms: 10000}"
```

## 可视化效果

当检测到充电桩时，RViz 中将显示：

```
    ─────────────────────────────  ← homeLine (红色线条)
    
    ● 中心点 (蓝色球体)
    → 朝向箭头 (黄色箭头)
```

## 数据来源

可视化数据直接来自 `RpHome` 对象：

- **homeLine**: `home.homeLine.line.start` 和 `home.homeLine.line.end`
- **中心点**: `home.getHomeCentralPoint()`
- **朝向**: `home.getHomeHeading()`

## 调试信息

节点会输出以下调试信息：

```
[INFO] [home_detect_action_node]: Published dock visualization with center at (1.234, 0.567), heading: 2.860
```

## 注意事项

1. 确保 `map` 坐标系正确设置
2. 可视化只在检测成功时发布
3. 每次检测成功会更新可视化内容
4. 可视化使用绝对坐标，基于 `map` 坐标系
5. 简化后的可视化只显示关键信息，更加清晰简洁

## 故障排除

如果看不到可视化：

1. 检查 RViz 中的 Topic 设置
2. 确认坐标系为 `map`
3. 检查是否有检测成功的日志输出
4. 使用 `ros2 topic echo /dock_visualization` 确认消息发布 