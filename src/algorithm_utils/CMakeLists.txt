cmake_minimum_required(VERSION 3.8)
project(algorithm_utils)

# Find required packages
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(interfaces REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(rclcpp_action REQUIRED)
find_package(angles REQUIRED)
find_package(visualization_msgs REQUIRED)

# Add include directories
include_directories(
  include
  ${EIGEN3_INCLUDE_DIR}
)

# Create static library
add_library(${PROJECT_NAME} STATIC
  src/dbscan_cluster.cpp
  src/point_utils.cpp
  src/pose_utils.cpp
  src/rphoughhelper.cpp
)

# Create shelf detect executable
add_executable(shelf_detect_node
  src/shelf_detect/shelf_detect_node.cpp
)

# Create home detect executable
add_executable(home_detect_node
  src/home_detect/home_detect_node.cpp
  src/home_detect/home_detect_service.cpp
  src/home_detect/location_filter.cpp
  src/home_detect/i_home_detect_service.cpp
  src/home_detect/home_detection.cpp
)

# Specify dependencies for ament
ament_target_dependencies(${PROJECT_NAME}
  rclcpp
  interfaces
  geometry_msgs
  sensor_msgs
  std_msgs
  tf2
  tf2_geometry_msgs
  rcl_interfaces
)

ament_target_dependencies(shelf_detect_node
  rclcpp
  interfaces
  geometry_msgs
  sensor_msgs
  std_msgs
  tf2
  tf2_geometry_msgs
  rcl_interfaces
)

ament_target_dependencies(home_detect_node
  rclcpp
  rclcpp_action
  interfaces
  geometry_msgs
  sensor_msgs
  std_msgs
  tf2
  tf2_ros
  angles
  Eigen3
  visualization_msgs
)

target_link_libraries(${PROJECT_NAME}
  Eigen3::Eigen
)

target_link_libraries(shelf_detect_node ${PROJECT_NAME})
target_link_libraries(home_detect_node ${PROJECT_NAME} Eigen3::Eigen)

# Install rules
install(TARGETS ${PROJECT_NAME}
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

# Install shelf_detect_node executable
install(TARGETS shelf_detect_node
  DESTINATION lib/${PROJECT_NAME}
)

# Install home_detect_node executable
install(TARGETS home_detect_node
  DESTINATION lib/${PROJECT_NAME}
)

install(DIRECTORY include/
  DESTINATION include
)

# Install launch files
install(DIRECTORY launch/
  DESTINATION share/${PROJECT_NAME}/launch
  FILES_MATCHING PATTERN "*.py"
)

install(DIRECTORY config/
  DESTINATION share/${PROJECT_NAME}/config
)

ament_package() 