#include <algorithm> 
#include <memory>
#include <cmath>
#include <cfloat>
#include <vector>
#include <cassert>
#include "dbscan_cluster.h"

namespace rp{ namespace algorithm {

    DbscanCluster::DbscanCluster(const float &radius, const int &minPTs):
        radius_(radius), minPTs_(minPTs)
    {

    }

    DbscanCluster::~DbscanCluster()
    {

    }

    bool DbscanCluster::clusterAndGetResult(const std::vector<WeightedPose> &weightPoses, const int &clusterNum, std::vector<ClusterResult>& results)
    {
        auto size = weightPoses.size();
        std::vector<ClusterData> vctClusterData;
        for (auto i = 0; i < size; ++i)
        {
            ClusterData data(weightPoses[i], i);
            vctClusterData.push_back(data);
        }
    	std::shared_ptr<KDTree> kdTree = buildKDTree_(vctClusterData);
        if (kdTree == nullptr)
        {
        	destroyKDTree_(kdTree);
            return false;
        }

        std::vector<Cluster> clusters;
        cluster_(kdTree, vctClusterData, clusters);

        RCLCPP_DEBUG(rclcpp::get_logger("dbscan_cluster"), "clusters size : %zu", clusters.size());
        if (clusterNum > 0 && clusters.size() > clusterNum)
            clusters.resize(clusterNum);
        results.clear();

        for (auto iter = clusters.begin(); iter != clusters.end(); ++iter)
        {
            ClusterResult result;
            result.clusterData = iter->getClusterData();
            getMeanPoseAndRegion_(*iter, result.mean, result.region);
            results.push_back(result);
            RCLCPP_DEBUG(rclcpp::get_logger("dbscan_cluster"), "cluster size : %zu, cluster weight : %f, mean pose : (%f, %f, %f), region: (%f, %f, %f, %f)", 
                result.clusterData.size(), iter->getWeight(),
                result.mean.pose.x,  result.mean.pose.y, result.mean.pose.theta, 
                result.region.x, result.region.y, result.region.width, result.region.height);
        }

        destroyKDTree_(kdTree);
        if (!clusters.size())
        {
            return false;
        }
        return true;
    }

    void DbscanCluster::getMeanPoseAndRegion_(Cluster &cluster, WeightedPose &weightPose, RectangleF &region)
    {
        auto vctClusterData = cluster.getClusterData();
        double			X = 0, Y = 0, YAW = 0;
        double			ang, w, W = 0;
        double			W_yaw_R = 0, W_yaw_L = 0;
        double			yaw_R = 0, yaw_L = 0;
        auto min_x = FLT_MAX;
        auto min_y = FLT_MAX;
        auto max_x = -FLT_MAX;
        auto max_y = -FLT_MAX;
        for (auto it = vctClusterData.begin(); it != vctClusterData.end(); ++it)
        {
            auto weightPose = it->weightPose;
            if (weightPose.pose.x > max_x)
                max_x = weightPose.pose.x;
            else if (weightPose.pose.x < min_x)
                min_x = weightPose.pose.x;

            if (weightPose.pose.y > max_y)
                max_y = weightPose.pose.y;
            else if (weightPose.pose.y < min_y)
                min_y = weightPose.pose.y;

            w = exp(weightPose.weight);
            W += w;

            X += w * weightPose.pose.x;
            Y += w * weightPose.pose.y;

            ang =  weightPose.pose.theta;
            if (fabs(ang) > 0.5*M_PI)
            {
                if (ang < 0) ang = (M_2PI + ang);
                yaw_L += ang * w;
                W_yaw_L += w;
            }
            else
            {
                yaw_R += ang * w;
                W_yaw_R += w;
            }     
        }

        if (W == 0) {
            weightPose.weight = 0.f;
            weightPose.pose.x = 0.f;
            weightPose.pose.y = 0.f;
            weightPose.pose.theta = 0.f;
            return;
        }

        X /= W;
        Y /= W;

        if (W_yaw_L > 0)	yaw_L /= W_yaw_L;  // [0,2pi]
        if (W_yaw_R > 0)	yaw_R /= W_yaw_R;  // [-pi,pi]
        if (yaw_L > M_PI) yaw_L = yaw_L - M_2PI;

        YAW = ((yaw_L * W_yaw_L + yaw_R * W_yaw_R) / (W_yaw_L + W_yaw_R));
        // Constrain angle to [-pi, pi] range
        while (YAW > M_PI) YAW -= 2 * M_PI;
        while (YAW < -M_PI) YAW += 2 * M_PI;

        weightPose.weight = W;
        weightPose.pose.x = X;
        weightPose.pose.y = Y;
        weightPose.pose.theta = YAW;
        region.x = min_x;
        region.y = min_y;
        region.width = max_x - min_x;
        region.height = max_y - min_y;
    }

    void DbscanCluster::cluster_(std::shared_ptr<KDTree> tree, const std::vector<ClusterData> &vctClusterData, std::vector<Cluster> &clusters)
    {
        clusters.clear();
        if (vctClusterData.empty())
            return;
        std::vector<int> visited(vctClusterData.size(), ClusterStateUnclassfied);
        int cluster_id = 0;
        float sum_weight = 0.f;
        for (auto i = 0; i < vctClusterData.size(); i++)
        {
            if (visited[i] == ClusterStateUnclassfied)
            {
                std::vector<ClusterData> neighbors;
                findNeighborsWithinRadius_(tree, vctClusterData[i], radius_, neighbors);
                if (neighbors.size() >= minPTs_)
                {
                    visited[i] = cluster_id;
                    std::vector<ClusterData> cluster;
                                                sum_weight += exp(vctClusterData[i].weightPose.weight);
                    cluster.push_back(vctClusterData[i]);
                    for (auto j = 0; j < neighbors.size(); j++)
                    {
                        int index = neighbors[j].ID;
                        if (visited[index] == ClusterStateUnclassfied)
                        {
                            visited[index] = cluster_id;
                            sum_weight += exp(vctClusterData[index].weightPose.weight);
                            cluster.push_back(vctClusterData[index]);
                            std::vector<ClusterData> neighbor_neighbors;
                            findNeighborsWithinRadius_(tree, neighbors[j], radius_, neighbor_neighbors);
                            if (neighbor_neighbors.size() >= minPTs_)
                            {
                                for (int k = 0; k < neighbor_neighbors.size(); k++)
                                {
                                    int iindex = neighbor_neighbors[k].ID;
                                    if (visited[iindex] == ClusterStateUnclassfied || visited[iindex] == ClusterStateNoise)
                                    {
                                        if (visited[iindex] == ClusterStateUnclassfied)
                                        {
                                            neighbors.push_back(neighbor_neighbors[k]);
                                        }
                                        visited[iindex] = cluster_id;
                                        sum_weight += exp(vctClusterData[iindex].weightPose.weight);
                                        cluster.push_back(vctClusterData[iindex]);
                                    }
                                }
                            }
                        }
                        else if (visited[index] == ClusterStateNoise)
                        {
                            visited[index] = cluster_id;
                            sum_weight += exp(vctClusterData[index].weightPose.weight);
                            cluster.push_back(vctClusterData[index]);
                        }
                        neighbors.erase(neighbors.begin() + j);
                        j--;
                    }
                    if (cluster.size() >= minPTs_)
                    {
                        clusters.push_back(Cluster(cluster, sum_weight));
                        cluster_id++;
                    }
                    sum_weight = 0.f;
                }
                else
                    visited[i] = ClusterStateNoise;
            }
        }
        if(!clusters.empty())
            std::sort(clusters.begin(), clusters.end(), std::greater<Cluster>());
    }

    std::shared_ptr<KDTree> DbscanCluster::buildKDTree_(std::vector<ClusterData> vctData)
    {
        int N = vctData.size();
        std::shared_ptr<KDTree> kdTree = std::make_shared<KDTree>(N);
        assert(kdTree != nullptr);
        kdTree->root = buildKDNode_(vctData, 1, 0, N - 1);
        return kdTree;
    }

    std::shared_ptr<KDNode> DbscanCluster::buildKDNode_(std::vector<ClusterData> &vctData, int split, int low, int high)
    {
        std::shared_ptr<KDNode> node = nullptr;
        std::shared_ptr<KDNode> left = nullptr;
        std::shared_ptr<KDNode> right = nullptr;
        if (low > high)
        {
            return nullptr;
        }
        int middle = findMedianPoint_(vctData, split, low, high);
        node = std::make_shared<KDNode>();
        assert(node != nullptr);
        node->data = vctData[middle];
        node->split = split;
        split = !split;
        left = buildKDNode_(vctData, split, low, middle - 1);
        if (left)
        {
            node->lChild = left;
            left->parent = node;
        }
        right = buildKDNode_(vctData, split, middle + 1, high);
        if (right)
        {
            node->rChild = right;
            right->parent = node;
        }
        return node;
    }

    void DbscanCluster::destroyKDTree_(std::shared_ptr<KDTree> tree)
    {
        if (tree)
        {
            destroyKDNode_(tree->root);
            tree->root.reset();
            tree.reset();
        }
    }

    void DbscanCluster::destroyKDNode_(std::shared_ptr<KDNode> node)
    {
        if (node)
        {
            destroyKDNode_(node->lChild);
            node->lChild.reset();
            destroyKDNode_(node->rChild);
            node->rChild.reset();
			node.reset();
        }
    }

    int DbscanCluster::findMedianPoint_(std::vector<ClusterData> &vctData, int split, int low, int high)
    {
        return selectPoint_(vctData, split, low, high, (high - low) / 2 + 1);
    }

    void DbscanCluster::findNeighborsWithinRadius_(std::shared_ptr<KDTree>  tree, const ClusterData& data, const float& radius, std::vector<ClusterData>& neighbors)
    {
        neighbors.clear();
        findNeighbors_(tree->root, data, radius * radius, neighbors);
    }

    void DbscanCluster::findNeighbors_(std::shared_ptr<KDNode> node, const ClusterData& data, const float& radius_square, std::vector<ClusterData>& neighbors)
    {
        SearchStatus searchStatus = SearchStatusNone;
        float radius = std::sqrt(radius_square);
        if (node == nullptr)
            return;
        auto dis = getDistance_(node->data.weightPose, data.weightPose);
        if (dis < radius_square)
        {
            if(node->data.ID != data.ID)
                neighbors.push_back(node->data);
            searchStatus = SearchStatusLeftOrRight;
        }
        else
        {
            searchStatus = getSearchStatus_(node->data.weightPose, data.weightPose, radius, node->split);
        }
        if (node->lChild != nullptr && (searchStatus == SearchStatusLeft || searchStatus == SearchStatusLeftOrRight))
            findNeighbors_(node->lChild, data, radius_square, neighbors);
        if (node->rChild != nullptr && (searchStatus == SearchStatusRight || searchStatus == SearchStatusLeftOrRight))
            findNeighbors_(node->rChild, data, radius_square, neighbors);
    }

    int DbscanCluster::selectPoint_(std::vector<ClusterData> &vctData, int split, int low, int high, int middle)
    {
        if (low == high)
            return low;
        int pivot = partition_(vctData, split, low, high);
        int pivotIdx = pivot - low + 1;
        if (pivotIdx == middle)
            return pivot;
        else if (pivotIdx > middle)
            return selectPoint_(vctData, split, low, pivot - 1, middle);
        else
            return selectPoint_(vctData, split, pivot + 1, high, middle - pivotIdx);
    }

    int DbscanCluster::partition_(std::vector<ClusterData> &vctData, int split, int low, int high)
    {
        auto data = vctData[low];
        while (low < high)
        {
            while (low < high && (!split && (vctData[high].weightPose.pose.x >= data.weightPose.pose.x) ||
                split && (vctData[high].weightPose.pose.y >= data.weightPose.pose.y)))
                high--;
            vctData[low] = vctData[high];
            while (low < high && (!split && (vctData[low].weightPose.pose.x <= data.weightPose.pose.x) ||
                split && (vctData[low].weightPose.pose.y <= data.weightPose.pose.y)))
                low++;
            vctData[high] = vctData[low];
        }
        vctData[low] = data;
        return low;
    }

}}