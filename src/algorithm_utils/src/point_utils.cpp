#include "algorithm_utils/point_utils.h"

namespace rp { namespace algorithm {

float PointUtils::pointDistance(const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2)
{
    float dx = p1.x - p2.x;
    float dy = p1.y - p2.y;
    return sqrt(dx * dx + dy * dy);
}

geometry_msgs::msg::Point PointUtils::pointSubtract(const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2)
{
    geometry_msgs::msg::Point result;
    result.x = p1.x - p2.x;
    result.y = p1.y - p2.y;
    result.z = 0.0f;
    return result;
}

geometry_msgs::msg::Point PointUtils::pointAdd(const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2)
{
    geometry_msgs::msg::Point result;
    result.x = p1.x + p2.x;
    result.y = p1.y + p2.y;
    result.z = 0.0f;
    return result;
}

void PointUtils::pointFirstAdd(geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2)
{
    p1.x += p2.x;
    p1.y += p2.y;
    p1.z += p2.z;
}

geometry_msgs::msg::Point PointUtils::pointMultiply(const geometry_msgs::msg::Point& p, float scalar)
{
    geometry_msgs::msg::Point result;
    result.x = p.x * scalar;
    result.y = p.y * scalar;
    result.z = 0.0f;
    return result;
}

geometry_msgs::msg::Point PointUtils::pointDivide(const geometry_msgs::msg::Point& p, float scalar)
{
    geometry_msgs::msg::Point result;
    result.x = p.x / scalar;
    result.y = p.y / scalar;
    result.z = 0.0f;
    return result;
}

geometry_msgs::msg::Point PointUtils::createPoint(float x, float y)
{
    geometry_msgs::msg::Point point;
    point.x = x;
    point.y = y;
    point.z = 0.0f;
    return point;
}

float PointUtils::pointNorm(const geometry_msgs::msg::Point& p)
{
    return sqrt(p.x * p.x + p.y * p.y);
}

float PointUtils::squaredDistance(const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2)
{
    return (p1.x - p2.x) * (p1.x - p2.x) + (p1.y - p2.y) * (p1.y - p2.y);
}

}} // namespace rp::algorithm 