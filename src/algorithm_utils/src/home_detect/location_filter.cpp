#include "algorithm_utils/home_detect/location_filter.h"
#include "algorithm_utils/point_utils.h"
#include "angles/angles.h"
#include <cassert>

namespace rp { namespace algorithm { namespace home_detect {

ChargingBaseLocationFilter::ChargingBaseLocationFilter(const rclcpp::Node::SharedPtr& node, int maxCandidateCount)
    : maxCandidateCount_(maxCandidateCount)
    , maxCandidateDivergence_(0.0f)
    , maxCandidateAngleDiff_(0.0f)
    , mostLikehoodCandidateID_(-1)
    , workingMode_(WORKINGMODE_CATCH_BEST)
    , maxAngleThresholdToRegisteredBase_(std::nullopt)
    , maxDistanceThresholdToRegisteredBase_(std::nullopt)
    , candidateVoltFirstRange_(0.0f)
    , candidateVoltSecondRange_(0.0f)
{
    weightedMeanHistoryData_.reserve(maxCandidateCount);
    minPatternMatchingFunctionValue_.reserve(maxCandidateCount);
    candidatePool_.reserve(maxCandidateCount);
    node->declare_parameter("location_filter.max_candidate_divergence", 0.15);
    node->declare_parameter("location_filter.max_candidate_angle_diff", 0.0875);
    node->declare_parameter("location_filter.candidate_volt_first_range", 0.3);
    node->declare_parameter("location_filter.candidate_volt_second_range", 0.8);
    maxCandidateDivergence_ = node->get_parameter("location_filter.max_candidate_divergence").as_double();
    maxCandidateAngleDiff_ = node->get_parameter("location_filter.max_candidate_angle_diff").as_double();
    if (node->has_parameter("location_filter.max_angle_threshold_to_registered_base"))
    {
        maxAngleThresholdToRegisteredBase_ = node->get_parameter("location_filter.max_angle_threshold_to_registered_base").as_double();
    }
    if (node->has_parameter("location_filter.max_distance_threshold_to_registered_base"))
    {
        maxDistanceThresholdToRegisteredBase_ = node->get_parameter("location_filter.max_distance_threshold_to_registered_base").as_double();
    }
    candidateVoltFirstRange_ = node->get_parameter("location_filter.candidate_volt_first_range").as_double();
    candidateVoltSecondRange_ = node->get_parameter("location_filter.candidate_volt_second_range").as_double();
}

ChargingBaseLocationFilter::~ChargingBaseLocationFilter()
{
}

void ChargingBaseLocationFilter::reset()
{
    mostLikehoodCandidateID_ = -1;
    candidatePool_.clear();
    minPatternMatchingFunctionValue_.clear();
    weightedMeanHistoryData_.clear();
}

size_t ChargingBaseLocationFilter::getMostLikehoodCandidateVote()
{
    if (mostLikehoodCandidateID_ < 0 || mostLikehoodCandidateID_ >= candidatePool_.size())
        return 0;
    return candidatePool_[mostLikehoodCandidateID_].second;
}

bool ChargingBaseLocationFilter::getEstimatedDesc(RpHome& homeDesc, size_t minVote)
{
    if (mostLikehoodCandidateID_ < 0 || mostLikehoodCandidateID_ >= candidatePool_.size() ||
        candidatePool_[mostLikehoodCandidateID_].second < minVote)
        return false;
    homeDesc = candidatePool_[mostLikehoodCandidateID_].first;
    return true;
}

void ChargingBaseLocationFilter::pushCandidate(const RpHome& homeDesc, const geometry_msgs::msg::Point& suggestedBaseLocation, float funcValue)
{    
    bool newCandidate = true;
    int candidateToMerge = -1;
    for (size_t pos = 0; pos < candidatePool_.size(); ++pos) {
        float currentDivergence = CalcCandidateDivergence(candidatePool_[pos].first, homeDesc);
        float curAngleDiff = CalcCandidateAngleDiff(candidatePool_[pos].first, homeDesc);
        if (currentDivergence < maxCandidateDivergence_ && curAngleDiff < maxCandidateAngleDiff_) {
            newCandidate = false;
            candidateToMerge = pos;
        }
    }
    int incrementVote = 1;
    const float distancToBaseLoc = PointUtils::pointDistance(homeDesc.getHomeCentralPoint(), suggestedBaseLocation);
    if (maxAngleThresholdToRegisteredBase_ && maxDistanceThresholdToRegisteredBase_) {
        incrementVote = 4;
    } else {
        if (distancToBaseLoc < candidateVoltFirstRange_) {
            incrementVote = 3;
        } else if (distancToBaseLoc < candidateVoltSecondRange_) {
            incrementVote = 2;
        }
    }
    
    // merge or insert ..
    if (newCandidate) {
        if (candidatePool_.size() < maxCandidateCount_) {
            candidatePool_.push_back(std::pair<RpHome, size_t>(homeDesc, incrementVote));
            if (workingMode_ == WORKINGMODE_CATCH_BEST)
                minPatternMatchingFunctionValue_.push_back(funcValue);
            else if (workingMode_ == WORKINGMODE_WEIGHTED_MEAN) {
                std::vector<std::pair<RpHome, float>> homeResults;
                homeResults.push_back(std::make_pair(homeDesc, distancToBaseLoc));
                weightedMeanHistoryData_.push_back(homeResults);
            }

            if (mostLikehoodCandidateID_ == -1) {
                mostLikehoodCandidateID_ = candidatePool_.size() - 1;
            }
        }
    }
    else {
        assert(candidateToMerge >= 0);
        if (workingMode_ == WORKINGMODE_CATCH_BEST && funcValue < minPatternMatchingFunctionValue_[candidateToMerge])
        {
            minPatternMatchingFunctionValue_[candidateToMerge] = funcValue;
            fuseCandidates(candidatePool_[candidateToMerge].first, candidatePool_[candidateToMerge].second, homeDesc);
        }
        else if (workingMode_ == WORKINGMODE_COMPUTE_MEAN)
        {
            fuseCandidates(candidatePool_[candidateToMerge].first, candidatePool_[candidateToMerge].second, homeDesc);
        }
        else if (workingMode_ == WORKINGMODE_WEIGHTED_MEAN)
        {
            weightedMeanHistoryData_[candidateToMerge].push_back(std::make_pair(homeDesc, distancToBaseLoc));
            fuseCandidates(weightedMeanHistoryData_[candidateToMerge], candidatePool_[candidateToMerge].first);
        }
        
        int prevMaxCandidateVote = getMostLikehoodCandidateVote();
        candidatePool_[candidateToMerge].second += incrementVote;
        if (candidatePool_[candidateToMerge].second > prevMaxCandidateVote)
        {
            mostLikehoodCandidateID_ = candidateToMerge;
        }
    }
}

float ChargingBaseLocationFilter::CalcCandidateDivergence(const RpHome& src, const RpHome& dest)
{
    float locationDistance = PointUtils::pointDistance(src.getHomeCentralPoint(), dest.getHomeCentralPoint());
    float landingPointDistance = PointUtils::pointDistance(src.verticalLine.end, dest.verticalLine.end);

    return locationDistance + landingPointDistance;
}

float ChargingBaseLocationFilter::CalcCandidateAngleDiff(const RpHome& src, const RpHome& dest)
{
    float srcHeading = src.getHomeHeading();
    float destHeading = dest.getHomeHeading();
    float diff = std::fabs(angles::shortest_angular_distance(srcHeading, destHeading));
    return diff;
}

void ChargingBaseLocationFilter::fuseCandidates(RpHome& dest, int voteCnt, const RpHome& src)
{
    geometry_msgs::msg::Point fusedHomeCenter, fusedHomeLandingPoint;
    if (workingMode_ == WORKINGMODE_CATCH_BEST)
    {
        fusedHomeCenter = PointUtils::createPoint(src.getHomeCentralPoint().x, src.getHomeCentralPoint().y);
        fusedHomeLandingPoint = PointUtils::createPoint(src.verticalLine.end.x, src.verticalLine.end.y);
    }
    else
    {
        fusedHomeCenter = PointUtils::createPoint((dest.getHomeCentralPoint().x * voteCnt + src.getHomeCentralPoint().x) / (voteCnt + 1),
                                                  (dest.getHomeCentralPoint().y * voteCnt + src.getHomeCentralPoint().y) / (voteCnt + 1));
        fusedHomeLandingPoint = PointUtils::createPoint((dest.verticalLine.end.x * voteCnt + src.verticalLine.end.x) / (voteCnt + 1),
                                                        (dest.verticalLine.end.y * voteCnt + src.verticalLine.end.y) / (voteCnt + 1));
    }

    dest.verticalLine.start = fusedHomeCenter;
    dest.verticalLine.end = fusedHomeLandingPoint;
}

void ChargingBaseLocationFilter::fuseCandidates(const std::vector<std::pair<RpHome, float>>& src, RpHome& dest)
{
    float weightSigma = 0.0f;
    geometry_msgs::msg::Point weightCenterPoint = PointUtils::createPoint(0.0, 0.0);
    geometry_msgs::msg::Point weightEndPoint = PointUtils::createPoint(0.0, 0.0);
    for (auto it = src.begin(); it != src.end(); it++)
    {
        float weight = 1.0f / it->second;
        weightSigma += weight;
        PointUtils::pointFirstAdd(weightCenterPoint, PointUtils::pointMultiply(it->first.getHomeCentralPoint(), weight));
        PointUtils::pointFirstAdd(weightEndPoint, PointUtils::pointMultiply(it->first.verticalLine.end, weight));
    }
    dest.verticalLine.start = PointUtils::pointDivide(weightCenterPoint, weightSigma);
    dest.verticalLine.end = PointUtils::pointDivide(weightEndPoint, weightSigma);
}

} } }
