#include "algorithm_utils/home_detect/home_detect_node.h"
#include "algorithm_utils/point_utils.h"
#include "tf2_ros/transform_listener.h"
#include "tf2_ros/buffer.h"
#include "tf2/transform_datatypes.h"
#include "tf2/utils.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"
#include "angles/angles.h"
#include <chrono>
#include <thread>

namespace rp { namespace algorithm { namespace home_detect {

HomeDetectActionNode::HomeDetectActionNode(const rclcpp::NodeOptions& options)
    : Node("home_detect_action_node", options)
    , is_detecting_(false)
{
    // Declare parameters
    this->declare_parameter("laser_scan_topic", "/fusion_scan");
    this->declare_parameter("base_link_frame", "base_link");
    this->declare_parameter("map_frame", "map");
    this->declare_parameter("max_candidate_count", 3);
    this->declare_parameter("default_timeout_ms", 30000);
    this->declare_parameter("valid_candidate_volt_threshold_in_pattern_matching", 5);

    // Get parameters
    laser_scan_topic_ = this->get_parameter("laser_scan_topic").as_string();
    base_link_frame_ = this->get_parameter("base_link_frame").as_string();
    map_frame_ = this->get_parameter("map_frame").as_string();
    max_candidate_count_ = this->get_parameter("max_candidate_count").as_int();
    default_timeout_ms_ = this->get_parameter("default_timeout_ms").as_int();
    valid_candidate_volt_threshold_ = this->get_parameter("valid_candidate_volt_threshold_in_pattern_matching").as_int();

    // Initialize TF
    tf_buffer_ = std::make_unique<tf2_ros::Buffer>(this->get_clock());
    tf_listener_ = std::make_unique<tf2_ros::TransformListener>(*tf_buffer_);
    
    // Subscribe to laser scan
    laser_scan_sub_ = this->create_subscription<sensor_msgs::msg::LaserScan>(
        laser_scan_topic_, 10,
        std::bind(&HomeDetectActionNode::laserScanCallback, this, std::placeholders::_1));

    // Create action server
    action_server_ = rclcpp_action::create_server<DetectHomeAction>(
        this,
        "detect_home",
        std::bind(&HomeDetectActionNode::handleGoal, this, std::placeholders::_1, std::placeholders::_2),
        std::bind(&HomeDetectActionNode::handleCancel, this, std::placeholders::_1),
        std::bind(&HomeDetectActionNode::handleAccepted, this, std::placeholders::_1));

    // Initialize charging dock visualization publisher
    dock_visualization_pub_ = this->create_publisher<visualization_msgs::msg::MarkerArray>("dock_visualization", 10);

    RCLCPP_INFO(this->get_logger(), "Home detect action node initialized");
}

void HomeDetectActionNode::init()
{
    auto node_ptr = this->shared_from_this();
    home_detect_service_ = std::make_shared<HomeDetectService>(node_ptr);
    if (!home_detect_service_->onStart()) {
        RCLCPP_ERROR(this->get_logger(), "Failed to start home detection service");
        throw std::runtime_error("Failed to start home detection service");
    }
    location_filter_ = std::make_shared<ChargingBaseLocationFilter>(node_ptr, max_candidate_count_);
}

HomeDetectActionNode::~HomeDetectActionNode()
{
    if (home_detect_service_) {
        home_detect_service_->onStop();
    }
}

rclcpp_action::GoalResponse HomeDetectActionNode::handleGoal(
    const rclcpp_action::GoalUUID& uuid,
    std::shared_ptr<const DetectHomeAction::Goal> goal)
{
    RCLCPP_INFO(this->get_logger(), "Received goal request for home detection");
    
    // Check if already detecting
    if (is_detecting_.load()) {
        RCLCPP_WARN(this->get_logger(), "Already detecting home, rejecting new goal");
        return rclcpp_action::GoalResponse::REJECT;
    }

    // Check if we have laser scan data
    {
        std::lock_guard<std::mutex> lock(laser_scan_mutex_);
        if (!latest_laser_scan_) {
            RCLCPP_WARN(this->get_logger(), "No laser scan data available, rejecting goal");
            return rclcpp_action::GoalResponse::REJECT;
        }
    }

    return rclcpp_action::GoalResponse::ACCEPT_AND_EXECUTE;
}

rclcpp_action::CancelResponse HomeDetectActionNode::handleCancel(
    const std::shared_ptr<GoalHandleDetectHome> goal_handle)
{
    RCLCPP_INFO(this->get_logger(), "Received request to cancel home detection");
    
    if (current_detect_handle_) {
        current_detect_handle_->cancel();
    }
    
    return rclcpp_action::CancelResponse::ACCEPT;
}

void HomeDetectActionNode::handleAccepted(
    const std::shared_ptr<GoalHandleDetectHome> goal_handle)
{
    std::thread{std::bind(&HomeDetectActionNode::executeDetectHome, this, goal_handle)}.detach();
}

void HomeDetectActionNode::executeDetectHome(
    const std::shared_ptr<GoalHandleDetectHome> goal_handle)
{
    RCLCPP_INFO(this->get_logger(), "Executing home detection");
    
    is_detecting_.store(true);
    current_detect_handle_.reset();
    location_filter_->reset();
    
    auto start_time = std::chrono::steady_clock::now();
    uint32_t timeout_ms = goal_handle->get_goal()->timeout_ms;
    if (timeout_ms == 0) {
        timeout_ms = default_timeout_ms_;
    }
    bool found = false;
    RpHome best_home;
    int best_votes = 0;
    int percent = 0;
    std::string feedback_msg;

    while (rclcpp::ok()) {
        // Timeout check
        auto now = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time).count() > timeout_ms) {
            feedback_msg = "Detection timeout";
            break;
        }
        if (goal_handle->is_canceling()) {
            feedback_msg = "Detection canceled";
            break;
        }

        // Get scan and pose
        sensor_msgs::msg::LaserScan::SharedPtr laser_scan;
        {
            std::lock_guard<std::mutex> lock(laser_scan_mutex_);
            laser_scan = latest_laser_scan_;
        }
        if (!laser_scan) {
            feedback_msg = "No laser scan data available";
            break;
        }
        geometry_msgs::msg::Pose robot_pose;
        if (!getRobotPose(robot_pose)) {
            feedback_msg = "Failed to get robot pose";
            break;
        }
        auto scan_data = convertLaserScanToScanData(laser_scan);

        // Get expected_home_pose and transform to map coordinate system
        geometry_msgs::msg::PoseStamped expected_pose_in = goal_handle->get_goal()->expected_home_pose;
        geometry_msgs::msg::PoseStamped expected_pose_map;
        try {
            tf_buffer_->transform(expected_pose_in, expected_pose_map, map_frame_, tf2::durationFromSec(0.5));
        } catch (tf2::TransformException& ex) {
            feedback_msg = std::string("Failed to transform expected_home_pose to map: ") + ex.what();
            break;
        }

        // Call detectHome
        DetectDesc detect_desc;
        detect_desc.detectType = DetectTypeByPatternMatch;
        detect_desc.scanData = std::make_shared<std::vector<interfaces::msg::ScanData>>(scan_data);
        detect_desc.robot_pose = robot_pose;
        // No need for notifyCbFun here, just call synchronously
        auto detect_handle = home_detect_service_->detectHome(detect_desc);
        if (detect_handle->waitUntilDone() != DetectStatusSucceed) {
            RCLCPP_WARN(this->get_logger(), "Detection failed, continue...");
            continue;
        }
        DetectResult detect_result;
        if (detect_handle->getDetectResult(detect_result)) {
            RCLCPP_INFO(this->get_logger(), "Detection result: %8.5f, %8.5f, %8.5f", detect_result.home.getHomeCentralPoint().x, detect_result.home.getHomeCentralPoint().y, detect_result.home.getHomeHeading() / M_PI * 180.0);
            // pushCandidate (with threshold check)
            geometry_msgs::msg::Point expected_location = expected_pose_map.pose.position;
            bool push = true;
            if (location_filter_->getMaxDistanceThresholdToRegisteredBase().has_value()) {
                float dist_diff = PointUtils::pointDistance(detect_result.home.getHomeCentralPoint(), expected_location);
                if (dist_diff > location_filter_->getMaxDistanceThresholdToRegisteredBase().value()) {
                    push = false;
                }
            }
            if (location_filter_->getMaxAngleThresholdToRegisteredBase().has_value()) {
                float detected_heading = detect_result.home.getHomeHeading();
                float expected_heading = tf2::getYaw(expected_pose_map.pose.orientation);
                float angle_diff = std::fabs(angles::shortest_angular_distance(detected_heading, expected_heading));
                if (angle_diff > location_filter_->getMaxAngleThresholdToRegisteredBase().value()) {
                    push = false;
                }
            }
            if (push) {
                location_filter_->pushCandidate(detect_result.home, expected_location, detect_result.funcValue);
            }
        }
        // Check votes
        best_votes = location_filter_->getMostLikehoodCandidateVote();
        if (best_votes >= valid_candidate_volt_threshold_) {
            found = location_filter_->getEstimatedDesc(best_home, valid_candidate_volt_threshold_);
            feedback_msg = "Sufficient votes, home detected.";
            break;
        }
        // feedback percent
        percent = std::min(100, (best_votes * 100) / std::max(1, valid_candidate_volt_threshold_));
        publishFeedback(goal_handle, DetectStatusRunning, percent, location_filter_->getValidCandidateCount(), best_votes, "Detecting...");
        //rclcpp::sleep_for(std::chrono::milliseconds(100));
    }

    auto result = std::make_shared<DetectHomeAction::Result>();
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    result->detection_time_ms = duration.count();

    if (found) {
        // Use already-global coordinates from best_home
        geometry_msgs::msg::Point home_center = best_home.getHomeCentralPoint();
        double home_heading = best_home.getHomeHeading();
        geometry_msgs::msg::Quaternion home_orientation = tf2::toMsg(tf2::Quaternion(0, 0, std::sin(home_heading/2), std::cos(home_heading/2)));
        // Construct PoseStamped
        geometry_msgs::msg::PoseStamped home_pose_stamped;
        home_pose_stamped.header.frame_id = map_frame_;
        home_pose_stamped.header.stamp = this->now();
        home_pose_stamped.pose.position = home_center;
        home_pose_stamped.pose.orientation = home_orientation;
        result->home_pose = home_pose_stamped;
        result->success = true;
        result->error_message = "";

        // Publish charging dock visualization
        publishDockVisualization(best_home);

        RCLCPP_INFO(this->get_logger(), "Home detection succeeded");
        goal_handle->succeed(result);
    } else {
        result->success = false;
        result->error_message = feedback_msg;
        goal_handle->abort(result);
    }
    is_detecting_.store(false);
    current_detect_handle_.reset();
    location_filter_->reset();
}

void HomeDetectActionNode::laserScanCallback(const sensor_msgs::msg::LaserScan::SharedPtr msg)
{
    std::lock_guard<std::mutex> lock(laser_scan_mutex_);
    latest_laser_scan_ = msg;
}

bool HomeDetectActionNode::getRobotPose(geometry_msgs::msg::Pose& robot_pose)
{
    try {
        geometry_msgs::msg::TransformStamped transform = 
            tf_buffer_->lookupTransform(map_frame_, base_link_frame_, tf2::TimePointZero);
        
        robot_pose.position.x = transform.transform.translation.x;
        robot_pose.position.y = transform.transform.translation.y;
        robot_pose.position.z = transform.transform.translation.z;
        robot_pose.orientation = transform.transform.rotation;
        
        return true;
    } catch (tf2::TransformException& ex) {
        RCLCPP_WARN(this->get_logger(), "Could not transform %s to %s: %s",
                   map_frame_.c_str(), base_link_frame_.c_str(), ex.what());
        return false;
    }
}

std::vector<interfaces::msg::ScanData> HomeDetectActionNode::convertLaserScanToScanData(
    const sensor_msgs::msg::LaserScan::SharedPtr& laser_scan)
{
    std::vector<interfaces::msg::ScanData> scan_data;
    scan_data.reserve(laser_scan->ranges.size());
    
    for (size_t i = 0; i < laser_scan->ranges.size(); ++i) {
        interfaces::msg::ScanData data;
        data.angle = laser_scan->angle_min + i * laser_scan->angle_increment;
        data.dist = laser_scan->ranges[i];
        data.valid = (laser_scan->ranges[i] >= laser_scan->range_min && 
                     laser_scan->ranges[i] <= laser_scan->range_max);
        scan_data.push_back(data);
    }
    
    return scan_data;
}

void HomeDetectActionNode::publishFeedback(
    const std::shared_ptr<GoalHandleDetectHome> goal_handle,
    uint8_t status, uint8_t progress, uint32_t candidate_count, 
    uint32_t best_votes, const std::string& status_msg)
{
    auto feedback = std::make_shared<DetectHomeAction::Feedback>();
    feedback->status = status;
    feedback->progress_percent = progress;
    feedback->candidate_count = candidate_count;
    feedback->best_candidate_votes = best_votes;
    feedback->status_message = status_msg;
    
    goal_handle->publish_feedback(feedback);
}

void HomeDetectActionNode::publishDockVisualization(const RpHome& home)
{
    visualization_msgs::msg::MarkerArray marker_array;
    
    // First add clear marker
    visualization_msgs::msg::Marker clear_marker;
    clear_marker.header.frame_id = map_frame_;
    clear_marker.header.stamp = this->now();
    clear_marker.ns = "dock_visualization";
    clear_marker.id = 0;
    clear_marker.action = visualization_msgs::msg::Marker::DELETEALL;
    marker_array.markers.push_back(clear_marker);
    
    // Get charging dock center point and heading
    geometry_msgs::msg::Point center = home.getHomeCentralPoint();
    double heading = home.getHomeHeading();
    
    // Create homeLine marker
    visualization_msgs::msg::Marker home_line;
    home_line.header.frame_id = map_frame_;
    home_line.header.stamp = this->now();
    home_line.ns = "home_line";
    home_line.id = 0;
    home_line.type = visualization_msgs::msg::Marker::LINE_STRIP;
    home_line.action = visualization_msgs::msg::Marker::ADD;
    
    // Add homeLine start and end points
    home_line.points.push_back(home.homeLine.start);
    home_line.points.push_back(home.homeLine.end);
    
    home_line.scale.x = 0.05;  // Line width
    home_line.color.r = 1.0;
    home_line.color.g = 0.0;
    home_line.color.b = 0.0;
    home_line.color.a = 1.0;
    
    marker_array.markers.push_back(home_line);
    
    // Create charging dock center point marker
    visualization_msgs::msg::Marker center_point;
    center_point.header.frame_id = map_frame_;
    center_point.header.stamp = this->now();
    center_point.ns = "center_point";
    center_point.id = 1;
    center_point.type = visualization_msgs::msg::Marker::SPHERE;
    center_point.action = visualization_msgs::msg::Marker::ADD;
    
    center_point.pose.position = center;
    center_point.pose.orientation.w = 1.0;
    center_point.scale.x = 0.1;
    center_point.scale.y = 0.1;
    center_point.scale.z = 0.1;
    center_point.color.r = 0.0;
    center_point.color.g = 0.0;
    center_point.color.b = 1.0;
    center_point.color.a = 1.0;
    
    marker_array.markers.push_back(center_point);
    
    // Create heading arrow
    visualization_msgs::msg::Marker direction_arrow;
    direction_arrow.header.frame_id = map_frame_;
    direction_arrow.header.stamp = this->now();
    direction_arrow.ns = "direction_arrow";
    direction_arrow.id = 2;
    direction_arrow.type = visualization_msgs::msg::Marker::ARROW;
    direction_arrow.action = visualization_msgs::msg::Marker::ADD;
    
    direction_arrow.pose.position = center;
    direction_arrow.pose.orientation.x = 0.0;
    direction_arrow.pose.orientation.y = 0.0;
    direction_arrow.pose.orientation.z = std::sin(heading / 2.0);
    direction_arrow.pose.orientation.w = std::cos(heading / 2.0);
    
    direction_arrow.scale.x = 0.3;  // Arrow length
    direction_arrow.scale.y = 0.05; // Arrow width
    direction_arrow.scale.z = 0.05; // Arrow height
    
    direction_arrow.color.r = 1.0;
    direction_arrow.color.g = 1.0;
    direction_arrow.color.b = 0.0;
    direction_arrow.color.a = 1.0;
    
    marker_array.markers.push_back(direction_arrow);
    
    // Publish visualization markers
    dock_visualization_pub_->publish(marker_array);
    
    RCLCPP_INFO(this->get_logger(), "Published dock visualization with center at (%.3f, %.3f), heading: %.3f", 
                center.x, center.y, heading / M_PI * 180.0);
}

} } }

// Standalone main function for direct node execution
int main(int argc, char ** argv)
{
    rclcpp::init(argc, argv);

    try
    {
        rclcpp::NodeOptions options;
        auto node = std::make_shared<rp::algorithm::home_detect::HomeDetectActionNode>(options);
        node->init();

        RCLCPP_INFO(node->get_logger(), "Home detect action node started...");
        rclcpp::spin(node);
    }
    catch (const std::exception& e)
    {
        RCLCPP_ERROR(rclcpp::get_logger("home_detect_node"), "Node initialization failed: %s", e.what());
        rclcpp::shutdown();
        return 1;
    }

    rclcpp::shutdown();
    return 0;
}

// #include "rclcpp_components/register_node_macro.hpp"
// RCLCPP_COMPONENTS_REGISTER_NODE(rp::algorithm::home_detect::HomeDetectActionNode)
