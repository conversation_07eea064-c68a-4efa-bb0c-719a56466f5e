#include "algorithm_utils/home_detect/home_detect_service.h"
#include "algorithm_utils/point_utils.h"
#include <cmath>
#include <tf2/LinearMath/Matrix3x3.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/utils.h>
#include <geometry_msgs/msg/point.hpp>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

namespace rp { namespace algorithm { namespace home_detect {

    DetectHandleBase::DetectHandleBase(const user_data_shared_ptr& userDat, const detect_cb_fun_t& cbFun)
        : userData_(userDat)
        , status_(DetectStatusPending)
        , detectCbFun_(cbFun)
    {
        //
    }
    
    DetectHandleBase::~DetectHandleBase()
    {
        //
    }
    
    DetectHandleBase::user_data_shared_ptr DetectHandleBase::getUserData() const
    {
        // it is better to let the user data be thread-safe.
        std::lock_guard<std::mutex> lkGuard(userDataLock_);
        return userData_;
    }

    DetectStatus DetectHandleBase::getStatus() const
    {
        return status_.load();
    }
    
    DetectStatus DetectHandleBase::waitUntilDone() const
    {
        auto tStatus = status_.load();
        if (isInDetectingStatus(tStatus))
        {
            {
                std::unique_lock<std::mutex> uniLk(notifyLock_);
                notifyCondVar_.wait(uniLk, is_finished_status_predicator(this));
            }
            tStatus = status_.load();
            assert(!isInDetectingStatus(tStatus));
        }
        return tStatus;
    }
    
    DetectStatus DetectHandleBase::waitFor(std::uint32_t maxWaitMS) const
    {
        auto tStatus = status_.load();
        if (isInDetectingStatus(tStatus))
        {
            {
                std::unique_lock<std::mutex> uniLk(notifyLock_);
                notifyCondVar_.wait_for(uniLk, std::chrono::milliseconds(maxWaitMS), is_finished_status_predicator(this));
            }
            tStatus = status_.load();
        }
        return tStatus;
    }

    void DetectHandleBase::noNotifySetStatus(DetectStatus tStatus)
    {
        assert(isValidDetectStatus(tStatus));
        status_.store(tStatus);
    }

    void DetectHandleBase::finishAndNotify(DetectStatus tStatus, bool bCallCbFun)
    {
        assert(!isInDetectingStatus(tStatus));
        assert(isInDetectingStatus(status_.load()));
        status_.store(tStatus);
        {
            std::lock_guard<std::mutex> lkGuard(notifyLock_);
            notifyCondVar_.notify_all();
        }

        if (detectCbFun_ && bCallCbFun)
            detectCbFun_(this->shared_from_this());
    }

    //////////////////////////////////////////////////////////////////////////

    HomeDetectService::detect_task_t::detect_task_t(const DetectDesc& tDetectDesc)
        : cancellation(false)
        , desc(tDetectDesc)
    {
        desc.notifyCbFun = DetectCallbackFun();
    }

    HomeDetectService::detect_handle_t::detect_handle_t(const DetectDesc& desc, const user_data_shared_ptr& userDat)
        : DetectHandleBase(userDat, desc.notifyCbFun)
    {
        task_ = std::make_shared<detect_task_t>(desc);
    }

    HomeDetectService::detect_handle_t::~detect_handle_t()
    {
        task_.reset();
    }

    bool HomeDetectService::detect_handle_t::getDetectResult(DetectResult& destRes) const
    {
        const auto tStatus = getStatus();
        if (!isInDetectingStatus(tStatus))
        {
            destRes = task_->detectResult;
            return true;
        }
        return false;
    }

    void HomeDetectService::detect_handle_t::cancel()
    {
        task_->cancellation.store(true);
    }

    HomeDetectService::HomeDetectService(const rclcpp::Node::SharedPtr& node)
        : node_(node)
        , isWorking_(false)
        , using_pattern_matching_old_(true)
        , detector_(nullptr)
        , detector_old_(nullptr)
    {
        //
        node->declare_parameter("home_detection_service.using_pattern_matching_old", true);
        node->declare_parameter("home_detection_service.lidar_scan_transform_angle", true);
        using_pattern_matching_old_ = node->get_parameter("home_detection_service.using_pattern_matching_old").as_bool();
        lidar_scan_transform_angle_ = node->get_parameter("home_detection_service.lidar_scan_transform_angle").as_bool();
    }

    HomeDetectService::~HomeDetectService()
    {
        assert(!currTask_);
        assert(jobs_.empty());
        assert(!workThread_.joinable());
    }

    bool HomeDetectService::onStart()
    {
        isWorking_.store(true);
        workThread_ = std::move(std::thread(std::bind(&HomeDetectService::worker_, this)));
        return true;
    }

    bool HomeDetectService::onStop()
    {
        isWorking_.store(false);
        abortAll();

        {
            std::lock_guard<std::mutex> lkGuard(workLock_);
            workCond_.notify_all();
        }

        if (workThread_.joinable())
            workThread_.join();
        return true;
    }

    std::shared_ptr<IDetectHandle> HomeDetectService::detectHome(const DetectDesc& desc, const user_data_shared_ptr& userDat)
    {
        detect_handle_shared_ptr hTask = std::make_shared<detect_handle_t>(desc, userDat);
        bool bIsPushed = false;
        {
            std::lock_guard<std::mutex> lkGuard(workLock_);
            if (isWorking_.load())
            {
                jobs_.push_back(hTask);
                workCond_.notify_all();
                bIsPushed = true;
            }
        }

        if (!bIsPushed)
            hTask->finishAndNotify(DetectStatusCanceled, !desc.doNotNotifyCbIfFailedToPushTask);
        return hTask;
    }

    void HomeDetectService::abortAll()
    {
        std::lock_guard<std::mutex> lkGuard(workLock_);

        if (currTask_)
            currTask_->cancel();

        for (auto itJob = jobs_.begin(), itJobEnd = jobs_.end(); itJobEnd != itJob; ++itJob)
            (*itJob)->cancel();
    }

    void HomeDetectService::worker_()
    {
        if (using_pattern_matching_old_) {
            detector_old_ = RpHomeDetector::create(node_);
        } else {
            detector_ = PatternMatchingHomeDetector::create(node_);
        }
        while (isWorking_.load())
        {
            workerPerformRunning_();
        }
        
        {
            std::list<detect_handle_shared_ptr> tmpJobs;
            {
                std::lock_guard<std::mutex> lkGuard(workLock_);
                assert(!isWorking_.load());
                assert(!currTask_);
                tmpJobs.swap(jobs_);
            }

            for (auto itJob = tmpJobs.begin(), itJobEnd = tmpJobs.end(); itJobEnd != itJob; ++itJob)
                (*itJob)->finishAndNotify(DetectStatusCanceled, true);
        }
    }
    
    void HomeDetectService::workerPerformRunning_()
    {
        detect_handle_shared_ptr hTask;
        {
            std::unique_lock<std::mutex> uniLk(workLock_);

            if (!isWorking_.load())
                return;
            
            if (jobs_.empty())
            {
                workCond_.wait_for(uniLk, std::chrono::milliseconds(1000 * 5));
                return;
            }
            
            hTask = jobs_.front();
            assert(hTask);
            jobs_.pop_front();
            hTask->noNotifySetStatus(DetectStatusRunning);
            currTask_ = hTask;
        }

        performDetectHome_(hTask);

        {
            std::lock_guard<std::mutex> lkGuard(workLock_);
            currTask_.reset();
        }
    }

    void HomeDetectService::performDetectHome_(detect_handle_shared_ptr hTask)
    {
        DetectStatus tStatus = DetectStatusFailed;

        std::chrono::steady_clock::time_point start = std::chrono::steady_clock::now();

        const auto& task = hTask->task();

        if (!task->cancellation.load())
        {
            try
            {
                tStatus = doDetectHome_(hTask);
            }
            catch (const std::exception& excp)
            {
                RCLCPP_ERROR(node_->get_logger(), "performDetectHome_(), exception: %s.", excp.what());
            }
            catch (...)
            {
                RCLCPP_ERROR(node_->get_logger(), "performDetectHome_(), unknown exception.");
            }
        }
        else
        {
            tStatus = DetectStatusCanceled;
        }

        const std::uint64_t msOfEnd = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - start).count();
        finishDetectHome_(hTask, tStatus, msOfEnd);
    }

    DetectStatus HomeDetectService::doDetectHome_(detect_handle_shared_ptr hTask)
    {
        const auto& task = hTask->task();
        const auto& desc = task->desc;
        auto& detectResult = task->detectResult;

        if (!desc.scanData)
        {
            RCLCPP_ERROR(node_->get_logger(), "doDetectHome_(), scanData is null.");
            return DetectStatusFailed;
        }

        std::vector<interfaces::msg::ScanData> scanDatas;
        if (lidar_scan_transform_angle_) {
            for (int i = desc.scanData->size() - 1; i >= 0; i--)
            {
                interfaces::msg::ScanData tmpScanData;
                tmpScanData.dist = desc.scanData->at(i).dist;
                tmpScanData.angle = desc.scanData->at(i).angle;
                tmpScanData.valid = desc.scanData->at(i).valid;
                tmpScanData.quality = desc.scanData->at(i).quality;
                scanDatas.push_back(tmpScanData);
            }
        }

        DetectStatus tStatus = DetectStatusFailed;
        switch (desc.detectType)
        {
        case DetectTypeByShell:
            {
                RCLCPP_ERROR(node_->get_logger(), "detect type %s not supported.", getDetectTypeName(desc.detectType).c_str());
            }
            break;
        case DetectTypeByPatternMatch:
            {
                detectResult.resCode = DetectResultCodeNone;
                if (using_pattern_matching_old_) {
                    if (detector_old_ && detector_old_->detectRelativeHomeByPatternMatch(lidar_scan_transform_angle_ ? scanDatas : *desc.scanData, detectResult.home, detectResult.funcValue)) {
                        tStatus = DetectStatusSucceed;
                    }
                } else {
                    if (detector_ && detector_->find(lidar_scan_transform_angle_ ? scanDatas : *desc.scanData, detectResult.home, detectResult.funcValue)) {
                        tStatus = DetectStatusSucceed;
                    }
                }
                if (tStatus == DetectStatusSucceed) {
                    // transform to global coordinate using robot_pose
                    double yaw = tf2::getYaw(desc.robot_pose.orientation);
                    const auto& pos = desc.robot_pose.position;
                    RCLCPP_INFO(node_->get_logger(), "robot_pose: %8.5f, %8.5f, %8.5f", pos.x, pos.y, yaw / M_PI * 180.0);
                    RCLCPP_INFO(node_->get_logger(), "dock pose: %8.5f, %8.5f, %8.5f", detectResult.home.getHomeCentralPoint().x, detectResult.home.getHomeCentralPoint().y, detectResult.home.getHomeHeading() / M_PI * 180.0);
                    auto transform_point = [&](const geometry_msgs::msg::Point& p) {
                        geometry_msgs::msg::Point out;
                        out.x = pos.x + p.x * std::cos(yaw) - p.y * std::sin(yaw);
                        out.y = pos.y + p.x * std::sin(yaw) + p.y * std::cos(yaw);
                        out.z = pos.z + p.z;
                        return out;
                    };
                    detectResult.home.homeLine.start = transform_point(detectResult.home.homeLine.start);
                    detectResult.home.homeLine.end = transform_point(detectResult.home.homeLine.end);
                    detectResult.home.verticalLine.start = transform_point(detectResult.home.verticalLine.start);
                    detectResult.home.verticalLine.end = transform_point(detectResult.home.verticalLine.end);
                    detectResult.resCode = DetectResultCodeByPatternMatching;
                }
            }
            break;
        default:
            {
                RCLCPP_ERROR(node_->get_logger(), "detect type %s not supported.", getDetectTypeName(desc.detectType).c_str());
            }
            break;
        }
        return tStatus;
    }

    void HomeDetectService::finishDetectHome_(const detect_handle_shared_ptr& hTask, DetectStatus tStatus, std::uint64_t msOfEnd)
    {
        assert(hTask);
        const auto& task = hTask->task();
        auto& detectResult = task->detectResult;

        detectResult.msOfEnd = msOfEnd;

        hTask->finishAndNotify(tStatus, true);
    }

} } }
