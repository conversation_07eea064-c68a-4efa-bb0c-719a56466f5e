#include "algorithm_utils/home_detect/home_detection_old.h"
#include "algorithm_utils/point_utils.h"
#include "algorithm_utils/rphoughhelper.h"
#include <chrono>
#include <cmath>
#include <limits>
#include <map>
#include <random>
#include <vector>
#include <ctime>
#include "tf2/LinearMath/Quaternion.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"
#include "angles/angles.h"
#include "rclcpp/rclcpp.hpp"

namespace {
    const int BOUNDARY_OF_SPARSE_AND_DENSE_LASER_SCAN = 1000;

    template < class T >
    inline T square(T v) {
        return v * v;
    }

    inline float DEG2RAD(float deg) {
        return deg * M_PI / 180.0f;
    }
}

namespace rp { namespace algorithm { namespace home_detect {

/*
    Matched Pattern Design (not drawn to scale)
       a              b               a
    +------+    +------------+    +------+  
    |      |    |            |    |      |    
    |      |  c |            | c  |      |   d
    |      +----+            +----+      |  
                                     
*/

std::shared_ptr<RpHomeDetector> RpHomeDetector::create(const rclcpp::Node::SharedPtr& node)
{
    RpHomeDetector::Options options;
    node->declare_parameter("home_detection_old.dock_side_line_length", 0.04);
    node->declare_parameter("home_detection_old.dock_center_line_length", 0.16);
    node->declare_parameter("home_detection_old.dock_inner_line_length", 0.05);
    node->declare_parameter("home_detection_old.dock_depth", 0.045);
    node->declare_parameter("home_detection_old.pattern_matching_max_search_radius", 1.3);
    node->declare_parameter("home_detection_old.max_mean_error_in_pattern_matching", 0.08);
    node->declare_parameter("home_detection_old.rough_pattern_matching_angle_step_in_deg", 2.0);
    node->declare_parameter("home_detection_old.pattern_matching_min_angle_range_number_dense_laser", 30);
    node->declare_parameter("home_detection_old.pattern_matching_min_valid_laser_scan_number_dense_laser", 70);
    node->declare_parameter("home_detection_old.pattern_matching_min_angle_range_number_sparse_laser", 10);
    node->declare_parameter("home_detection_old.pattern_matching_min_valid_laser_scan_number_sparse_laser", 20);
    node->declare_parameter("home_detection_old.enable_length_filter_in_pattern_matching", true);
    node->declare_parameter("home_detection_old.pattern_matching_max_residual_mean_difference_threshold", 0.005);
    node->declare_parameter("home_detection_old.empirical_base_center_adjust_distance_in_pattern_matching", 0.0);
    node->declare_parameter("home_detection_old.empirical_base_center_adjust_depth_in_pattern_matching", 0.0);
    node->declare_parameter("home_detection_old.precise_pattern_matching_search_angle_range_in_deg", 3.0);
    node->declare_parameter("home_detection_old.precise_pattern_matching_angle_step_in_deg", 0.5);
    
    options.dock_side_line_length = node->get_parameter("home_detection_old.dock_side_line_length").as_double();
    options.dock_center_line_length = node->get_parameter("home_detection_old.dock_center_line_length").as_double();
    options.dock_inner_line_length = node->get_parameter("home_detection_old.dock_inner_line_length").as_double();
    options.dock_depth = node->get_parameter("home_detection_old.dock_depth").as_double();
    options.pattern_matching_max_search_radius = node->get_parameter("home_detection_old.pattern_matching_max_search_radius").as_double();
    options.max_mean_error_in_pattern_matching = node->get_parameter("home_detection_old.max_mean_error_in_pattern_matching").as_double();
    options.rough_pattern_matching_angle_step_in_deg = node->get_parameter("home_detection_old.rough_pattern_matching_angle_step_in_deg").as_double();
    options.pattern_matching_min_angle_range_number_dense_laser = node->get_parameter("home_detection_old.pattern_matching_min_angle_range_number_dense_laser").as_int();
    options.pattern_matching_min_valid_laser_scan_number_dense_laser = node->get_parameter("home_detection_old.pattern_matching_min_valid_laser_scan_number_dense_laser").as_int();
    options.pattern_matching_min_angle_range_number_sparse_laser = node->get_parameter("home_detection_old.pattern_matching_min_angle_range_number_sparse_laser").as_int();
    options.pattern_matching_min_valid_laser_scan_number_sparse_laser = node->get_parameter("home_detection_old.pattern_matching_min_valid_laser_scan_number_sparse_laser").as_int();
    options.enable_length_filter_in_pattern_matching = node->get_parameter("home_detection_old.enable_length_filter_in_pattern_matching").as_bool();
    options.pattern_matching_max_residual_mean_difference_threshold = node->get_parameter("home_detection_old.pattern_matching_max_residual_mean_difference_threshold").as_double();
    options.empirical_base_center_adjust_distance_in_pattern_matching = node->get_parameter("home_detection_old.empirical_base_center_adjust_distance_in_pattern_matching").as_double();
    options.empirical_base_center_adjust_depth_in_pattern_matching = node->get_parameter("home_detection_old.empirical_base_center_adjust_depth_in_pattern_matching").as_double();
    options.precise_pattern_matching_search_angle_range_in_deg = node->get_parameter("home_detection_old.precise_pattern_matching_search_angle_range_in_deg").as_double();
    options.precise_pattern_matching_angle_step_in_deg = node->get_parameter("home_detection_old.precise_pattern_matching_angle_step_in_deg").as_double();

    return std::make_shared<RpHomeDetector>(options);
}

RpHomeDetector::RpHomeDetector(const Options& options)
    : options_(options)
{
}

bool RpHomeDetector::detectRelativeHomeByPatternMatch(const std::vector<interfaces::msg::ScanData>& scanDatas, RpHome& home, float& funcValue)
{
    bool result = false;
    auto start = std::chrono::steady_clock::now();
    if (detectHomeByPatternMatching_(scanDatas, home, funcValue))
        result = true;
    auto end = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    RCLCPP_INFO(rclcpp::get_logger("RpHomeDetector"), "cost:%ldms best:%8.5f.", duration.count(), funcValue);
    return result;
}

bool RpHomeDetector::detectHomeByPatternMatching_(const std::vector<interfaces::msg::ScanData>& scanDatas, RpHome& home, float& funcValue)
{
    auto start = std::chrono::steady_clock::now();
    std::vector<interfaces::msg::ScanData> data(scanDatas);
    float dock_a = options_.dock_side_line_length;
    float dock_b = options_.dock_center_line_length;
    float dock_c = options_.dock_inner_line_length;
    float dock_d = options_.dock_depth;
    float dock_length = dock_a * 2 + dock_b + dock_c * 2;

    std::vector<float> disToVlines(4, 0.0f);
    disToVlines[0] = dock_a;
    disToVlines[1] = dock_a + dock_c;
    disToVlines[2] = dock_a + dock_c + dock_b;
    disToVlines[3] = dock_a + dock_c * 2 + dock_b;

    float searchRadius = options_.pattern_matching_max_search_radius;
    float errorThreshold = options_.max_mean_error_in_pattern_matching;
    float errorBenchMark = errorThreshold * errorThreshold;
    float angleStep = DEG2RAD(options_.rough_pattern_matching_angle_step_in_deg);
    int minAngleRangeNum, minValidScanNum;
    if (data.size() > BOUNDARY_OF_SPARSE_AND_DENSE_LASER_SCAN)
    {
        minAngleRangeNum = options_.pattern_matching_min_angle_range_number_dense_laser;
        minValidScanNum = options_.pattern_matching_min_valid_laser_scan_number_dense_laser;
    }
    else
    {
        minAngleRangeNum = options_.pattern_matching_min_angle_range_number_sparse_laser;
        minValidScanNum = options_.pattern_matching_min_valid_laser_scan_number_sparse_laser;
    }

    if(options_.enable_length_filter_in_pattern_matching)
        RpHoughHelper::lengthFilterData(data, minValidScanNum, dock_c / 2, 0.05f, dock_b + dock_c);

    InputResidualParams resParams;
    resParams.scanDatas = data;
    resParams.disToVlines = disToVlines;

    float minAngleRange = std::fabs(data.back().angle - data.front().angle) / (data.size() - 1) * minAngleRangeNum; //add config
    std::map<int, MatchingResult> results;
    int index = 0;
    for (auto it = data.begin(); it != data.end(); it++, index++)
    {
        if (!it->valid || it->dist > searchRadius) //dist can be another filter parameter
            continue;
        PolarLine obsLine(it->dist, it->angle);
        geometry_msgs::msg::Point originalPoint = PointUtils::createPoint(obsLine.rho * std::cos(obsLine.theta), obsLine.rho * std::sin(obsLine.theta));
        float minTangentRadius = std::sin(minAngleRange) * obsLine.rho;
        if (minTangentRadius >= dock_length)
            continue;
        //compute search start angle and search end angle
        float sideLineLength = std::sqrt(std::fabs(square(dock_length) - square(minTangentRadius)));
        float matchingStartAngle, matchingEndAngle;
        if (obsLine.rho <= dock_length)
        {
            matchingStartAngle = angleStep;
            matchingEndAngle = M_PI / 2 - minAngleRange + std::atan2(sideLineLength, minTangentRadius);
        }
        else
        {
            matchingStartAngle = M_PI / 2 - minAngleRange - std::atan2(sideLineLength, minTangentRadius);
            matchingEndAngle = M_PI / 2 - minAngleRange + std::atan2(sideLineLength, minTangentRadius);
        }

        resParams.obsLine = obsLine;
        resParams.originalPoint = originalPoint;
        resParams.index = index;

        while (matchingStartAngle <= matchingEndAngle + 0.0001f)
        {
            CalcResidualResult calcResidualResult;
            resParams.curMatchedAngle = matchingStartAngle;
            calcResidualSum_(resParams, calcResidualResult, errorThreshold);
            if (calcResidualResult.sigmaNum > minValidScanNum && !calcResidualResult.outOfErrorRange) //add to config
            {
                float mean = calcResidualResult.residualSum / calcResidualResult.sigmaNum;
                if (mean <= errorBenchMark)
                {
                    auto iter = results.find(index);
                    if (iter != results.end())
                    {
                        if (iter->second.residualMean > mean)
                        {
                            iter->second.residualMean = mean;
                            iter->second.matchingNum = calcResidualResult.sigmaNum;
                            iter->second.matchingAngle = matchingStartAngle;
                            iter->second.matchingEndAngle = calcResidualResult.matchedObsLine.theta;
                        }
                    }
                    else
                    {
                        MatchingResult res;
                        res.matchingAngle = matchingStartAngle;
                        res.matchingNum = calcResidualResult.sigmaNum;
                        res.residualMean = mean;
                        res.matchingEndAngle = calcResidualResult.matchedObsLine.theta;
                        results.insert(std::make_pair(index, res));
                    }
                }
            }
            matchingStartAngle += angleStep;
        }
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    RCLCPP_INFO(rclcpp::get_logger("RpHomeDetector"), "rough matching time cost is %ld ms.", duration.count());
    return catchTheBestMatchingResult_(data, results, home, funcValue);
}

float RpHomeDetector::calcSampleDist_(const geometry_msgs::msg::Point& baseInterPoint, const PolarLine& horiLine, const PolarLine& vlineLeft, const PolarLine& vlineRight, const PolarLine& matchedLine)
{
    float standardDist = PointUtils::pointNorm(baseInterPoint);
    float sampleDist = 0.0f;
    geometry_msgs::msg::Point interPoint_horiLine, interPoint_vline1, interPoint_vline2;
    calcIntersection_(matchedLine, horiLine, interPoint_horiLine);
    calcIntersection_(matchedLine, vlineLeft, interPoint_vline1);
    calcIntersection_(matchedLine, vlineRight, interPoint_vline2);

    float deltaDist = std::numeric_limits<float>::max();
    float horiToBase = PointUtils::pointDistance(baseInterPoint, interPoint_horiLine);
    float vLeftToBase = PointUtils::pointDistance(baseInterPoint, interPoint_vline1);
    float vRightToBase = PointUtils::pointDistance(baseInterPoint, interPoint_vline2);
    float horiDist = PointUtils::pointNorm(interPoint_horiLine);
    float vLeftDist = PointUtils::pointNorm(interPoint_vline1);
    float vRightDist = PointUtils::pointNorm(interPoint_vline2);
    if (horiDist > standardDist && horiToBase < deltaDist)
    {
        deltaDist = horiToBase;
        sampleDist = horiDist;
    }
    if (vLeftDist > standardDist && vLeftToBase < deltaDist)
    {
        deltaDist = vLeftToBase;
        sampleDist = vLeftDist;
    }
    if (vRightDist > standardDist && vRightToBase < deltaDist)
    {
        deltaDist = vRightToBase;
        sampleDist = vRightDist;
    }
    return sampleDist;
}

bool RpHomeDetector::catchTheBestMatchingResult_(const std::vector<interfaces::msg::ScanData>& scanDatas, const std::map<int, MatchingResult>& results, RpHome& home, float& funcValue)
{
    auto start = std::chrono::steady_clock::now();
    if (results.empty())
        return false;
    float dock_a = options_.dock_side_line_length;
    float dock_b = options_.dock_center_line_length;
    float dock_c = options_.dock_inner_line_length;
    float dock_d = options_.dock_depth;
    float dock_length = dock_a * 2 + dock_b + dock_c * 2;
    float disToCenter = dock_a + dock_c + dock_b / 2;
    ToleranceMatchingResult finalResult;
    finalResult.index = results.begin()->first;
    finalResult.MRes = results.begin()->second;
    finalResult.toleranceDis = disToCenter;
    for (auto it = results.begin(); it != results.end(); it++)
    {
        int index = it->first;
        if ((finalResult.MRes.residualMean - it->second.residualMean > options_.pattern_matching_max_residual_mean_difference_threshold)
            || ((std::fabs(finalResult.MRes.residualMean - it->second.residualMean) < options_.pattern_matching_max_residual_mean_difference_threshold)
               && finalResult.MRes.matchingNum < it->second.matchingNum))
        {
            finalResult.index = index;
            finalResult.MRes = it->second;
            finalResult.toleranceDis = disToCenter;
        }
        PolarLine obsLine(scanDatas[index].dist, scanDatas[index].angle);
        float matchingAngle = it->second.matchingAngle;
        float datumError;
        if (index > 0 && scanDatas[index - 1].valid)
        {
            PolarLine preObsLine(scanDatas[index - 1].dist, scanDatas[index - 1].angle);
            PolarLine baseLine;
            baseLine.rho = (matchingAngle > M_PI / 2) ? std::sin(M_PI - matchingAngle) * obsLine.rho : std::sin(matchingAngle) * obsLine.rho;
            baseLine.theta = angles::normalize_angle(obsLine.theta + matchingAngle - M_PI / 2);
            geometry_msgs::msg::Point interPoint_obsLine, interPoint_preObsLine;
            calcIntersection_(obsLine, baseLine, interPoint_obsLine);
            calcIntersection_(preObsLine, baseLine, interPoint_preObsLine);
            datumError = PointUtils::pointDistance(interPoint_obsLine, interPoint_preObsLine);
        }
        else
        {
            float averageAngleRange = std::fabs(scanDatas.back().angle - scanDatas.front().angle) / (scanDatas.size() - 1);
            int averageAngleNum = 0;
            int idx = index - 1;
            while (idx >= 0 && !scanDatas[idx].valid)
            {
                averageAngleNum++;
                idx--;
            }
            datumError = std::sqrt((2 - 2 * std::cos(averageAngleRange * averageAngleNum)) * square(obsLine.rho));
        }
        calcTheBestResultAtFixedStart_(scanDatas, index, datumError, finalResult);
    }
    float searchAngle = finalResult.MRes.matchingAngle;
    //first get the opposite side length(center point rho), then get the rotation angle(center point theta), all based on inner product formulation
    float d1 = scanDatas[finalResult.index].dist;
    float baseAngle = scanDatas[finalResult.index].angle;
    float d2 = finalResult.toleranceDis + options_.empirical_base_center_adjust_distance_in_pattern_matching;
    float dCenterSquare = square(d1) + square(d2) - 2 * std::cos(searchAngle) * d1 * d2;
    float dCenter = std::sqrt(dCenterSquare);
    float rotationAngle = std::acos((square(d1) + dCenterSquare - square(d2)) / (2 * d1 * dCenter));
    float centerAngle = angles::normalize_angle(baseAngle - rotationAngle);
    float baseLineRho;
    float baseLineTheta;
    if (searchAngle > M_PI / 2)
    {
        baseLineRho = std::sin(M_PI - searchAngle);
        float rotateAng = searchAngle - M_PI / 2;
        baseLineTheta = baseAngle + rotateAng;
    }
    else
    {
        baseLineRho = d1 * std::sin(searchAngle);
        float rotateAng = M_PI / 2 - searchAngle;
        baseLineTheta = baseAngle - rotateAng;
    }   
    baseLineTheta = angles::normalize_angle(baseLineTheta);
    float sinBaseLineTheta = std::sin(baseLineTheta);
    float cosBaseLineTheta = std::cos(baseLineTheta);
    geometry_msgs::msg::Point potentialCenterPoint = PointUtils::createPoint(dCenter * std::cos(centerAngle), dCenter * std::sin(centerAngle));
    RCLCPP_INFO(rclcpp::get_logger("RpHomeDetector"), "potentialCenterPoint: %f, %f", potentialCenterPoint.x, potentialCenterPoint.y);
    geometry_msgs::msg::Point vec = PointUtils::createPoint(-dock_length / 2 * sinBaseLineTheta, dock_length / 2 * cosBaseLineTheta);
    geometry_msgs::msg::Point potentialStartPoint = PointUtils::pointAdd(potentialCenterPoint, vec);
    geometry_msgs::msg::Point potentialEndPoint = PointUtils::pointSubtract(potentialCenterPoint, vec);

    float incurvateDepth = dock_d / 2 + options_.empirical_base_center_adjust_depth_in_pattern_matching;
    geometry_msgs::msg::Point vecDepth = PointUtils::createPoint(incurvateDepth * cosBaseLineTheta, incurvateDepth * sinBaseLineTheta);
    interfaces::msg::Line homeLine, verticalLine;
    homeLine.start = PointUtils::pointAdd(potentialStartPoint, vecDepth);
    homeLine.end = PointUtils::pointAdd(potentialEndPoint, vecDepth);
    geometry_msgs::msg::Point verticalP = PointUtils::createPoint(0.1 * cosBaseLineTheta, 0.1 * sinBaseLineTheta);

    verticalLine.start = PointUtils::pointDivide(PointUtils::pointAdd(homeLine.start, homeLine.end), 2);
    verticalLine.end = PointUtils::pointAdd(verticalLine.start, verticalP);
    home.homeLine = homeLine;
    home.verticalLine = verticalLine;
    funcValue = finalResult.MRes.residualMean;

    auto end = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    RCLCPP_INFO(rclcpp::get_logger("RpHomeDetector"), "precise matching time cost is %ld ms.", duration.count());
    return true;
}

void RpHomeDetector::calcTheBestResultAtFixedStart_(const std::vector<interfaces::msg::ScanData>& scanDatas, int index, float error, ToleranceMatchingResult& result)
{
    float dock_a = options_.dock_side_line_length;
    float dock_b = options_.dock_center_line_length;
    float dock_c = options_.dock_inner_line_length;
    float dock_d = options_.dock_depth;
    float dock_length = dock_a * 2 + dock_b + dock_c * 2;
    std::uniform_real_distribution<float> dist(0.0f, 1.0f);
    std::mt19937 random;
    random.seed((unsigned int)time(0));
    int count = 4;
    float step = 1.0f / count;
    std::vector<float> coefficient;
    while (count > 0)
    {
        coefficient.push_back(step * (count - 1) + step * dist(random));
        count--;
    }

    PolarLine obsLine(scanDatas[index].dist, scanDatas[index].angle);
    geometry_msgs::msg::Point originalPoint = PointUtils::createPoint(obsLine.rho * std::cos(obsLine.theta), obsLine.rho * std::sin(obsLine.theta));
    float matchingAngle = result.MRes.matchingAngle;
    float searchAngRange = options_.precise_pattern_matching_search_angle_range_in_deg;
    float startMatchingAngle = std::max(0.0f, matchingAngle - DEG2RAD(searchAngRange));
    float endMatchingAngle = std::min<float>(M_PI, matchingAngle + DEG2RAD(searchAngRange));
    float angStep = DEG2RAD(options_.precise_pattern_matching_angle_step_in_deg);
    int minValidScanNum;
    if (scanDatas.size() > BOUNDARY_OF_SPARSE_AND_DENSE_LASER_SCAN)
        minValidScanNum = options_.pattern_matching_min_valid_laser_scan_number_dense_laser;
    else
        minValidScanNum = options_.pattern_matching_min_valid_laser_scan_number_sparse_laser;
    //float errorBenchMark = dock_c * dock_c + dock_d * dock_d + 0.05f * 0.05f;
    float errorThreshold = options_.max_mean_error_in_pattern_matching;
    float errorBenchMark = errorThreshold * errorThreshold;

    InputResidualParams resParams;
    resParams.scanDatas = scanDatas;
    resParams.obsLine = obsLine;
    resParams.originalPoint = originalPoint;
    resParams.index = index;

    for (auto it = coefficient.begin(); it != coefficient.end(); it++)
    {
        float virtualError = error * (*it);
        std::vector<float> disToVlines(4, 0.0);
        disToVlines[0] = dock_a - virtualError;
        disToVlines[1] = dock_a + dock_c - virtualError;
        disToVlines[2] = dock_a + dock_c + dock_b - virtualError;
        disToVlines[3] = dock_a + dock_c * 2 + dock_b - virtualError;
        float disToHomeCenter = dock_a + dock_c + dock_b / 2 - virtualError;
        float dock_error_length = dock_length - virtualError;
        float curMatchedAngle = startMatchingAngle;

        resParams.disToVlines = disToVlines;

        while (curMatchedAngle <= endMatchingAngle + 0.0001f)
        {
            CalcResidualResult calcResidualResult;
            resParams.curMatchedAngle = curMatchedAngle;
            calcResidualSum_(resParams, calcResidualResult, errorThreshold);

            if (calcResidualResult.sigmaNum > minValidScanNum && !calcResidualResult.outOfErrorRange) //add to config
            {
                float mean = calcResidualResult.residualSum / calcResidualResult.sigmaNum;
                if (mean <= errorBenchMark && mean < result.MRes.residualMean)
                {
                    result.MRes.matchingAngle = curMatchedAngle;
                    result.MRes.matchingNum = calcResidualResult.sigmaNum;
                    result.MRes.residualMean = mean;
                    result.MRes.matchingEndAngle = calcResidualResult.matchedObsLine.theta;
                    result.toleranceDis = disToHomeCenter;
                    result.index = index;
                }
            }
            curMatchedAngle += angStep;
        }      
    }
}

void RpHomeDetector::calcIntersection_(const PolarLine& matchedObsLine, const PolarLine& dockLine, geometry_msgs::msg::Point& interPoint_dockLine)
{
    if (std::fabs(std::fabs(matchedObsLine.theta) - M_PI / 2) < 0.000001)
    {
        interPoint_dockLine.x = 0.0;
        interPoint_dockLine.y = static_cast<double>(dockLine.rho / std::sin(dockLine.theta));
    }
    else
    {
        interPoint_dockLine.x = static_cast<double>(dockLine.rho / (std::cos(dockLine.theta) + std::sin(dockLine.theta) * std::tan(matchedObsLine.theta)));
        interPoint_dockLine.y = static_cast<double>(std::tan(matchedObsLine.theta) * interPoint_dockLine.x);
    }
}

void RpHomeDetector::calcEquationsOfVlines_(const InputEquationsParams& equParams, std::vector<PolarLine>& vlines)
{
    /*
    +------+----+--------baseline----------+----+------+
           |    |                          |    |
        vline3 vline2                  vline1  vline0
           |    |                          | c  |
    +------+----+------horizontalline------+----+------+
    */
    if (equParams.curMatchedAngle <= M_PI / 2)
    {
        for (size_t i = 0; i < equParams.disToVlines.size(); i++)
        {
            if (equParams.disToVlines[i] < equParams.distToVerticalLine)
            {
                vlines[i].theta = angles::normalize_angle(equParams.baseLine.theta + M_PI);
                vlines[i].rho = std::fabs(equParams.distToVerticalLine - equParams.disToVlines[i]);
            }
            else
            {
                vlines[i].theta = angles::normalize_angle(equParams.baseLine.theta - M_PI);
                vlines[i].rho = std::fabs(equParams.distToVerticalLine + equParams.disToVlines[i]);
            }
        }
    }
    else
    {
        for (size_t i = 0; i < equParams.disToVlines.size(); i++)
        {
            vlines[i].theta = angles::normalize_angle(equParams.baseLine.theta - M_PI);
            vlines[i].rho = std::fabs(equParams.distToVerticalLine + equParams.disToVlines[i]);
        }
    }
}

void RpHomeDetector::calcResidualSum_(const InputResidualParams& resParams, CalcResidualResult& calcResidualResult, float errorThreshold)
{
    float dock_a = options_.dock_side_line_length;
    float dock_b = options_.dock_center_line_length;
    float dock_c = options_.dock_inner_line_length;
    float dock_d = options_.dock_depth;
    float dock_length = dock_a * 2 + dock_b + dock_c * 2;
    float errorBenchMark = errorThreshold * errorThreshold;

    PolarLine baseLine, horiLine;
    baseLine.rho = (resParams.curMatchedAngle > M_PI / 2) ? std::sin(M_PI - resParams.curMatchedAngle) * resParams.obsLine.rho : std::sin(resParams.curMatchedAngle) * resParams.obsLine.rho;
    baseLine.theta = angles::normalize_angle(resParams.obsLine.theta + resParams.curMatchedAngle - M_PI / 2);
    horiLine.rho = baseLine.rho + dock_d;
    horiLine.theta = baseLine.theta;
    float distToVerticalLine = std::sqrt(std::fabs(square(resParams.obsLine.rho) - square(baseLine.rho)));
    InputEquationsParams equParams;
    equParams.curMatchedAngle = resParams.curMatchedAngle;
    equParams.distToVerticalLine = distToVerticalLine;
    equParams.baseLine = baseLine;
    equParams.disToVlines = resParams.disToVlines;
    std::vector<PolarLine> vlines;
    vlines.resize(4);
    calcEquationsOfVlines_(equParams, vlines);

    int curIndex = resParams.index;
    float lastDistToOriPoint = 0.0f;

    PolarLine matchedObsLine(resParams.obsLine.rho, resParams.obsLine.theta);
    float residualSum = 0.0f;
    bool outOfErrorRange = false;
    int sigmaNum = 0;

    while (true)
    {
        //compute residual sum of at the sure direction
        float deltaTheta;
        if (resParams.obsLine.theta < 0 && matchedObsLine.theta > 0)
            deltaTheta = 2 * M_PI - matchedObsLine.theta + resParams.obsLine.theta;
        else
            deltaTheta = resParams.obsLine.theta - matchedObsLine.theta;
        if (deltaTheta > M_PI - resParams.curMatchedAngle)
            break;
        geometry_msgs::msg::Point interPoint_baseline;
        calcIntersection_(matchedObsLine, baseLine, interPoint_baseline);
        float distToOriPoint = PointUtils::pointDistance(resParams.originalPoint, interPoint_baseline);

        //TPoint2D matchedPoint(matchedObsLine.rho * std::cos(matchedObsLine.theta), matchedObsLine.rho * std::sin(matchedObsLine.theta));
        float sampleResult;

        if (distToOriPoint > dock_length)
        {
            if (distToOriPoint - lastDistToOriPoint > dock_length / 2)
                outOfErrorRange = true;
            break;
        }
        else if (distToOriPoint > resParams.disToVlines[0] && distToOriPoint < resParams.disToVlines[1])
            sampleResult = calcSampleDist_(interPoint_baseline, horiLine, vlines[0], vlines[1], matchedObsLine);
        else if (distToOriPoint > resParams.disToVlines[2] && distToOriPoint < resParams.disToVlines[3])
            sampleResult = calcSampleDist_(interPoint_baseline, horiLine, vlines[2], vlines[3], matchedObsLine);
        else
            sampleResult = PointUtils::pointNorm(interPoint_baseline);
        //here can be optimized
        lastDistToOriPoint = distToOriPoint;
        float residualError = square(matchedObsLine.rho - sampleResult);
        if (residualError > errorBenchMark)
        {
            outOfErrorRange = true;
            break;
        }
        residualSum += residualError;
        //residualSum += fabs(matchedObsLine.rho - sampleResult);
        curIndex += 1;
        //itercopy += 1;
        sigmaNum += 1;
        size_t accessIndex = curIndex % resParams.scanDatas.size();
        bool accessOneCircle = false;
        while (!resParams.scanDatas[accessIndex].valid)
        {
            curIndex += 1;
            accessIndex = curIndex % resParams.scanDatas.size();
            if (accessIndex == (size_t)resParams.index)
            {
                accessOneCircle = true;
                break;
            }
        }
        if (accessOneCircle)
            break;
        matchedObsLine.rho = resParams.scanDatas[accessIndex].dist;
        matchedObsLine.theta = resParams.scanDatas[accessIndex].angle;
    }

    calcResidualResult.matchedObsLine = matchedObsLine;
    calcResidualResult.sigmaNum = sigmaNum;
    calcResidualResult.outOfErrorRange = outOfErrorRange;
    calcResidualResult.residualSum = residualSum;
}

} } }