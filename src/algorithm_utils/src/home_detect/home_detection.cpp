#include "home_detect/home_detection.h"
#include "angles/angles.h"
#include "point_utils.h"
#include "rphoughhelper.h"
#include <chrono>

namespace rp { namespace algorithm { namespace home_detect {

    std::shared_ptr<PatternMatchingHomeDetector> PatternMatchingHomeDetector::create(const rclcpp::Node::SharedPtr& node)
    {
        PatternMatchingHomeDetector::Options options;
        node->declare_parameter("home_detection.dock_side_line_length", 0.04);
        node->declare_parameter("home_detection.dock_center_line_length", 0.16);
        node->declare_parameter("home_detection.dock_inner_line_length", 0.05);
        node->declare_parameter("home_detection.dock_depth", 0.045);
        node->declare_parameter("home_detection.center_error", 0.08);
        node->declare_parameter("home_detection.coarse_range", 1.0472);
        node->declare_parameter("home_detection.coarse_res", 0.01745);
        node->declare_parameter("home_detection.refine_range", 0.08727);
        node->declare_parameter("home_detection.refine_res", 0.00175);
        node->declare_parameter("home_detection.transform_range", 0.05);
        node->declare_parameter("home_detection.transform_res", 0.001);
        node->declare_parameter("home_detection.search_radius", 1.3);
        node->declare_parameter("home_detection.acceptable_error", 0.05);
        node->declare_parameter("home_detection.acceptable_side_count", 4);
        node->declare_parameter("home_detection.search_line_gap", 0.01);
        
        options.side = node->get_parameter("home_detection.dock_side_line_length").as_double();
        options.center = node->get_parameter("home_detection.dock_center_line_length").as_double();
        options.inner = node->get_parameter("home_detection.dock_inner_line_length").as_double();
        options.depth = node->get_parameter("home_detection.dock_depth").as_double();
        options.center_error = node->get_parameter("home_detection.center_error").as_double();
        options.coarse = Eigen::Vector2d(node->get_parameter("home_detection.coarse_range").as_double(), node->get_parameter("home_detection.coarse_res").as_double());
        options.refine = Eigen::Vector2d(node->get_parameter("home_detection.refine_range").as_double(), node->get_parameter("home_detection.refine_res").as_double());
        options.transform = Eigen::Vector2d(node->get_parameter("home_detection.transform_range").as_double(), node->get_parameter("home_detection.transform_res").as_double());
        options.search_radius = node->get_parameter("home_detection.search_radius").as_double();
        options.acceptable_error = node->get_parameter("home_detection.acceptable_error").as_double();
        options.acceptable_side_count = node->get_parameter("home_detection.acceptable_side_count").as_int();
        options.search_line_gap = node->get_parameter("home_detection.search_line_gap").as_double();
        return std::make_shared<PatternMatchingHomeDetector>(options);
    }

    PatternMatchingHomeDetector::PatternMatchingHomeDetector(const Options& options)
        : options_(options)
    {
        createLookuptable();
    }

    bool PatternMatchingHomeDetector::find(const std::vector<interfaces::msg::ScanData>& scan, RpHome& home, float& cost)
    {
        auto start = std::chrono::steady_clock::now();
        
        // 1. find center line and detect
        std::vector<interfaces::msg::HoughLine> lines;
        std::vector<std::pair<int, int>> segments;
        searchCenterLine(scan, options_.search_line_gap, segments, lines); 
        
        std::shared_ptr<PatternModel> best_model = nullptr;
        float best_score = -1;
        // 2. find best candidate
        for (int pos = 0; pos < lines.size(); pos++) {
            if (lines[pos].length < std::max<float>(options_.center_error, options_.center - options_.center_error) 
                || lines[pos].length > (options_.center + options_.center_error)) {
                continue;
            }
            // filter
            geometry_msgs::msg::Point center = PointUtils::createPoint((lines[pos].line.end.x + lines[pos].line.start.x) / 2.,
                                                                       (lines[pos].line.end.y + lines[pos].line.start.y) / 2.);
            if (PointUtils::pointNorm(center) > options_.search_radius) {
                continue;
            }
            std::shared_ptr<PatternModel> model = nullptr;
            auto score = search(scan, segments[pos].first, segments[pos].second, lines[pos], model);
            if (score < 0. || !model) { continue; }
            if (score < best_score || best_score < 0.) {
                best_score = score;
                best_model = model;
            }
        }

        geometry_msgs::msg::Point head = PointUtils::createPoint(0., 0.);
        if (best_model) {
            float theta = best_model->bl.theta + M_PI;
            head = PointUtils::createPoint(best_model->center.x + options_.depth * std::cos(theta),
                                           best_model->center.y + options_.depth * std::sin(theta));
        }

        auto end = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "cost:%ldms best:%8.5f.", duration.count(), best_score);
        if (!best_model || best_score < 0.) {
            cost = -1.;
            RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "not found dock.");
            return false;
        }

        RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "best_model->lcsp: %8.5f, %8.5f", best_model->lcsp.x, best_model->lcsp.y);
        RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "best_model->rcsp: %8.5f, %8.5f", best_model->rcsp.x, best_model->rcsp.y);
        RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "best_model->center: %8.5f, %8.5f", best_model->center.x, best_model->center.y);
        RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "head: %8.5f, %8.5f", head.x, head.y);
        home.homeLine.line.start = best_model->lcsp;
        home.homeLine.line.end = best_model->rcsp;
        home.verticalLine.line.start = best_model->center;
        home.verticalLine.line.end = head;
        cost = best_score;
        return true;
    }

    float PatternMatchingHomeDetector::search(const std::vector<interfaces::msg::ScanData>& scan, int begin, int end,
        const interfaces::msg::HoughLine& candidate, std::shared_ptr<PatternModel>& best)
    {
        best = nullptr;
        std::shared_ptr<PatternModel> best_model = nullptr;
        // 1. coarse search
        geometry_msgs::msg::Point candidate_center = PointUtils::createPoint((candidate.line.end.x + candidate.line.start.x) / 2.,
                                                                             (candidate.line.end.y + candidate.line.start.y) / 2.);
        float theta = angles::normalize_angle(std::atan2((candidate.line.end.y - candidate.line.start.y), (candidate.line.end.x - candidate.line.start.x)) + M_PI_2);
        float rho = candidate_center.x * std::cos(theta) + candidate_center.y * std::sin(theta);
        if (rho < 0.) { theta = angles::normalize_angle(theta + M_PI); }
        float detect_theta = theta;
        float score = search(scan, begin, end, theta, candidate_center, options_.coarse[0], options_.coarse[1], best_model, detect_theta);
        RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "candidate_center: %8.5f, %8.5f", candidate_center.x, candidate_center.y);
        RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "coarse cost: %8.5f ", score);
        if (!best_model || score < 0.) { return -1.; }
        if (score > options_.acceptable_error) {
            return -1.;
        }
        // 2. refine search
        std::vector<geometry_msgs::msg::Point> centers;
        calccenter(scan, begin, end, candidate_center, options_.transform[0], options_.transform[1], centers);
        //RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "refine centers size: %ld", centers.size());
        float best_score = score;
        for (auto iter = centers.begin(); iter != centers.end(); iter++) {
            //RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "refine center: %8.5f, %8.5f", iter->x, iter->y);
            std::shared_ptr<PatternModel> model = best_model;
            float dummy = 0;
            score = search(scan, begin, end, detect_theta, *iter, options_.refine[0], options_.refine[1], model, dummy);
            if (score < best_score || best_score < 0.) {
                best_score = score;
                best_model = model;
            }
        }
        best = best_model;
        RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "refine cost: %8.5f.", best_score);
        return best_score;
    }

    float PatternMatchingHomeDetector::search(const std::vector<interfaces::msg::ScanData>& scan, int begin, int end, float theta,
        const geometry_msgs::msg::Point& center, float range, float resolution, std::shared_ptr<PatternModel>& best, float& best_theta)
    {
        float half_theta_range = range / 2.;
        int size = range / resolution;
        float theta_begin = angles::shortest_angular_distance(theta, half_theta_range);
        float half_length = options_.center / 2. + options_.inner + options_.side;

        best = nullptr;
        float best_score = -1.;

        for (int index = 0; index < size; index++) {
            float detect_theta = angles::normalize_angle(theta_begin + index * resolution);
            auto model = makeModel(detect_theta, center);
            
            int lsi = 0; int rsi = 0;
            float sumdist = 0.; int sumcount = 0;
            for (int pos = begin; pos < end; pos++) {
                auto& laser = scan[pos]; if (!laser.valid || laser.dist < 0.) { continue; }
                PolarLine ray(0., angles::normalize_angle(laser.angle - M_PI_2));
                geometry_msgs::msg::Point intersection;
                if (!intersect(model, ray, intersection)) { continue; }
                sumdist += std::abs(PointUtils::pointNorm(intersection) - laser.dist);
                sumcount++;
            }
            for (int pos = (begin - 1); pos >= 0; pos--) {
                auto& laser = scan[pos]; if (!laser.valid || laser.dist < 0.) { continue; }
                PolarLine ray(0., angles::normalize_angle(laser.angle - M_PI_2));
                geometry_msgs::msg::Point intersection;
                if (!intersect(model, ray, intersection)) { break; }
                sumdist += std::abs(PointUtils::pointNorm(intersection) - laser.dist);
                sumcount++;
                if (PointUtils::pointDistance(model->lcsp, intersection) < options_.side) {
                    lsi++;
                }
                if (PointUtils::pointDistance(model->rcsp, intersection) < options_.side) {
                    rsi++;
                }
            }
            for (int pos = end; pos < scan.size(); pos++) {
                auto& laser = scan[pos]; if (!laser.valid || laser.dist < 0.) { continue; }
                PolarLine ray(0., angles::normalize_angle(laser.angle - M_PI_2));
                geometry_msgs::msg::Point intersection;
                if (!intersect(model, ray, intersection)) { break; }
                sumdist += std::abs(PointUtils::pointNorm(intersection) - laser.dist);
                sumcount++;
                if (PointUtils::pointDistance(model->lcsp, intersection) < options_.side) {
                    lsi++;
                }
                if (PointUtils::pointDistance(model->rcsp, intersection) < options_.side) {
                    rsi++;
                }
            }
            float score = sumdist / sumcount;
            //RCLCPP_INFO(rclcpp::get_logger("PatternMatchingHomeDetector"), "score: %8.5f, lsi: %d, rsi: %d", score, lsi, rsi);
            if (lsi <= options_.acceptable_side_count || rsi <= options_.acceptable_side_count) {
                score = -1.;
            }
            if (score > 0. && score < best_score || best_score < 0.) {
                best_score = score;
                best = model;
                best_theta = detect_theta;
            }
        }
        return best_score;
    }

    void PatternMatchingHomeDetector::searchCenterLine(const std::vector<interfaces::msg::ScanData>& scan, float min_gap,
        std::vector<std::pair<int, int>>& segments, std::vector<interfaces::msg::HoughLine>& lines)
    {
        interfaces::msg::HoughLine line;
        line.rho = 0;
        std::vector<geometry_msgs::msg::Point> points;
        int index = 0;
        for (int pos = 0; pos < scan.size(); pos++) {
            auto& laser = scan[pos];
            if (!laser.valid || laser.dist < 0.) { continue; }
            int count = points.size();
            geometry_msgs::msg::Point point = PointUtils::createPoint(laser.dist * std::cos(laser.angle),
                                                                      laser.dist * std::sin(laser.angle));
            if (count <= 0) {
                line.line.start = point;
                points.push_back(point);
                index = pos;
                continue;
            }
            float gap = PointUtils::pointDistance(point, points.back());
            if (count < 2) {
                if (gap > min_gap) {
                    points.clear();
                    points.push_back(point);
                    line.line.start = point;
                    index = pos;
                    continue;
                }
                line.line.end = point;
                RpHoughHelper::calculateHoughParameter(line);
                points.push_back(point);
                continue;
            }
            if (gap > min_gap) {
                RpHoughHelper::projection(line, points);
                lines.push_back(line);
                segments.push_back(std::make_pair(index, pos - 1));
                points.clear();
                continue;
            }
            line.line.end = point;
            points.push_back(point);
            for (int j = 0; j < 2; j++) {
                RpHoughHelper::nonlinearRegressionGN(line, points);
            }
        }
    }
    
    void PatternMatchingHomeDetector::calccenter(const std::vector<interfaces::msg::ScanData>& scan, int begin, int end,
        const geometry_msgs::msg::Point& candidate, float range, float resolution, std::vector<geometry_msgs::msg::Point>& centers)
    {
        centers.clear();
        centers.push_back(candidate);
        if (range < 0. || resolution < 0.) {
            return;
        }
        float center_norm = PointUtils::pointNorm(candidate);
        float center_theta = std::atan2(candidate.y, candidate.x);
        float transform_theta_res = std::atan2(resolution, center_norm);
        float half_transform_theta_range = std::atan2(range / 2.f, center_norm);

        interfaces::msg::ScanData interpolated;
        for (int pos = begin; pos < (end - 1); pos++) {
            auto& laser = scan[pos];
            if (!laser.valid || !scan[pos + 1].valid) { continue; }
            if (std::abs(angles::shortest_angular_distance(laser.angle, center_theta)) > half_transform_theta_range) { continue; }
            geometry_msgs::msg::Point point = PointUtils::createPoint(laser.dist * std::cos(laser.angle),
                                                                      laser.dist * std::sin(laser.angle));
            centers.push_back(point);
            interpolated = laser;
            while (true) {
                float diff = scan[pos + 1].angle - interpolated.angle;
                if (diff < transform_theta_res) {
                    break;
                }
                float angle = interpolated.angle + transform_theta_res;
                float dist = scan[pos].dist + (scan[pos + 1].dist - scan[pos].dist) * (transform_theta_res / diff);
                geometry_msgs::msg::Point point = PointUtils::createPoint(dist * std::cos(angle),
                                                                          dist * std::sin(angle));
                centers.push_back(point);
                interpolated.angle = angle;
                interpolated.dist = dist;
            }
        }
    }
    
    bool PatternMatchingHomeDetector::intersect(const PolarLine& base, const PolarLine& match,
        geometry_msgs::msg::Point& point)
    {
        // $$ r = x cos\theta + y sin\theta $$
        if (std::abs(angles::shortest_angular_distance(match.theta, base.theta)) < std::numeric_limits<float>::epsilon()) {
            return false;
        }
        if (std::abs(angles::shortest_angular_distance(match.theta, (float)M_PI_2)) < std::numeric_limits<float>::epsilon()
            || std::abs(angles::shortest_angular_distance(match.theta, (float)-M_PI_2)) < std::numeric_limits<float>::epsilon()) {
            point.y = match.rho;
            point.x = -base.tan_theta * point.y + (base.rho / base.cos_theta);
            return true;
        }
        point.y = (match.rho / match.cos_theta - base.rho / base.cos_theta) / (match.tan_theta - base.tan_theta);
        point.x = -match.tan_theta * point.y + (match.rho / match.cos_theta);
        return true;
    }

    bool PatternMatchingHomeDetector::intersect(std::shared_ptr<PatternModel> model, const PolarLine& match,
        geometry_msgs::msg::Point& point)
    {
        float half_length = options_.center / 2. + options_.inner + options_.side;
        // 1. intersect to bl
        if (!intersect(model->bl, match, point)) {
            return false;
        }
        float dist2center = PointUtils::pointDistance(model->center, point);
        if(dist2center > half_length) { 
            return false; 
        }
        
        float dc = options_.center / 2.;
        float ds = dc + options_.inner;
        if (dist2center < dc || dist2center > ds) {
            return true;
        }

        PolarLine ls, lc; 
        geometry_msgs::msg::Point cip, cp, hcip, hcp;
        if (PointUtils::pointDistance(model->lcip, point) < options_.inner) {
            ls = model->sl; lc = model->cl; cip = model->lcip; cp = model->lcp; hcip = model->lhcip; hcp = model->lhcp;
        }
        else {
            ls = model->sr; lc = model->cr; cip = model->rcip; cp = model->rcp; hcip = model->rhcip; hcp = model->rhcp;
        }

        // 2. intersect to hl
        if (!intersect(model->hl, match, point)) {
            return false;
        }

        float dist2hcip = PointUtils::pointDistance(hcip, point);
        float dist2hcp = PointUtils::pointDistance(hcp, point);
        if (dist2hcip < options_.inner && dist2hcp < options_.inner) {
            return true;
        }
        // 3. intersect to ls cl
        bool result = false;
        if (dist2hcip > dist2hcp) {
            result = intersect(lc, match, point);
        }
        else {
            result = intersect(ls, match, point);
        }
        return result;
    }
    
    void PatternMatchingHomeDetector::createLookuptable()
    {
        /*float theta_res = options_.refine[1];
        int size = 2 * M_PI * 2. / theta_res;
        lut_.resize(size);
        for (int pos = 0; pos < size; pos++) {
            float theta = pos * theta_res;
            auto model = makeModel(theta);
            lut_[pos] = model;
        }*/
    }

    std::shared_ptr<PatternModel> PatternMatchingHomeDetector::makeModel(float theta)
    {
        float rho = 0.;
        theta = angles::normalize_angle(theta);
        float thetapi2 = angles::normalize_angle(theta + M_PI_2);
        float dc = options_.center / 2.;
        float dci = dc + options_.inner;
        float dcs = dci + options_.side;
        float sin_theta = std::sin(theta);
        float cos_theta = std::cos(theta);
        float sin_thetanpi2 = std::sin(theta - M_PI_2);
        float cos_thetanpi2 = std::cos(theta - M_PI_2);
        float sin_thetapi2 = std::sin(theta + M_PI_2);
        float cos_thetapi2 = std::cos(theta + M_PI_2);

        auto model = std::make_shared<PatternModel>();
        model->center = PointUtils::createPoint(0., 0.);
        model->lcp = PointUtils::createPoint(cos_thetanpi2 * dc, sin_thetanpi2 * dc);
        model->lcip = PointUtils::createPoint(cos_thetanpi2 * dci, sin_thetanpi2 * dci);
        model->lcsp = PointUtils::createPoint(cos_thetanpi2 * dcs, sin_thetanpi2 * dcs);
        model->rcp = PointUtils::createPoint(cos_thetapi2 * dc, sin_thetapi2 * dc);
        model->rcip = PointUtils::createPoint(cos_thetapi2 * dci, sin_thetapi2 * dci);
        model->rcsp = PointUtils::createPoint(cos_thetapi2 * dcs, sin_thetapi2 * dcs);

        model->bl = PolarLine(rho, theta);
        model->hl = PolarLine(rho + options_.depth, theta);
        model->cl = PolarLine(model->lcp.x * cos_thetapi2 + model->lcp.y * sin_thetapi2, thetapi2);
        model->sl = PolarLine(model->lcip.x * cos_thetapi2 + model->lcip.y * sin_thetapi2, thetapi2);
        model->cr = PolarLine(model->rcp.x * cos_thetapi2 + model->rcp.y * sin_thetapi2, thetapi2);
        model->sr = PolarLine(model->rcip.x * cos_thetapi2 + model->rcip.y * sin_thetapi2, thetapi2);

        intersect(model->hl, model->sl, model->lhcip);
        intersect(model->hl, model->cl, model->lhcp);
        intersect(model->hl, model->sr, model->rhcip);
        intersect(model->hl, model->cr, model->rhcp);

        return model;
    }

    std::shared_ptr<PatternModel> PatternMatchingHomeDetector::moveModel(std::shared_ptr<PatternModel> model, const geometry_msgs::msg::Point& center)
    {
        geometry_msgs::msg::Point relative = PointUtils::createPoint(center.x - model->center.x, center.y - model->center.y);
        auto result = std::make_shared<PatternModel>();
        *result = *model;
        PointUtils::pointAdd(result->center, relative);
        PointUtils::pointAdd(result->lcp, relative);
        PointUtils::pointAdd(result->lcip, relative);
        PointUtils::pointAdd(result->lcsp, relative);
        PointUtils::pointAdd(result->rcp, relative);
        PointUtils::pointAdd(result->rcip, relative);
        PointUtils::pointAdd(result->rcsp, relative);

        result->bl.rho = result->center.x * result->bl.cos_theta + result->center.y * result->bl.sin_theta;
        result->hl.rho = result->bl.rho + options_.depth;
        result->cl.rho = result->lcp.x * result->cl.cos_theta + result->lcp.y * result->cl.sin_theta;
        result->sl.rho = result->lcip.x * result->sl.cos_theta + result->lcip.y * result->sl.sin_theta;
        result->cr.rho = result->rcp.x * result->cr.cos_theta + result->rcp.y * result->cr.sin_theta;
        result->sr.rho = result->rcip.x * result->sr.cos_theta + result->rcip.y * result->sr.sin_theta;

        intersect(result->hl, result->sl, result->lhcip);
        intersect(result->hl, result->cl, result->lhcp);
        intersect(result->hl, result->sr, result->rhcip);
        intersect(result->hl, result->cr, result->rhcp);
        return result;
    }
    
    std::shared_ptr<PatternModel> PatternMatchingHomeDetector::makeModel(float theta, const geometry_msgs::msg::Point& center)
    {
        return moveModel(makeModel(theta), center);
    }

} } }
