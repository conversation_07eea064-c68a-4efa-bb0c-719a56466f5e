#include "algorithm_utils/home_detect/i_home_detect_service.h"

namespace rp { namespace algorithm { namespace home_detect {

    static const std::string s_unknownEnumTypeName("Unknown");
    static const std::string s_detectStatusName_Pending("Pending");
    static const std::string s_detectStatusName_Running("Running");
    static const std::string s_detectStatusName_Succeed("Succeed");
    static const std::string s_detectStatusName_Failed("Failed");
    static const std::string s_detectStatusName_Canceled("Canceled");
    static const std::string s_detectStatusName_Timeout("Timeout");
    //
    const std::string& getDetectStatusName(DetectStatus detectStatus)
    {
        switch (detectStatus)
        {
        case DetectStatusPending:
            return s_detectStatusName_Pending;
        case DetectStatusRunning:
            return s_detectStatusName_Running;
        case DetectStatusSucceed:
            return s_detectStatusName_Succeed;
        case DetectStatusFailed:
            return s_detectStatusName_Failed;
        case DetectStatusCanceled:
            return s_detectStatusName_Canceled;
        case DetectStatusTimeout:
            return s_detectStatusName_Timeout;
        default:
            break;
        }
        return s_unknownEnumTypeName;
    }

    static const std::string s_detectTypeName_ByShell("ByShell");
    static const std::string s_detectTypeName_ByPatternMatch("ByPatternMatch");
    //
    const std::string& getDetectTypeName(DetectType detectType)
    {
        switch (detectType)
        {
        case DetectTypeByShell:
            return s_detectTypeName_ByShell;
        case DetectTypeByPatternMatch:
            return s_detectTypeName_ByPatternMatch;
        default:
            break;
        }
        return s_unknownEnumTypeName;
    }

} } }
