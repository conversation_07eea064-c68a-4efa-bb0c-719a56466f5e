#include "algorithm_utils/shelf_detect/shelf_detect_node.h"
#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <geometry_msgs/msg/point.hpp>
#include <geometry_msgs/msg/pose.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <cmath>
#include <algorithm>
#include <chrono>
#include <thread>
#include <mutex>
#include <sstream>
#include <iomanip>
#include <boost/make_shared.hpp>
#include <Eigen/Dense>
#include <tf2_ros/buffer.h>
#include <tf2/exceptions.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

namespace rp { namespace algorithm { namespace shelf_detect {

ShelfDetectNode::ShelfDetectNode(const rclcpp::NodeOptions & options)
    : Node("shelf_detect", options)
{
    RCLCPP_INFO(this->get_logger(), "Initializing shelf detection node...");
    
    // 初始化TF2
    tf2_buffer_ = std::make_shared<tf2_ros::Buffer>(this->get_clock());
    tf2_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf2_buffer_);
    
    initializeParameters();
    
    initializeSubscribers();
    
    initializeServices();
    
    RCLCPP_INFO(this->get_logger(), "Shelf detection node initialization completed");
}

void ShelfDetectNode::initializeParameters()
{
    this->declare_parameter("laser_topic", "fusion_scan");
    this->declare_parameter("max_columnar_detect_distance", 1.5);
    this->declare_parameter("dbscan_cluster_radius", 0.12);
    this->declare_parameter("dbscan_min_cluster_capacity", 3);
    this->declare_parameter("dbscan_max_cluster_num", 30);
    this->declare_parameter("fov_of_find_tag", 1.047198);
    
    params_.laser_topic = this->get_parameter("laser_topic").as_string();
    params_.max_columnar_detect_distance = this->get_parameter("max_columnar_detect_distance").as_double();
    params_.dbscan_cluster_radius = this->get_parameter("dbscan_cluster_radius").as_double();
    params_.dbscan_min_cluster_capacity = this->get_parameter("dbscan_min_cluster_capacity").as_int();
    params_.dbscan_max_cluster_num = this->get_parameter("dbscan_max_cluster_num").as_int();
    params_.fov_of_find_tag = this->get_parameter("fov_of_find_tag").as_double();
    
    RCLCPP_INFO(this->get_logger(), "Parameter initialization completed:");
    RCLCPP_INFO(this->get_logger(), "Get laser topic: %s", params_.laser_topic.c_str());
}

void ShelfDetectNode::initializeSubscribers()
{ 
    laser_subscription_ = this->create_subscription<sensor_msgs::msg::LaserScan>(
        params_.laser_topic,
        rclcpp::SystemDefaultsQoS(),
        [&](const sensor_msgs::msg::LaserScan::SharedPtr msg) 
        {
            std::lock_guard<std::mutex> lock(received_scan_mutex_);
            received_scan_ = msg;
        });
    
    marker_pub_ = this->create_publisher<visualization_msgs::msg::MarkerArray>(
        "detected_shelf_markers", 10);
    
    points_pub_ = this->create_publisher<visualization_msgs::msg::MarkerArray>(
        "detected_shelf_columnars", 10);
    
    RCLCPP_INFO(this->get_logger(), "Laser scan subscriber initialized for topic: %s", 
                params_.laser_topic.c_str());
}

void ShelfDetectNode::initializeServices()
{
    detect_service_ = this->create_service<interfaces::srv::DetectShelf>(
        "detect_shelf",
        std::bind(&ShelfDetectNode::detectShelvesCallback, this, 
                 std::placeholders::_1, std::placeholders::_2));
    
    RCLCPP_INFO(this->get_logger(), "Services initialization completed");
    RCLCPP_INFO(this->get_logger(), "Service '/detect_shelf' is ready");
}

void ShelfDetectNode::detectShelvesCallback(
    const std::shared_ptr<interfaces::srv::DetectShelf::Request> request,
    std::shared_ptr<interfaces::srv::DetectShelf::Response> response)
{
    auto start_time = std::chrono::high_resolution_clock::now();
    RCLCPP_INFO(this->get_logger(), "Received shelf detection request");
    
    // 验证请求参数
    if (!validateDetectionParameters(*request)) {
        response->success = false;
        response->message = "Invalid detection parameters";
        RCLCPP_WARN(this->get_logger(), "Service call failed: Invalid parameters");
        return;
    }
    
    try {
        RCLCPP_INFO(this->get_logger(), "Requesting laser scan data from topic: %s", 
                   params_.laser_topic.c_str());

        sensor_msgs::msg::LaserScan::SharedPtr laserScan;
        {
            std::lock_guard<std::mutex> lock(received_scan_mutex_);
            laserScan = std::make_shared<sensor_msgs::msg::LaserScan>(*received_scan_);
        }
        
        if (!laserScan || laserScan->ranges.empty()) {
            response->success = false;
            response->message = "Failed to get laser scan data within timeout";
            RCLCPP_WARN(this->get_logger(), "Service call failed: No laser scan data received");
            return;
        }
        
        RCLCPP_INFO(this->get_logger(), "Received laser scan data with %zu points", 
                   laserScan->ranges.size());
        
        // 执行货架检测
        geometry_msgs::msg::PoseStamped shelfDockPoseStamped;
        bool success = performShelfDetection(laserScan, *request, shelfDockPoseStamped);
        if(!success)
        {
            response->success = false;
            response->message = "Shelf detection failed";
            RCLCPP_WARN(this->get_logger(), "Service call failed: Detection failed");
            return;
        }
        
        // 填充响应
        response->success = true;
        response->message = "Shelf detection completed successfully";
        response->shelf_in_robot_view = shelfDockPoseStamped;
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);  
        RCLCPP_INFO(this->get_logger(), "Detection completed in %f seconds", duration.count() / 1e6);
    } 
    catch (const std::exception& e) 
    {
        response->success = false;
        response->message = std::string("Detection failed: ") + e.what();
        RCLCPP_ERROR(this->get_logger(), "Detection failed: %s", e.what());
    }
}

sensor_msgs::msg::LaserScan::SharedPtr ShelfDetectNode::waitForLaserScan(
    const std::string& topic, double timeout_seconds)
{
    sensor_msgs::msg::LaserScan::SharedPtr received_scan = nullptr;
    bool scan_received = false;
    
    laser_subscription_ = this->create_subscription<sensor_msgs::msg::LaserScan>(
        topic, 
        rclcpp::SystemDefaultsQoS(),
        [&](const sensor_msgs::msg::LaserScan::SharedPtr msg) 
        {
            if (!scan_received) 
            {
                received_scan = msg;
                scan_received = true;
                RCLCPP_INFO(this->get_logger(), "Received laser scan data on-demand");
            }
        });

    auto start_time = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::duration<double>(timeout_seconds);
    
    while (!scan_received && rclcpp::ok()) 
    {
        auto current_time = std::chrono::steady_clock::now();
        if (current_time - start_time > timeout_duration) 
        {
            RCLCPP_WARN(this->get_logger(), "Timeout waiting for laser scan data");
            break;
        }

        //std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    // 自动销毁临时订阅器（subscription会在函数结束时自动销毁）
    RCLCPP_INFO(this->get_logger(), "Temporary laser scan subscription destroyed");
    
    return received_scan;
}

bool ShelfDetectNode::performShelfDetection(const sensor_msgs::msg::LaserScan::SharedPtr laserScan, const interfaces::srv::DetectShelf::Request& request, geometry_msgs::msg::PoseStamped& shelfDockPoseStamped)
{
    std::vector<interfaces::msg::ScanData> scanData;
    for (int i = 0; i < laserScan->ranges.size(); i++) 
    {
        interfaces::msg::ScanData scanPoint;
        scanPoint.dist = laserScan->ranges[i];
        scanPoint.angle = laserScan->angle_min + i * laserScan->angle_increment;
        scanPoint.valid = (laserScan->ranges[i] >= laserScan->range_min && 
                     laserScan->ranges[i] <= laserScan->range_max);
        if(scanPoint.valid)
        {
            scanData.push_back(scanPoint);
        }
    }

    std::vector<interfaces::msg::ShelfInfo> shelfColumnarSizeList;
    for (auto& shelf : request.shelves)
    {
        shelfColumnarSizeList.push_back(shelf);
    }

    landingPose_ = request.landing_pose;

    geometry_msgs::msg::TransformStamped transform;
    geometry_msgs::msg::Pose robotPose;
    try 
    {
        // sensor_time 是激光帧的时间戳
        transform = tf2_buffer_->lookupTransform(
            "map",       
            "base_link",  
            laserScan->header.stamp,   
            rclcpp::Duration::from_seconds(0.1) 
        );
        robotPose.position.x = transform.transform.translation.x;
        robotPose.position.y = transform.transform.translation.y;
        robotPose.position.z = transform.transform.translation.z;
        robotPose.orientation = transform.transform.rotation;
    } 
    catch (tf2::TransformException &ex) 
    {
        RCLCPP_WARN(this->get_logger(), "Transform failed: %s", ex.what());
        robotPose.position.x = 0.0;
        robotPose.position.y = 0.0;
        robotPose.position.z = 0.0;
        robotPose.orientation.x = 0.0;
        robotPose.orientation.y = 0.0;
        robotPose.orientation.z = 0.0;
        robotPose.orientation.w = 1.0;
    }

    std::vector<geometry_msgs::msg::Point> columnarsInRobotView;
    std::vector<geometry_msgs::msg::Point> columnars;
    std::vector<rp::algorithm::ClusterResult> columnarsResults;
    std::vector<geometry_msgs::msg::Point> shelfInRobotView;
    std::vector<geometry_msgs::msg::Point> shelf;
    interfaces::msg::ShelfInfo currentShelfColumnarSize;
    
    detectLaserCluster(scanData, columnarsInRobotView, columnarsResults);
    localToWorld(columnarsInRobotView, robotPose, columnars);
    extractShelf(shelfColumnarSizeList, robotPose, columnarsInRobotView, columnars, shelfInRobotView, currentShelfColumnarSize);
    localToWorld(shelfInRobotView, robotPose, shelf);
    
    // 检查是否检测到货架
    if (shelfInRobotView.empty()) {
        RCLCPP_WARN(this->get_logger(), "No shelf detected in robot view");
        return false;
    }

    publishPoints(shelf);
    
    geometry_msgs::msg::Pose shelfDockPose = computeShelfDockPose(shelf, currentShelfColumnarSize, request.dock_allowance);  
    shelfDockPoseStamped.pose = shelfDockPose;
    shelfDockPoseStamped.header.stamp = laserScan->header.stamp;
    shelfDockPoseStamped.header.frame_id = "map";

    std::vector<geometry_msgs::msg::Pose> publishMarkerPoses;
    publishMarkerPoses.push_back(shelfDockPose);
    publishMarkers(publishMarkerPoses);
    
    return true;
}

geometry_msgs::msg::Pose ShelfDetectNode::computeShelfDockPose(const std::vector<geometry_msgs::msg::Point>& shelf, const interfaces::msg::ShelfInfo& currentShelfColumnarSize, const float dock_allowance)
{
    geometry_msgs::msg::Pose shelfDockPose;

    // 先计算两个柱子之间的中点和朝向
    rp::algorithm::projectLine(shelf[0], shelf[1], shelfDockPose);
    
    // 从柱子中点沿着朝向的方向移动货架长度的一半
    double yaw = rp::algorithm::getYawFromPose(shelfDockPose);
    
    // 计算货架深度（考虑回缩）
    double shelf_depth = currentShelfColumnarSize.shelf_columnar_length;
    double offset_distance = shelf_depth / 2.0 - dock_allowance;
    
    // 计算货架中点的最终位置
    shelfDockPose.position.x += offset_distance * cos(yaw);
    shelfDockPose.position.y += offset_distance * sin(yaw);
    
    RCLCPP_INFO(this->get_logger(), "Computed shelf dock pose: (%.3f, %.3f), yaw: %.3f, offset distance: %.3f", 
                shelfDockPose.position.x, shelfDockPose.position.y, yaw * 180.0 / M_PI, offset_distance);
    
    return shelfDockPose;
}

bool ShelfDetectNode::validateDetectionParameters(const interfaces::srv::DetectShelf::Request& request)
{
    if(request.shelves.empty())
    {
        RCLCPP_ERROR(this->get_logger(), "No shelf configs provided");
        return false;
    }

    for (auto& shelf : request.shelves)
    {
        if (shelf.shelf_columnar_length <= 0 || shelf.shelf_columnar_width <= 0 || shelf.shelf_columnar_diameter <= 0)
        {
            RCLCPP_ERROR(this->get_logger(), "Invalid shelf columnar size: length: %f, width: %f, diameter: %f", shelf.shelf_columnar_length, shelf.shelf_columnar_width, shelf.shelf_columnar_diameter);
            return false;
        }
    }
    
    return true;
}

void ShelfDetectNode::detectLaserCluster(std::vector<interfaces::msg::ScanData>& scanData,
        std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<rp::algorithm::ClusterResult>& columnarsResults)
{ 
    filterLaserScan(scanData);

    // dbscan cluster
    if (!cluster_) {
        cluster_ = std::make_shared<rp::algorithm::DbscanCluster>(params_.dbscan_cluster_radius, params_.dbscan_min_cluster_capacity);
    }
    std::vector<interfaces::msg::WeightPose> laserPts;
    for (int pos = 0; pos < scanData.size(); pos++) {
        auto& laser = scanData[pos];
        interfaces::msg::WeightPose point;
        point.pose.x = laser.dist * std::cos(laser.angle);
        point.pose.y = laser.dist * std::sin(laser.angle);
        point.pose.theta = 0.0;
        point.weight = 1.0;
        laserPts.push_back(point);
    }
    cluster_->clusterAndGetResult(laserPts, params_.dbscan_max_cluster_num, columnarsResults);
    for (int i = 0; i < columnarsResults.size(); i++) {
        auto& cluster = columnarsResults[i];
        geometry_msgs::msg::Point point;
        point.x = cluster.mean.pose.x;
        point.y = cluster.mean.pose.y;
        point.z = 0.0;
        columnarsInRobotView.push_back(point);
        RCLCPP_INFO(this->get_logger(), "dbscan cluster mean in robot view:(%f,%f) %d", point.x, point.y, static_cast<int>(cluster.clusterData.size()));
    }  
}

void ShelfDetectNode::filterLaserScan(std::vector<interfaces::msg::ScanData>& scan)
{
    std::vector<interfaces::msg::ScanData> tmpData;
    
    rp::algorithm::RpHoughHelper::lengthFilterData(scan, 1, 0.05, 0.3, 0.1);

    for (auto iter = scan.begin(); iter != scan.end(); iter++)
    {
        if (!(*iter).valid || (*iter).dist > params_.max_columnar_detect_distance || (*iter).dist < 0.)
            continue;
        tmpData.push_back((*iter));
    }

    std::sort(tmpData.begin(), tmpData.end(), [](const interfaces::msg::ScanData& a, const interfaces::msg::ScanData& b) {
        return a.dist > b.dist;
    });

    tmpData.swap(scan);
}

void ShelfDetectNode::localToWorld(const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, const geometry_msgs::msg::Pose& robotPose, std::vector<geometry_msgs::msg::Point>& columnars)
{
    columnars.clear();
    tf2::Transform tf_robot;
    tf2::fromMsg(robotPose, tf_robot);

    for (const auto& pt_local : columnarsInRobotView)
    {
        // 将点从geometry_msgs::Point转为tf2::Vector3
        tf2::Vector3 local_point(pt_local.x, pt_local.y, pt_local.z);

        // 使用机器人Pose进行坐标变换
        tf2::Vector3 world_point = tf_robot * local_point;

        geometry_msgs::msg::Point pt_world;
        pt_world.x = world_point.x();
        pt_world.y = world_point.y();
        pt_world.z = world_point.z();

        columnars.push_back(pt_world);
    }
}


void ShelfDetectNode::extractShelf(const std::vector<interfaces::msg::ShelfInfo>& shelfColumnarSizeList, const geometry_msgs::msg::Pose& robotPose, const std::vector<geometry_msgs::msg::Point>& columnars, const std::vector<geometry_msgs::msg::Point>& columnarsInWorld, std::vector<geometry_msgs::msg::Point>& shelfInRobotView, interfaces::msg::ShelfInfo& currentShelfColumnarSize)
{
    if (columnars.empty() || columnarsInWorld.empty())
    {
        RCLCPP_INFO(this->get_logger(), "lidar scan detect 0 columnar");
        return;
    }

    for (auto i = 0; i < columnars.size(); ++i)
    {   
        RCLCPP_INFO(this->get_logger(), "colunmar before filter in (%f, %f)", columnars[i].x, columnars[i].y);
    }

    if ((columnars.size() < 2 || columnars.size() != columnarsInWorld.size()))
    {
        RCLCPP_INFO(this->get_logger(), "columnar size mismatching, columnar size: %ld, columnar in world size: %ld", columnars.size(), columnarsInWorld.size());
        return;
    }

    for(auto iter = shelfColumnarSizeList.begin(); iter != shelfColumnarSizeList.end(); ++iter)
    {
        currentShelfColumnarSize.shelf_columnar_length = iter->shelf_columnar_length;
        currentShelfColumnarSize.shelf_columnar_width = iter->shelf_columnar_width;
        currentShelfColumnarSize.shelf_columnar_diameter = iter->shelf_columnar_diameter;
        currentShelfColumnarSize.shelf_length_retraction = iter->shelf_length_retraction;

        RCLCPP_INFO(this->get_logger(), "extract shelf, this loop use this size to match: length: %f, width: %f, diameter: %f, length retraction: %f, landing pose: (%f, %f, %f)", currentShelfColumnarSize.shelf_columnar_length, currentShelfColumnarSize.shelf_columnar_width, currentShelfColumnarSize.shelf_columnar_diameter, currentShelfColumnarSize.shelf_length_retraction, landingPose_.position.x, landingPose_.position.y, landingPose_.position.z);

        std::vector<geometry_msgs::msg::Point> filteredColumnars;

        float halfFov = params_.fov_of_find_tag / 2.0f;
        for (uint32_t i = 0; i < columnars.size(); i++)
        {
            auto& tagPose = columnars[i];
            auto& tagInWorld = columnarsInWorld[i];
            Eigen::Vector2f locInView;
            rp::algorithm::project(landingPose_, tagInWorld, locInView);
            auto yaw = atan2f(locInView.y(), locInView.x());
            if (fabs(yaw) > halfFov)
            {
                RCLCPP_INFO(this->get_logger(), "columnar(%.4f, %.4f) is out of fov, yaw to landing pose:%.4f", tagInWorld.x, tagInWorld.y, yaw);
                continue;
            }
            filteredColumnars.push_back(tagPose);
        }

        std::vector<geometry_msgs::msg::Point> tmpFilteredColumnars;
        if (filteredColumnars.size() >= 4 && extractFourShelfColumnar(currentShelfColumnarSize, filteredColumnars, tmpFilteredColumnars))
        {
            RCLCPP_INFO(this->get_logger(), "detect four columnars to match shelf.");

            std::vector<geometry_msgs::msg::Point> neraestCloumnarsInRobotView, otherCloumnarsInRobotView;
            findNearestTwoCloumnars(tmpFilteredColumnars, neraestCloumnarsInRobotView, otherCloumnarsInRobotView);
            filteredColumnars.swap(neraestCloumnarsInRobotView);
        }
        else
        {
            RCLCPP_INFO(this->get_logger(), "can not match four columnars, try to match two columnars.");

            std::vector<geometry_msgs::msg::Point> filteredColumnarsByDistance;
            for (uint32_t i = 0; i < filteredColumnars.size(); i++)
            {
                auto& tagPose = filteredColumnars[i];
                geometry_msgs::msg::Point zeroPoint;
                zeroPoint.x = 0;
                zeroPoint.y = 0;
                zeroPoint.z = 0;
                if (rp::algorithm::PointUtils::pointDistance(tagPose, zeroPoint) > params_.max_columnar_detect_distance)
                {
                    RCLCPP_INFO(this->get_logger(), "distant columnar detected(%.4f, %.4f), ignore it", tagPose.x, tagPose.y);
                    continue;
                }
                filteredColumnarsByDistance.push_back(tagPose);
            }
            filteredColumnars.swap(filteredColumnarsByDistance);

            if (filteredColumnars.size() >= 2)
            {
                std::vector<geometry_msgs::msg::Point> filteredColumnars_tmp;
                for (auto iter = 0; iter < filteredColumnars.size() - 1; iter++)
                {
                    for (auto iter2 = iter + 1; iter2 < filteredColumnars.size(); iter2++)
                    {
                        double distance = std::sqrt(std::pow(filteredColumnars[iter].x - filteredColumnars[iter2].x, 2) + std::pow(filteredColumnars[iter].y - filteredColumnars[iter2].y, 2));
                        if (distance > (currentShelfColumnarSize.shelf_columnar_width - 2 * currentShelfColumnarSize.shelf_columnar_diameter) && distance < currentShelfColumnarSize.shelf_columnar_width)
                        {
                            RCLCPP_INFO(this->get_logger(), "distance between (%f, %f) and (%f, %f) satisfy distance threshold.", filteredColumnars[iter].x, filteredColumnars[iter].y, filteredColumnars[iter2].x, filteredColumnars[iter2].y);
                            filteredColumnars_tmp.clear();
                            filteredColumnars_tmp.push_back(filteredColumnars[iter]);
                            filteredColumnars_tmp.push_back(filteredColumnars[iter2]);
                        }
                    }
                }
                filteredColumnars.swap(filteredColumnars_tmp);
            }
        }

        if (filteredColumnars.empty())
        {
            RCLCPP_INFO(this->get_logger(), "colunmar after filter have 0 colunmar");
            continue;
        }
        else
        {
            for (auto i = 0; i < filteredColumnars.size(); ++i)
            {
                RCLCPP_INFO(this->get_logger(), "colunmar after filter in (%f, %f)", filteredColumnars[i].x, filteredColumnars[i].y);
            }
        }

        if (filteredColumnars.size() != 2)
        {
            RCLCPP_INFO(this->get_logger(), "detect filtered columnar num mismatching config, detect num: %ld", filteredColumnars.size());
            continue;
        }

        shelfInRobotView.swap(filteredColumnars);
        currentShelfColumnarSize.shelf_columnar_diameter = currentShelfColumnarSize.shelf_columnar_diameter;
        currentShelfColumnarSize.shelf_columnar_length = currentShelfColumnarSize.shelf_columnar_length;
        currentShelfColumnarSize.shelf_columnar_width = currentShelfColumnarSize.shelf_columnar_width;
        currentShelfColumnarSize.shelf_length_retraction = currentShelfColumnarSize.shelf_length_retraction;
        return;
    }
    shelfInRobotView.clear();
    currentShelfColumnarSize.shelf_columnar_diameter = -1;
    currentShelfColumnarSize.shelf_columnar_length = -1;
    currentShelfColumnarSize.shelf_columnar_width = -1;
    currentShelfColumnarSize.shelf_length_retraction = -1;
    return;
}

bool ShelfDetectNode::compareYawError(const geometry_msgs::msg::Pose& robotPose, const geometry_msgs::msg::Pose& landingPose, const geometry_msgs::msg::Point& candidatePose1, const geometry_msgs::msg::Point& candidatePose2, double& error)
{
    // 简化的角度误差比较实现
    // 计算两个候选点的中点
    geometry_msgs::msg::Point midPoint;
    midPoint.x = (candidatePose1.x + candidatePose2.x) / 2.0;
    midPoint.y = (candidatePose1.y + candidatePose2.y) / 2.0;
    midPoint.z = 0.0;
    
    // 计算从机器人到中点的角度
    double robotToMidAngle = atan2(midPoint.y - robotPose.position.y, midPoint.x - robotPose.position.x);
    double robotYaw = rp::algorithm::getYawFromPose(robotPose);
    double landingYaw = rp::algorithm::getYawFromPose(landingPose);
    
    // 计算角度误差
    double tmpError = fabs(robotToMidAngle - landingYaw);
    if (tmpError > M_PI) {
        tmpError = 2 * M_PI - tmpError;
    }
    
    RCLCPP_INFO(this->get_logger(), "compareYawError: robot angle: %f, landing angle: %f, error: %f", robotYaw, landingYaw, tmpError);
    
    // 检查是否满足条件
    if (tmpError < 0.175 && tmpError < error)
    {
        error = tmpError;
        return true;
    }

    return false;
}

void ShelfDetectNode::findFourthPoint(const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<geometry_msgs::msg::Point>& guessShelfInRobotView)
{
    geometry_msgs::msg::Point p1, p2, p3, p4;

    double d12 = std::sqrt(std::pow((columnarsInRobotView[0].x - columnarsInRobotView[1].x), 2) + std::pow((columnarsInRobotView[0].y - columnarsInRobotView[1].y), 2));
    double d13 = std::sqrt(std::pow((columnarsInRobotView[0].x - columnarsInRobotView[2].x), 2) + std::pow((columnarsInRobotView[0].y - columnarsInRobotView[2].y), 2));
    double d23 = std::sqrt(std::pow((columnarsInRobotView[1].x - columnarsInRobotView[2].x), 2) + std::pow((columnarsInRobotView[1].y - columnarsInRobotView[2].y), 2));

    if (d12 >= d13 && d12 >= d23) 
    {
        p1 = columnarsInRobotView[0];
        p2 = columnarsInRobotView[2];           
        p3 = columnarsInRobotView[1];
    }
    else if (d13 >= d12 && d13 >= d23) 
    {
        p1 = columnarsInRobotView[0];
        p2 = columnarsInRobotView[1];
        p3 = columnarsInRobotView[2];
    }
    else 
    {
        p1 = columnarsInRobotView[1];
        p2 = columnarsInRobotView[0];
        p3 = columnarsInRobotView[2];
    }

    geometry_msgs::msg::Point center;
    center.x = (p1.x + p3.x) / 2;
    center.y = (p1.y + p3.y) / 2;
    center.z = 0;
    p4.x = 2 * center.x - p2.x;
    p4.y = 2 * center.y - p2.y;
    p4.z = 0;

    guessShelfInRobotView.clear();
    guessShelfInRobotView.push_back(p1);
    guessShelfInRobotView.push_back(p2);
    guessShelfInRobotView.push_back(p3);
    guessShelfInRobotView.push_back(p4);

    RCLCPP_INFO(this->get_logger(), "guess the shelf in (%f, %f) (%f, %f) (%f, %f) (%f, %f)", guessShelfInRobotView[0].x, guessShelfInRobotView[0].y
        , guessShelfInRobotView[1].x, guessShelfInRobotView[1].y
        , guessShelfInRobotView[2].x, guessShelfInRobotView[2].y
        , guessShelfInRobotView[3].x, guessShelfInRobotView[3].y);
}

bool ShelfDetectNode::extractFourShelfColumnar(const interfaces::msg::ShelfInfo& currentShelfColumnarSize, const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<geometry_msgs::msg::Point>& shelvesInRobotView)
{
    shelvesInRobotView.clear();
    int n = columnarsInRobotView.size();
    if (n < 4)
    {
        return false;
    }

    for (int i = 0; i < n - 3; ++i)
    {
        for (int j = i + 1; j < n - 2; ++j)
        {
            for (int k = j + 1; k < n - 1; ++k)
            {
                for (int l = k + 1; l < n; ++l)
                {
                    if (isRectangle(currentShelfColumnarSize, columnarsInRobotView[i], columnarsInRobotView[j], columnarsInRobotView[k], columnarsInRobotView[l]))
                    {
                        RCLCPP_INFO(this->get_logger(), "find the shelves in (%f, %f) (%f, %f) (%f, %f) (%f, %f)", columnarsInRobotView[i].x, columnarsInRobotView[i].y
                            , columnarsInRobotView[j].x, columnarsInRobotView[j].y
                            , columnarsInRobotView[k].x, columnarsInRobotView[k].y
                            , columnarsInRobotView[l].x, columnarsInRobotView[l].y);
                        shelvesInRobotView.push_back(columnarsInRobotView[i]);
                        shelvesInRobotView.push_back(columnarsInRobotView[j]);
                        shelvesInRobotView.push_back(columnarsInRobotView[k]);
                        shelvesInRobotView.push_back(columnarsInRobotView[l]);
                        return true;
                    }
                }
            }
        }
    }
    return false;
}

bool ShelfDetectNode::isRectangle(const interfaces::msg::ShelfInfo& currentShelfColumnarSize, const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2, const geometry_msgs::msg::Point& p3, const geometry_msgs::msg::Point& p4) 
{
    std::vector<double> d;
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p1, p2));
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p1, p3));
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p1, p4));
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p2, p3));
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p2, p4));
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p3, p4));

    std::sort(d.begin(), d.end());

    if ((fabs(d[0] - d[1]) < (currentShelfColumnarSize.shelf_columnar_diameter * 2)) && (fabs(d[2] - d[3]) < (currentShelfColumnarSize.shelf_columnar_diameter * 2)) && (fabs(d[4] - d[5]) < (currentShelfColumnarSize.shelf_columnar_diameter * 2)))
    {
        return true;
    }
    return false;
}

void ShelfDetectNode::findNearestTwoCloumnars(const std::vector<geometry_msgs::msg::Point>& cloumnarsList, std::vector<geometry_msgs::msg::Point>& neraestCloumnars, std::vector<geometry_msgs::msg::Point>& otherCloumnars)
{
    std::vector<std::pair<float, geometry_msgs::msg::Point>> distances;
    for (auto iter = cloumnarsList.begin(); iter != cloumnarsList.end(); ++iter)
    {
        float distance = sqrt(pow(iter->x, 2) + pow(iter->y, 2));
        distances.push_back(std::make_pair(distance, *iter));
    }

    std::sort(distances.begin(), distances.end(), [](const std::pair<float, geometry_msgs::msg::Point>& a, const std::pair<float, geometry_msgs::msg::Point>& b) {
        return a.first < b.first;
        });

    for (int i = 0; i < 2; ++i)
    {
        neraestCloumnars.push_back(distances[i].second);
    }

    for (int i = 2; i < distances.size(); ++i)
    {
        otherCloumnars.push_back(distances[i].second);
    }

    RCLCPP_INFO(this->get_logger(), "the shelf nearset columnars in (%f, %f) (%f, %f)", neraestCloumnars[0].x, neraestCloumnars[0].y
        , neraestCloumnars[1].x, neraestCloumnars[1].y);
}

void ShelfDetectNode::publishMarkers(const std::vector<geometry_msgs::msg::Pose>& markerPoses)
{
    visualization_msgs::msg::MarkerArray marker_array;
    
    // 清除之前的标记
    visualization_msgs::msg::Marker clear_marker;
    clear_marker.header.frame_id = "map";
    clear_marker.header.stamp = this->now();
    clear_marker.ns = "shelf_markers";
    clear_marker.action = visualization_msgs::msg::Marker::DELETEALL;
    marker_array.markers.push_back(clear_marker);
    
    // 清除箭头标记
    visualization_msgs::msg::Marker clear_arrow_marker;
    clear_arrow_marker.header.frame_id = "map";
    clear_arrow_marker.header.stamp = this->now();
    clear_arrow_marker.ns = "shelf_markers_arrows";
    clear_arrow_marker.action = visualization_msgs::msg::Marker::DELETEALL;
    marker_array.markers.push_back(clear_arrow_marker);
    
    // 清除文本标记
    visualization_msgs::msg::Marker clear_text_marker;
    clear_text_marker.header.frame_id = "map";
    clear_text_marker.header.stamp = this->now();
    clear_text_marker.ns = "shelf_markers_yaw_text";
    clear_text_marker.action = visualization_msgs::msg::Marker::DELETEALL;
    marker_array.markers.push_back(clear_text_marker);
    
    // 为每个检测到的pose创建标记
    for (size_t i = 0; i < markerPoses.size(); ++i) {
        // 1. 创建圆柱体标记显示位置
        visualization_msgs::msg::Marker cylinder_marker;
        cylinder_marker.header.frame_id = "map";
        cylinder_marker.header.stamp = this->now();
        cylinder_marker.ns = "shelf_markers";
        cylinder_marker.id = static_cast<int>(i);
        cylinder_marker.type = visualization_msgs::msg::Marker::CYLINDER;
        cylinder_marker.action = visualization_msgs::msg::Marker::ADD;
        
        // 设置位置
        cylinder_marker.pose.position = markerPoses[i].position;
        cylinder_marker.pose.position.z = 0.0;  // 不抬高显示
        
        // 设置方向（圆柱体不需要显示朝向）
        cylinder_marker.pose.orientation.x = 0.0;
        cylinder_marker.pose.orientation.y = 0.0;
        cylinder_marker.pose.orientation.z = 0.0;
        cylinder_marker.pose.orientation.w = 1.0;
        
        // 设置尺寸（圆柱体）
        cylinder_marker.scale.x = 0.1;  // 直径
        cylinder_marker.scale.y = 0.1;  // 直径
        cylinder_marker.scale.z = 0.5;  // 高度
        
        // 设置颜色 (红色)
        cylinder_marker.color.r = 1.0;
        cylinder_marker.color.g = 0.0;
        cylinder_marker.color.b = 0.0;
        cylinder_marker.color.a = 0.8;  // 透明度
        
        // 设置生存时间
        cylinder_marker.lifetime = rclcpp::Duration::from_seconds(10.0);
        
        marker_array.markers.push_back(cylinder_marker);
        
        // 2. 创建箭头标记显示方向
        visualization_msgs::msg::Marker arrow_marker;
        arrow_marker.header.frame_id = "map";
        arrow_marker.header.stamp = this->now();
        arrow_marker.ns = "shelf_markers_arrows";
        arrow_marker.id = static_cast<int>(i);
        arrow_marker.type = visualization_msgs::msg::Marker::ARROW;
        arrow_marker.action = visualization_msgs::msg::Marker::ADD;
        
        // 设置箭头的位置和方向
        arrow_marker.pose = markerPoses[i];
        arrow_marker.pose.position.z = 0.0;  // 不抬高显示
        
        // 设置箭头尺寸
        arrow_marker.scale.x = 0.3;  // 箭头长度
        arrow_marker.scale.y = 0.05; // 箭头宽度
        arrow_marker.scale.z = 0.05; // 箭头高度
        
        // 设置颜色 (绿色)
        arrow_marker.color.r = 0.0;
        arrow_marker.color.g = 1.0;
        arrow_marker.color.b = 0.0;
        arrow_marker.color.a = 0.9;
        
        // 设置生存时间
        arrow_marker.lifetime = rclcpp::Duration::from_seconds(10.0);
        
        marker_array.markers.push_back(arrow_marker);
        
        // 3. 创建文本标记显示yaw角度
        visualization_msgs::msg::Marker text_marker;
        text_marker.header.frame_id = "map";
        text_marker.header.stamp = this->now();
        text_marker.ns = "shelf_markers_yaw_text";
        text_marker.id = static_cast<int>(i);
        text_marker.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
        text_marker.action = visualization_msgs::msg::Marker::ADD;
        
        // 设置文本位置
        text_marker.pose.position = markerPoses[i].position;
        text_marker.pose.position.z = 0.0;  // 不抬高显示
        text_marker.pose.orientation.x = 0.0;
        text_marker.pose.orientation.y = 0.0;
        text_marker.pose.orientation.z = 0.0;
        text_marker.pose.orientation.w = 1.0;
        
        // 计算yaw角度并设置文本内容
        double yaw = atan2(2.0 * (markerPoses[i].orientation.w * markerPoses[i].orientation.z + 
                                 markerPoses[i].orientation.x * markerPoses[i].orientation.y),
                          1.0 - 2.0 * (markerPoses[i].orientation.y * markerPoses[i].orientation.y + 
                                       markerPoses[i].orientation.z * markerPoses[i].orientation.z));
        double yaw_degrees = yaw * 180.0 / M_PI;
        
        std::ostringstream ss;
        ss << std::fixed << std::setprecision(1) << yaw_degrees << "°";
        text_marker.text = ss.str();
        
        // 设置文本尺寸
        text_marker.scale.z = 0.1;  // 文字高度
        
        // 设置颜色 (白色)
        text_marker.color.r = 1.0;
        text_marker.color.g = 1.0;
        text_marker.color.b = 1.0;
        text_marker.color.a = 1.0;
        
        // 设置生存时间
        text_marker.lifetime = rclcpp::Duration::from_seconds(10.0);
        
        marker_array.markers.push_back(text_marker);
    }
    
    // 发布标记数组
    marker_pub_->publish(marker_array);
    
    RCLCPP_INFO(this->get_logger(), "Published %zu pose markers (cylinder + arrow + text) to RViz", markerPoses.size());
}

void ShelfDetectNode::publishPoints(const std::vector<geometry_msgs::msg::Point>& points)
{
    visualization_msgs::msg::MarkerArray marker_array;
    
    // 清除之前的点标记
    visualization_msgs::msg::Marker clear_marker;
    clear_marker.header.frame_id = "map";
    clear_marker.header.stamp = this->now();
    clear_marker.ns = "detected_shelf_columnars";
    clear_marker.action = visualization_msgs::msg::Marker::DELETEALL;
    marker_array.markers.push_back(clear_marker);
    
    // 为每个点创建球形标记
    for (size_t i = 0; i < points.size(); ++i) {
        visualization_msgs::msg::Marker sphere_marker;
        sphere_marker.header.frame_id = "map";
        sphere_marker.header.stamp = this->now();
        sphere_marker.ns = "detected_shelf_columnars";
        sphere_marker.id = static_cast<int>(i);
        sphere_marker.type = visualization_msgs::msg::Marker::SPHERE;
        sphere_marker.action = visualization_msgs::msg::Marker::ADD;
        
        // 设置位置
        sphere_marker.pose.position = points[i];
        sphere_marker.pose.position.z = 0.0;  // 不抬高显示
        
        // 设置方向
        sphere_marker.pose.orientation.x = 0.0;
        sphere_marker.pose.orientation.y = 0.0;
        sphere_marker.pose.orientation.z = 0.0;
        sphere_marker.pose.orientation.w = 1.0;
        
        // 设置尺寸（球体）
        sphere_marker.scale.x = 0.08;  // 直径
        sphere_marker.scale.y = 0.08;  // 直径
        sphere_marker.scale.z = 0.08;  // 直径
        
        // 设置颜色 (蓝色)
        sphere_marker.color.r = 0.0;
        sphere_marker.color.g = 0.0;
        sphere_marker.color.b = 1.0;
        sphere_marker.color.a = 0.8;  // 透明度
        
        // 设置生存时间
        sphere_marker.lifetime = rclcpp::Duration::from_seconds(10.0);
        
        marker_array.markers.push_back(sphere_marker);
    }
    
    // 发布标记数组
    points_pub_->publish(marker_array);
    
    RCLCPP_INFO(this->get_logger(), "Published %zu points to RViz", points.size());
}

}}} // namespace rp::algorithm::shelf_detect

int main(int argc, char ** argv)
{
    rclcpp::init(argc, argv);
    
    std::shared_ptr<rp::algorithm::shelf_detect::ShelfDetectNode> node = nullptr;
    
    try 
    {
        rclcpp::NodeOptions options;
        
        node = std::make_shared<rp::algorithm::shelf_detect::ShelfDetectNode>(options);
        
        RCLCPP_INFO(node->get_logger(), "Shelf detection service node started (on-demand mode)...");
        RCLCPP_INFO(node->get_logger(), "Ready to accept detection requests on service '/detect_shelves'");
        RCLCPP_INFO(node->get_logger(), "Laser data will be requested only when detection is needed");
        
        rclcpp::spin(node);
        
    } 
    catch (const std::exception& e) 
    {
        if (node) 
        {
            RCLCPP_ERROR(node->get_logger(), "Node runtime exception: %s", e.what());
        } 
        else 
        {
            RCLCPP_ERROR(rclcpp::get_logger("shelf_detect_node"), "Node initialization failed: %s", e.what());
        }
        rclcpp::shutdown();
        return 1;
    }
    
    rclcpp::shutdown();
    
    return 0;
}
