#include "shelf_detect/shelf_detect_node.h"
#include <rclcpp/rclcpp.hpp>
#include <cmath>
#include <algorithm>
#include <chrono>
#include <thread>
#include <boost/make_shared.hpp>
#include <Eigen/Dense>

namespace rp { namespace algorithm { namespace shelf_detect {

ShelfDetectNode::ShelfDetectNode(const rclcpp::NodeOptions & options)
    : Node("shelf_detect_node", options)
{
    RCLCPP_INFO(this->get_logger(), "Initializing shelf detection node...");
    
    initializeParameters();
    
    initializeServices();
    
    RCLCPP_INFO(this->get_logger(), "Shelf detection node initialization completed");
}

void ShelfDetectNode::initializeParameters()
{
    this->declare_parameter("laser_topic", "/fusion_scan");
    this->declare_parameter("max_columnar_detect_distance", 1.5);
    this->declare_parameter("dbscan_cluster_radius", 0.12);
    this->declare_parameter("dbscan_min_cluster_capacity", 3);
    this->declare_parameter("dbscan_max_cluster_num", 30);
    this->declare_parameter("fov_of_find_tag", 1.047198);
    
    params_.laser_topic = this->get_parameter("laser_topic").as_string();
    params_.max_columnar_detect_distance = this->get_parameter("max_columnar_detect_distance").as_double();
    params_.dbscan_cluster_radius = this->get_parameter("dbscan_cluster_radius").as_double();
    params_.dbscan_min_cluster_capacity = this->get_parameter("dbscan_min_cluster_capacity").as_int();
    params_.dbscan_max_cluster_num = this->get_parameter("dbscan_max_cluster_num").as_int();
    params_.fov_of_find_tag = this->get_parameter("fov_of_find_tag").as_double();
    
    RCLCPP_INFO(this->get_logger(), "Parameter initialization completed:");
    RCLCPP_INFO(this->get_logger(), "Get laser topic: %s", params_.laser_topic.c_str());
}

void ShelfDetectNode::initializeServices()
{
    // 创建货架检测服务（按需订阅模式）
    detect_service_ = this->create_service<interfaces::srv::DetectShelf>(
        "detect_shelf",
        std::bind(&ShelfDetectNode::detectShelvesCallback, this, 
                 std::placeholders::_1, std::placeholders::_2));
    
    RCLCPP_INFO(this->get_logger(), "Services initialization completed");
    RCLCPP_INFO(this->get_logger(), "Service '/detect_shelf' is ready");
}

void ShelfDetectNode::detectShelvesCallback(
    const std::shared_ptr<interfaces::srv::DetectShelf::Request> request,
    std::shared_ptr<interfaces::srv::DetectShelf::Response> response)
{
    auto start_time = std::chrono::high_resolution_clock::now();
    RCLCPP_INFO(this->get_logger(), "Received shelf detection request");
    
    // 验证请求参数
    if (!validateDetectionParameters(*request)) {
        response->success = false;
        response->message = "Invalid detection parameters";
        response->shelf_in_robot_view.clear();
        RCLCPP_WARN(this->get_logger(), "Service call failed: Invalid parameters");
        return;
    }
    
    try {
        // 按需获取激光雷达数据
        RCLCPP_INFO(this->get_logger(), "Requesting laser scan data from topic: %s", 
                   params_.laser_topic.c_str());
        
        auto laserScan = waitForLaserScan(params_.laser_topic, 1.0);  // 5秒超时
        
        if (!laserScan) {
            response->success = false;
            response->message = "Failed to get laser scan data within timeout";
            response->shelf_in_robot_view.clear();
            RCLCPP_WARN(this->get_logger(), "Service call failed: No laser scan data received");
            return;
        }
        
        RCLCPP_INFO(this->get_logger(), "Received laser scan data with %zu points", 
                   laserScan->ranges.size());
        
        // 执行货架检测
        auto shelf = performShelfDetection(laserScan, *request);
        
        // 填充响应
        response->success = true;
        response->message = "Detection completed successfully";
        response->shelf_in_robot_view.clear();
        
        // 将检测到的货架信息转换为Point消息
        for (const auto& shelfPoint : shelf) 
        {
            response->shelf_in_robot_view.push_back(shelfPoint);
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);     
    } 
    catch (const std::exception& e) 
    {
        response->success = false;
        response->message = std::string("Detection failed: ") + e.what();
        response->shelf_in_robot_view.clear();
        RCLCPP_ERROR(this->get_logger(), "Detection failed: %s", e.what());
    }
}

sensor_msgs::msg::LaserScan::SharedPtr ShelfDetectNode::waitForLaserScan(
    const std::string& topic, double timeout_seconds)
{
    // 创建临时订阅器
    sensor_msgs::msg::LaserScan::SharedPtr received_scan = nullptr;
    bool scan_received = false;
    
    auto subscription = this->create_subscription<sensor_msgs::msg::LaserScan>(
        topic, 
        rclcpp::SensorDataQoS(),
        [&](const sensor_msgs::msg::LaserScan::SharedPtr msg) 
        {
            if (!scan_received) 
            {
                received_scan = msg;
                scan_received = true;
                RCLCPP_DEBUG(this->get_logger(), "Received laser scan data on-demand");
            }
        });
    
    // 等待数据或超时
    auto start_time = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::duration<double>(timeout_seconds);
    
    while (!scan_received && rclcpp::ok()) 
    {
        auto current_time = std::chrono::steady_clock::now();
        if (current_time - start_time > timeout_duration) 
        {
            RCLCPP_WARN(this->get_logger(), "Timeout waiting for laser scan data");
            break;
        }
        
        rclcpp::spin_some(this->get_node_base_interface());
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 自动销毁临时订阅器（subscription会在函数结束时自动销毁）
    RCLCPP_DEBUG(this->get_logger(), "Temporary laser scan subscription destroyed");
    
    return received_scan;
}

std::vector<geometry_msgs::msg::Point> ShelfDetectNode::performShelfDetection(
    const sensor_msgs::msg::LaserScan::SharedPtr laserScan,
    const interfaces::srv::DetectShelf::Request& request)
{
    std::vector<interfaces::msg::ScanData> scanData;
    for (int i = 0; i < laserScan->ranges.size(); i++) 
    {
        interfaces::msg::ScanData scanPoint;
        scanPoint.dist = laserScan->ranges[i];
        scanPoint.angle = laserScan->angle_min + i * laserScan->angle_increment;
        scanData.push_back(scanPoint);
    }

    std::vector<interfaces::msg::ShelfInfo> shelfColumnarSizeList;
    for (auto& shelf : request.shelves)
    {
        shelfColumnarSizeList.push_back(shelf);
    }

    landingPose_ = request.landing_pose;

    std::vector<geometry_msgs::msg::Point> columnarsInRobotView;
    std::vector<geometry_msgs::msg::Point> columnars;
    std::vector<rp::algorithm::ClusterResult> columnarsResults;
    std::vector<geometry_msgs::msg::Point> shelfInRobotView;
    std::vector<geometry_msgs::msg::Point> shelf;
    interfaces::msg::ShelfInfo currentShelfColumnarSize;
    geometry_msgs::msg::Pose robotPose;
    robotPose.position.x = 0;
    robotPose.position.y = 0;
    robotPose.position.z = 0;
    robotPose.orientation.x = 0;
    robotPose.orientation.y = 0;
    robotPose.orientation.z = 0;
    robotPose.orientation.w = 0;
    
    detectLaserCluster(scanData, columnarsInRobotView, columnarsResults);
    localToWorld(columnarsInRobotView, robotPose, columnars);
    extractShelf(shelfColumnarSizeList, robotPose, columnarsInRobotView, columnars, shelfInRobotView, currentShelfColumnarSize);
    localToWorld(shelfInRobotView, robotPose, shelf);
    
    return shelf;
}

bool ShelfDetectNode::validateDetectionParameters(const interfaces::srv::DetectShelf::Request& request)
{
    for (auto& shelf : request.shelves)
    {
        if (shelf.shelf_columnar_length <= 0 || shelf.shelf_columnar_width <= 0 || shelf.shelf_columnar_diameter <= 0)
        {
            RCLCPP_ERROR(this->get_logger(), "Invalid shelf columnar size: length: %f, width: %f, diameter: %f", shelf.shelf_columnar_length, shelf.shelf_columnar_width, shelf.shelf_columnar_diameter);
            return false;
        }
    }
    
    return true;
}

void ShelfDetectNode::detectLaserCluster(const std::vector<interfaces::msg::ScanData>& scanData,
        std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<rp::algorithm::ClusterResult>& columnarsResults)
{  
    auto scan = scanData;
    filterLaserScan(scan);

    // dbscan cluster
    if (!cluster_) {
        cluster_ = std::make_shared<rp::algorithm::DbscanCluster>(params_.dbscan_cluster_radius, params_.dbscan_min_cluster_capacity);
    }
    std::vector<interfaces::msg::WeightPose> laserPts;
    for (int pos = 0; pos < scanData.size(); pos++) {
        auto& laser = scanData[pos];
        interfaces::msg::WeightPose point;
        point.pose.x = laser.dist * std::cos(laser.angle);
        point.pose.y = laser.dist * std::sin(laser.angle);
        point.pose.theta = 0.0;
        point.weight = 1.0;
        laserPts.push_back(point);
    }
    cluster_->clusterAndGetResult(laserPts, params_.dbscan_max_cluster_num, columnarsResults);
    for (int i = 0; i < columnarsResults.size(); i++) {
        auto& cluster = columnarsResults[i];
        geometry_msgs::msg::Point point;
        point.x = cluster.mean.pose.x;
        point.y = cluster.mean.pose.y;
        point.z = 0.0;
        columnarsInRobotView.push_back(point);
        RCLCPP_INFO(this->get_logger(), "dbscan cluster mean in robot view:(%f,%f) %d", point.x, point.y, static_cast<int>(cluster.clusterData.size()));
    }
}

void ShelfDetectNode::filterLaserScan(std::vector<interfaces::msg::ScanData>& scan)
{
    std::vector<interfaces::msg::ScanData> tmpData;
    
    rp::algorithm::RpHoughHelper::lengthFilterData(scan, 1, 0.05, 0.3, 0.1);

    for (auto iter = scan.begin(); iter != scan.end(); iter++)
    {
        if (!(*iter).valid || (*iter).dist > params_.max_columnar_detect_distance || (*iter).dist < 0.)
            continue;
        tmpData.push_back((*iter));
    }

    std::sort(tmpData.begin(), tmpData.end(), [](const interfaces::msg::ScanData& a, const interfaces::msg::ScanData& b) {
        return a.dist > b.dist;
    });

    tmpData.swap(scan);
}

void ShelfDetectNode::localToWorld(const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, const geometry_msgs::msg::Pose& robotPose, std::vector<geometry_msgs::msg::Point>& columnars)
{
    columnars.clear();
    columnars.reserve(columnarsInRobotView.size());
    
    double robotYaw = rp::algorithm::getYawFromPose(robotPose);
    double cosYaw = cos(robotYaw);
    double sinYaw = sin(robotYaw);
    
    for (const auto& localPoint : columnarsInRobotView) {
        geometry_msgs::msg::Point worldPoint;
        
        // 旋转并平移到世界坐标系
        worldPoint.x = robotPose.position.x + localPoint.x * cosYaw - localPoint.y * sinYaw;
        worldPoint.y = robotPose.position.y + localPoint.x * sinYaw + localPoint.y * cosYaw;
        worldPoint.z = robotPose.position.z + localPoint.z;
        
        columnars.push_back(worldPoint);
    }
}

void ShelfDetectNode::extractShelf(const std::vector<interfaces::msg::ShelfInfo>& shelfColumnarSizeList, const geometry_msgs::msg::Pose& robotPose, const std::vector<geometry_msgs::msg::Point>& columnars, const std::vector<geometry_msgs::msg::Point>& columnarsInWorld, std::vector<geometry_msgs::msg::Point>& shelfInRobotView, interfaces::msg::ShelfInfo& currentShelfColumnarSize)
{
    if (columnars.empty() || columnarsInWorld.empty())
    {
        RCLCPP_INFO(this->get_logger(), "lidar scan detect 0 columnar");
        return;
    }

    for (auto i = 0; i < columnars.size(); ++i)
    {   
        RCLCPP_INFO(this->get_logger(), "colunmar before filter in (%f, %f)", columnars[i].x, columnars[i].y);
    }

    if ((columnars.size() < 2 || columnars.size() != columnarsInWorld.size()))
    {
        RCLCPP_INFO(this->get_logger(), "columnar size mismatching, columnar size: %ld, columnar in world size: %ld", columnars.size(), columnarsInWorld.size());
        return;
    }

    for(auto iter = shelfColumnarSizeList.begin(); iter != shelfColumnarSizeList.end(); ++iter)
    {
        currentShelfColumnarSize.shelf_columnar_length = iter->shelf_columnar_length;
        currentShelfColumnarSize.shelf_columnar_width = iter->shelf_columnar_width;
        currentShelfColumnarSize.shelf_columnar_diameter = iter->shelf_columnar_diameter;
        currentShelfColumnarSize.shelf_length_retraction = iter->shelf_length_retraction;

        RCLCPP_INFO(this->get_logger(), "extract shelf, this loop use this size to match: length: %f, width: %f, diameter: %f, length retraction: %f", currentShelfColumnarSize.shelf_columnar_length, currentShelfColumnarSize.shelf_columnar_width, currentShelfColumnarSize.shelf_columnar_diameter, currentShelfColumnarSize.shelf_length_retraction);

        std::vector<geometry_msgs::msg::Point> filteredColumnars;

        float halfFov = params_.fov_of_find_tag / 2.0f;
        for (uint32_t i = 0; i < columnars.size(); i++)
        {
            auto& tagPose = columnars[i];
            auto& tagInWorld = columnarsInWorld[i];
            Eigen::Vector2f locInView;
            rp::algorithm::project(landingPose_, tagInWorld, locInView);
            auto yaw = atan2f(locInView.y(), locInView.x());
            if (fabs(yaw) > halfFov)
            {
                RCLCPP_INFO(this->get_logger(), "columnar(%.4f, %.4f) is out of fov, yaw to landing pose:%.4f", tagInWorld.x, tagInWorld.y, yaw);
                continue;
            }
            filteredColumnars.push_back(tagPose);
        }

        std::vector<geometry_msgs::msg::Point> tmpFilteredColumnars;
        if (filteredColumnars.size() >= 4 && extractFourShelfColumnar(currentShelfColumnarSize, filteredColumnars, tmpFilteredColumnars))
        {
            RCLCPP_INFO(this->get_logger(), "detect four columnars to match shelf.");

            std::vector<geometry_msgs::msg::Point> neraestCloumnarsInRobotView, otherCloumnarsInRobotView;
            findNearestTwoCloumnars(tmpFilteredColumnars, neraestCloumnarsInRobotView, otherCloumnarsInRobotView);
            double minError = std::numeric_limits<double>::max();
            if (compareYawError(robotPose, landingPose_, neraestCloumnarsInRobotView[0], neraestCloumnarsInRobotView[1], minError))
            {
                filteredColumnars.swap(neraestCloumnarsInRobotView);
            }
        }
        else
        {
            RCLCPP_INFO(this->get_logger(), "can not match four columnars, try to match two columnars.");

            std::vector<geometry_msgs::msg::Point> filteredColumnarsByDistance;
            for (uint32_t i = 0; i < filteredColumnars.size(); i++)
            {
                auto& tagPose = filteredColumnars[i];
                geometry_msgs::msg::Point zeroPoint;
                zeroPoint.x = 0;
                zeroPoint.y = 0;
                zeroPoint.z = 0;
                if (rp::algorithm::PointUtils::pointDistance(tagPose, zeroPoint) > params_.max_columnar_detect_distance)
                {
                    RCLCPP_INFO(this->get_logger(), "distant columnar detected(%.4f, %.4f), ignore it", tagPose.x, tagPose.y);
                    continue;
                }
                filteredColumnarsByDistance.push_back(tagPose);
            }
            filteredColumnars.swap(filteredColumnarsByDistance);

            if (filteredColumnars.size() >= 2)
            {
                double minError = std::numeric_limits<double>::max();
                std::vector<geometry_msgs::msg::Point> filteredColumnars_tmp;
                for (auto iter = 0; iter < filteredColumnars.size() - 1; iter++)
                {
                    for (auto iter2 = iter + 1; iter2 < filteredColumnars.size(); iter2++)
                    {
                        double distance = std::sqrt(std::pow(filteredColumnars[iter].x - filteredColumnars[iter2].x, 2) + std::pow(filteredColumnars[iter].y - filteredColumnars[iter2].y, 2));
                        if (distance > (currentShelfColumnarSize.shelf_columnar_width - 2 * currentShelfColumnarSize.shelf_columnar_diameter) && distance < currentShelfColumnarSize.shelf_columnar_width)
                        {
                            RCLCPP_INFO(this->get_logger(), "distance between (%f, %f) and (%f, %f) satisfy distance threshold.", filteredColumnars[iter].x, filteredColumnars[iter].y, filteredColumnars[iter2].x, filteredColumnars[iter2].y);
                            if (compareYawError(robotPose, landingPose_, filteredColumnars[iter], filteredColumnars[iter2], minError))
                            {
                                filteredColumnars_tmp.clear();
                                filteredColumnars_tmp.push_back(filteredColumnars[iter]);
                                filteredColumnars_tmp.push_back(filteredColumnars[iter2]);
                            }
                        }
                    }
                }
                filteredColumnars.swap(filteredColumnars_tmp);
            }
        }

        if (filteredColumnars.empty())
        {
            RCLCPP_INFO(this->get_logger(), "colunmar after filter have 0 colunmar");
            continue;
        }
        else
        {
            for (auto i = 0; i < filteredColumnars.size(); ++i)
            {
                RCLCPP_INFO(this->get_logger(), "colunmar after filter in (%f, %f)", filteredColumnars[i].x, filteredColumnars[i].y);
            }
        }

        if (filteredColumnars.size() != 2)
        {
            RCLCPP_INFO(this->get_logger(), "detect filtered columnar num mismatching config, detect num: %ld", filteredColumnars.size());
            continue;
        }

        shelfInRobotView.swap(filteredColumnars);
        currentShelfColumnarSize.shelf_columnar_diameter = currentShelfColumnarSize.shelf_columnar_diameter;
        currentShelfColumnarSize.shelf_columnar_length = currentShelfColumnarSize.shelf_columnar_length;
        currentShelfColumnarSize.shelf_columnar_width = currentShelfColumnarSize.shelf_columnar_width;
        currentShelfColumnarSize.shelf_length_retraction = currentShelfColumnarSize.shelf_length_retraction;
        return;
    }
    shelfInRobotView.clear();
    currentShelfColumnarSize.shelf_columnar_diameter = -1;
    currentShelfColumnarSize.shelf_columnar_length = -1;
    currentShelfColumnarSize.shelf_columnar_width = -1;
    currentShelfColumnarSize.shelf_length_retraction = -1;
    return;
}

bool ShelfDetectNode::compareYawError(const geometry_msgs::msg::Pose& robotPose, const geometry_msgs::msg::Pose& landingPose, const geometry_msgs::msg::Point& candidatePose1, const geometry_msgs::msg::Point& candidatePose2, double& error)
{
    // 简化的角度误差比较实现
    // 计算两个候选点的中点
    geometry_msgs::msg::Point midPoint;
    midPoint.x = (candidatePose1.x + candidatePose2.x) / 2.0;
    midPoint.y = (candidatePose1.y + candidatePose2.y) / 2.0;
    midPoint.z = 0.0;
    
    // 计算从机器人到中点的角度
    double robotToMidAngle = atan2(midPoint.y - robotPose.position.y, midPoint.x - robotPose.position.x);
    double robotYaw = rp::algorithm::getYawFromPose(robotPose);
    double landingYaw = rp::algorithm::getYawFromPose(landingPose);
    
    // 计算角度误差
    double tmpError = fabs(robotToMidAngle - landingYaw);
    if (tmpError > M_PI) {
        tmpError = 2 * M_PI - tmpError;
    }
    
    RCLCPP_DEBUG(this->get_logger(), "compareYawError: robot angle: %f, landing angle: %f, error: %f", robotYaw, landingYaw, tmpError);
    
    // 检查是否满足条件
    if (tmpError < 0.175 && tmpError < error)
    {
        error = tmpError;
        return true;
    }

    return false;
}

void ShelfDetectNode::findFourthPoint(const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<geometry_msgs::msg::Point>& guessShelfInRobotView)
{
    geometry_msgs::msg::Point p1, p2, p3, p4;

    double d12 = std::sqrt(std::pow((columnarsInRobotView[0].x - columnarsInRobotView[1].x), 2) + std::pow((columnarsInRobotView[0].y - columnarsInRobotView[1].y), 2));
    double d13 = std::sqrt(std::pow((columnarsInRobotView[0].x - columnarsInRobotView[2].x), 2) + std::pow((columnarsInRobotView[0].y - columnarsInRobotView[2].y), 2));
    double d23 = std::sqrt(std::pow((columnarsInRobotView[1].x - columnarsInRobotView[2].x), 2) + std::pow((columnarsInRobotView[1].y - columnarsInRobotView[2].y), 2));

    if (d12 >= d13 && d12 >= d23) 
    {
        p1 = columnarsInRobotView[0];
        p2 = columnarsInRobotView[2];           
        p3 = columnarsInRobotView[1];
    }
    else if (d13 >= d12 && d13 >= d23) 
    {
        p1 = columnarsInRobotView[0];
        p2 = columnarsInRobotView[1];
        p3 = columnarsInRobotView[2];
    }
    else 
    {
        p1 = columnarsInRobotView[1];
        p2 = columnarsInRobotView[0];
        p3 = columnarsInRobotView[2];
    }

    geometry_msgs::msg::Point center;
    center.x = (p1.x + p3.x) / 2;
    center.y = (p1.y + p3.y) / 2;
    center.z = 0;
    p4.x = 2 * center.x - p2.x;
    p4.y = 2 * center.y - p2.y;
    p4.z = 0;

    guessShelfInRobotView.clear();
    guessShelfInRobotView.push_back(p1);
    guessShelfInRobotView.push_back(p2);
    guessShelfInRobotView.push_back(p3);
    guessShelfInRobotView.push_back(p4);

    RCLCPP_INFO(this->get_logger(), "guess the shelf in (%f, %f) (%f, %f) (%f, %f) (%f, %f)", guessShelfInRobotView[0].x, guessShelfInRobotView[0].y
        , guessShelfInRobotView[1].x, guessShelfInRobotView[1].y
        , guessShelfInRobotView[2].x, guessShelfInRobotView[2].y
        , guessShelfInRobotView[3].x, guessShelfInRobotView[3].y);
}

bool ShelfDetectNode::extractFourShelfColumnar(const interfaces::msg::ShelfInfo& currentShelfColumnarSize, const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<geometry_msgs::msg::Point>& shelvesInRobotView)
{
    shelvesInRobotView.clear();
    int n = columnarsInRobotView.size();
    if (n < 4)
    {
        return false;
    }

    for (int i = 0; i < n - 3; ++i)
    {
        for (int j = i + 1; j < n - 2; ++j)
        {
            for (int k = j + 1; k < n - 1; ++k)
            {
                for (int l = k + 1; l < n; ++l)
                {
                    if (isRectangle(currentShelfColumnarSize, columnarsInRobotView[i], columnarsInRobotView[j], columnarsInRobotView[k], columnarsInRobotView[l]))
                    {
                        RCLCPP_INFO(this->get_logger(), "find the shelves in (%f, %f) (%f, %f) (%f, %f) (%f, %f)", columnarsInRobotView[i].x, columnarsInRobotView[i].y
                            , columnarsInRobotView[j].x, columnarsInRobotView[j].y
                            , columnarsInRobotView[k].x, columnarsInRobotView[k].y
                            , columnarsInRobotView[l].x, columnarsInRobotView[l].y);
                        shelvesInRobotView.push_back(columnarsInRobotView[i]);
                        shelvesInRobotView.push_back(columnarsInRobotView[j]);
                        shelvesInRobotView.push_back(columnarsInRobotView[k]);
                        shelvesInRobotView.push_back(columnarsInRobotView[l]);
                        return true;
                    }
                }
            }
        }
    }
    return false;
}

bool ShelfDetectNode::isRectangle(const interfaces::msg::ShelfInfo& currentShelfColumnarSize, const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2, const geometry_msgs::msg::Point& p3, const geometry_msgs::msg::Point& p4) 
{
    std::vector<double> d;
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p1, p2));
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p1, p3));
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p1, p4));
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p2, p3));
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p2, p4));
    d.push_back(rp::algorithm::PointUtils::squaredDistance(p3, p4));

    std::sort(d.begin(), d.end());

    if ((fabs(d[0] - d[1]) < (currentShelfColumnarSize.shelf_columnar_diameter * 2)) && (fabs(d[2] - d[3]) < (currentShelfColumnarSize.shelf_columnar_diameter * 2)) && (fabs(d[4] - d[5]) < (currentShelfColumnarSize.shelf_columnar_diameter * 2)))
    {
        return true;
    }
    return false;
}

void ShelfDetectNode::findNearestTwoCloumnars(const std::vector<geometry_msgs::msg::Point>& cloumnarsList, std::vector<geometry_msgs::msg::Point>& neraestCloumnars, std::vector<geometry_msgs::msg::Point>& otherCloumnars)
{
    std::vector<std::pair<float, geometry_msgs::msg::Point>> distances;
    for (auto iter = cloumnarsList.begin(); iter != cloumnarsList.end(); ++iter)
    {
        float distance = sqrt(pow(iter->x, 2) + pow(iter->y, 2));
        distances.push_back(std::make_pair(distance, *iter));
    }

    std::sort(distances.begin(), distances.end(), [](const std::pair<float, geometry_msgs::msg::Point>& a, const std::pair<float, geometry_msgs::msg::Point>& b) {
        return a.first < b.first;
        });

    for (int i = 0; i < 2; ++i)
    {
        neraestCloumnars.push_back(distances[i].second);
    }

    for (int i = 2; i < distances.size(); ++i)
    {
        otherCloumnars.push_back(distances[i].second);
    }

    RCLCPP_DEBUG(this->get_logger(), "the shelf nearset columnars in (%f, %f) (%f, %f)", neraestCloumnars[0].x, neraestCloumnars[0].y
        , neraestCloumnars[1].x, neraestCloumnars[1].y);
}

}}} // namespace rp::algorithm::shelf_detect

int main(int argc, char ** argv)
{
    rclcpp::init(argc, argv);
    
    std::shared_ptr<rp::algorithm::shelf_detect::ShelfDetectNode> node = nullptr;
    
    try 
    {
        rclcpp::NodeOptions options;
        
        node = std::make_shared<rp::algorithm::shelf_detect::ShelfDetectNode>(options);
        
        RCLCPP_INFO(node->get_logger(), "Shelf detection service node started (on-demand mode)...");
        RCLCPP_INFO(node->get_logger(), "Ready to accept detection requests on service '/detect_shelves'");
        RCLCPP_INFO(node->get_logger(), "Laser data will be requested only when detection is needed");
        
        rclcpp::spin(node);
        
    } 
    catch (const std::exception& e) 
    {
        if (node) 
        {
            RCLCPP_ERROR(node->get_logger(), "Node runtime exception: %s", e.what());
        } 
        else 
        {
            RCLCPP_ERROR(rclcpp::get_logger("shelf_detect_node"), "Node initialization failed: %s", e.what());
        }
        rclcpp::shutdown();
        return 1;
    }
    
    rclcpp::shutdown();
    
    return 0;
}
