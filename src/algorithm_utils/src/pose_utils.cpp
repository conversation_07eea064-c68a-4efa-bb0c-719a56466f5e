#include "algorithm_utils/pose_utils.h"
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <tf2/utils.h>
#include "algorithm_utils/point_utils.h"
#include "angles/angles.h"

namespace rp { namespace algorithm {

void project(const geometry_msgs::msg::Point& base, const geometry_msgs::msg::Point& loc, Eigen::Vector2f& locToBase)
{
    locToBase.x() = static_cast<float>(loc.x - base.x);
    locToBase.y() = static_cast<float>(loc.y - base.y);
}

void project(const geometry_msgs::msg::Point& base, float yaw, const geometry_msgs::msg::Point& loc, Eigen::Vector2f& locInView, const bool& isReversePlan)
{
    Eigen::Rotation2Df rot(-yaw);
    Eigen::Vector2f locToBase;
    project(base, loc, locToBase);
    locInView = rot * locToBase;
    if (isReversePlan)
    {
        locInView.x() = -locInView.x();
        locInView.y() = -locInView.y();
    }
}

void project(const geometry_msgs::msg::Pose& viewPoint, const geometry_msgs::msg::Point& loc, Eigen::Vector2f& locInView, const bool& isReversePlan)
{
    geometry_msgs::msg::Point location = getLocationFromPose(viewPoint);
    double yaw = getYawFromPose(viewPoint);
    project(location, static_cast<float>(yaw), loc, locInView, isReversePlan);
}

double getYawFromPose(const geometry_msgs::msg::Pose& pose)
{
    tf2::Quaternion q(
        pose.orientation.x,
        pose.orientation.y,
        pose.orientation.z,
        pose.orientation.w
    );
    
    tf2::Matrix3x3 m(q);
    double roll, pitch, yaw;
    m.getRPY(roll, pitch, yaw);
    return yaw;
}

geometry_msgs::msg::Point getLocationFromPose(const geometry_msgs::msg::Pose& pose)
{
    geometry_msgs::msg::Point point;
    point.x = pose.position.x;
    point.y = pose.position.y;
    point.z = pose.position.z;
    return point;
}

void projectLine(const geometry_msgs::msg::Point& start, const geometry_msgs::msg::Point& end, geometry_msgs::msg::Pose& poseInView)
{ 
    geometry_msgs::msg::Point center;
    center.x = (start.x + end.x) / 2.0;
    center.y = (start.y + end.y) / 2.0;
    center.z = 0.0;
    
    float centerYaw = atan2(center.y, center.x);
    
    geometry_msgs::msg::Point line = rp::algorithm::PointUtils::pointSubtract(end, start);
    float lineYaw = atan2(line.y, line.x); 
    
    float zVect = line.x * start.y - start.x * line.y;
    double yaw = lineYaw + ((zVect < 0) ? -M_PI_2 : M_PI_2);

    while (yaw > M_PI) yaw -= 2 * M_PI;
    while (yaw < -M_PI) yaw += 2 * M_PI;

    poseInView.position.x = center.x;
    poseInView.position.y = center.y;
    poseInView.position.z = center.z;
    
    tf2::Quaternion q;
    q.setRPY(0, 0, yaw);
    poseInView.orientation.x = q.x();
    poseInView.orientation.y = q.y();
    poseInView.orientation.z = q.z();
    poseInView.orientation.w = q.w();
}

double l2NormAngle(const geometry_msgs::msg::Pose & a, const geometry_msgs::msg::Pose & b)
{
    double angle_a = tf2::getYaw(a.orientation);
    double angle_b = tf2::getYaw(b.orientation);
    double delta_angle = angles::shortest_angular_distance(angle_a, angle_b);
    return fabs(delta_angle);
}

double l2NormPosition(const geometry_msgs::msg::Pose & a, const geometry_msgs::msg::Pose & b)
{
    return sqrt(
        (a.position.x - b.position.x) * (a.position.x - b.position.x) +
        (a.position.y - b.position.y) * (a.position.y - b.position.y));
}

double l2Norm(const geometry_msgs::msg::Pose & a, const geometry_msgs::msg::Pose & b)
{
  double angle_a = tf2::getYaw(a.orientation);
  double angle_b = tf2::getYaw(b.orientation);
  double delta_angle = angles::shortest_angular_distance(angle_a, angle_b);
  return sqrt(
    (a.position.x - b.position.x) * (a.position.x - b.position.x) +
    (a.position.y - b.position.y) * (a.position.y - b.position.y) +
    delta_angle * delta_angle);
}

}}