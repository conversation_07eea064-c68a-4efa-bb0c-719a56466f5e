#include "pose_utils.h"
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>

namespace rp { namespace algorithm {

void project(const geometry_msgs::msg::Point& base, const geometry_msgs::msg::Point& loc, Eigen::Vector2f& locToBase)
{
    locToBase.x() = static_cast<float>(loc.x - base.x);
    locToBase.y() = static_cast<float>(loc.y - base.y);
}

void project(const geometry_msgs::msg::Point& base, float yaw, const geometry_msgs::msg::Point& loc, Eigen::Vector2f& locInView, const bool& isReversePlan)
{
    Eigen::Rotation2Df rot(-yaw);
    Eigen::Vector2f locToBase;
    project(base, loc, locToBase);
    locInView = rot * locToBase;
    if (isReversePlan)
    {
        locInView.x() = -locInView.x();
        locInView.y() = -locInView.y();
    }
}

void project(const geometry_msgs::msg::Pose& viewPoint, const geometry_msgs::msg::Point& loc, Eigen::Vector2f& locInView, const bool& isReversePlan)
{
    geometry_msgs::msg::Point location = getLocationFromPose(viewPoint);
    double yaw = getYawFromPose(viewPoint);
    project(location, static_cast<float>(yaw), loc, locInView, isReversePlan);
}

double getYawFromPose(const geometry_msgs::msg::Pose& pose)
{
    tf2::Quaternion q(
        pose.orientation.x,
        pose.orientation.y,
        pose.orientation.z,
        pose.orientation.w
    );
    
    tf2::Matrix3x3 m(q);
    double roll, pitch, yaw;
    m.getRPY(roll, pitch, yaw);
    return yaw;
}

geometry_msgs::msg::Point getLocationFromPose(const geometry_msgs::msg::Pose& pose)
{
    geometry_msgs::msg::Point point;
    point.x = pose.position.x;
    point.y = pose.position.y;
    point.z = pose.position.z;
    return point;
}

}}