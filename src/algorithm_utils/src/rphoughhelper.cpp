#include <vector>
#include <cmath>
#include <algorithm>
#include <map>
#include <Eigen/Dense>
#include "rphoughhelper.h"
#include "point_utils.h"

namespace rp{ namespace algorithm {

using namespace std;
static const int CONF_FIT_LINE_MAX_ITERATIONS = 30;

bool RpHoughHelper::calculateHoughParameter(interfaces::msg::HoughLine &line)
{
    float &rho = line.rho;
    float &theta = line.theta;
    geometry_msgs::msg::Point &startP = line.line.start;
    geometry_msgs::msg::Point &endP = line.line.end;
    line.length = PointUtils::pointDistance(startP, endP);

    if(startP.x == endP.x && startP.y == endP.y)
        return false;

    if(startP.x == endP.x) {
        theta = 0;
    } else if(startP.y == endP.y) {
        theta = M_PI/2;
    } else {
        float k = (startP.y - endP.y)/(startP.x - endP.x);
        theta = atan(-1/k);
    }

    //Hough transformation
    rho = startP.x * cos(theta) + startP.y * sin(theta);
    if(rho<0)
    {
        rho = -rho;
        theta += M_PI;
    }

    // Normalize angle to [0, 2*pi)
    while(theta < 0) theta += 2 * M_PI;
    while(theta >= 2 * M_PI) theta -= 2 * M_PI;

    line.cos_theta = cos(theta);
    line.sin_theta = sin(theta);
    float rho_check = endP.x * line.cos_theta + endP.y * line.sin_theta;
    float check = abs(rho-rho_check);
    if(check>0.1)
    {
        //should not run here, any time run into here denotes error actually
        return false;
    }

    return true;
}

void RpHoughHelper::projection(interfaces::msg::HoughLine &line, const std::vector<geometry_msgs::msg::Point> &points)
{
    getProjectedPoint(line, points.front(), line.line.start);
    getProjectedPoint(line, points.back(), line.line.end);
    line.length = PointUtils::pointDistance(line.line.start, line.line.end);
}

void RpHoughHelper::getProjectedPoint(const interfaces::msg::HoughLine &line, const geometry_msgs::msg::Point &point, geometry_msgs::msg::Point &projectedPoint)
{
    float distance = point.x * line.cos_theta + point.y * line.sin_theta - line.rho;
    geometry_msgs::msg::Point offset = PointUtils::createPoint(distance * line.cos_theta, distance * line.sin_theta);
    projectedPoint = PointUtils::pointSubtract(point, offset);
}

void RpHoughHelper::nonlinearRegressionGN(interfaces::msg::HoughLine &line, std::vector<geometry_msgs::msg::Point> &points)
{
    // please refer to confluence page for detailed information:
    // https://confluence.slamtec.com/pages/viewpage.action?pageId=2359298
    float theta = line.theta;
    float cosTheta = line.cos_theta;
    float sinTheta = line.sin_theta;
    Eigen::Matrix2f H = Eigen::Matrix2f::Zero();
    Eigen::Vector2f dTr = Eigen::Vector2f::Zero();
    int size = points.size();
    for(int i=0; i<size; i+=2)
    {
        geometry_msgs::msg::Point & point = points.at(i);
        Eigen::Vector2f partialDerivative;
        partialDerivative << point.x * (-sinTheta) + point.y * cosTheta, -1;
        float funVal = point.x * cosTheta + point.y * sinTheta - line.rho;

        dTr[0] += partialDerivative[0] * (-funVal);
        dTr[1] += partialDerivative[1] * (-funVal);

        H(0, 0) += partialDerivative[0] * partialDerivative[0];
        H(1, 1) += partialDerivative[1] * partialDerivative[1];

        H(0, 1) += partialDerivative[0] * partialDerivative[1];

        if(i==0) i = -1;

    }
    H(1, 0) = H(0, 1);

    Eigen::Vector2f searchDir(H.inverse() * dTr);
    if(std::isnan(searchDir[0]) || std::isnan(searchDir[1]))
    {
        searchDir.setZero();
    }
    line.theta += searchDir[0];
    line.rho   += searchDir[1];

    if(line.rho < 0)
    {
        line.rho = -line.rho;
        line.theta += M_PI;
    }

    line.cos_theta = cos(line.theta);
    line.sin_theta = sin(line.theta);
}

void RpHoughHelper::lengthFilterData(
    std::vector<interfaces::msg::ScanData> &rawScanData,
    int countThreshold,
    float rhoBiasThreshold,
    float maxAcceptGap,
    float lengthThreshold,
    bool lowPass)
{
    int maxFailCounter     = 0;
    int backSpace          = 0;

    const size_t n = rawScanData.size();

    float distance = 0;
    geometry_msgs::msg::Point startP = PointUtils::createPoint(0, 0);
    geometry_msgs::msg::Point endP = PointUtils::createPoint(0, 0);
    interfaces::msg::HoughLine line;
    std::vector<geometry_msgs::msg::Point> points;
    std::map<int, int> filterMap;
    int start = 0;
    int failCounter = 0;
    for(size_t i=0; i<n; i++, backSpace++)
    {
        interfaces::msg::ScanData & scanData = rawScanData[i];

        float angle = scanData.angle;

        if(scanData.valid)
        {
            distance = scanData.dist;
            if(distance<0)
            {
                continue;
            }

            int counter = points.size();
            float cos_angle = std::cos(angle);
            float sin_angle = std::sin(angle);
            geometry_msgs::msg::Point point = PointUtils::createPoint(distance*cos_angle, distance*sin_angle);

            if(counter==0) {
                line.line.start = point;
                points.push_back(point);
                backSpace = 0;
                start = i;
            } else {
                float gapThreshold = maxAcceptGap;
                if(counter<2) {
                    float gap = PointUtils::pointDistance(point, points.back());
                    if(gap>gapThreshold) {
                        points.clear();
                        points.push_back(point);
                        failCounter = 0;
                        line.line.start = point;
                        start = i;
                    } else {
                        line.line.end = point;
                        calculateHoughParameter(line);
                        points.push_back(point);
                    }
                    backSpace = 0;
                } else {
                    float rho_check = point.x * line.cos_theta + point.y * line.sin_theta;
                    float check = abs(line.rho-rho_check);
                    float gap = PointUtils::pointDistance(point, points.back());
                    float rhoThreshold = rhoBiasThreshold;
                    if(check>rhoThreshold || gap>gapThreshold) {
                        if(failCounter<maxFailCounter) {
                            failCounter++;
                        } else {
                            if(counter >= countThreshold)
                            {
                                projection(line, points);
                                if(lowPass && line.length > lengthThreshold || !lowPass && line.length < lengthThreshold)
                                {
                                    filterMap[start] = (i - backSpace);
                                }
                            }

                            i -= (backSpace + 1);
                            failCounter = 0;
                            points.clear();
                        }
                    } else {
                        failCounter = 0;
                        backSpace = 0;
                        line.line.end = point;
                        points.push_back(point);
                        for(int j=0; j<2; j++)
                        {
                            nonlinearRegressionGN(line, points);
                        }
                    }
                }
            }
        }
        if(i==n-1 && points.size()>=std::max<int>(2, countThreshold))
        {
            projection(line, points);
            if(lowPass && line.length > lengthThreshold || !lowPass && line.length < lengthThreshold)
            {
                filterMap[start] = (i - backSpace);
            }
        }
    }

    for(std::map<int, int>::iterator it = filterMap.begin(); it!=filterMap.end(); it++)
    {
        int start = it->first;
        int end = it->second;
        for(int i=start; i<end; i++)
        {
            rawScanData[i].valid = false;
        }
    }
}

}}

