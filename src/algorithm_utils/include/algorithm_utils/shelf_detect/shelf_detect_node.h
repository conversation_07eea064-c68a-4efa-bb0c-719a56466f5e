#pragma once

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <std_msgs/msg/header.hpp>
#include <geometry_msgs/msg/pose.hpp>
#include <geometry_msgs/msg/pose2_d.hpp>
#include <geometry_msgs/msg/point.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <interfaces/msg/shelf_info.hpp>
#include <interfaces/msg/scan_data.hpp>
#include <interfaces/msg/weight_pose.hpp>
#include <interfaces/srv/detect_shelf.hpp>
#include <rcl_interfaces/msg/parameter_descriptor.hpp>
#include <rcl_interfaces/msg/set_parameters_result.hpp>
#include "algorithm_utils/dbscan_cluster.h"
#include "algorithm_utils/point_utils.h"
#include "algorithm_utils/rphoughhelper.h"
#include "algorithm_utils/pose_utils.h"
#include <tf2_ros/buffer.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <visualization_msgs/msg/marker.hpp>
#include <visualization_msgs/msg/marker_array.hpp>

#include <memory>
#include <vector>
#include <string>
#include <future>
#include <chrono>

namespace rp { namespace algorithm { namespace shelf_detect {

struct ShelfDetectParams 
{
    std::string laser_topic;
    double max_columnar_detect_distance;
    double dbscan_cluster_radius;
    int dbscan_min_cluster_capacity;
    int dbscan_max_cluster_num;
    double fov_of_find_tag;
};

class ShelfDetectNode : public rclcpp::Node
{
public:
    explicit ShelfDetectNode(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());

    virtual ~ShelfDetectNode() = default;

private:
    void initializeParameters();
    void initializeServices();
    void detectShelvesCallback(const std::shared_ptr<interfaces::srv::DetectShelf::Request> request, std::shared_ptr<interfaces::srv::DetectShelf::Response> response);
    sensor_msgs::msg::LaserScan::SharedPtr waitForLaserScan(const std::string& topic, double timeout_seconds = 5.0);
    bool performShelfDetection(const sensor_msgs::msg::LaserScan::SharedPtr laserScan, const interfaces::srv::DetectShelf::Request& params, geometry_msgs::msg::PoseStamped& shelfDockPoseStamped);
    bool validateDetectionParameters(const interfaces::srv::DetectShelf::Request& request);
    void detectLaserCluster(std::vector<interfaces::msg::ScanData>& scanData, std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<rp::algorithm::ClusterResult>& columnarsResults); 
    void filterLaserScan(std::vector<interfaces::msg::ScanData>& scanData);
    void localToWorld(const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, const geometry_msgs::msg::Pose& robotPose, std::vector<geometry_msgs::msg::Point>& columnars);
    void extractShelf(const std::vector<interfaces::msg::ShelfInfo>& shelfColumnarSizeList, const geometry_msgs::msg::Pose& robotPose, const std::vector<geometry_msgs::msg::Point>& columnars, const std::vector<geometry_msgs::msg::Point>& columnarsInWorld, std::vector<geometry_msgs::msg::Point>& shelfInRobotView, interfaces::msg::ShelfInfo& currentShelfColumnarSize);
    bool extractFourShelfColumnar(const interfaces::msg::ShelfInfo& currentShelfColumnarSize, const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<geometry_msgs::msg::Point>& shelvesInRobotView);
    void findNearestTwoCloumnars(const std::vector<geometry_msgs::msg::Point>& cloumnarsList, std::vector<geometry_msgs::msg::Point>& neraestCloumnars, std::vector<geometry_msgs::msg::Point>& otherCloumnars);
    void findFourthPoint(const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<geometry_msgs::msg::Point>& guessShelfInRobotView);
    bool compareYawError(const geometry_msgs::msg::Pose& robotPose, const geometry_msgs::msg::Pose& landingPose, const geometry_msgs::msg::Point& candidatePose1, const geometry_msgs::msg::Point& candidatePose2, double& error);
    bool isRectangle(const interfaces::msg::ShelfInfo& currentShelfColumnarSize, const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2, const geometry_msgs::msg::Point& p3, const geometry_msgs::msg::Point& p4);
    geometry_msgs::msg::Pose computeShelfDockPose(const geometry_msgs::msg::Pose& robotPose, const std::vector<geometry_msgs::msg::Point>& shelfInRobotView, const interfaces::msg::ShelfInfo& currentShelfColumnarSize, const float dock_allowance, const bool backward);
    void initializeSubscribers();
    void publishMarkers(const std::vector<geometry_msgs::msg::Pose>& columnarsInRobotView);
    void publishPoints(const std::vector<geometry_msgs::msg::Point>& points);
    
private:
    ShelfDetectParams params_;
    rclcpp::Service<interfaces::srv::DetectShelf>::SharedPtr detect_service_;
    std::shared_ptr<rp::algorithm::DbscanCluster> cluster_;
    geometry_msgs::msg::Pose landingPose_;
    rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr laser_subscription_;
    sensor_msgs::msg::LaserScan::SharedPtr received_scan_;
    std::mutex received_scan_mutex_;
    rclcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr marker_pub_;
    rclcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr points_pub_;
    
    // TF2 
    std::shared_ptr<tf2_ros::Buffer> tf2_buffer_;
    std::shared_ptr<tf2_ros::TransformListener> tf2_listener_;
};

}}}
