
#pragma once

#include "home_detection.h"
#include "interfaces/msg/scan_data.hpp"
#include "geometry_msgs/msg/pose.hpp"
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <cstdint>
#include <cassert>

namespace rp { namespace algorithm { namespace home_detect {

    enum DetectStatus
    {
        DetectStatusUnknown = -1, // Note: IDetectHandle::getStatus() SHOULD NOT return DetectStatusUnknown!
        DetectStatusPending,
        DetectStatusRunning,
        DetectStatusSucceed,
        DetectStatusFailed,
        DetectStatusCanceled,
        DetectStatusTimeout
    };
    const std::string& getDetectStatusName(DetectStatus detectStatus);
    inline bool isValidDetectStatus(DetectStatus tStatus)
    {
        return (DetectStatusUnknown < tStatus && tStatus <= DetectStatusTimeout);
    }
    // returns true if the detect status is pending or running.
    inline bool isInDetectingStatus(DetectStatus tStatus)
    {
        assert(isValidDetectStatus(tStatus));
        return DetectStatusPending == tStatus || DetectStatusRunning == tStatus;
    }

    enum DetectType
    {
        DetectTypeByShell,
        DetectTypeByPatternMatch
    };
    const std::string& getDetectTypeName(DetectType detectType);

    enum DetectResultCode
    {
        DetectResultCodeUnknown = -1,
        DetectResultCodeNone,
        DetectResultCodeByCenterLine,
        DetectResultCodeByShellLine,
        DetectResultCodeByPatternMatching
    };

    class IDetectHandle;
    typedef std::shared_ptr<IDetectHandle>                        IDetectHandle_SharedPtr;
    typedef std::function< void(IDetectHandle_SharedPtr) >        DetectCallbackFun;

    struct DetectDesc
    {
        DetectType detectType;
        std::shared_ptr< std::vector<interfaces::msg::ScanData>> scanData;
        geometry_msgs::msg::Pose robot_pose;

        DetectCallbackFun notifyCbFun;
        bool doNotNotifyCbIfFailedToPushTask;
        
        DetectDesc()
            : detectType(DetectTypeByShell)
            , robot_pose(geometry_msgs::msg::Pose())
            , doNotNotifyCbIfFailedToPushTask(false)
        {
            //
        }
    };

    struct DetectResult
    {
        DetectResultCode resCode;
        RpHome home;
        float funcValue;

        std::uint64_t msOfEnd;                         // elapsed MS from the detect beginning to finished or failed.

        DetectResult()
            : resCode(DetectResultCodeNone)
            , funcValue(-1.0f)
            , msOfEnd(0)
        {
            //
        }
    };

    class IDetectHandleUserData
    {
    public:
        virtual ~IDetectHandleUserData() {}
    };
    typedef std::shared_ptr<IDetectHandleUserData>         IDetectHandleUserData_SharedPtr;

    class IDetectHandle
    {
    public:
        typedef IDetectHandleUserData_SharedPtr         user_data_shared_ptr; // just for the caller, the path finder will not use it.
        typedef DetectCallbackFun                       detect_cb_fun_t;

    public:
        virtual ~IDetectHandle() {}

        virtual user_data_shared_ptr getUserData() const = 0; // the user data specified by the caller, the path finder will not use it.

        virtual DetectStatus getStatus() const = 0; // Note: this interface SHOULD NOT return DetectStatusUnknown!

        virtual DetectStatus waitUntilDone() const = 0;                     // wait until "!isInDetectingStatus(status)".
        virtual DetectStatus waitFor(std::uint32_t maxWaitMS) const = 0;    // wait "maxWaitMS" milliseconds for "!isInDetectingStatus(status)".

        virtual bool getDetectResult(DetectResult& destRes) const = 0;
        virtual void cancel() = 0;
    };

    class IHomeDetectService
    {
    public:
        typedef IDetectHandleUserData_SharedPtr         user_data_shared_ptr; // just for the caller, the path finder will not use it.

    public:
        virtual ~IHomeDetectService() {}

        virtual bool onStart() = 0;
        virtual bool onStop() = 0;

        virtual IDetectHandle_SharedPtr detectHome(const DetectDesc& desc, const user_data_shared_ptr& userDat = nullptr) = 0;
        virtual void abortAll() = 0;
    };

} } }
