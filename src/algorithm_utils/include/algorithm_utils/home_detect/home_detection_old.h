#pragma once

#include "home_detection_common.h"
#include "rclcpp/rclcpp.hpp"
#include <memory>
#include "interfaces/msg/scan_data.hpp"
#include "geometry_msgs/msg/point.hpp"

namespace rp { namespace algorithm { namespace home_detect {

class RpHomeDetector {
public:
    static std::shared_ptr<RpHomeDetector> create(const rclcpp::Node::SharedPtr& node);
public:
    struct Options {
        float dock_side_line_length;
        float dock_center_line_length;
        float dock_inner_line_length;
        float dock_depth;
        float empirical_base_center_adjust_distance_in_pattern_matching;
        float empirical_base_center_adjust_depth_in_pattern_matching;
        float precise_pattern_matching_angle_step_in_deg;
        float precise_pattern_matching_search_angle_range_in_deg;
        float pattern_matching_min_valid_laser_scan_number_sparse_laser;
        float pattern_matching_min_valid_laser_scan_number_dense_laser;
        float max_mean_error_in_pattern_matching;
        float pattern_matching_max_search_radius;
        float rough_pattern_matching_angle_step_in_deg;
        float pattern_matching_min_angle_range_number_dense_laser;
        float pattern_matching_min_angle_range_number_sparse_laser;
        float pattern_matching_max_residual_mean_difference_threshold;
        bool enable_length_filter_in_pattern_matching;
    };
public:
    RpHomeDetector(const Options& options);
    virtual ~RpHomeDetector() {}

public:
    bool detectRelativeHomeByPatternMatch(const std::vector<interfaces::msg::ScanData>& scanDatas, RpHome& home, float& funcValue);

private:
    bool detectHomeByPatternMatching_(const std::vector<interfaces::msg::ScanData>& scanDatas, RpHome& home, float& funcValue);
    float calcSampleDist_(const geometry_msgs::msg::Point& baseInterPoint, const PolarLine& horiLine, const PolarLine& vlineLeft, const PolarLine& vlineRight, const PolarLine& matchedLine);
    bool catchTheBestMatchingResult_(const std::vector<interfaces::msg::ScanData>& scanDatas, const std::map<int, MatchingResult>& results, RpHome& home, float& funcValue);
    void calcTheBestResultAtFixedStart_(const std::vector<interfaces::msg::ScanData>& scanDatas, int index, float error, ToleranceMatchingResult& result);
    void calcIntersection_(const PolarLine& matchedObsLine, const PolarLine& dockLine, geometry_msgs::msg::Point& interPoint_dockLine);
    void calcEquationsOfVlines_(const InputEquationsParams& equParams, std::vector<PolarLine>& vlines);
    void calcResidualSum_(const InputResidualParams& resParams, CalcResidualResult& calcResidualResult, float errorThreshold);

private:
    Options options_;
};

} } }