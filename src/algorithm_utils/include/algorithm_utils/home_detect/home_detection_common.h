#pragma once
#include "interfaces/msg/line.hpp"
#include "interfaces/msg/scan_data.hpp"
#include "geometry_msgs/msg/point.hpp"
#include <cmath>

namespace rp { namespace algorithm { namespace home_detect {

    struct PolarLine {
        float rho;
        float theta;
        float cos_theta;
        float sin_theta;
        float tan_theta;
        PolarLine(): rho(0.), theta(0.), cos_theta(std::cos(theta)), sin_theta(std::sin(theta)), tan_theta(std::tan(theta)){}
        PolarLine(float r, float beta) : rho(r), theta(beta), cos_theta(std::cos(theta)), sin_theta(std::sin(theta)), tan_theta(std::tan(theta)) {}
    };

    struct RpHome {
        interfaces::msg::Line homeLine;
        interfaces::msg::Line verticalLine;

        geometry_msgs::msg::Point getHomeCentralPoint() const
        { 
            return verticalLine.start;
        }

        float getHomeHeading() const
        {
            return atan2(verticalLine.end.y - verticalLine.start.y, verticalLine.end.x - verticalLine.start.x);
        }
    };

    struct MatchingResult {
        int matchingNum;
        float residualMean;
        float matchingAngle;
        float matchingEndAngle;
    };

    struct ToleranceMatchingResult {
        MatchingResult MRes;
        float toleranceDis;
        int index;
    };

    struct InputEquationsParams {
        float curMatchedAngle;
        float distToVerticalLine;
        PolarLine baseLine;
        std::vector<float> disToVlines;
    };

    struct InputResidualParams {
        std::vector<interfaces::msg::ScanData> scanDatas;
        std::vector<float> disToVlines;
        PolarLine obsLine;
        geometry_msgs::msg::Point originalPoint;
        float curMatchedAngle;
        int index;
    };

    struct CalcResidualResult {
        PolarLine matchedObsLine;
        int sigmaNum;
        bool outOfErrorRange;
        float residualSum;
    };

} } }