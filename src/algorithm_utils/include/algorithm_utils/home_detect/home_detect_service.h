#pragma once

#include "i_home_detect_service.h"
#include "home_detection.h"
#include "home_detection_old.h"
#include "rclcpp/rclcpp.hpp"
#include <thread>
#include <mutex>
#include <condition_variable>
#include <list>
#include <atomic>
#include <cstdint>
#include <cassert>
#include <memory>

namespace rp { namespace algorithm { namespace home_detect {

    class DetectHandleBase
        : public IDetectHandle
        , public std::enable_shared_from_this<DetectHandleBase>
    {
    public:
        DetectHandleBase(const user_data_shared_ptr& userDat, const detect_cb_fun_t& cbFun);
        virtual ~DetectHandleBase();

        DetectHandleBase(const DetectHandleBase&) = delete;
        DetectHandleBase& operator=(const DetectHandleBase&) = delete;

        virtual user_data_shared_ptr getUserData() const override;

        virtual DetectStatus getStatus() const override;

        virtual DetectStatus waitUntilDone() const override;
        virtual DetectStatus waitFor(std::uint32_t maxWaitMS) const override;

    public:
        virtual void noNotifySetStatus(DetectStatus tStatus);
        virtual void finishAndNotify(DetectStatus tStatus, bool bCallCbFun = true);

    protected:
        struct is_finished_status_predicator
        {
            explicit is_finished_status_predicator(const DetectHandleBase* detectHandlePtr)
                : detectHandlePtr_(detectHandlePtr)
            {
                assert(nullptr != detectHandlePtr_);
            }

            inline bool operator()() const
            {
                const auto tStatus = detectHandlePtr_->status_.load();
                return !isInDetectingStatus(tStatus);
            }

        private:
            const DetectHandleBase* const detectHandlePtr_;
        };

    private:
        mutable std::mutex userDataLock_; // it is better to let the user data be thread-safe.
        user_data_shared_ptr const userData_; // JUST for the user.

    protected:
        std::atomic<DetectStatus> status_;

        detect_cb_fun_t detectCbFun_;

        mutable std::mutex notifyLock_;
        mutable std::condition_variable notifyCondVar_;
    };

    class HomeDetectService : public IHomeDetectService
    {
    private:
        struct detect_task_t
        {
        public:
            std::atomic<bool> cancellation;
            
            DetectDesc desc;

            DetectResult detectResult;

        public:
            explicit detect_task_t(const DetectDesc& tDetectDesc);
        };
        typedef std::shared_ptr<detect_task_t>           detect_task_shared_ptr;

        class detect_handle_t: public DetectHandleBase
        {
        public:
            explicit detect_handle_t(const DetectDesc& desc, const user_data_shared_ptr& userDat);
            virtual ~detect_handle_t();

            virtual bool getDetectResult(DetectResult& destRes) const override;
            virtual void cancel() override;

        public:
            const detect_task_shared_ptr& task() { return task_; }

        private:
            detect_task_shared_ptr task_;
        };
        typedef std::shared_ptr<detect_handle_t>             detect_handle_shared_ptr;

    public:
        HomeDetectService(const rclcpp::Node::SharedPtr& node);
        virtual ~HomeDetectService();

    public:
        virtual bool onStart() override;
        virtual bool onStop() override;

        virtual std::shared_ptr<IDetectHandle> detectHome(const DetectDesc& desc, const user_data_shared_ptr& userDat = nullptr) override;
        virtual void abortAll() override;

    private:
        void worker_();
        
        void workerPerformRunning_();

        void performDetectHome_(detect_handle_shared_ptr hTask);
        DetectStatus doDetectHome_(detect_handle_shared_ptr hTask);

        void finishDetectHome_(const detect_handle_shared_ptr& hTask, DetectStatus tStatus, std::uint64_t msOfEnd);

    private:
        rclcpp::Node::SharedPtr node_;

        std::mutex workLock_;
        std::condition_variable workCond_;

        std::thread workThread_;
        std::atomic_bool isWorking_;

        std::list<detect_handle_shared_ptr> jobs_;
        detect_handle_shared_ptr currTask_;
        bool using_pattern_matching_old_;
        std::shared_ptr<PatternMatchingHomeDetector> detector_;
        std::shared_ptr<RpHomeDetector> detector_old_;
    };

} } }