#pragma once

#include <geometry_msgs/msg/pose.hpp>
#include <geometry_msgs/msg/point.hpp>
#include <geometry_msgs/msg/pose2_d.hpp>
#include <Eigen/Dense>
#include <Eigen/Geometry>
#include <cmath>

namespace rp { namespace algorithm {

// 类型别名，使用Eigen向量
using Vector2f = Eigen::Vector2f;
using Location = geometry_msgs::msg::Point;
using Pose = geometry_msgs::msg::Pose;

/**
 * @brief 计算两个位置之间的向量
 * @param base 基准位置
 * @param loc 目标位置
 * @param locToBase 输出向量（从base到loc）
 */
void project(const geometry_msgs::msg::Point& base, const geometry_msgs::msg::Point& loc, Eigen::Vector2f& locToBase);

/**
 * @brief 将位置投影到指定视角坐标系中
 * @param base 基准位置
 * @param yaw 旋转角度（弧度）
 * @param loc 要投影的位置
 * @param locInView 投影后的坐标
 * @param isReversePlan 是否反向规划
 */
void project(const geometry_msgs::msg::Point& base, float yaw, const geometry_msgs::msg::Point& loc, Eigen::Vector2f& locInView, const bool& isReversePlan = false);

/**
 * @brief 将位置投影到姿态坐标系中
 * @param viewPoint 视角姿态
 * @param loc 要投影的位置
 * @param locInView 投影后的坐标
 * @param isReversePlan 是否反向规划
 */
void project(const geometry_msgs::msg::Pose& viewPoint, const geometry_msgs::msg::Point& loc, Eigen::Vector2f& locInView, const bool& isReversePlan = false);

/**
 * @brief 从四元数中提取yaw角度
 * @param pose 包含四元数的姿态
 * @return yaw角度（弧度）
 */
double getYawFromPose(const geometry_msgs::msg::Pose& pose);

/**
 * @brief 获取姿态的位置部分
 * @param pose 姿态
 * @return 位置点
 */
geometry_msgs::msg::Point getLocationFromPose(const geometry_msgs::msg::Pose& pose);

}}
