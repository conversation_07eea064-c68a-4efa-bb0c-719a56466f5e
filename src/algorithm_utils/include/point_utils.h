#pragma once
#include <cmath>
#include "geometry_msgs/msg/point.hpp"

namespace rp { namespace algorithm {

class PointUtils 
{
public:
    // Point operation helper functions
    static float pointDistance(const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2);

    static geometry_msgs::msg::Point pointSubtract(const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2);

    static geometry_msgs::msg::Point createPoint(float x, float y);

    static void pointAdd(geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2);

    static geometry_msgs::msg::Point pointMultiply(const geometry_msgs::msg::Point& p, float scalar);

    static geometry_msgs::msg::Point pointDivide(const geometry_msgs::msg::Point& p, float scalar);

    static float pointNorm(const geometry_msgs::msg::Point& p);

    static float squaredDistance(const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2);
};

}} // namespace rp::algorithm 