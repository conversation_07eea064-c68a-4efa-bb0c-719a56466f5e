#pragma once
#include <vector>
#include <cmath>
#include <Eigen/Dense>
#include "interfaces/msg/hough_line.hpp"
#include "interfaces/msg/scan_data.hpp"
#include "geometry_msgs/msg/point.hpp"
#include "point_utils.h"

namespace rp{ namespace algorithm {

class RpHoughHelper 
{
public:
    // Core algorithm functions
    static bool calculateHoughParameter(interfaces::msg::HoughLine &line);

    static void nonlinearRegressionGN(interfaces::msg::HoughLine &line, std::vector<geometry_msgs::msg::Point> &points);

    static void projection(interfaces::msg::<PERSON><PERSON><PERSON><PERSON> &line, const std::vector<geometry_msgs::msg::Point> &points);

    static void getProjectedPoint(const interfaces::msg::HoughLine &line, const geometry_msgs::msg::Point &point, geometry_msgs::msg::Point &projectedPoint);

    static void lengthFilterData(
        std::vector<interfaces::msg::ScanData> &rawScanData, 
        int countThreshold = 10,
        float rhoBiasThreshold = 0.05,
        float maxAcceptGap = 0.3,
        float lengthThreshold=0.2,
        bool lowPass=true);

private:
};

}}