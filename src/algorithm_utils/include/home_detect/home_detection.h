#pragma once
#include "interfaces/msg/hough_line.hpp"
#include "interfaces/msg/scan_data.hpp"
#include "geometry_msgs/msg/point.hpp"
#include <vector>
#include <memory>
#include <Eigen/Dense>
#include <cmath>
#include <rclcpp/rclcpp.hpp>

namespace rp { namespace algorithm { namespace home_detect {
    struct PolarLine {
        float rho;
        float theta;
        float cos_theta;
        float sin_theta;
        float tan_theta;
        PolarLine(): rho(0.), theta(0.), cos_theta(std::cos(theta)), sin_theta(std::sin(theta)), tan_theta(std::tan(theta)){}
        PolarLine(float r, float beta) : rho(r), theta(beta), cos_theta(std::cos(theta)), sin_theta(std::sin(theta)), tan_theta(std::tan(theta)) {}
    };

    struct RpHome {
        interfaces::msg::HoughLine homeLine;
        interfaces::msg::HoughLine verticalLine;

        geometry_msgs::msg::Point getHomeCentralPoint() const
        { 
            return verticalLine.line.start;
        }

        float getHomeHeading() const
        {
            return atan2(verticalLine.line.end.y - verticalLine.line.start.y, verticalLine.line.end.x - verticalLine.line.start.x);
        }
    };

    /*
    *
    *    lcsp  lcip  lcp  center  rcp rcip  rcsp
    *    +------+    +------+------+    +------+
    *    |      |    |      bl     |    |      |
    *    |   sl |    | cl       cr |    | sr   |
    *    |      +----+             +----+      |
    *    |   lhcip  lhcp    hl   rhcip rhcp    |
    *    |_____________________________________|
    */
    class PatternModel {
    public:
        // base line, horizontall line
        PolarLine bl, hl;
        // center left line, center right line, side left line, side right line
        PolarLine cl, cr, sl, sr;
        geometry_msgs::msg::Point center;
        geometry_msgs::msg::Point lcp;
        geometry_msgs::msg::Point lcip;
        geometry_msgs::msg::Point lcsp;
        geometry_msgs::msg::Point lhcip;
        geometry_msgs::msg::Point lhcp;
        geometry_msgs::msg::Point rcp;
        geometry_msgs::msg::Point rcip;
        geometry_msgs::msg::Point rcsp;
        geometry_msgs::msg::Point rhcip;
        geometry_msgs::msg::Point rhcp;
    };

    /* 
    *                 home pattern
    *      side          center
    *    +------+    +------------+    +------+  
    *    |      |    |            |    |      |    
    *    |      |    |  depth     |    |      |
    *    |      +----+            +----+      |  
    *    |       inner                        |
    *    |____________________________________|
    */
    class PatternMatchingHomeDetector {
    public:
        static std::shared_ptr<PatternMatchingHomeDetector> create(const rclcpp::Node::SharedPtr& node);
    public:
        struct Options {
            float side, center, inner, depth;
            float center_error;
            // [0]: range, [1]: resolution
            Eigen::Vector2d coarse;
            Eigen::Vector2d refine;
            Eigen::Vector2d transform;
            float search_radius;
            float acceptable_error;
            int acceptable_side_count;
            float search_line_gap;
        };
    public:
        PatternMatchingHomeDetector(const Options& options);
        virtual ~PatternMatchingHomeDetector() {}
    public:
        bool find(const std::vector<interfaces::msg::ScanData>& scan, RpHome& home, float& cost);
    protected:
        float search(const std::vector<interfaces::msg::ScanData>& scan, int begin, int end,
            const interfaces::msg::HoughLine& candidate, std::shared_ptr<PatternModel>& best);
        float search(const std::vector<interfaces::msg::ScanData>& scan, int begin, int end,
            float theta, const geometry_msgs::msg::Point& center, float range, float resolution, std::shared_ptr<PatternModel>& best, float& best_theta);

        void searchCenterLine(const std::vector<interfaces::msg::ScanData>& scan, float min_gap, 
            std::vector<std::pair<int, int>>& segments, std::vector<interfaces::msg::HoughLine>& lines);
        void calccenter(const std::vector<interfaces::msg::ScanData>& scan, int begin, int end,
            const geometry_msgs::msg::Point& candidate, float range, float resolution, std::vector<geometry_msgs::msg::Point>& centers);
        bool intersect(const PolarLine& base, const PolarLine& match, geometry_msgs::msg::Point& point);
        bool intersect(std::shared_ptr<PatternModel> model, const PolarLine& match, geometry_msgs::msg::Point& point);
        void createLookuptable();
        std::shared_ptr<PatternModel> makeModel(float theta);
        std::shared_ptr<PatternModel> moveModel(std::shared_ptr<PatternModel> model, const geometry_msgs::msg::Point& center);
        std::shared_ptr<PatternModel> makeModel(float theta, const geometry_msgs::msg::Point& center);
    protected:
        Options options_;
        std::vector<std::shared_ptr<PatternModel>> lut_;
    };

} } }
