#pragma once

#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/point.hpp"
#include "home_detection.h"
#include <optional>
#include <vector>
#include <utility>

namespace rp { namespace algorithm { namespace home_detect {

class ChargingBaseLocationFilter
{
public:
    enum ChargingBaseLocationFilterWorkingMode
    {
        WORKINGMODE_COMPUTE_MEAN,
        WORKINGMODE_CATCH_BEST,
        WORKINGMODE_WEIGHTED_MEAN
    };

    ChargingBaseLocationFilter(const rclcpp::Node::SharedPtr& node, int maxCandidateCount = 3);
    ~ChargingBaseLocationFilter();
    void reset();

    size_t  getValidCandidateCount() {
        return candidatePool_.size();
    }
    const std::vector<std::pair<RpHome, size_t>>& getValidCandidates() const {
        return candidatePool_;
    }

    size_t  getMostLikehoodCandidateVote();
    bool getEstimatedDesc(RpHome& homeDesc, size_t minVote = 1);
    void pushCandidate(const RpHome& homeDesc, const geometry_msgs::msg::Point& suggestedBaseLocation, float funcValue = -1.0f);
    void setWorkingMode(ChargingBaseLocationFilterWorkingMode mode) {
        workingMode_ = mode;
    }

    std::optional<float> getMaxAngleThresholdToRegisteredBase() const { return maxAngleThresholdToRegisteredBase_; }
    std::optional<float> getMaxDistanceThresholdToRegisteredBase() const { return maxDistanceThresholdToRegisteredBase_; }

public:
    static float CalcCandidateDivergence(const RpHome& src, const RpHome& dest);
    static float CalcCandidateAngleDiff(const RpHome& src, const RpHome& dest);

protected:
    void  fuseCandidates(RpHome& dest, int voteCnt, const RpHome& src);
    void  fuseCandidates(const std::vector<std::pair<RpHome, float>>& src, RpHome& dest);
protected:
    size_t maxCandidateCount_;
    float  maxCandidateDivergence_;
    float  maxCandidateAngleDiff_;
    int    mostLikehoodCandidateID_;
    std::vector<float> minPatternMatchingFunctionValue_;
    std::vector<std::vector<std::pair<RpHome, float>>> weightedMeanHistoryData_;
    ChargingBaseLocationFilterWorkingMode workingMode_;
    std::vector<std::pair<RpHome, size_t>> candidatePool_;
    std::optional<float> maxAngleThresholdToRegisteredBase_;
    std::optional<float> maxDistanceThresholdToRegisteredBase_;
    float candidateVoltFirstRange_;
    float candidateVoltSecondRange_;
};

} } }