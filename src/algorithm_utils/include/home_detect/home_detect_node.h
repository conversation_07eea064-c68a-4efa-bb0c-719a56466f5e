#pragma once

#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "interfaces/action/detect_home.hpp"
#include "home_detect_service.h"
#include "location_filter.h"
#include "sensor_msgs/msg/laser_scan.hpp"
#include "tf2_ros/buffer.h"
#include "tf2_ros/transform_listener.h"
#include "geometry_msgs/msg/pose_stamped.hpp"
#include "visualization_msgs/msg/marker_array.hpp"
#include <memory>
#include <atomic>

namespace rp { namespace algorithm { namespace home_detect {

class HomeDetectActionNode : public rclcpp::Node
{
public:
    using DetectHomeAction = interfaces::action::DetectHome;
    using GoalHandleDetectHome = rclcpp_action::ServerGoalHandle<DetectHomeAction>;

    explicit HomeDetectActionNode(const rclcpp::NodeOptions& options = rclcpp::NodeOptions());
    void init();
    virtual ~HomeDetectActionNode();

private:
    // Action server callbacks
    rclcpp_action::GoalResponse handleGoal(
        const rclcpp_action::GoalUUID& uuid,
        std::shared_ptr<const DetectHomeAction::Goal> goal);

    rclcpp_action::CancelResponse handleCancel(
        const std::shared_ptr<GoalHandleDetectHome> goal_handle);

    void handleAccepted(
        const std::shared_ptr<GoalHandleDetectHome> goal_handle);

    // Main execution function
    void executeDetectHome(const std::shared_ptr<GoalHandleDetectHome> goal_handle);

    // Data subscription callbacks
    void laserScanCallback(const sensor_msgs::msg::LaserScan::SharedPtr msg);
    
    // Utility functions
    bool getRobotPose(geometry_msgs::msg::Pose& robot_pose);
    std::vector<interfaces::msg::ScanData> convertLaserScanToScanData(
        const sensor_msgs::msg::LaserScan::SharedPtr& laser_scan);
    void publishFeedback(const std::shared_ptr<GoalHandleDetectHome> goal_handle,
                        uint8_t status, uint8_t progress, uint32_t candidate_count, 
                        uint32_t best_votes, const std::string& status_msg);
    
    // 新增: 发布充电桩可视化
    void publishDockVisualization(const RpHome& home);

private:
    // Action server
    rclcpp_action::Server<DetectHomeAction>::SharedPtr action_server_;

    // Home detection service
    std::shared_ptr<HomeDetectService> home_detect_service_;
    
    // Location filter for candidate selection
    std::shared_ptr<ChargingBaseLocationFilter> location_filter_;

    // Data subscriptions
    rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr laser_scan_sub_;
    sensor_msgs::msg::LaserScan::SharedPtr latest_laser_scan_;
    std::mutex laser_scan_mutex_;

    // TF for coordinate transformations
    std::unique_ptr<tf2_ros::Buffer> tf_buffer_;
    std::unique_ptr<tf2_ros::TransformListener> tf_listener_;

    // Parameters
    std::string laser_scan_topic_;
    std::string base_link_frame_;
    std::string map_frame_;
    int max_candidate_count_;
    uint32_t default_timeout_ms_;
    int valid_candidate_volt_threshold_;
    bool backward_docking_;

    // State management
    std::atomic<bool> is_detecting_;
    std::shared_ptr<IDetectHandle> current_detect_handle_;

    // 新增: 发布充电桩可视化
    rclcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr dock_visualization_pub_;
};

} } }
