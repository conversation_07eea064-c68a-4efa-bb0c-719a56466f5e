#pragma once
#include <vector>
#include <memory>
#include <cmath>
#include "interfaces/msg/weight_pose.hpp"
#include "interfaces/msg/rectangle_f.hpp"
#include "geometry_msgs/msg/pose2_d.hpp"
#include <rclcpp/logging.hpp>

#ifndef M_2PI
#	define M_2PI 6.283185307179586476925286766559	// The 2*PI constant
#endif
namespace rp{ namespace algorithm {
    const static double DISTANCE_EPSILON = 0.001;
    typedef interfaces::msg::WeightPose WeightedPose;
    typedef interfaces::msg::RectangleF RectangleF;

    struct ClusterData
    {
        ClusterData() {}
        ClusterData(WeightedPose pose, unsigned int id) : weightPose(pose), ID(id) {}

        WeightedPose weightPose;
        unsigned int ID;

        ClusterData& operator=(const ClusterData& that)
        {
            weightPose = that.weightPose;
            ID = that.ID;
            return *this;
        }
    };
    
    struct ClusterResult
    {
        WeightedPose mean;
        RectangleF region;
        std::vector<ClusterData> clusterData;
    };

    struct KDNode
    {
        ClusterData data;
    	std::shared_ptr<KDNode> parent;
    	std::shared_ptr<KDNode> lChild;
    	std::shared_ptr<KDNode> rChild;
        int split;
    	KDNode(): split(-1)
    	{
        	parent = nullptr;
        	lChild = nullptr;
        	rChild = nullptr;
    	}
    };

struct KDTree
{
    std::shared_ptr<KDNode> root;
    size_t size;
    KDTree(const size_t& n)
    {
        root = nullptr;
        size = n;
    }
};

    enum ClusterState
    {
        ClusterStateUnclassfied = -1,
        ClusterStateNoise = -2,
    };
    enum SearchStatus
    {
        SearchStatusLeft = 0,
        SearchStatusRight = 1,
        SearchStatusLeftOrRight = 2,
        SearchStatusNone = 3,
    };

    class Cluster
    {
    public:
        Cluster() {}
    	Cluster(std::vector<ClusterData> vctCluster, float weight = 0.f) :cluster_(vctCluster), weight_(weight) {}
        inline bool operator<(const Cluster &cluster) const
        {
            return this->weight_ < cluster.weight_;
        }
        inline bool operator>(const Cluster &cluster) const
        {
            return this->weight_ > cluster.weight_;
        }
        inline bool operator==(const Cluster &cluster) const
        {
            return this->weight_ == cluster.weight_;
        }

        float getWeight() { return weight_; }
        std::vector<ClusterData> &getClusterData() { return cluster_; }

    protected:
    private:
        std::vector<ClusterData> cluster_;
        float weight_;
    };

    class DbscanCluster
    {
    public:
        DbscanCluster(const float &radius, const int &minPTs);
        ~DbscanCluster();

        bool clusterAndGetResult(const std::vector<WeightedPose> &weightPoses, const int &clusterNum, std::vector<ClusterResult> &results);

    protected:
    private:
    	std::shared_ptr<KDTree> buildKDTree_(std::vector<ClusterData> vctData);
    	std::shared_ptr<KDNode> buildKDNode_(std::vector<ClusterData>& vctData, int split, int low, int high);
    	void destroyKDTree_(std::shared_ptr<KDTree> tree);
    	void destroyKDNode_(std::shared_ptr<KDNode> node);
        int findMedianPoint_(std::vector<ClusterData> &vctData, int split, int low, int high);
    	void findNeighborsWithinRadius_(std::shared_ptr<KDTree> tree, const ClusterData &data, const float &radius, std::vector<ClusterData>& neighbors);
   	 	void findNeighbors_(std::shared_ptr<KDNode> node, const ClusterData &data, const float &radius_square, std::vector<ClusterData>& neighbors);
        inline double getDistance_(WeightedPose a, WeightedPose b)
        {
            return std::pow(a.pose.x - b.pose.x, 2) + std::pow(a.pose.y - b.pose.y, 2);
        }
        int selectPoint_(std::vector<ClusterData> &vctData, int split, int low, int high, int middle);
        int partition_(std::vector<ClusterData> &vctData, int split, int low, int high);
    	void cluster_(std::shared_ptr<KDTree> tree, const std::vector<ClusterData>& vctClusterData, std::vector<Cluster>& clusters);
        void getMeanPoseAndRegion_(Cluster &cluster, WeightedPose &weightPose, RectangleF &region);
        inline SearchStatus getSearchStatus_(const WeightedPose &a, const WeightedPose &b, const float &radius, const int &split)
        {
            if (split == 0)
            {
                if (fabs(a.pose.x - b.pose.x) <= radius)
                    return SearchStatusLeftOrRight;
                else if (a.pose.x - b.pose.x > radius)
                    return SearchStatusLeft;
                else
                    return SearchStatusRight;
            }
            else
            {
                if (fabs(a.pose.y - b.pose.y) <= radius)
                    return SearchStatusLeftOrRight;
                else if (a.pose.y - b.pose.y > radius)
                    return SearchStatusLeft;
                else
                    return SearchStatusRight;
            }
        }

        float radius_;
        int minPTs_;

    };
}}