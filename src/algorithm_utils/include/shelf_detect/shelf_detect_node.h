#pragma once

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <std_msgs/msg/header.hpp>
#include <geometry_msgs/msg/pose.hpp>
#include <geometry_msgs/msg/pose2_d.hpp>
#include <geometry_msgs/msg/point.hpp>
#include <interfaces/msg/shelf_info.hpp>
#include <interfaces/msg/scan_data.hpp>
#include <interfaces/msg/weight_pose.hpp>
#include <interfaces/srv/detect_shelf.hpp>
#include <rcl_interfaces/msg/parameter_descriptor.hpp>
#include <rcl_interfaces/msg/set_parameters_result.hpp>
#include <dbscan_cluster.h>
#include <point_utils.h>
#include <rphoughhelper.h>
#include "pose_utils.h"

#include <memory>
#include <vector>
#include <string>
#include <future>
#include <chrono>

namespace rp { namespace algorithm { namespace shelf_detect {

struct ShelfDetectParams 
{
    std::string laser_topic;
    double max_columnar_detect_distance;
    double dbscan_cluster_radius;
    int dbscan_min_cluster_capacity;
    int dbscan_max_cluster_num;
    double fov_of_find_tag;
};

/**
 * @brief 货架检测节点类 - 按需订阅模式：只在检测时接收雷达数据
 */
class ShelfDetectNode : public rclcpp::Node
{
public:
    explicit ShelfDetectNode(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());

    virtual ~ShelfDetectNode() = default;

private:
    void initializeParameters();
    void initializeServices();
    void detectShelvesCallback(const std::shared_ptr<interfaces::srv::DetectShelf::Request> request, std::shared_ptr<interfaces::srv::DetectShelf::Response> response);
    sensor_msgs::msg::LaserScan::SharedPtr waitForLaserScan(const std::string& topic, double timeout_seconds = 5.0);
    std::vector<geometry_msgs::msg::Point> performShelfDetection(const sensor_msgs::msg::LaserScan::SharedPtr laserScan, const interfaces::srv::DetectShelf::Request& params);
    bool validateDetectionParameters(const interfaces::srv::DetectShelf::Request& request);
    void detectLaserCluster(const std::vector<interfaces::msg::ScanData>& scanData, std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<rp::algorithm::ClusterResult>& columnarsResults); 
    void filterLaserScan(std::vector<interfaces::msg::ScanData>& scanData);
    void localToWorld(const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, const geometry_msgs::msg::Pose& robotPose, std::vector<geometry_msgs::msg::Point>& columnars);
    void extractShelf(const std::vector<interfaces::msg::ShelfInfo>& shelfColumnarSizeList, const geometry_msgs::msg::Pose& robotPose, const std::vector<geometry_msgs::msg::Point>& columnars, const std::vector<geometry_msgs::msg::Point>& columnarsInWorld, std::vector<geometry_msgs::msg::Point>& shelfInRobotView, interfaces::msg::ShelfInfo& currentShelfColumnarSize);
    bool extractFourShelfColumnar(const interfaces::msg::ShelfInfo& currentShelfColumnarSize, const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<geometry_msgs::msg::Point>& shelvesInRobotView);
    void findNearestTwoCloumnars(const std::vector<geometry_msgs::msg::Point>& cloumnarsList, std::vector<geometry_msgs::msg::Point>& neraestCloumnars, std::vector<geometry_msgs::msg::Point>& otherCloumnars);
    void findFourthPoint(const std::vector<geometry_msgs::msg::Point>& columnarsInRobotView, std::vector<geometry_msgs::msg::Point>& guessShelfInRobotView);
    bool compareYawError(const geometry_msgs::msg::Pose& robotPose, const geometry_msgs::msg::Pose& landingPose, const geometry_msgs::msg::Point& candidatePose1, const geometry_msgs::msg::Point& candidatePose2, double& error);
    bool isRectangle(const interfaces::msg::ShelfInfo& currentShelfColumnarSize, const geometry_msgs::msg::Point& p1, const geometry_msgs::msg::Point& p2, const geometry_msgs::msg::Point& p3, const geometry_msgs::msg::Point& p4);

private:
    ShelfDetectParams params_;
    rclcpp::Service<interfaces::srv::DetectShelf>::SharedPtr detect_service_;
    std::shared_ptr<rp::algorithm::DbscanCluster> cluster_;
    geometry_msgs::msg::Pose landingPose_;
};

}}}
