#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from geometry_msgs.msg import Pose
from interfaces.action import DetectHome
from geometry_msgs.msg import PoseStamped
from visualization_msgs.msg import MarkerArray
import time
import math

class HomeDetectTestNode(Node):
    def __init__(self):
        super().__init__('home_detect_test_node')
        
        # Create action client
        self.action_client = ActionClient(self, DetectHome, 'detect_home')
        
        # Create pose publisher for testing
        self.pose_pub = self.create_publisher(PoseStamped, 'expected_home_pose', 10)
        
        # Create subscriber for dock visualization
        self.visualization_sub = self.create_subscription(
            MarkerArray, 
            'dock_visualization', 
            self.visualization_callback, 
            10
        )
        
        self.get_logger().info('Home detect test node initialized')

    def visualization_callback(self, msg):
        """Callback for dock visualization markers"""
        self.get_logger().info(f'Received dock visualization with {len(msg.markers)} markers')
        for i, marker in enumerate(msg.markers):
            self.get_logger().info(f'Marker {i}: type={marker.type}, ns={marker.ns}, id={marker.id}')

    def send_goal(self):
        # Create goal
        goal = DetectHome.Goal()
        
        # Set expected home pose (optional)
        goal.expected_home_pose.position.x = 3.0
        goal.expected_home_pose.position.y = 1.44
        goal.expected_home_pose.position.z = 0.0
        
        # Set orientation (yaw = 2.86 radians)
        yaw = 2.86
        goal.expected_home_pose.orientation.x = 0.0
        goal.expected_home_pose.orientation.y = 0.0
        goal.expected_home_pose.orientation.z = math.sin(yaw / 2.0)
        goal.expected_home_pose.orientation.w = math.cos(yaw / 2.0)
        
        goal.timeout_ms = 10000  # 10 seconds timeout
        
        # Send goal
        self.get_logger().info('Sending goal...')
        self.action_client.wait_for_server()
        self.send_goal_future = self.action_client.send_goal_async(goal)
        self.send_goal_future.add_done_callback(self.goal_response_callback)

    def goal_response_callback(self, future):
        goal_handle = future.result()
        if not goal_handle.accepted:
            self.get_logger().info('Goal rejected')
            return
        
        self.get_logger().info('Goal accepted')
        self.get_result_future = goal_handle.get_result_async()
        self.get_result_future.add_done_callback(self.get_result_callback)

    def get_result_callback(self, future):
        result = future.result().result
        if result.success:
            self.get_logger().info('Home detection succeeded!')
            self.get_logger().info(f'Home pose: x={result.home_pose.position.x:.3f}, '
                                 f'y={result.home_pose.position.y:.3f}, '
                                 f'z={result.home_pose.position.z:.3f}')
            self.get_logger().info(f'Detection time: {result.detection_time_ms}ms')
        else:
            self.get_logger().error(f'Home detection failed: {result.error_message}')
        
        # Shutdown after getting result
        rclpy.shutdown()

def main(args=None):
    rclpy.init(args=args)
    
    node = HomeDetectTestNode()
    
    # Send goal after a short delay
    node.create_timer(2.0, lambda: node.send_goal())
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main() 