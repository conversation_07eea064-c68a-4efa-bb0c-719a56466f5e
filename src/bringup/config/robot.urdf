<?xml version="1.0"?>
<robot name="Phoebus" xmlns:xacro="http://ros.org/wiki/xacro">
  <link name="base_link">
    <visual>
      <geometry>
        <box size="0.78 0.5 0.269"/>
      </geometry>
      <material name="blue">
        <color rgba="0 0.5 1 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <box size="0.78 0.5 0.269"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="50.0"/>
      <inertia ixx="1.0417" ixy="0" ixz="0" iyy="1.0417" iyz="0" izz="2.0833"/>
    </inertial>
  </link> 
  <link name="bumper_link"> 
    <visual>
      <geometry>
        <box size="0.1 0.1 0.1"/>
      </geometry>
      <material name="blue">
        <color rgba="0 0.5 1 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <box size="0.1 0.1 0.1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="50.0"/>
      <inertia ixx="1.0417" ixy="0" ixz="0" iyy="1.0417" iyz="0" izz="2.0833"/>
    </inertial>
  </link>
  <joint name="bumper_joint" type="fixed">
    <parent link="base_link"/>
    <child link="bumper_link"/>
    <origin xyz="0 0.0 0" rpy="0 0 0"/>
  </joint>
  <link name="rplidar_front"> 
    <visual>
      <geometry>
        <cylinder length="0.05" radius="0.03"/>
      </geometry>
      <material name="black">
          <color rgba="0 0 0 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <cylinder length="0.05" radius="0.03"/>
      </geometry>
    </collision> 
    <inertial>
      <mass value="0.5"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
  <joint name="front_laser_joint" type="fixed">
    <parent link="base_link"/>
    <child link="rplidar_front"/>
    <origin xyz="0.323 0.0 0.21060" rpy="0 0 3.1415926"/>
  </joint>

  <link name="rplidar_back"> 
    <visual>
      <geometry>
        <cylinder length="0.05" radius="0.03"/>
      </geometry>
      <material name="black">
        <color rgba="0 0 0 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <cylinder length="0.05" radius="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.5"/>
      <inertia ixx="0.0002" ixy="0" ixz="0" iyy="0.0002" iyz="0" izz="0.0002"/>
    </inertial>
  </link> 
  <joint name="back_laser_joint" type="fixed">
    <parent link="base_link"/>
    <child link="rplidar_back"/>
    <origin xyz="-0.323 0.0 0.21060" rpy="0 0 0"/>
  </joint>

  <link name="ascamera_nuwa_camera_link_0">
    <visual>
      <geometry>
        <box size="0.06 0.04 0.03"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <box size="0.06 0.04 0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.3"/>
      <inertia ixx="0.00005" ixy="0" ixz="0" iyy="0.00005" iyz="0" izz="0.00005"/>
    </inertial>
  </link> 
  <joint name="depth_camera_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ascamera_nuwa_camera_link_0"/>
    <origin xyz="0.36472 0 0.0728" rpy="-1.1693665 0 -1.5707963"/>
  </joint>
</robot>
