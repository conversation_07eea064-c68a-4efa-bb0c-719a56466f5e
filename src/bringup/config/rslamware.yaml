stcm_manager_node:
  ros__parameters:
    # Map storage configuration
    map_storage_path: "/opt/rslamware_data/maps/"
 
    keep_mask_topic: "/keepout_filter_mask"
    speed_limit_area_topic: "/speed_limit"

    # Map server subscription configuration
    map_server_topic: "/map"

    # Sensor disable area configuration
    sensor_disable_publish_period_ms: 100

    # Dangerous area configuration
    dangerous_area_publish_period_ms: 100

    map_server_type: "slamkit"

robot_monitor_node:
  ros__parameters:
    # Health monitoring period in seconds
    health_monitor_period: 1.0
    system_monitor_period: 60.0

    # System monitoring thresholds  
    disk_usage_warning_threshold: 90.0    # Percentage 
    
    # Temperature monitoring (if available)
    temperature_warning_threshold: 80.0   # Celsius 
     
    # Enable/disable specific monitoring features 
    enable_disk_monitoring: true
    enable_temperature_monitoring: true 

    lidar_topic: "/fusion_scan"
    
    action_names: ["dock_robot","navigate_to_pose"]
     
costmap_filter_info_server:
  ros__parameters:
    type: 0
    filter_info_topic: "/costmap_filter_info"
    mask_topic: "/keepout_filter_mask"
    base: 0.0
    multiplier: 1.0
filter_mask_server:
  ros__parameters:
    frame_id: "map"
    topic_name: "/keepout_filter_mask"
    yaml_filename: "keepout_mask.yaml"
