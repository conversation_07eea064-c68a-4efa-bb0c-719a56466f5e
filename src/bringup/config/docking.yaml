docking_server:
  ros__parameters:
    controller_frequency: 50.0
    initial_perception_timeout: 5.0
    wait_charge_timeout: 5.0
    dock_approach_timeout: 30.0
    undock_linear_tolerance: 0.05
    undock_angular_tolerance: 0.1
    max_retries: 3
    base_frame: "base_link"
    fixed_frame: "odom"
    dock_backwards: true
    dock_prestaging_tolerance: 0.1
    rotate_to_dock: true
    rotation_angular_tolerance: 0.05

    # Types of docks
    dock_plugins: ['simple_charging_dock', 'shelf_dock']
    simple_charging_dock:
      plugin: 'opennav_docking::SimpleChargingDock'
      docking_threshold: 0.53
      staging_x_offset: -0.8
      staging_yaw_offset: 0.0
      use_external_detection_pose: true
      use_battery_status: false # true
      use_stall_detection: false
      external_detection_timeout: 1.0
      external_detection_translation_x: 0.0
      external_detection_translation_y: 0.0
      external_detection_rotation_roll: 0.0
      external_detection_rotation_pitch: 0.0
      external_detection_rotation_yaw: 0.0
      filter_coef: 0.1
    shelf_dock:
      plugin: "opennav_docking::ShelfDock"
      docking_threshold: 0.05
      use_external_detection_pose: true
      external_detection_timeout: 10.0
      external_detection_translation_x: 0.0
      external_detection_translation_y: 0.0
      external_detection_rotation_roll: 0.0
      external_detection_rotation_pitch: 0.0
      external_detection_rotation_yaw: 0.0
      filter_coef: 0.1

    # Dock instances
    docks: ['home_dock']
    home_dock:
      type: 'simple_charging_dock'
      frame: map
      pose: [0.0, 0.0, 0.0]

    controller:
      k_phi: 3.0
      k_delta: 2.0
      v_linear_min: 0.15
      v_linear_max: 0.15
      use_collision_detection: true
      dock_collision_threshold: 0.1
