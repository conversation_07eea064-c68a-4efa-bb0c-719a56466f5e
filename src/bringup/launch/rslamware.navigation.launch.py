import os
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription, DeclareLaunchArgument, LogInfo, SetEnvironmentVariable
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution, PythonExpression, TextSubstitution  
from launch.launch_description_sources import PythonLaunchDescriptionSource
from ament_index_python.packages import get_package_share_directory
from launch.conditions import IfCondition

def generate_launch_description():

    ENCRYPTION_MODE=False

    # 启动模式，分别为真机、仿真、回放
    mode_arg = DeclareLaunchArgument(
        name='mode',
        default_value='real',
        description='Operation mode: real | simulation | replay',
        choices=['real', 'simulation', 'replay']
    )

    scan_topic_arg = DeclareLaunchArgument(
        "scan_topic",
        default_value="fusion_scan",
        description="Topic name for laser scan topic"
    )

    no_gui_arg = DeclareLaunchArgument(
        "no_gui",
        default_value="False",
        description="Flag to disable Gazebo GUI client"
    )

    bringup_dir = get_package_share_directory('rslamware_bringup')

    set_mode_env = SetEnvironmentVariable(
        name='RSLAMWARE_MODE',
        value=LaunchConfiguration('mode')
    )

    if not ENCRYPTION_MODE:
         
        ros_sdk = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    get_package_share_directory('slamware_ros_sdk'),
                    'launch/slamware_ros_sdk_server_node.xml'
                ])
            )
        )

        navigation = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    get_package_share_directory('nav2_bringup'),
                    'launch/bringup_navigation_launch.py'
                ])
            ),
            launch_arguments={
                "scan_topic": LaunchConfiguration("scan_topic"),
                "mode": LaunchConfiguration("mode")
            }.items()
        )

        agent_server = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    get_package_share_directory('agent'),
                    'launch/agent_server.launch.py'
                ])
            ),
            launch_arguments={
                "scan_topic": LaunchConfiguration("scan_topic"),
                "mode": LaunchConfiguration("mode")
            }.items()
        )

        # stcm service
        stcm_manager = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    get_package_share_directory('stcm_manager'),
                    'launch/stcm_manager.launch.py'
                ])
            ),
            launch_arguments={
                'mode': LaunchConfiguration('mode'),
            }.items()
        )


    else:
        # for encrytion mode, only real mode is supported
        launch_dir = os.path.dirname(os.path.realpath(__file__))

        ros_sdk = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    launch_dir,
                    'slamware_ros_sdk_server_node.xml'
                ])
            ), 
        )

        navigation = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    launch_dir,
                    'bringup_navigation_launch.py'
                ])
            ),
            launch_arguments={
                "scan_topic": LaunchConfiguration("scan_topic"),
                "mode": LaunchConfiguration("mode")
            }.items()
        )

        agent_server = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    launch_dir,
                    'agent_server.launch.py'
                ])
            ),
            launch_arguments={
                "scan_topic": LaunchConfiguration("scan_topic"),
                "mode": LaunchConfiguration("mode")
            }.items()
        )

        # stcm service
        stcm_manager = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    launch_dir,
                    'stcm_manager.launch.py'
                ])
            ),
            launch_arguments={
                'mode': LaunchConfiguration('mode'),
            }.items()
        )

    return LaunchDescription([
        # 基础参数
        mode_arg,
        # 设置环境变量
        set_mode_env,
        scan_topic_arg,
        no_gui_arg,
        DeclareLaunchArgument(
            'use_sim_time',
            default_value=PythonExpression(['"true" if "', LaunchConfiguration('mode'), '" == "simulation" else "false"']),
            description='Use simulation time'
        ),

        LogInfo(msg=PythonExpression(['"Running in \'" + "', LaunchConfiguration('mode'), '" + "\' mode"'])), 

        ros_sdk,

        navigation,

        # RESTful API
        agent_server,
        stcm_manager
    ])
