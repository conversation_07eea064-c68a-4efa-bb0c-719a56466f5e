import os
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch_ros.actions import Node
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.actions import RegisterEventHandler
from launch.event_handlers import OnProcessExit
from ament_index_python.packages import get_package_share_directory
import yaml

def restart_node(target_action, node_name, package, executable, parameters, remappings=None):
    def handler(event, context):
        if event.returncode is not None and event.returncode < 0:
            print(f"Node {node_name} was killed by signal {-event.returncode}, not restarting.")
            return None
        else:
            print(f"Node {node_name} exited with code {event.returncode}, restarting.")
            return Node(
                package=package,
                executable=executable,
                name=node_name,
                parameters=parameters,
                remappings=remappings,
                output='screen'
            )
    return RegisterEventHandler(
        OnProcessExit(
            target_action=target_action,
            on_exit=handler
        )
    )

def generate_launch_description():
    ENCRYPTION_MODE=False

    config_dir = os.path.join(get_package_share_directory('rslamware_bringup'), 'config') 
    urdf_file = os.path.join(config_dir, 'robot.urdf')
    
    
    if not ENCRYPTION_MODE:
        common_topics_path = os.path.join(config_dir, 'topics.yaml')
    else:
        launch_dir = os.path.dirname(os.path.realpath(__file__))
        config_dir = os.path.join(launch_dir, '..', 'config')
        config_dir = os.path.abspath(config_dir)
        common_topics_path = os.path.join(config_dir, 'combined_config.yaml')



    with open(common_topics_path, 'r') as f:
        topic_config = yaml.safe_load(f)['topics']['ros__parameters']
 
    front_laser_topic = topic_config['front_laser_scan']
    back_laser_topic = topic_config['back_laser_scan']
    filtered_front_laser_topic = topic_config['filtered_front_laser_scan']
    filtered_back_laser_topic = topic_config['filtered_back_laser_scan']
    fusion_laser_topic = topic_config['fusion_scan']
    undistortion_front_laser_scan = topic_config['undistortion_front_laser_scan']
    undistortion_back_laser_scan = topic_config['undistortion_back_laser_scan']


    if not ENCRYPTION_MODE:
        sl_vcu_all = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(get_package_share_directory('sl_vcu_all'), 'launch/sl_vcu_all.launch.py')
            )
        )

        multi_nuwa = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(get_package_share_directory('ascamera'), 'launch', 'multi_nuwa.launch.py')
           ),
        )

        depth_filter = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(get_package_share_directory('depth_process'), 'launch', 'depth_filter.launch.py')
           ),
        )

        multi_lidar_data_sync = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(get_package_share_directory('multi_lidar_data_sync'), 'launch', 'multi_lidar_data_sync.launch.py')
            ),
            launch_arguments={
                'main_scan_topic': undistortion_front_laser_scan,
                'sub_scan_topic': undistortion_back_laser_scan,
                'scan_pub_topic': fusion_laser_topic,
            }.items(),
        )

        lidar_undistortion_2d = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(get_package_share_directory('lidar_undistortion_2d'), 'launch', 'lidar_undistortion_2d.launch.py')
            ),
            launch_arguments={
                'lidar0_scan_sub_topic': filtered_front_laser_topic,
                'lidar0_scan_pub_topic': undistortion_front_laser_scan,
                'lidar1_scan_sub_topic': filtered_back_laser_topic,
                'lidar1_scan_pub_topic': undistortion_back_laser_scan,
            }.items(),
        )

        docking_server = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(get_package_share_directory('opennav_docking'),'launch', 'docking_server.launch.py')
            ),
            launch_arguments={
                'params_file': os.path.join(config_dir, 'docking.yaml'),
            }.items(),
        )

        shelf_detect = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(get_package_share_directory('algorithm_utils'),'launch', 'shelf_detect.launch.py')
            )
        )

        home_detect = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(get_package_share_directory('algorithm_utils'),'launch', 'home_detect.launch.py')
            )
        )

        rplidar_node_front = Node(
            package='rplidar_ros',
            executable='rplidar_node',
            name='rplidar_node_front',
            parameters=[ 
                common_topics_path,
                os.path.join(config_dir, 'rplidar.yaml'),
                {'topic_name': front_laser_topic},
            ], 
            output='screen'
        )

        rplidar_front_restart = restart_node(
            rplidar_node_front, 'rplidar_node_front', 'rplidar_ros', 'rplidar_node',
            [common_topics_path, {'topic_name': front_laser_topic}]
        )

        rplidar_node_back = Node(
            package='rplidar_ros',
            executable='rplidar_node',
            name='rplidar_node_back',
            parameters=[ 
                common_topics_path,
                os.path.join(config_dir, 'rplidar.yaml'),
                {'topic_name': back_laser_topic},
            ], 
            output='screen'
        )

        rplidar_back_restart = restart_node(
            rplidar_node_back, 'rplidar_node_back', 'rplidar_ros', 'rplidar_node',
            [common_topics_path, {'topic_name': back_laser_topic}]
        )

        laser_filter_front = Node(
            package="laser_filters",
            executable="scan_to_scan_filter_chain",
            name='laser_filter_front',
            parameters=[os.path.join(config_dir, 'laser_filter.yaml')], 
            remappings=[ ('scan', front_laser_topic),
                ('scan_filtered', filtered_front_laser_topic),
            ],
            prefix="taskset -c 4-7",
            output='screen'
        )

        laser_filter_back = Node(
            package="laser_filters",
            executable="scan_to_scan_filter_chain",
            name='laser_filter_back',
            parameters=[os.path.join(config_dir, 'laser_filter.yaml')], 
            remappings=[ ('scan', back_laser_topic),
                ('scan_filtered', filtered_back_laser_topic),
            ],
            prefix="taskset -c 4-7",
            output='screen'
        )
    else:
        sl_vcu_all = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(launch_dir, 'sl_vcu_all.launch.py')
            )
        )

        multi_nuwa = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(launch_dir, 'multi_nuwa.launch.py')
           ),
        )

        depth_filter = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(launch_dir, 'depth_filter.launch.py')
           ),
        )

        multi_lidar_data_sync = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(launch_dir, 'multi_lidar_data_sync.launch.py')
            ),
        )

        lidar_undistortion_2d = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(launch_dir, 'lidar_undistortion_2d.launch.py')
            ),
        )

        docking_server = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(launch_dir, 'docking_server.launch.py')
            ),
            launch_arguments={
                'params_file': common_topics_path,
            }.items(),
        )

        shelf_detect = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(launch_dir, 'shelf_detect.launch.py')
            ),
            launch_arguments={
                'params_file': common_topics_path,
            }.items(),
        )
        
        home_detect = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(launch_dir, 'home_detect.launch.py')
            ),
            launch_arguments={
                'params_file': common_topics_path,
            }.items(),
        )

        rplidar_node_front = Node(
            package='rplidar_ros',
            executable='rplidar_node',
            name='rplidar_node_front',
            parameters=[ 
                common_topics_path,
                {'topic_name': front_laser_topic},
            ], 
            output='screen'
        )

        rplidar_front_restart = restart_node(
            rplidar_node_front, 'rplidar_node_front', 'rplidar_ros', 'rplidar_node',
            [common_topics_path, {'topic_name': front_laser_topic}]
        )

        rplidar_node_back = Node(
            package='rplidar_ros',
            executable='rplidar_node',
            name='rplidar_node_back',
            parameters=[ 
                common_topics_path,
                {'topic_name': back_laser_topic},
            ], 
            output='screen'
        )

        rplidar_back_restart = restart_node(
            rplidar_node_back, 'rplidar_node_back', 'rplidar_ros', 'rplidar_node',
            [common_topics_path, {'topic_name': back_laser_topic}]
        )

        laser_filter_front = Node(
            package="laser_filters",
            executable="scan_to_scan_filter_chain",
            name='laser_filter_front',
            parameters=[common_topics_path], 
            remappings=[ ('scan', front_laser_topic),
                ('scan_filtered', filtered_front_laser_topic),
            ],
            prefix="taskset -c 4-7",
            output='screen'
        )

        laser_filter_back = Node(
            package="laser_filters",
            executable="scan_to_scan_filter_chain",
            name='laser_filter_back',
            parameters=[common_topics_path], 
            remappings=[ ('scan', back_laser_topic),
                ('scan_filtered', filtered_back_laser_topic),
            ],
            prefix="taskset -c 4-7",
            output='screen'
        )




    #add other launch
    return LaunchDescription([
        Node(
            package='robot_state_publisher',
            executable='robot_state_publisher',
            name='robot_state_publisher',
            output='screen',
            parameters=[{'robot_description': open(urdf_file).read()}]
        ),
        
        sl_vcu_all,

        rplidar_node_front,

        rplidar_front_restart,
        
        rplidar_node_back,

        rplidar_back_restart,
        
        laser_filter_front,
        
        laser_filter_back,
        
        docking_server,

        shelf_detect,

        home_detect,

        multi_nuwa,
        
        depth_filter,
        
        multi_lidar_data_sync,

        lidar_undistortion_2d, 
        
    ])
