<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>depth_process</name>
  <version>0.0.1</version>
  <description>A ROS2 package for depth image filtering and point cloud generation</description>
  <maintainer email="<EMAIL>">maintainer</maintainer>
  <license>Apache License 2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>sensor_msgs</depend>
  <depend>cv_bridge</depend>
  <depend>pcl_conversions</depend>
  <depend>pcl_ros</depend>
  <depend>libpcl-all-dev</depend>
  <depend>libopencv-dev</depend>
  <depend>interfaces</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>