/*
 * Software License Agreement (BSD License)
 *
 *  Copyright (c) 2017, Locus Robotics
 *  All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *   * Redistributions of source code must retain the above copyright
 *     notice, this list of conditions and the following disclaimer.
 *   * Redistributions in binary form must reproduce the above
 *     copyright notice, this list of conditions and the following
 *     disclaimer in the documentation and/or other materials provided
 *     with the distribution.
 *   * Neither the name of the copyright holder nor the names of its
 *     contributors may be used to endorse or promote products derived
 *     from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 *  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 *  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 *  INCIDENT<PERSON>, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUE<PERSON>IA<PERSON> DAMAGES (INCLUDING,
 *  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 *  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 *  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 *  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 *  POSSIBILITY OF SUCH DAMAGE.
 */

#include <algorithm>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <optional>

#include "dwb_core/dwb_local_planner.hpp"
#include "dwb_core/exceptions.hpp"
#include "dwb_core/illegal_trajectory_tracker.hpp"
#include "dwb_msgs/msg/critic_score.hpp"
#include "nav_2d_msgs/msg/twist2_d.hpp"
#include "nav_2d_utils/conversions.hpp"
#include "nav_2d_utils/parameters.hpp"
#include "nav_2d_utils/tf_help.hpp"
#include "nav_2d_utils/path_ops.hpp"
#include "nav2_util/geometry_utils.hpp"
#include "nav2_util/lifecycle_node.hpp"
#include "nav2_util/node_utils.hpp"
#include "pluginlib/class_list_macros.hpp"
#include "nav_msgs/msg/path.hpp"
#include "geometry_msgs/msg/twist_stamped.hpp"

using nav2_util::declare_parameter_if_not_declared;
using nav2_util::geometry_utils::euclidean_distance;

namespace dwb_core
{

DWBLocalPlanner::DWBLocalPlanner()
: traj_gen_loader_("dwb_core", "dwb_core::TrajectoryGenerator"),
  critic_loader_("dwb_core", "dwb_core::TrajectoryCritic")
{
}

void DWBLocalPlanner::configure(
  const rclcpp_lifecycle::LifecycleNode::WeakPtr & parent,
  std::string name, std::shared_ptr<tf2_ros::Buffer> tf,
  std::shared_ptr<nav2_costmap_2d::Costmap2DROS> costmap_ros)
{
  node_ = parent;
  auto node = node_.lock();

  logger_ = node->get_logger();
  clock_ = node->get_clock();
  costmap_ros_ = costmap_ros;
  tf_ = tf;
  dwb_plugin_name_ = name;
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".critics",
    rclcpp::PARAMETER_STRING_ARRAY);
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".default_critic_namespaces",
    rclcpp::ParameterValue(std::vector<std::string>()));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".prune_plan",
    rclcpp::ParameterValue(true));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".prune_distance",
    rclcpp::ParameterValue(2.0));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".forward_prune_distance",
    rclcpp::ParameterValue(2.0));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".debug_trajectory_details",
    rclcpp::ParameterValue(false));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".trajectory_generator_name",
    rclcpp::ParameterValue(std::string("dwb_plugins::StandardTrajectoryGenerator")));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".transform_tolerance",
    rclcpp::ParameterValue(0.1));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".shorten_transformed_plan",
    rclcpp::ParameterValue(true));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".short_circuit_trajectory_evaluation",
    rclcpp::ParameterValue(true));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".min_path_size_for_select_local_goal",
    rclcpp::ParameterValue(10));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".yaw_diff_threshold_for_select_local_goal",
    rclcpp::ParameterValue(0.262));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".speed_reduce_enabled",
    rclcpp::ParameterValue(false));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".speed_reduce_cost_check_window_size",
    rclcpp::ParameterValue(0.05));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".speed_reduce_min_threshold",
    rclcpp::ParameterValue(0.5));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".precise_move_distance",
    rclcpp::ParameterValue(0.5));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".precise_move_speed",
    rclcpp::ParameterValue(0.15));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".sim_time",
    rclcpp::ParameterValue(1.5));
  declare_parameter_if_not_declared(
    node, dwb_plugin_name_ + ".max_vel_x",
    rclcpp::ParameterValue(1.0));

  std::string traj_generator_name;

  double transform_tolerance;
  node->get_parameter(dwb_plugin_name_ + ".transform_tolerance", transform_tolerance);
  transform_tolerance_ = rclcpp::Duration::from_seconds(transform_tolerance);
  RCLCPP_INFO(logger_, "Setting transform_tolerance to %f", transform_tolerance);

  node->get_parameter(dwb_plugin_name_ + ".prune_plan", prune_plan_);
  node->get_parameter(dwb_plugin_name_ + ".prune_distance", prune_distance_);
  node->get_parameter(dwb_plugin_name_ + ".forward_prune_distance", forward_prune_distance_);
  node->get_parameter(dwb_plugin_name_ + ".debug_trajectory_details", debug_trajectory_details_);
  node->get_parameter(dwb_plugin_name_ + ".trajectory_generator_name", traj_generator_name);
  node->get_parameter(
    dwb_plugin_name_ + ".short_circuit_trajectory_evaluation",
    short_circuit_trajectory_evaluation_);
  node->get_parameter(dwb_plugin_name_ + ".shorten_transformed_plan", shorten_transformed_plan_);
  node->get_parameter(
    dwb_plugin_name_ + ".min_path_size_for_select_local_goal",
    min_path_size_for_select_local_goal_);
  node->get_parameter(
    dwb_plugin_name_ + ".yaw_diff_threshold_for_select_local_goal",
    yaw_diff_threshold_for_select_local_goal_);
  node->get_parameter(
    dwb_plugin_name_ + ".speed_reduce_enabled",
    speed_reduce_enabled_);
  node->get_parameter(
    dwb_plugin_name_ + ".speed_reduce_cost_check_window_size",
    speed_reduce_cost_check_window_size_);
  node->get_parameter(
    dwb_plugin_name_ + ".speed_reduce_min_threshold",
    speed_reduce_min_threshold_);
  node->get_parameter(dwb_plugin_name_ + ".precise_move_distance", precise_move_distance_);
  node->get_parameter(dwb_plugin_name_ + ".precise_move_speed", precise_move_speed_);
  node->get_parameter(dwb_plugin_name_ + ".max_vel_x", origin_move_speed_);
  node->get_parameter(dwb_plugin_name_ + ".sim_time", origin_sim_time_);
  
  pub_ = std::make_unique<DWBPublisher>(node, dwb_plugin_name_);
  pub_->on_configure();

  traj_generator_ = traj_gen_loader_.createUniqueInstance(traj_generator_name);

  traj_generator_->initialize(node, dwb_plugin_name_);

  try {
    loadCritics();
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "Couldn't load critics! Caught exception: %s", e.what());
    throw;
  } 

  param_callback_handle_ = node->add_on_set_parameters_callback( std::bind(&DWBLocalPlanner::paramCallback, this, std::placeholders::_1));
}

void
DWBLocalPlanner::activate()
{
  pub_->on_activate();
}

void
DWBLocalPlanner::deactivate()
{
  pub_->on_deactivate();
}

void
DWBLocalPlanner::cleanup()
{
  pub_->on_cleanup();

  traj_generator_.reset();
}

std::string
DWBLocalPlanner::resolveCriticClassName(std::string base_name)
{
  if (base_name.find("Critic") == std::string::npos) {
    base_name = base_name + "Critic";
  }

  if (base_name.find("::") == std::string::npos) {
    for (unsigned int j = 0; j < default_critic_namespaces_.size(); j++) {
      std::string full_name = default_critic_namespaces_[j] + "::" + base_name;
      if (critic_loader_.isClassAvailable(full_name)) {
        return full_name;
      }
    }
  }
  return base_name;
}

void
DWBLocalPlanner::loadCritics()
{
  auto node = node_.lock();
  if (!node) {
    throw std::runtime_error{"Failed to lock node"};
  }

  node->get_parameter(dwb_plugin_name_ + ".default_critic_namespaces", default_critic_namespaces_);
  if (default_critic_namespaces_.empty()) {
    default_critic_namespaces_.emplace_back("dwb_critics");
  }

  std::vector<std::string> critic_names;
  if (!node->get_parameter(dwb_plugin_name_ + ".critics", critic_names)) {
    throw std::runtime_error("No critics defined for " + dwb_plugin_name_);
  }

  for (unsigned int i = 0; i < critic_names.size(); i++) {
    std::string critic_plugin_name = critic_names[i];
    std::string plugin_class;

    declare_parameter_if_not_declared(
      node, dwb_plugin_name_ + "." + critic_plugin_name + ".class",
      rclcpp::ParameterValue(critic_plugin_name));
    node->get_parameter(dwb_plugin_name_ + "." + critic_plugin_name + ".class", plugin_class);

    plugin_class = resolveCriticClassName(plugin_class);

    TrajectoryCritic::Ptr plugin = critic_loader_.createUniqueInstance(plugin_class);
    RCLCPP_INFO(
      logger_,
      "Using critic \"%s\" (%s)", critic_plugin_name.c_str(), plugin_class.c_str());
    critics_.push_back(plugin);
    try {
      plugin->initialize(node, critic_plugin_name, dwb_plugin_name_, costmap_ros_);
    } catch (const std::exception & e) {
      RCLCPP_ERROR(logger_, "Couldn't initialize critic plugin!");
      throw;
    }
    if(critic_plugin_name == "GoalAlign") {
      double scale;
      node->get_parameter(dwb_plugin_name_ + "." + critic_plugin_name + ".scale", scale);
      precise_critics_scales_[plugin] = std::make_pair(0.0, scale);
    }
    else if(critic_plugin_name == "GoalDist") {
      double scale; 
      node->get_parameter(dwb_plugin_name_ + "." + critic_plugin_name + ".scale", scale);
      precise_critics_scales_[plugin] = std::make_pair(100.0, scale);
    }
    else if(critic_plugin_name == "PathAlign") {
      double scale;
      node->get_parameter(dwb_plugin_name_ + "." + critic_plugin_name + ".scale", scale);
      precise_critics_scales_[plugin] = std::make_pair(0.0, scale);
    }
    else if(critic_plugin_name == "RotateToGoal") {
      double scale;
      node->get_parameter(dwb_plugin_name_ + "." + critic_plugin_name + ".scale", scale);
      precise_critics_scales_[plugin] = std::make_pair(0.0, scale);
    }
    else if(critic_plugin_name == "ObstacleFootprint") {
      double scale;
      node->get_parameter(dwb_plugin_name_ + "." + critic_plugin_name + ".scale", scale);
      precise_critics_scales_[plugin] = std::make_pair(0.1, scale);
    }
    RCLCPP_INFO(logger_, "Critic plugin initialized");
  }
}

void
DWBLocalPlanner::setPlan(const nav_msgs::msg::Path & path)
{
  auto path2d = nav_2d_utils::pathToPath2D(path);
  for (TrajectoryCritic::Ptr & critic : critics_) {
    critic->reset();
  }

  traj_generator_->reset();

  pub_->publishGlobalPlan(path2d);
  global_plan_ = path2d;
}

geometry_msgs::msg::TwistStamped
DWBLocalPlanner::computeVelocityCommands(
  const geometry_msgs::msg::PoseStamped & pose,
  const geometry_msgs::msg::Twist & velocity,
  nav2_core::GoalChecker * /*goal_checker*/)
{
  std::shared_ptr<dwb_msgs::msg::LocalPlanEvaluation> results = nullptr;
  if (pub_->shouldRecordEvaluation()) {
    results = std::make_shared<dwb_msgs::msg::LocalPlanEvaluation>();
  }

  try {
    nav_2d_msgs::msg::Twist2DStamped cmd_vel2d = computeVelocityCommands(
      nav_2d_utils::poseStampedToPose2D(pose),
      nav_2d_utils::twist3Dto2D(velocity), results);
    pub_->publishEvaluation(results);
    geometry_msgs::msg::TwistStamped cmd_vel;
    cmd_vel.twist = nav_2d_utils::twist2Dto3D(cmd_vel2d.velocity);
    return cmd_vel;
  } catch (const nav2_core::PlannerException & e) {
    pub_->publishEvaluation(results);
    throw;
  }
}

void
DWBLocalPlanner::prepareGlobalPlan(
  const nav_2d_msgs::msg::Pose2DStamped & pose, nav_2d_msgs::msg::Path2D & transformed_plan,
  nav_2d_msgs::msg::Pose2DStamped & goal_pose, bool publish_plan)
{
  transformed_plan = transformGlobalPlan(pose);
  if (publish_plan) {
    pub_->publishTransformedPlan(transformed_plan);
  }

  if (transformed_plan.poses.empty()) {
    throw nav2_core::PlannerException("Transformed plan is empty.");
  }

  auto local_goal_pose = transformed_plan.poses[0], start_pose = transformed_plan.poses[0];
  std::optional<float> path_direction;
  if (transformed_plan.poses.size() > static_cast<size_t>(min_path_size_for_select_local_goal_))
  {
    path_direction = std::atan2(transformed_plan.poses[min_path_size_for_select_local_goal_].y - start_pose.y,
                                transformed_plan.poses[min_path_size_for_select_local_goal_].x - start_pose.x);
  }
  RCLCPP_DEBUG(logger_, "start find local goal, transformed_plan.poses.size: %d", static_cast<int>(transformed_plan.poses.size()));
  for (size_t i = 1; i < transformed_plan.poses.size(); ++i) {
    double g_x = transformed_plan.poses[i].x;
    double g_y = transformed_plan.poses[i].y;
    unsigned int map_x, map_y;
    if (!costmap_ros_->getCostmap()->worldToMap(g_x, g_y, map_x, map_y) || costmap_ros_->getCostmap()->getCost(map_x, map_y) == nav2_costmap_2d::NO_INFORMATION)
    {
      RCLCPP_DEBUG(logger_, "out off local cost map or obstacle");
      break;
    }

    if (i > static_cast<size_t>(min_path_size_for_select_local_goal_) && path_direction) {
      float current_direction = std::atan2(transformed_plan.poses[i].y - start_pose.y,
                                           transformed_plan.poses[i].x - start_pose.x);
      float direction_diff = current_direction - *path_direction;
      double delta = std::fmod(direction_diff + M_PI, 2 * M_PI);
      if (delta < 0) 
        delta += 2 * M_PI;
      direction_diff =  delta - M_PI;
      if (std::fabs(direction_diff) > yaw_diff_threshold_for_select_local_goal_) {
        RCLCPP_DEBUG(logger_, "find inflection point: (%f,%f)", transformed_plan.poses[i].x, transformed_plan.poses[i].y);
        break;
      }
    }
    local_goal_pose = transformed_plan.poses[i];
  }

  goal_pose.header.frame_id = transformed_plan.header.frame_id;
  goal_pose.pose = local_goal_pose;
}

nav_2d_msgs::msg::Twist2DStamped
DWBLocalPlanner::computeVelocityCommands(
  const nav_2d_msgs::msg::Pose2DStamped & pose,
  const nav_2d_msgs::msg::Twist2D & velocity,
  std::shared_ptr<dwb_msgs::msg::LocalPlanEvaluation> & results)
{
  if (results) {
    results->header.frame_id = pose.header.frame_id;
    results->header.stamp = clock_->now();
  }

  nav_2d_msgs::msg::Path2D transformed_plan;
  nav_2d_msgs::msg::Pose2DStamped goal_pose;

  prepareGlobalPlan(pose, transformed_plan, goal_pose);

  pub_->publishLocalGoal(goal_pose);

  // Check if local goal is the final goal (last pose in transformed plan)
  bool is_near_final_goal = false;
  double precise_dist_ratio = 1.0f;
  if (!global_plan_.poses.empty()) {
    const auto& last_pose = global_plan_.poses.back();
    
    // Transform robot pose to global plan frame for comparison
    nav_2d_msgs::msg::Pose2DStamped robot_pose_in_global_frame;
    if (!nav_2d_utils::transformPose(
        tf_, global_plan_.header.frame_id, pose,
        robot_pose_in_global_frame, transform_tolerance_))
    {
        RCLCPP_WARN(logger_, "Failed to transform robot pose to global plan frame");
        is_near_final_goal = false;
    } else {
        double distance_to_final = std::hypot(
            robot_pose_in_global_frame.pose.x - last_pose.x, 
            robot_pose_in_global_frame.pose.y - last_pose.y);
        if(distance_to_final < precise_move_distance_)
        {
            is_near_final_goal = true; 
            precise_dist_ratio = 2*distance_to_final / precise_move_distance_ - 1; 
        }
    }
  }

  if(is_near_final_goal) {
    for(auto & critic : precise_critics_scales_) {
      critic.first->setScale(critic.second.first);
    }
    //node_.lock()->set_parameter(rclcpp::Parameter(dwb_plugin_name_ + ".sim_time", 0.5));
    double precise_speed = std::max(precise_move_speed_ , origin_move_speed_ * precise_dist_ratio);
    traj_generator_->setMaxVelocityX(precise_speed);
    //RCLCPP_INFO(logger_, "in precise mode,set max vel x to %.4f", precise_move_speed_);
  }else{
    //node_.lock()->set_parameter(rclcpp::Parameter(dwb_plugin_name_ + ".sim_time", origin_sim_time_)); 
    traj_generator_->clearSpeedLimit();
    for(auto & critic : precise_critics_scales_) {
      critic.first->setScale(critic.second.second);
    }
  }

  nav2_costmap_2d::Costmap2D * costmap = costmap_ros_->getCostmap();
  std::unique_lock<nav2_costmap_2d::Costmap2D::mutex_t> lock(*(costmap->getMutex()));

  for (TrajectoryCritic::Ptr & critic : critics_) {
    if (!critic->prepare(pose.pose, velocity, goal_pose.pose, transformed_plan)) {
      RCLCPP_WARN(rclcpp::get_logger("DWBLocalPlanner"), "A scoring function failed to prepare");
    } 
  }

  if (speed_reduce_enabled_)
    computeAndSetMaxLineSpeedByPathCost_(transformed_plan);

  try {
    dwb_msgs::msg::TrajectoryScore best = coreScoringAlgorithm(pose.pose, velocity, results);

    // Return Value
    nav_2d_msgs::msg::Twist2DStamped cmd_vel;
    cmd_vel.header.stamp = clock_->now();
    cmd_vel.velocity = best.traj.velocity;
    
    RCLCPP_INFO_THROTTLE(logger_, *clock_, 1000, "dwb vel x: %.4f, omega: %.4f",
      best.traj.velocity.x, best.traj.velocity.theta);

    // debrief stateful scoring functions
    for (TrajectoryCritic::Ptr & critic : critics_) {
      critic->debrief(cmd_vel.velocity);
    }

    lock.unlock();

    pub_->publishLocalPlan(pose.header, best.traj);
    pub_->publishCostGrid(costmap_ros_, critics_);

    return cmd_vel;
  } catch (const dwb_core::NoLegalTrajectoriesException & e) {
    nav_2d_msgs::msg::Twist2D empty_cmd;
    dwb_msgs::msg::Trajectory2D empty_traj;
    // debrief stateful scoring functions
    for (TrajectoryCritic::Ptr & critic : critics_) {
      critic->debrief(empty_cmd);
    }

    lock.unlock();

    pub_->publishLocalPlan(pose.header, empty_traj);
    pub_->publishCostGrid(costmap_ros_, critics_);

    throw;
  }
}

dwb_msgs::msg::TrajectoryScore
DWBLocalPlanner::coreScoringAlgorithm(
  const geometry_msgs::msg::Pose2D & pose,
  const nav_2d_msgs::msg::Twist2D velocity,
  std::shared_ptr<dwb_msgs::msg::LocalPlanEvaluation> & results)
{
  nav_2d_msgs::msg::Twist2D twist;
  dwb_msgs::msg::Trajectory2D traj;
  dwb_msgs::msg::TrajectoryScore best, worst;
  best.total = -1;
  worst.total = -1;
  IllegalTrajectoryTracker tracker;

  traj_generator_->startNewIteration(velocity);
  while (traj_generator_->hasMoreTwists()) {
    twist = traj_generator_->nextTwist();
    traj = traj_generator_->generateTrajectory(pose, velocity, twist);

    try {
      dwb_msgs::msg::TrajectoryScore score = scoreTrajectory(traj, best.total);
      tracker.addLegalTrajectory();
      if (results) {
        results->twists.push_back(score);
      }
      if (best.total < 0 || score.total < best.total) {
        best = score;
        if (results) {
          results->best_index = results->twists.size() - 1;
        }
      }
      if (worst.total < 0 || score.total > worst.total) {
        worst = score;
        if (results) {
          results->worst_index = results->twists.size() - 1;
        }
      }
    } catch (const dwb_core::IllegalTrajectoryException & e) {
      if (results) {
        dwb_msgs::msg::TrajectoryScore failed_score;
        failed_score.traj = traj;

        dwb_msgs::msg::CriticScore cs;
        cs.name = e.getCriticName();
        cs.raw_score = -1.0;
        failed_score.scores.push_back(cs);
        failed_score.total = -1.0;
        results->twists.push_back(failed_score);
      }
      tracker.addIllegalTrajectory(e);
    }
  }

  if (best.total < 0) {
    if (debug_trajectory_details_) {
      RCLCPP_ERROR(rclcpp::get_logger("DWBLocalPlanner"), "%s", tracker.getMessage().c_str());
      for (auto const & x : tracker.getPercentages()) {
        RCLCPP_ERROR(
          rclcpp::get_logger(
            "DWBLocalPlanner"), "%.2f: %10s/%s", x.second,
          x.first.first.c_str(), x.first.second.c_str());
      }
    }
    throw NoLegalTrajectoriesException(tracker);
  }

  return best;
}

dwb_msgs::msg::TrajectoryScore
DWBLocalPlanner::scoreTrajectory(
  const dwb_msgs::msg::Trajectory2D & traj,
  double best_score)
{
  dwb_msgs::msg::TrajectoryScore score;
  score.traj = traj;

  for (TrajectoryCritic::Ptr & critic : critics_) {
    dwb_msgs::msg::CriticScore cs;
    cs.name = critic->getName();
    cs.scale = critic->getScale();

    if (cs.scale == 0.0) {
      score.scores.push_back(cs);
      continue;
    }

    double critic_score = critic->scoreTrajectory(traj);
    cs.raw_score = critic_score;
    score.scores.push_back(cs);
    score.total += critic_score * cs.scale;
    if (short_circuit_trajectory_evaluation_ && best_score > 0 && score.total > best_score) {
      // since we keep adding positives, once we are worse than the best, we will stay worse
      break;
    }
  }

  return score;
}

nav_2d_msgs::msg::Path2D
DWBLocalPlanner::transformGlobalPlan(
  const nav_2d_msgs::msg::Pose2DStamped & pose)
{
  if (global_plan_.poses.empty()) {
    throw nav2_core::PlannerException("Received plan with zero length");
  }

  // let's get the pose of the robot in the frame of the plan
  nav_2d_msgs::msg::Pose2DStamped robot_pose;
  if (!nav_2d_utils::transformPose(
      tf_, global_plan_.header.frame_id, pose,
      robot_pose, transform_tolerance_))
  {
    throw dwb_core::
          PlannerTFException("Unable to transform robot pose into global plan's frame");
  }

  // we'll discard points on the plan that are outside the local costmap
  nav2_costmap_2d::Costmap2D * costmap = costmap_ros_->getCostmap();
  double dist_threshold = std::max(costmap->getSizeInCellsX(), costmap->getSizeInCellsY()) *
    costmap->getResolution() / 2.0;

  // If prune_plan is enabled (it is by default) then we want to restrict the
  // plan to distances within that range as well.
  double prune_dist = prune_distance_;

  // Set the maximum distance we'll include points before getting to the part
  // of the path where the robot is located (the start of the plan). Basically,
  // these are the points the robot has already passed.
  double transform_start_threshold;
  if (prune_plan_) {
    transform_start_threshold = std::min(dist_threshold, prune_dist);
  } else {
    transform_start_threshold = dist_threshold;
  }

  // Set the maximum distance we'll include points after the part of the plan
  // near the robot (the end of the plan). This determines the amount of the
  // plan passed on to the critics
  double transform_end_threshold;
  double forward_prune_dist = forward_prune_distance_;
  if (shorten_transformed_plan_) {
    transform_end_threshold = std::min(dist_threshold, forward_prune_dist);
  } else {
    transform_end_threshold = dist_threshold;
  }

  // Find the first pose in the global plan that's further than prune distance
  // from the robot using integrated distance
  auto prune_point = nav2_util::geometry_utils::first_after_integrated_distance(
    global_plan_.poses.begin(), global_plan_.poses.end(), prune_dist);

  // Find the first pose in the plan (upto prune_point) that's less than transform_start_threshold
  // from the robot.
  auto transformation_begin = std::find_if(
    begin(global_plan_.poses), prune_point,
    [&](const auto & global_plan_pose) {
      return euclidean_distance(robot_pose.pose, global_plan_pose) < transform_start_threshold;
    });

  // Find the first pose in the end of the plan that's further than transform_end_threshold
  // from the robot using integrated distance
  auto transformation_end = std::find_if(
    transformation_begin, global_plan_.poses.end(),
    [&](const auto & pose) {
      return euclidean_distance(pose, robot_pose.pose) > transform_end_threshold;
    });

  // Transform the near part of the global plan into the robot's frame of reference.
  nav_2d_msgs::msg::Path2D transformed_plan;
  transformed_plan.header.frame_id = costmap_ros_->getGlobalFrameID();
  transformed_plan.header.stamp = pose.header.stamp;

  // Helper function for the transform below. Converts a pose2D from global
  // frame to local
  auto transformGlobalPoseToLocal = [&](const auto & global_plan_pose) {
      nav_2d_msgs::msg::Pose2DStamped stamped_pose, transformed_pose;
      stamped_pose.header.frame_id = global_plan_.header.frame_id;
      stamped_pose.pose = global_plan_pose;
      nav_2d_utils::transformPose(
        tf_, transformed_plan.header.frame_id,
        stamped_pose, transformed_pose, transform_tolerance_);
      return transformed_pose.pose;
    };

  std::transform(
    transformation_begin, transformation_end,
    std::back_inserter(transformed_plan.poses),
    transformGlobalPoseToLocal);

  // Remove the portion of the global plan that we've already passed so we don't
  // process it on the next iteration.
  if (prune_plan_) {
    global_plan_.poses.erase(begin(global_plan_.poses), transformation_begin);
    pub_->publishGlobalPlan(global_plan_);
  }

  if (transformed_plan.poses.empty()) {
    throw nav2_core::PlannerException("Resulting plan has 0 poses in it.");
  }
  return transformed_plan;
}

void DWBLocalPlanner::computeAndSetMaxLineSpeedByPathCost_(const nav_2d_msgs::msg::Path2D &transformed_plan)
{
  double max_vel_x = 0.0;
  if (traj_generator_->getMaxVelocityX(max_vel_x)) {       
    if (speed_reduce_min_threshold_ >= max_vel_x) {
      return;
    }
    if (transformed_plan.poses.size() < 2) {
      return;
    }
    std::optional<geometry_msgs::msg::Pose2D> lastCheckPoint;
    uint8_t maxCost = 0;
    for (auto &pose : transformed_plan.poses)
    {
      unsigned int map_x, map_y;
      if (!costmap_ros_->getCostmap()->worldToMap(pose.x, pose.y, map_x, map_y) || costmap_ros_->getCostmap()->getCost(map_x, map_y) == nav2_costmap_2d::NO_INFORMATION) {
        break;
      }

      if (!lastCheckPoint) {
        lastCheckPoint = pose;
        continue;
      } else {
        double dist = euclidean_distance(pose, *lastCheckPoint);
        if (dist < speed_reduce_cost_check_window_size_) {
          continue;
        }
      }

      double resolution = costmap_ros_->getCostmap()->getResolution();
      double startX = pose.x - speed_reduce_cost_check_window_size_;
      double startY = pose.y - speed_reduce_cost_check_window_size_;
      double endX = pose.x + speed_reduce_cost_check_window_size_ + resolution / 2.0;
      double endY = pose.y + speed_reduce_cost_check_window_size_ + resolution / 2.0;
      for (int i = 0; startX + i * resolution < endX; i++) {
        for (int j = 0; startY + j * resolution < endY; j++) {
          unsigned int map_x, map_y;
          costmap_ros_->getCostmap()->worldToMap(startX + i * resolution, startY + j * resolution, map_x, map_y);
          uint8_t cost = costmap_ros_->getCostmap()->getCost(map_x, map_y);
          if (cost >= nav2_costmap_2d::INSCRIBED_INFLATED_OBSTACLE) {
            traj_generator_->setMaxVelocityX(speed_reduce_min_threshold_);
            return;
          }
          maxCost = cost > maxCost ? cost : maxCost;
        }
      }
    }

    double deltaSpeed = (1 - 1.0 * maxCost / nav2_costmap_2d::INSCRIBED_INFLATED_OBSTACLE) * (max_vel_x - speed_reduce_min_threshold_);
    double resSpeed = speed_reduce_min_threshold_ + deltaSpeed;
    traj_generator_->setMaxVelocityX(resSpeed);
  }
} 

rcl_interfaces::msg::SetParametersResult DWBLocalPlanner::paramCallback(const std::vector<rclcpp::Parameter>& parameters)
{
  auto result = rcl_interfaces::msg::SetParametersResult();
  result.successful = true;

  for (const auto& param : parameters) {
    if (param.get_name().find(".scale")  != std::string::npos) {
      auto critic_name = param.get_name().substr(0, param.get_name().find(".scale"));
      critic_name = critic_name.substr(critic_name.rfind(".") + 1); 
      double critic_scale = param.as_double(); 
      for (TrajectoryCritic::Ptr & critic : critics_) {
        if (critic->getName() == critic_name) {
          RCLCPP_INFO(logger_, "Set %s scale to %f", critic_name.c_str(), critic_scale);
          critic->setScale(critic_scale);
          if(precise_critics_scales_.find(critic) != precise_critics_scales_.end()) {
            precise_critics_scales_[critic].second = critic_scale;
          }
          break;
        }
      } 
    }
  }
  return result; 
}

}  // namespace dwb_core

// Register this controller as a nav2_core plugin
PLUGINLIB_EXPORT_CLASS(
  dwb_core::DWBLocalPlanner,
  nav2_core::Controller)
