// Copyright (c) 2024 Polaris Xia
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef NAV2_BEHAVIOR_TREE__PLUGINS__CONDITION__DISTANCE_TO_GOAL_HPP_
#define NAV2_BEHAVIOR_TREE__PLUGINS__CONDITION__DISTANCE_TO_GOAL_HPP_

#include <string>
#include <memory>

#include "behaviortree_cpp_v3/condition_node.h"
#include "geometry_msgs/msg/pose_stamped.hpp"
#include "rclcpp/rclcpp.hpp"
#include "tf2_ros/buffer.h"
#include "tf2_ros/transform_listener.h"

namespace nav2_behavior_tree
{

/**
 * @brief A BT::ConditionNode that checks if the distance to goal is less than threshold
 * Returns SUCCESS if distance < threshold
 * Returns FAILURE if distance >= threshold
 */
class DistanceToGoal : public BT::ConditionNode
{
public:
  /**
   * @brief A constructor for nav2_behavior_tree::DistanceToGoal
   * @param condition_name Name for the XML tag for this node
   * @param conf BT node configuration
   */
  DistanceToGoal(
    const std::string & condition_name,
    const BT::NodeConfiguration & conf);

  DistanceToGoal() = delete;

  /**
   * @brief The main override required by a BT condition
   * @return BT::NodeStatus Status of tick execution
   */
  BT::NodeStatus tick() override;

  /**
   * @brief Creates list of BT ports
   * @return BT::PortsList Containing node-specific ports
   */
  static BT::PortsList providedPorts()
  {
    return {
      BT::InputPort<geometry_msgs::msg::PoseStamped>("goal", "Goal pose to check distance to"),
      BT::InputPort<double>("distance_threshold", 0.5, "Distance threshold in meters"),
    };
  }

private:
  /**
   * @brief Calculate distance between robot and goal
   * @param goal_pose Goal pose
   * @return Distance in meters
   */
  double calculateDistance(const geometry_msgs::msg::PoseStamped & goal_pose);

  rclcpp::Node::SharedPtr node_;
  std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
  std::shared_ptr<tf2_ros::TransformListener> tf_listener_;
  
  double distance_threshold_;
  geometry_msgs::msg::PoseStamped robot_pose_;
  bool robot_pose_valid_;
};

}  // namespace nav2_behavior_tree

#endif  // NAV2_BEHAVIOR_TREE__PLUGINS__CONDITION__DISTANCE_TO_GOAL_HPP_
