#pragma once

#include "behaviortree_cpp_v3/action_node.h"
#include "nav_msgs/msg/path.hpp"
#include "geometry_msgs/msg/pose_stamped.hpp"

#include "rclcpp/rclcpp.hpp"
#include "tf2_ros/buffer.h"
#include "tf2_ros/transform_listener.h"
#include "nav2_msgs/msg/costmap.hpp"
#include "nav2_costmap_2d/costmap_subscriber.hpp"

#include <chrono>

namespace nav2_behavior_tree
{

class PathEvaluator : public BT::SyncActionNode
{
public:
  PathEvaluator(const std::string & name, const BT::NodeConfiguration & conf);

  static BT::PortsList providedPorts()
  {
    return {
      BT::InputPort<nav_msgs::msg::Path>("new_path"),
      BT::InputPort<nav_msgs::msg::Path>("current_path"),
      BT::OutputPort<nav_msgs::msg::Path>("selected_path"),
      BT::InputPort<double>("length_factor"),
      BT::InputPort<int>("obstacle_num_threshold"),
      BT::InputPort<double>("path_obstacle_dist_threshold")
    };
  }

  BT::NodeStatus tick() override;

private:
  bool isGoalChange_(
    const nav_msgs::msg::Path & new_path,
    const nav_msgs::msg::Path & current_path);

  bool isPathObstructed_(const nav_msgs::msg::Path & path);
  bool isCurrentPathTimeout_();
  bool reduceCurrentPath_(const geometry_msgs::msg::PoseStamped & current_pose, nav_msgs::msg::Path & current_path);
  bool getRobotPose_(geometry_msgs::msg::PoseStamped & pose);

private:
  rclcpp::Node::SharedPtr node_;
  std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
  std::shared_ptr<tf2_ros::TransformListener> tf_listener_;
  std::unique_ptr<nav2_costmap_2d::CostmapSubscriber> costmap_sub_;
  std::chrono::steady_clock::time_point update_time_;
  int obstacleNumThreshold_;
  double pathObstacleDistThreshold_;
};

}