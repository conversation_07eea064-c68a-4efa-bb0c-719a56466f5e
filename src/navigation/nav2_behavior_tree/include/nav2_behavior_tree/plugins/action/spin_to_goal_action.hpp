// Copyright (c) 2018 Intel Corporation
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef NAV2_BEHAVIOR_TREE__PLUGINS__ACTION__SPIN_TO_GOAL_ACTION_HPP_
#define NAV2_BEHAVIOR_TREE__PLUGINS__ACTION__SPIN_TO_GOAL_ACTION_HPP_

#include <string>
#include <memory>

#include "nav2_behavior_tree/plugins/action/spin_action.hpp"
#include "geometry_msgs/msg/pose_stamped.hpp"
#include "tf2_ros/buffer.h"

namespace nav2_behavior_tree
{

/**
 * @brief A nav2_behavior_tree::BtActionNode class that extends SpinAction
 * to automatically align with the goal orientation
 */
class SpinToGoalAction : public SpinAction
{
public:
  /**
   * @brief A constructor for nav2_behavior_tree::SpinToGoalAction
   * @param xml_tag_name Name for the XML tag for this node
   * @param action_name Action name this node creates a client for
   * @param conf BT node configuration
   */
  SpinToGoalAction(
    const std::string & xml_tag_name,
    const std::string & action_name,
    const BT::NodeConfiguration & conf);

  /**
   * @brief Function to perform some user-defined operation on tick
   */
  void on_tick() override;

  void on_wait_for_result(std::shared_ptr<const nav2_msgs::action::Spin::Feedback>/*feedback*/) override;

  /**
   * @brief Creates list of BT ports
   * @return BT::PortsList Containing basic ports along with node-specific ports
   */
  static BT::PortsList providedPorts()
  {
    return providedBasicPorts(
      {
        BT::InputPort<double>("time_allowance", 10.0, "Allowed time for spinning"),
        BT::InputPort<geometry_msgs::msg::PoseStamped>("goal_pose", "Goal pose to align to"),
        BT::InputPort<std::string>("robot_frame", "base_link", "Robot frame ID"),
        BT::InputPort<std::string>("global_frame", "map", "Global frame ID"),
        BT::InputPort<double>("transform_tolerance", 0.3, "Transform tolerance in seconds"),
        BT::InputPort<double>("alignment_tolerance", 0.02, "Tolerance for considering aligned (radians)"),
        BT::InputPort<std::string>("server_name", "/spin", "Spin server name")
      });
  }

private:
  /**
   * @brief Get the current robot pose
   * @param current_pose Output parameter for current robot pose
   * @return True if pose was successfully retrieved
   */
  bool getCurrentPose(geometry_msgs::msg::PoseStamped & current_pose);
  
  /**
   * @brief Calculate the yaw difference between current and goal orientations
   * @param current_yaw Current robot yaw
   * @param goal_yaw Goal yaw
   * @return Shortest angular distance between the two yaws
   */
  double calculateYawDifference(double current_yaw, double goal_yaw);
  
  bool hasGoalChanged(
    const geometry_msgs::msg::PoseStamped& current_goal);

  std::string robot_frame_;
  std::string global_frame_;
  double transform_tolerance_;
  double alignment_tolerance_;
  std::string server_name_;
  std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
  geometry_msgs::msg::PoseStamped goal_pose_;
};

}  // namespace nav2_behavior_tree

#endif  // NAV2_BEHAVIOR_TREE__PLUGINS__ACTION__SPIN_TO_GOAL_ACTION_HPP_ 