<?xml version="1.0"?>
<!--
  For instructions on using Groot and description of the following BehaviorTree nodes,
  please refer to the groot_instructions.md and REAMDE.md respectively located in the
  nav2_behavior_tree package.
-->
<root>
  <TreeNodesModel>
    <!-- ############################### ACTION NODES ################################# -->
    <Action ID="BackUp">
      <input_port name="backup_dist">Distance to backup</input_port>
      <input_port name="backup_speed">Speed at which to backup</input_port>
      <input_port name="time_allowance">Allowed time for reversing</input_port>
      <input_port name="server_name">Server name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="DriveOnHeading">
      <input_port name="dist_to_travel">Distance to travel</input_port>
      <input_port name="speed">Speed at which to travel</input_port>
      <input_port name="time_allowance">Allowed time for reversing</input_port>
      <input_port name="server_name">Server name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="CancelControl">
      <input_port name="server_name">Server name to cancel the controller server</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="CancelBackUp">
      <input_port name="server_name">Server name to cancel the backup behavior</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="CancelDriveOnHeading">
      <input_port name="server_name">Service name to cancel the drive on heading behavior</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="CancelSpin">
      <input_port name="server_name">Server name to cancel the spin behavior</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="CancelAssistedTeleop">
      <input_port name="server_name">Server name to cancel the assisted teleop behavior</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="CancelWait">
      <input_port name="server_name">Server name to cancel the wait behavior</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="ClearEntireCostmap">
      <input_port name="service_name">Service name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="ClearCostmapExceptRegion">
      <input_port name="service_name">Service name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
      <input_port name="reset_distance">Distance from the robot above which obstacles are cleared</input_port>
    </Action>

    <Action ID="ClearCostmapAroundRobot">
      <input_port name="service_name">Service name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
      <input_port name="reset_distance">Distance from the robot under which obstacles are cleared</input_port>
    </Action>

    <Action ID="ComputePathToPose">
      <input_port name="goal">Destination to plan to</input_port>
      <input_port name="start">Start pose of the path if overriding current robot pose</input_port>
      <input_port name="planner_id">Mapped name to the planner plugin type to use</input_port>
      <input_port name="server_name">Server name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
      <output_port name="path">Path created by ComputePathToPose node</output_port>
    </Action>

    <Action ID="ComputePathThroughPoses">
      <input_port name="goals">Destinations to plan through</input_port>
      <input_port name="start">Start pose of the path if overriding current robot pose</input_port>
      <input_port name="server_name">Server name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
      <input_port name="planner_id">Mapped name to the planner plugin type to use</input_port>
      <output_port name="path">Path created by ComputePathToPose node</output_port>
    </Action>

    <Action ID="RemovePassedGoals">
      <input_port name="input_goals">Input goals to remove if passed</input_port>
      <input_port name="radius">Radius tolerance on a goal to consider it passed</input_port>
      <input_port name="global_frame">Global frame</input_port>
      <input_port name="robot_base_frame">Robot base frame</input_port>
      <output_port name="output_goals">Set of goals after removing any passed</output_port>
    </Action>

    <Action ID="SmoothPath">
      <input_port name="smoother_id" default="SmoothPath"/>
      <input_port name="unsmoothed_path">Path to be smoothed</input_port>
      <input_port name="max_smoothing_duration">Maximum smoothing duration</input_port>
      <input_port name="check_for_collisions">Bool if collision check should be performed</input_port>
      <output_port name="smoothed_path">Smoothed path</output_port>
      <output_port name="smoothing_duration">Smoothing duration</output_port>
      <output_port name="was_completed">True if smoothing was not interrupted by time limit</output_port>
    </Action>

    <Action ID="FollowPath">
      <input_port name="controller_id" default="FollowPath"/>
      <input_port name="path">Path to follow</input_port>
      <input_port name="goal_checker_id">Goal checker</input_port>
      <input_port name="server_name">Server name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="NavigateToPose">
        <input_port name="goal">Goal</input_port>
        <input_port name="server_name">Server name</input_port>
        <input_port name="server_timeout">Server timeout</input_port>
        <input_port name="behavior_tree">Behavior tree to run</input_port>
    </Action>

    <Action ID="NavigateThroughPoses">
        <input_port name="goals">Goals</input_port>
        <input_port name="server_name">Server name</input_port>
        <input_port name="server_timeout">Server timeout</input_port>
        <input_port name="behavior_tree">Behavior tree to run</input_port>
    </Action>

    <Action ID="ReinitializeGlobalLocalization">
        <input_port name="service_name">Service name</input_port>
        <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="TruncatePath">
        <input_port name="distance">Distance before goal to truncate</input_port>
        <input_port name="input_path">Path to truncate</input_port>
        <output_port name="output_path">Truncated path to utilize</output_port>
    </Action>

    <Action ID="TruncatePathLocal">
      <input_port name="distance_forward">Distance in forward direction</input_port>
      <input_port name="distance_backward">Distance in backward direction</input_port>
      <input_port name="robot_frame">Robot base frame id</input_port>
      <input_port name="transform_tolerance">Transform lookup tolerance</input_port>
      <input_port name="pose">Manually specified pose to be used if overriding current robot pose</input_port>
      <input_port name="angular_distance_weight">Weight of angular distance relative to positional distance when finding which path pose is closest to robot. Not applicable on paths without orientations assigned</input_port>
      <input_port name="max_robot_pose_search_dist">Maximum forward integrated distance along the path (starting from the last detected pose) to bound the search for the closest pose to the robot. When set to infinity (default), whole path is searched every time</input_port>
      <output_port name="output_path">Truncated path to utilize</output_port>
    </Action>

    <Action ID="PlannerSelector">
        <input_port name="topic_name">Name of the topic to receive planner selection commands</input_port>
        <input_port name="default_planner">Default planner of the planner selector</input_port>
        <output_port name="selected_planner">Name of the selected planner received from the topic subcription</output_port>
    </Action>

    <Action ID="ControllerSelector">
        <input_port name="topic_name">Name of the topic to receive controller selection commands</input_port>
        <input_port name="default_controller">Default controller of the controller selector</input_port>
        <output_port name="selected_controller">Name of the selected controller received from the topic subcription</output_port>
    </Action>

    <Action ID="SmootherSelector">
        <input_port name="topic_name">Name of the topic to receive smoother selection commands</input_port>
        <input_port name="default_smoother">Default smoother of the smoother selector</input_port>
        <output_port name="selected_smoother">Name of the selected smoother received from the topic subcription</output_port>
    </Action>

    <Action ID="GoalCheckerSelector">
        <input_port name="topic_name">Name of the topic to receive goal checker selection commands</input_port>
        <input_port name="default_goal_checker">Default goal checker of the controller selector</input_port>
        <output_port name="selected_goal_checker">Name of the selected goal checker received from the topic subcription</output_port>
    </Action>

    <Action ID="ProgressCheckerSelector">
        <input_port name="topic_name">Name of the topic to receive progress checker selection commands</input_port>
        <input_port name="default_progress_checker">Default progress checker of the controller selector</input_port>
        <output_port name="selected_progress_checker">Name of the selected progress checker received from the topic subcription</output_port>
    </Action>

    <Action ID="Spin">
      <input_port name="spin_dist">Spin distance</input_port>
      <input_port name="time_allowance">Allowed time for spinning</input_port>
      <input_port name="server_name">Server name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="SpinToGoal">
      <input_port name="time_allowance">Allowed time for spinning</input_port>
      <input_port name="goal_pose">Goal pose to align to</input_port>
      <input_port name="robot_frame">Robot frame ID</input_port>
      <input_port name="global_frame">Global frame ID</input_port>
      <input_port name="transform_tolerance">Transform tolerance in seconds</input_port>
      <input_port name="alignment_tolerance">Tolerance for considering aligned (radians)</input_port>
      <input_port name="server_name">Server name</input_port>
    </Action>

    <Action ID="Wait">
      <input_port name="wait_duration">Wait time</input_port>
      <input_port name="server_name">Server name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="AssistedTeleop">
      <input_port name="time_allowance">Allowed time for spinning</input_port>
      <input_port name="is_recovery">If true recovery count will be incremented</input_port>
      <input_port name="server_name">Service name</input_port>
      <input_port name="server_timeout">Server timeout</input_port>
    </Action>

    <Action ID="PathMonitorNode">
      <input_port name="path">Remaining Path</input_port>
      <input_port name="cost_threshold">Min cost threshold for narrow corridor</input_port>
      <input_port name="length_threshold">Min path size for narrow corridor</input_port>
      <input_port name="recover_cost_threshold">Cost threshold for restore original parameters</input_port>
      <input_port name="inflation_radius_in_narrow_corridor">Inflation_radius in narrow corridor</input_port> 
      <input_port name="goal_scale_in_narrow_corridor">DWB goal_scale in narrow corridor</input_port> 
      <input_port name="local_resolution_in_narrow_corridor">local costmap resolution in narrow corridor</input_port>
    </Action>
    
    <Action ID="PathEvaluator">
      <input_port name="new_path">New planned Path</input_port>
      <input_port name="current_path">Current planned path</input_port>
      <input_port name="length_factor">Length multiplication factor to check if the path is significantly shorter </input_port>
      <input_port name="obstacle_num_threshold">Obstacle number threshold</input_port>
      <input_port name="path_obstacle_dist_threshold">Path obstacle distance threshold</input_port>
      <output_port name="selected_path">Select planned path</output_port>
    </Action>

    <!-- ############################### CONDITION NODES ############################## -->
    <Condition ID="GoalReached">
        <input_port name="goal">Destination</input_port>
        <input_port name="global_frame">Reference frame</input_port>
        <input_port name="robot_base_frame">Robot base frame</input_port>
    </Condition>

    <Condition ID="IsStuck"/>

    <Condition ID="TransformAvailable">
      <input_port name="child">Child frame for transform</input_port>
      <input_port name="parent">Parent frame for transform</input_port>
    </Condition>

    <Condition ID="GoalUpdated"/>

    <Condition ID="GlobalUpdatedGoal"/>

    <Condition ID="IsBatteryLow">
      <input_port name="min_battery">Min battery % or voltage before triggering</input_port>
      <input_port name="battery_topic">Topic for battery info</input_port>
      <input_port name="is_voltage">Bool if check based on voltage or total %</input_port>
    </Condition>

    <Condition ID="IsBatteryCharging">
      <input_port name="battery_topic">Topic for battery info</input_port>
    </Condition>

    <Condition ID="DistanceTraveled">
      <input_port name="distance">Distance to check if passed</input_port>
      <input_port name="global_frame">reference frame to check in</input_port>
      <input_port name="robot_base_frame">Robot frame to check relative to global_frame</input_port>
    </Condition>

    <Condition ID="TimeExpired">
      <input_port name="seconds">Time to check if expired</input_port>
    </Condition>


    <Condition ID="PathExpiringTimer">
      <input_port name="seconds">Time to check if expired</input_port>
      <input_port name="path">Check if path has been updated to enable timer reset</input_port>
    </Condition>

    <Condition ID="InitialPoseReceived">
    </Condition>

    <Condition ID="IsPathValid">
      <input_port name="path"> Path to validate </input_port>
      <input_port name="server_timeout"> Server timeout </input_port>
    </Condition>

    <!-- ############################### CONTROL NODES ################################ -->
    <Control ID="PipelineSequence"/>

    <Control ID="RecoveryNode">
      <input_port name="number_of_retries">Number of retries</input_port>
    </Control>

    <Control ID="RoundRobin"/>

    <!-- ############################### DECORATOR NODES ############################## -->
    <Decorator ID="RateController">
      <input_port name="hz">Rate</input_port>
    </Decorator>

    <Decorator ID="DistanceController">
      <input_port name="distance">Distance</input_port>
      <input_port name="global_frame">Reference frame</input_port>
      <input_port name="robot_base_frame">Robot base frame</input_port>
    </Decorator>

    <Decorator ID="SingleTrigger">
    </Decorator>

    <Decorator ID="GoalUpdater">
      <input_port name="input_goal">Original goal in</input_port>
      <output_port name="output_goal">Output goal set by subscription</output_port>
    </Decorator>

    <Decorator ID="SpeedController">
      <input_port name="min_rate">Minimum rate</input_port>
      <input_port name="max_rate">Maximum rate</input_port>
      <input_port name="min_speed">Minimum speed</input_port>
      <input_port name="max_speed">Maximum speed</input_port>
    </Decorator>

    <Decorator ID="PathLongerOnApproach">
      <input_port name="path">Planned Path</input_port>
      <input_port name="prox_len">Proximity length (m) for the path to be longer on approach</input_port>
      <input_port name="length_factor">Length multiplication factor to check if the path is significantly longer </input_port>
    </Decorator>

    <Decorator ID="GoalUpdatedController">
    </Decorator>

  </TreeNodesModel>
</root>
