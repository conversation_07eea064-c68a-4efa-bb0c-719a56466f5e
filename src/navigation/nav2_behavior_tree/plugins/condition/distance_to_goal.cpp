// Copyright (c) 2024 Polaris Xia
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <string>
#include <memory>
#include <cmath>

#include "behaviortree_cpp_v3/condition_node.h"
#include "nav2_behavior_tree/plugins/condition/distance_to_goal.hpp"
#include "tf2/utils.h"

namespace nav2_behavior_tree
{

DistanceToGoal::DistanceToGoal(
  const std::string & condition_name,
  const BT::NodeConfiguration & conf)
: BT::ConditionNode(condition_name, conf),
  robot_pose_valid_(false)
{
  node_ = config().blackboard->get<rclcpp::Node::SharedPtr>("node");
  
  getInput("distance_threshold", distance_threshold_);
  
  // Initialize TF
  tf_buffer_ = std::make_shared<tf2_ros::Buffer>(node_->get_clock());
  tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
}

BT::NodeStatus DistanceToGoal::tick()
{
  geometry_msgs::msg::PoseStamped goal_pose;
  if (!getInput("goal", goal_pose)) {
    RCLCPP_ERROR(node_->get_logger(), "Failed to get goal pose from input port");
    return BT::NodeStatus::FAILURE;
  }
  
  // Get current robot pose
  try {
    geometry_msgs::msg::TransformStamped transform = tf_buffer_->lookupTransform(
      "map", "base_link", tf2::TimePointZero);
    
    robot_pose_.header.frame_id = "map";
    robot_pose_.header.stamp = node_->now();
    robot_pose_.pose.position.x = transform.transform.translation.x;
    robot_pose_.pose.position.y = transform.transform.translation.y;
    robot_pose_.pose.position.z = transform.transform.translation.z;
    robot_pose_.pose.orientation = transform.transform.rotation;
    robot_pose_valid_ = true;
  } catch (tf2::TransformException & ex) {
    RCLCPP_WARN(node_->get_logger(), "Could not transform robot pose: %s", ex.what());
    robot_pose_valid_ = false;
  }
  
  if (!robot_pose_valid_) {
    RCLCPP_WARN(node_->get_logger(), "Robot pose not valid");
    return BT::NodeStatus::FAILURE;
  }
  
  // Calculate distance to goal
  double distance = calculateDistance(goal_pose);
  
  // Check if distance is less than threshold
  if (distance < distance_threshold_) {
    RCLCPP_INFO(node_->get_logger(), "Distance to goal (%.2f) is less than threshold (%.2f)", 
                distance, distance_threshold_);
    return BT::NodeStatus::SUCCESS;
  } else {
    RCLCPP_INFO(node_->get_logger(), "Distance to goal (%.2f) is not less than threshold (%.2f)", 
                 distance, distance_threshold_);
    return BT::NodeStatus::FAILURE;
  }
}

double DistanceToGoal::calculateDistance(const geometry_msgs::msg::PoseStamped & goal_pose)
{
  if (!robot_pose_valid_) {
    return std::numeric_limits<double>::max();
  }
  
  double dx = goal_pose.pose.position.x - robot_pose_.pose.position.x;
  double dy = goal_pose.pose.position.y - robot_pose_.pose.position.y;
  
  return std::sqrt(dx * dx + dy * dy);
}

}  // namespace nav2_behavior_tree

#include "behaviortree_cpp_v3/bt_factory.h"
BT_REGISTER_NODES(factory)
{
  factory.registerNodeType<nav2_behavior_tree::DistanceToGoal>("DistanceToGoal");
}
