// Copyright (c) 2019 Intel Corporation
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <string>
#include <memory>

#include "nav2_util/robot_utils.hpp"
#include "geometry_msgs/msg/pose_stamped.hpp"
#include "nav2_util/node_utils.hpp"
#include "nav2_costmap_2d/cost_values.hpp"

#include "nav2_behavior_tree/plugins/condition/goal_unreachable_condition.hpp"

namespace nav2_behavior_tree
{

GoalUnreachableCondition::GoalUnreachableCondition(
  const std::string & condition_name,
  const BT::NodeConfiguration & conf)
: BT::ConditionNode(condition_name, conf),
  initialized_(false)
{
  initialize();
}

GoalUnreachableCondition::~GoalUnreachableCondition()
{
  cleanup();
}

BT::NodeStatus GoalUnreachableCondition::tick()
{
  if (!initialized_) {
    initialize();
  }

  rclcpp::spin_some(node_);

  if (isGoalUnreachable()) {
    return BT::NodeStatus::SUCCESS;
  }
  return BT::NodeStatus::FAILURE;
}

void GoalUnreachableCondition::initialize()
{
  node_ = config().blackboard->get<rclcpp::Node::SharedPtr>("node");
  costmap_sub_ = std::make_unique<nav2_costmap_2d::CostmapSubscriber>(node_,"/global_costmap/costmap_raw"); 
  tf_buffer_ = std::make_shared<tf2_ros::Buffer>(node_->get_clock());
  tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);

  initialized_ = true;
}

bool GoalUnreachableCondition::isGoalUnreachable()
{
  try {
    geometry_msgs::msg::PoseStamped goal;
    if (!getInput("goal", goal)) {
      RCLCPP_ERROR(node_->get_logger(), "Goal is not available");
      return true;
    }

    auto costmap = costmap_sub_->getCostmap();
    if (!costmap) { 
      RCLCPP_ERROR(node_->get_logger(), "Costmap is not available");
      return true;
    }
      
    auto costmap_frame = costmap_sub_->getFrameID();
    if (costmap_frame.empty()) {
      RCLCPP_ERROR(node_->get_logger(), "costmap frame id is empty");
      return true;
    }

    auto transform = tf_buffer_->lookupTransform(costmap_frame, goal.header.frame_id, tf2::TimePointZero);
    geometry_msgs::msg::PoseStamped transformed_pose;
    tf2::doTransform(goal, transformed_pose, transform);

    unsigned int mx, my;
    if (!costmap->worldToMap(transformed_pose.pose.position.x, transformed_pose.pose.position.y, mx, my)) {
      RCLCPP_ERROR(node_->get_logger(), "Goal is out of costmap bounds");
      return true;
    }

    if (costmap->getCost(mx, my) >= nav2_costmap_2d::INSCRIBED_INFLATED_OBSTACLE) {
      RCLCPP_ERROR(node_->get_logger(), "Goal cost Greater than or equal to %d", nav2_costmap_2d::INSCRIBED_INFLATED_OBSTACLE);
      return true;
    }
  } catch (tf2::TransformException &ex) {
    RCLCPP_WARN(node_->get_logger(), "TF lookup failed: %s", ex.what());
    return true;
  }

  return false;
}

}  // namespace nav2_behavior_tree

#include "behaviortree_cpp_v3/bt_factory.h"
BT_REGISTER_NODES(factory)
{
  factory.registerNodeType<nav2_behavior_tree::GoalUnreachableCondition>("GoalUnreachable");
}
