// Copyright (c) 2018 Intel Corporation
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "nav2_behavior_tree/plugins/action/spin_to_goal_action.hpp"

#include <memory>
#include <string>

#include "nav2_util/geometry_utils.hpp"
#include "nav2_util/robot_utils.hpp"
#include "tf2/utils.h"
#include "angles/angles.h"

namespace nav2_behavior_tree
{

SpinToGoalAction::SpinToGoalAction(
  const std::string & xml_tag_name,
  const std::string & action_name,
  const BT::NodeConfiguration & conf)
: SpinAction(xml_tag_name, action_name, conf)
{
  // Get input parameters
  getInput("robot_frame", robot_frame_);
  getInput("global_frame", global_frame_);
  getInput("transform_tolerance", transform_tolerance_);
  getInput("alignment_tolerance", alignment_tolerance_);
  getInput("server_name", server_name_);
  tf_buffer_ = config().blackboard->get<std::shared_ptr<tf2_ros::Buffer>>("tf_buffer");
}

void SpinToGoalAction::on_tick()
{
  // Get goal pose from input port
  if (!getInput("goal_pose", goal_pose_)) {
    RCLCPP_ERROR(
      node_->get_logger(),
      "SpinToGoalAction: Could not get goal_pose from input port");
    return;
  } 

  // Get current robot pose
  geometry_msgs::msg::PoseStamped current_pose;
  if (!getCurrentPose(current_pose)) {
    RCLCPP_ERROR(
      node_->get_logger(),
      "SpinToGoalAction: Could not get current robot pose");
    return;
  }

  // Extract yaw angles
  double current_yaw = tf2::getYaw(current_pose.pose.orientation);
  double goal_yaw = tf2::getYaw(goal_pose_.pose.orientation);
  goal_.use_goal_pose = true;
  goal_.goal_pose = goal_pose_;

  // Calculate the shortest angular distance to goal yaw
  double yaw_difference = calculateYawDifference(current_yaw, goal_yaw);

  // Check if already aligned
  if (std::abs(yaw_difference) <= alignment_tolerance_) {
    RCLCPP_INFO(
      node_->get_logger(),
      "SpinToGoalAction: Already aligned with goal (difference: %.3f rad)", 
      yaw_difference);
    // Set a very small spin to avoid action failure
    goal_.target_yaw = 0.01;
  } else {
    // Set the target yaw to the calculated difference
    goal_.target_yaw = yaw_difference;
    RCLCPP_INFO(
      node_->get_logger(),
      "SpinToGoalAction: Current yaw: %.3f, Goal yaw: %.3f, Target spin: %.3f", 
      current_yaw, goal_yaw, yaw_difference);
  }

  // Call parent's on_tick to handle recovery and time allowance
  SpinAction::on_tick();
}

void SpinToGoalAction::on_wait_for_result(
  std::shared_ptr<const nav2_msgs::action::Spin::Feedback>/*feedback*/) 
{
  // Get latest goal from blackboard
  geometry_msgs::msg::PoseStamped latest_goal;
  if (config().blackboard->get<geometry_msgs::msg::PoseStamped>("goal", latest_goal)) {
    // Check if goal has changed significantly
    if (hasGoalChanged(latest_goal)) {
      RCLCPP_WARN( node_->get_logger(), "Goal changed, cancelling current SpinToGoalAction"); 
      // Cancel current action and return failure

      setStatus(BT::NodeStatus::FAILURE);
      throw std::runtime_error("Goal was rejected by the action server"); 
      return;
    }
  }
}

bool SpinToGoalAction::getCurrentPose(geometry_msgs::msg::PoseStamped & current_pose)
{
  return nav2_util::getCurrentPose(
    current_pose, *tf_buffer_, global_frame_, robot_frame_, transform_tolerance_);
}

double SpinToGoalAction::calculateYawDifference(double current_yaw, double goal_yaw)
{
  return angles::shortest_angular_distance(current_yaw, goal_yaw);
}

bool SpinToGoalAction::hasGoalChanged(
  const geometry_msgs::msg::PoseStamped& current_goal)
{
  const double position_tolerance = 0.1;  // 10cm
  const double orientation_tolerance = 0.1;  // ~5.7 degrees
  
  double dx = current_goal.pose.position.x - goal_pose_.pose.position.x;
  double dy = current_goal.pose.position.y - goal_pose_.pose.position.y;
  double distance = std::sqrt(dx * dx + dy * dy);
  
  double current_yaw = tf2::getYaw(current_goal.pose.orientation);
  double goal_yaw = tf2::getYaw(goal_pose_.pose.orientation);
  double yaw_diff = std::abs(angles::shortest_angular_distance(current_yaw, goal_yaw));
  
  return (distance > position_tolerance) || (yaw_diff > orientation_tolerance);
}

}  // namespace nav2_behavior_tree

#include "behaviortree_cpp_v3/bt_factory.h"
BT_REGISTER_NODES(factory)
{
  BT::NodeBuilder builder =
    [](const std::string & name, const BT::NodeConfiguration & config)
    {
      return std::make_unique<nav2_behavior_tree::SpinToGoalAction>(name, "spin", config);
    };

  factory.registerBuilder<nav2_behavior_tree::SpinToGoalAction>("SpinToGoal", builder);
} 
