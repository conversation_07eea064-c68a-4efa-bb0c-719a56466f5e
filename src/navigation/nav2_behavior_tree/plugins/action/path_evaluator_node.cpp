#include <string>
#include <memory>
#include <limits>
#include <cmath>

#include "nav2_behavior_tree/plugins/action/path_evaluator_node.hpp"
#include "nav2_util/geometry_utils.hpp"
#include "tf2/time.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"
#include "nav2_costmap_2d/costmap_2d_ros.hpp"
#include "angles/angles.h"

namespace {
    const int kUpdateTimeoutMs = 1000;
    const double kMinDistThreshold = 0.3;
    const double kAngleDiffThreshold = 2 * M_PI / 3;
    const int kMinPathSize = 5;
    const int kMaxReducePathSize = 7;
    const double kMinDistDiffThreshold = 0.5;
    const double kStepSize = 0.05;
    const int kObstacleNumThreshold = 10;
    const double kPathObstacleDistThreshold = 3.0;
}

namespace nav2_behavior_tree
{

PathEvaluator::PathEvaluator(const std::string & name, const BT::NodeConfiguration & conf)
: BT::SyncActionNode(name, conf)
{
  node_ = config().blackboard->get<rclcpp::Node::SharedPtr>("node");
  costmap_sub_ = std::make_unique<nav2_costmap_2d::CostmapSubscriber>(node_,"/global_costmap/costmap_raw"); 
  tf_buffer_ = std::make_shared<tf2_ros::Buffer>(node_->get_clock());
  tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
}

BT::NodeStatus PathEvaluator::tick()
{
  rclcpp::spin_some(node_);

  nav_msgs::msg::Path new_path;
  nav_msgs::msg::Path current_path;
  double length_factor = 1.2;
  getInput("length_factor", length_factor);

  if (!getInput("obstacle_num_threshold", obstacleNumThreshold_)) {
    obstacleNumThreshold_ = kObstacleNumThreshold;
  }
  if (!getInput("path_obstacle_dist_threshold", pathObstacleDistThreshold_)) {
    pathObstacleDistThreshold_ = kPathObstacleDistThreshold;
  }

  if (!getInput("new_path", new_path)) {
    throw BT::RuntimeError("PathEvaluator: missing required input [new_path]");
  }

  if (!getInput("current_path", current_path)) {
    setOutput("selected_path", new_path);
    RCLCPP_INFO(node_->get_logger(), "current_path is empty, new_path will be selected");
    update_time_ = std::chrono::steady_clock::now();
    return BT::NodeStatus::SUCCESS;
  }

  if (isGoalChange_(new_path, current_path)) {
    setOutput("selected_path", new_path);
    RCLCPP_INFO(node_->get_logger(), "goal changed, new_path will be selected");
    update_time_ = std::chrono::steady_clock::now();
    return BT::NodeStatus::SUCCESS;
  }

  if (isPathObstructed_(current_path)) {
    setOutput("selected_path", new_path);
    RCLCPP_INFO(node_->get_logger(), "current_path is obstructed, new_path will be selected");
    update_time_ = std::chrono::steady_clock::now();
    return BT::NodeStatus::SUCCESS;
  }

  if (isCurrentPathTimeout_()) {
    setOutput("selected_path", new_path);
    RCLCPP_INFO(node_->get_logger(), "current_path is timeout, new_path will be selected");
    update_time_ = std::chrono::steady_clock::now();
    return BT::NodeStatus::SUCCESS;
  }

  geometry_msgs::msg::PoseStamped current_pose;
  if (getRobotPose_(current_pose) && current_path.poses.size() >= kMinPathSize) {
    const auto &first_pose = current_path.poses.front();
    const auto &min_size_pose = current_path.poses[kMinPathSize - 1];
    double director_yaw = std::atan2(
      min_size_pose.pose.position.y - first_pose.pose.position.y,
      min_size_pose.pose.position.x - first_pose.pose.position.x);
    double angle_diff = angles::shortest_angular_distance(
        tf2::getYaw(current_pose.pose.orientation), director_yaw);
    if (std::fabs(angle_diff) > kAngleDiffThreshold) {
      RCLCPP_INFO(node_->get_logger(), "current path direction is too different from robot direction, new_path will be selected");
      setOutput("selected_path", new_path);
      update_time_ = std::chrono::steady_clock::now();
      return BT::NodeStatus::SUCCESS;
    }

    if (!reduceCurrentPath_(current_pose, current_path)) {
      setOutput("selected_path", new_path);
      RCLCPP_INFO(node_->get_logger(), "current_path reduce failed, new_path will be selected");
      update_time_ = std::chrono::steady_clock::now();
      return BT::NodeStatus::SUCCESS;
    }
  }

  double new_len = nav2_util::geometry_utils::calculate_path_length(new_path, 0);
  double old_len = nav2_util::geometry_utils::calculate_path_length(current_path, 0);  

  RCLCPP_DEBUG(node_->get_logger(), "new_size: %ld, new_len: %f, old_size: %ld, old_len: %f",
    new_path.poses.size(), new_len, current_path.poses.size(), old_len);

  if (new_len < old_len / length_factor) {
    setOutput("selected_path", new_path);
    RCLCPP_INFO(node_->get_logger(), "new_path is shorter than current_path, new_path will be selected");
  } else {
      setOutput("selected_path", current_path);
    RCLCPP_DEBUG(node_->get_logger(), "current_path is shorter than new_path, current_path will be selected");
  }
  update_time_ = std::chrono::steady_clock::now();
  return BT::NodeStatus::SUCCESS;
}

bool PathEvaluator::isGoalChange_(
  const nav_msgs::msg::Path & new_path,
  const nav_msgs::msg::Path & current_path)
{
  if (new_path.poses.size() == 0 || current_path.poses.size() == 0) {
    return false;
  }
  const auto & old_goal = current_path.poses.back().pose.position;
  const auto & new_goal = new_path.poses.back().pose.position;
  return (std::abs(old_goal.x - new_goal.x) > 1e-3 || std::abs(old_goal.y - new_goal.y) > 1e-3);
}

bool PathEvaluator::isPathObstructed_(const nav_msgs::msg::Path & current_path)
{
  try { 
    auto costmap = costmap_sub_->getCostmap();
    if (!costmap) { 
      RCLCPP_WARN(node_->get_logger(), "No costmap available");
      return false;
    }
      
    auto costmap_frame = costmap_sub_->getFrameID();
    if (costmap_frame.empty()) {
      RCLCPP_WARN(node_->get_logger(), "costmap frame id is empty");
      return false;
    }
    auto transform = tf_buffer_->lookupTransform(costmap_frame, current_path.header.frame_id, tf2::TimePointZero);
    auto isPointObstructed = [&](const geometry_msgs::msg::PoseStamped& pose) {
      unsigned int mx, my;
      if (!costmap->worldToMap(pose.pose.position.x, pose.pose.position.y, mx, my)) {
        return false;
      }

      if (costmap->getCost(mx, my) < nav2_costmap_2d::INSCRIBED_INFLATED_OBSTACLE) {
        return false;
      }
        
      RCLCPP_DEBUG(rclcpp::get_logger("PathEvaluator"), 
          "Obstacle at (%.2f, %.2f)", 
          pose.pose.position.x, 
          pose.pose.position.y);
      return true;
    };

    int obstructed_count = 0;
    for (size_t i = 0; i < current_path.poses.size(); ++i) {
      if (i > 0) {
        const auto & first_pose = current_path.poses[0].pose.position;
        const auto & curr_pose = current_path.poses[i].pose.position;
        double distance = std::hypot(curr_pose.x - first_pose.x, curr_pose.y - first_pose.y);
        if (distance > pathObstacleDistThreshold_) {
          break;
        }
      }
    
      geometry_msgs::msg::PoseStamped transformed_pose;
      tf2::doTransform(current_path.poses[i], transformed_pose, transform);
        
      unsigned int mx, my;
      if (!costmap->worldToMap(transformed_pose.pose.position.x, transformed_pose.pose.position.y, mx, my)) {
        RCLCPP_DEBUG(rclcpp::get_logger("PathEvaluator"), 
            "Point (%.2f, %.2f) out of costmap bounds",
            transformed_pose.pose.position.x, 
            transformed_pose.pose.position.y);
        break;
      }

      if (isPointObstructed(transformed_pose)) {
        ++obstructed_count;
      }

      if (i < current_path.poses.size() - 1) {
        geometry_msgs::msg::PoseStamped next_pose;
        tf2::doTransform(current_path.poses[i+1], next_pose, transform);
          
        const double dx = next_pose.pose.position.x - transformed_pose.pose.position.x;
        const double dy = next_pose.pose.position.y - transformed_pose.pose.position.y;
        const double distance = std::hypot(dx, dy);
        const double steps = distance / kStepSize;
        for (double step = 1; step < steps; ++step) {
          const double ratio = step / steps;
          geometry_msgs::msg::PoseStamped interpolated_pose = current_path.poses[i];
          interpolated_pose.pose.position.x += dx * ratio;
          interpolated_pose.pose.position.y += dy * ratio;
          
          geometry_msgs::msg::PoseStamped transform_interpolated_pose;
          tf2::doTransform(interpolated_pose, transform_interpolated_pose, transform);
          if (isPointObstructed(transform_interpolated_pose)) {
            ++obstructed_count;
          }
        }
      }
    }
    if (obstructed_count > obstacleNumThreshold_) {
      RCLCPP_WARN(node_->get_logger(), "Path is obstructed, obstructed count: %d", obstructed_count);
      return true;
    }
  } catch (tf2::TransformException & ex) {
    RCLCPP_WARN(node_->get_logger(), "Could not transform path: %s", ex.what()); 
  } catch (std::exception & ex) {
    RCLCPP_WARN(node_->get_logger(), "exception: %s", ex.what()); 
  }

  return false;
}

bool PathEvaluator::isCurrentPathTimeout_()
{
  auto now = std::chrono::steady_clock::now();
  if (now - update_time_ > std::chrono::milliseconds(kUpdateTimeoutMs)) {
    return true;
  }
  return false;
}

bool PathEvaluator::reduceCurrentPath_(const geometry_msgs::msg::PoseStamped & current_pose, nav_msgs::msg::Path & current_path)
{
  double min_distance = std::numeric_limits<double>::max();
  size_t min_index = 0;
  bool found_valid_point = false;
  
  for (size_t i = 0; i < current_path.poses.size(); ++i) {
    const auto & pose = current_path.poses[i].pose;
    double distance = std::hypot(pose.position.x - current_pose.pose.position.x, 
                               pose.position.y - current_pose.pose.position.y);
    
    double director_yaw = std::atan2(pose.position.y - current_pose.pose.position.y,
                                   pose.position.x - current_pose.pose.position.x);
    double angle_diff = angles::shortest_angular_distance(
        tf2::getYaw(current_pose.pose.orientation), director_yaw);

    RCLCPP_DEBUG(node_->get_logger(), "current_pose: [%f, %f, %f], pose:[%f, %f], distance: %f, director_yaw: %f, angle_diff: %f",
      current_pose.pose.position.x, current_pose.pose.position.y, tf2::getYaw(current_pose.pose.orientation),
      pose.position.x, pose.position.y, distance, director_yaw, angle_diff);
    
    if (distance < min_distance && std::fabs(angle_diff) < kAngleDiffThreshold) {
      min_distance = distance;
      min_index = i;
      found_valid_point = true;
    }
    
    if (found_valid_point && distance > min_distance + kMinDistThreshold) {
      break;
    }
  }
  
  if (!found_valid_point) {
    RCLCPP_WARN(node_->get_logger(), "No valid point found for path reduction");
    return false;
  }

  if (min_distance > kMinDistDiffThreshold && min_index > kMaxReducePathSize) {
    RCLCPP_WARN(node_->get_logger(), "Reduce path to bigger or min_distance to large, min_distance: %f, points: %zu, current_pose:[%f, %f], first_path_pose:[%f, %f]",
      min_distance, min_index, current_pose.pose.position.x, current_pose.pose.position.y,
      current_path.poses.front().pose.position.x, current_path.poses.front().pose.position.y);
    return false;
  }
  
  current_path.poses.erase(current_path.poses.begin(), current_path.poses.begin() + min_index + 1);
  RCLCPP_DEBUG(node_->get_logger(), "Path reduced by %zu points, min_distance: %f, current_pose:[%f, %f], first_path_pose:[%f, %f]",
    min_index, min_distance, current_pose.pose.position.x, current_pose.pose.position.y,
    current_path.poses.front().pose.position.x, current_path.poses.front().pose.position.y);
  current_path.poses.insert(current_path.poses.begin(), current_pose);
  
  return true;
}

bool PathEvaluator::getRobotPose_(geometry_msgs::msg::PoseStamped & pose)
{
  try {
    auto transform = tf_buffer_->lookupTransform("map", "base_link", tf2::TimePointZero);
    pose.header = transform.header;
    pose.pose.position.x = transform.transform.translation.x;
    pose.pose.position.y = transform.transform.translation.y;
    pose.pose.position.z = transform.transform.translation.z;
    pose.pose.orientation = transform.transform.rotation;
    RCLCPP_DEBUG(node_->get_logger(), "Robot pose: (%.2f, %.2f, %.2f)", 
      pose.pose.position.x, pose.pose.position.y, pose.pose.position.z);
    return true;
  } catch (tf2::TransformException &ex) {
    RCLCPP_WARN(node_->get_logger(), "TF lookup failed: %s", ex.what());
    return false;
  }
}

}

#include "behaviortree_cpp_v3/bt_factory.h"
BT_REGISTER_NODES(factory)
{
  factory.registerNodeType<nav2_behavior_tree::PathEvaluator>("PathEvaluator");
}
