// Copyright 2024 Polaris Xia
// Copyright 2025 <PERSON><PERSON>
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "pb_nav2_plugins/behaviors/back_up_free_space.hpp"
#include "angles/angles.h"
#include <optional>

namespace pb_nav2_behaviors
{

void BackUpFreeSpace::onConfigure()
{
  auto node = node_.lock();
  if (!node) {
    throw std::runtime_error{"Failed to lock node"};
  }

  nav2_util::declare_parameter_if_not_declared(node, "global_frame", rclcpp::ParameterValue("map"));
  nav2_util::declare_parameter_if_not_declared(node, "max_radius", rclcpp::ParameterValue(1.0));
  nav2_util::declare_parameter_if_not_declared(
    node, "service_name", rclcpp::ParameterValue("local_costmap/get_costmap"));
  nav2_util::declare_parameter_if_not_declared(node, "visualize", rclcpp::ParameterValue(false));
  nav2_util::declare_parameter_if_not_declared(node, "minimum_angular_speed", rclcpp::ParameterValue(0.05));
  nav2_util::declare_parameter_if_not_declared(node, "rotation_check_angle_step", rclcpp::ParameterValue(M_PI / 36.0));
  nav2_util::declare_parameter_if_not_declared(node, "rotatable_judgment_success", rclcpp::ParameterValue(true));

  node->get_parameter("global_frame", global_frame_);
  node->get_parameter("max_radius", max_radius_);
  node->get_parameter("service_name", service_name_);
  node->get_parameter("visualize", visualize_);
  node->get_parameter("minimum_angular_speed", minimum_angular_speed_);
  node->get_parameter("rotation_check_angle_step", rotation_check_angle_step_);
  node->get_parameter("rotatable_judgment_success", rotatable_judgment_success_);

  costmap_client_ = node->create_client<nav2_msgs::srv::GetCostmap>(service_name_);

  nav2_behaviors::DriveOnHeading<nav2_msgs::action::BackUp>::onConfigure();

  if (visualize_) {
    marker_pub_ = node->template create_publisher<visualization_msgs::msg::MarkerArray>(
      "back_up_free_space_markers", 1);
    marker_pub_->on_activate();
  }
}

void BackUpFreeSpace::onCleanup()
{
  costmap_client_.reset();
  marker_pub_.reset();
}

nav2_behaviors::Status BackUpFreeSpace::onRun(
  const std::shared_ptr<const BackUpAction::Goal> command)
{
  while (!costmap_client_->wait_for_service(std::chrono::seconds(1))) {
    if (!rclcpp::ok()) {
      RCLCPP_ERROR(logger_, "Interrupted while waiting for the service. Exiting.");
      return nav2_behaviors::Status::FAILED;
    }
    RCLCPP_WARN(logger_, "service not available, waiting again...");
  }

  auto request = std::make_shared<nav2_msgs::srv::GetCostmap::Request>();
  auto result = costmap_client_->async_send_request(request);
  if (result.wait_for(std::chrono::seconds(1)) == std::future_status::timeout) {
    RCLCPP_ERROR(logger_, "Interrupted while waiting for the service. Exiting.");
    return nav2_behaviors::Status::FAILED;
  }

  // get costmap
  auto costmap = result.get()->map;

  if (!nav2_util::getCurrentPose(
        initial_pose_, *tf_, global_frame_, robot_base_frame_, transform_tolerance_)) {
    RCLCPP_ERROR(logger_, "Initial robot pose is not available.");
    return nav2_behaviors::Status::FAILED;
  }

  // get current pose
  geometry_msgs::msg::Pose2D pose;
  pose.x = initial_pose_.pose.position.x;
  pose.y = initial_pose_.pose.position.y;
  pose.theta = tf2::getYaw(initial_pose_.pose.orientation);

  angle_maps_.clear();
  std::vector<double> forward_angle_list, backward_angle_list;
  findAngleList(costmap, pose, -M_PI, -2.0 * M_PI / 3.0, max_radius_, M_PI / 36.0, backward_angle_list);
  findAngleList(costmap, pose, 2.0 * M_PI / 3.0, M_PI, max_radius_, M_PI / 36.0, backward_angle_list);
  if (!backward_angle_list.empty()) {
    angle_maps_.emplace(AngleDirect::BACKWARD, backward_angle_list);
    RCLCPP_INFO(logger_, "backward_angle_list size(): %ld", backward_angle_list.size());
  }
  findAngleList(costmap, pose, 0, M_PI / 3.0, max_radius_, M_PI / 36.0, forward_angle_list);
  findAngleList(costmap, pose, -M_PI / 3.0, 0, max_radius_, M_PI / 36.0, forward_angle_list);
  if (!forward_angle_list.empty()) {
    angle_maps_.emplace(AngleDirect::FORWARD, forward_angle_list);
    RCLCPP_INFO(logger_, "forward_angle_list size(): %ld", forward_angle_list.size());
  }

  if (forward_angle_list.empty() && backward_angle_list.empty()) {
    RCLCPP_ERROR(logger_, "No free space found to back up.");
    return nav2_behaviors::Status::FAILED;
  }

  // // Find the best direction to back up
  // auto best_angle = findBestDirection(costmap, pose, start_angle, end_angle, max_radius_, M_PI / 32.0);
  // if (!best_angle.has_value()) {
  //   RCLCPP_ERROR(logger_, "No free space found to back up.");
  //   return nav2_behaviors::Status::FAILED;
  // }
  // RCLCPP_INFO(logger_, "best_angle: %f", *best_angle);

  // target_pose_.x = pose.x + command->target.x * std::cos(*best_angle);
  // target_pose_.y = pose.y + command->target.x * std::sin(*best_angle);
  // double target_angle = std::atan2(target_pose_.y - pose.y, target_pose_.x - pose.x);

  // double angle_diff = target_angle - pose.theta;
  // RCLCPP_DEBUG(logger_, "target_angle:%f, pose.theta:%f, angle_diff: %f", target_angle, pose.theta, angle_diff);
  // angle_diff = std::atan2(std::sin(angle_diff), std::cos(angle_diff));
  // RCLCPP_DEBUG(logger_, "target_pose_.theta: %f", angle_diff);

  max_speed_ = command->speed;
  RCLCPP_DEBUG(logger_, "max_speed_: %f", max_speed_);

  command_x_ = command->target.x;
  command_time_allowance_ = command->time_allowance;
  end_time_ = clock_->now() + command_time_allowance_;

  // if (fabsf(angle_diff) <= M_PI/2) {
  //   twist_x_ = command->speed;  
  //   target_pose_.theta = target_angle;
  //   RCLCPP_DEBUG(logger_, "forward");
  // } else {
  //   twist_x_ = -command->speed;  
  //   target_pose_.theta = target_angle + M_PI;
  //   target_pose_.theta = std::atan2(std::sin(target_pose_.theta), std::cos(target_pose_.theta));
  //   RCLCPP_DEBUG(logger_, "backward, target_pose_.theta: %f", target_pose_.theta);
  // }

  // if (!nav2_util::getCurrentPose(
  //       initial_pose_, *tf_, global_frame_, robot_base_frame_, transform_tolerance_)) {
  //   RCLCPP_ERROR(logger_, "Initial robot pose is not available.");
  //   return nav2_behaviors::Status::FAILED;
  // }

  return nav2_behaviors::Status::SUCCEEDED;
}

nav2_behaviors::Status BackUpFreeSpace::onCycleUpdate()
{
  try {
    rclcpp::Duration time_remaining = end_time_ - clock_->now();
    if (time_remaining.seconds() < 0.0 && command_time_allowance_.seconds() > 0.0) {
      stopRobot();
      RCLCPP_WARN(
        logger_,
        "Exceeded time allowance before reaching the "
        "DriveOnHeading goal - Exiting DriveOnHeading");
      best_angle_.reset();
      return nav2_behaviors::Status::FAILED;
    }

    // Check whether the robot can rotate at the current position
    if (rotatable_judgment_success_ && canRotateInPlace()) {
      RCLCPP_INFO(logger_, "Robot can rotate in place, considering as successful");
      stopRobot();
      best_angle_.reset();
      return nav2_behaviors::Status::SUCCEEDED;
    }

    if (angle_maps_.empty() && !best_angle_) {
      stopRobot();
      RCLCPP_WARN(logger_, "angle list tried all, failed");
      return nav2_behaviors::Status::FAILED;
    }

    if (!best_angle_) {
      auto it = angle_maps_.begin();
      AngleDirect angle_direct = AngleDirect::BACKWARD;
      while (it != angle_maps_.end()) {
        if (!it->second.empty()) {
          angle_direct = it->first;
          best_angle_ = it->second.front();
          it->second.erase(it->second.begin());

          if (it->second.empty()) {
            angle_maps_.erase(it);
          }
          break;
        } else {
          it = angle_maps_.erase(it);
        }
      }

      if (!best_angle_) {
        RCLCPP_WARN(logger_, "No free space found to back up.");
        return nav2_behaviors::Status::FAILED;
      }
    
      RCLCPP_INFO(logger_, "chose best angle: %f", best_angle_.value() * 180.0 / M_PI);
      target_pose_.x = initial_pose_.pose.position.x + command_x_ * std::cos(angles::normalize_angle(*best_angle_ + tf2::getYaw(initial_pose_.pose.orientation)));
      target_pose_.y = initial_pose_.pose.position.y + command_x_ * std::sin(angles::normalize_angle(*best_angle_ + tf2::getYaw(initial_pose_.pose.orientation)));
      double target_angle = std::atan2(target_pose_.y - initial_pose_.pose.position.y, target_pose_.x - initial_pose_.pose.position.x);
    
      if (angle_direct == AngleDirect::FORWARD) {
        twist_x_ = max_speed_;
        target_pose_.theta = target_angle;
      } else {
        twist_x_ = -max_speed_; 
        target_pose_.theta = angles::normalize_angle(target_angle + M_PI);
      }
      RCLCPP_INFO(logger_, "twist_x_: %f, target_pose_.theta: %f", twist_x_, target_pose_.theta);
    }

    geometry_msgs::msg::PoseStamped current_pose;
    if (!nav2_util::getCurrentPose(
          current_pose, *tf_, global_frame_, robot_base_frame_, transform_tolerance_)) {
      RCLCPP_ERROR(logger_, "Current robot pose is not available.");
      best_angle_.reset();
      return nav2_behaviors::Status::FAILED;
    }

    float diff_x = initial_pose_.pose.position.x - current_pose.pose.position.x;
    float diff_y = initial_pose_.pose.position.y - current_pose.pose.position.y;
    float distance = hypot(diff_x, diff_y);

    feedback_->distance_traveled = distance;
    action_server_->publish_feedback(feedback_);

    double yaw_error = angles::normalize_angle(target_pose_.theta - tf2::getYaw(current_pose.pose.orientation));

    if (std::fabs(distance - std::fabs(command_x_)) <= distance_tolerance_ && std::fabs(yaw_error) <= yaw_tolerance_) {
      RCLCPP_INFO(logger_, "succeed, distance: %f, yaw_error: %f", distance, yaw_error);
      stopRobot();
      best_angle_.reset();
      return nav2_behaviors::Status::SUCCEEDED;
    }

    auto remaining_distance = std::fabs(command_x_) - distance;
  
    auto cmd_vel = std::make_unique<geometry_msgs::msg::Twist>();
  
    const double k_ang = 1.5; 
    const double k_lin = 1.0;  
  
    double v = k_lin * remaining_distance;
    double w = k_ang * yaw_error;
  
    if (twist_x_ < 0) {
      v = -v;  
    }
  
    v = std::clamp(v, -max_speed_, max_speed_);

    if (acceleration_limit_ != 0.0 && deceleration_limit_ != 0.0) 
    {
      double max_vel_to_stop = std::sqrt(-2.0 * deceleration_limit_ * remaining_distance);
      if (max_vel_to_stop < std::abs(v)) {
        v = (v >= 0) ? max_vel_to_stop : -max_vel_to_stop;
      }
    }

    if (std::fabs(v) < minimum_speed_) {
      v = (v >= 0) ? minimum_speed_ : -minimum_speed_;
    }
    if (std::abs(w) < minimum_angular_speed_) {
      w = (w >= 0) ? minimum_angular_speed_ : -minimum_angular_speed_;
    }

    if(std::fabs(yaw_error) <= yaw_tolerance_) {
      w = 0.0;
    }

    if(std::fabs(remaining_distance) <= distance_tolerance_) {
      v = 0.0;
    }
  
    cmd_vel->linear.x = v;
    cmd_vel->linear.y = 0.0; 
    cmd_vel->angular.z = w;

    RCLCPP_INFO(logger_, "cmd_vel: %f, %f, remaining_distance: %f, yaw_error: %f", 
      cmd_vel->linear.x, cmd_vel->angular.z, remaining_distance, yaw_error);

    geometry_msgs::msg::Pose2D pose;
    pose.x = current_pose.pose.position.x;
    pose.y = current_pose.pose.position.y;
    pose.theta = tf2::getYaw(current_pose.pose.orientation);

    if (!isCollisionFree(distance, cmd_vel.get(), pose)) {
      stopRobot();
      RCLCPP_WARN(logger_, "Collision Ahead - Exiting DriveOnHeading");
      best_angle_.reset();
      if (distance <= 0.05) {
        return nav2_behaviors::Status::RUNNING;
      } else if (distance >= std::fabs(command_x_) / 2.0) {
        return nav2_behaviors::Status::SUCCEEDED;
      } else {
        return nav2_behaviors::Status::FAILED;
      }
    }

    vel_pub_->publish(std::move(cmd_vel));
    return nav2_behaviors::Status::RUNNING;
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "111111111111111111111111111 Unexpected exception in onCycleUpdate: %s", e.what());
    stopRobot();
    best_angle_.reset();
    return nav2_behaviors::Status::FAILED;
  }
}

std::optional<float> BackUpFreeSpace::findBestDirection(
  const nav2_msgs::msg::Costmap & costmap, geometry_msgs::msg::Pose2D pose, float start_angle,
  float end_angle, float radius, float angle_increment)
{
  float best_angle = start_angle;

  float first_safe_angle = -1.0f;
  float last_unsafe_angle = -1.0f;

  float final_safe_angle = 0.0f;
  float final_unsafe_angle = 0.0f;

  float resolution = costmap.metadata.resolution;
  float origin_x = costmap.metadata.origin.position.x;
  float origin_y = costmap.metadata.origin.position.y;
  int size_x = costmap.metadata.size_x;
  int size_y = costmap.metadata.size_y;

  float map_min_x = origin_x;
  float map_max_x = origin_x + (size_x * resolution);
  float map_min_y = origin_y;
  float map_max_y = origin_y + (size_y * resolution);

  std::optional<float> first_unsafe_angle = std::nullopt;
  std::optional<float> last_safe_angle = std::nullopt;

  for (float angle = start_angle; angle <= end_angle; angle += angle_increment) {
    bool is_safe = true;
    for (float r = 0; r <= radius; r += resolution) {
      float x = pose.x + r * std::cos(angle);
      float y = pose.y + r * std::sin(angle);

      if (x >= map_min_x && x <= map_max_x && y >= map_min_y && y <= map_max_y) {
        int i = static_cast<int>((x - origin_x) / resolution);
        int j = static_cast<int>((y - origin_y) / resolution);

        if (i >= 0 && i < size_x && j >= 0 && j < size_y) {
          if (costmap.data[i + j * size_x] >= 253) {
            is_safe = false;
            break;
          }
        } else {
          is_safe = false;
          break;
        }
      } else {
        is_safe = false;
        break;
      }
    }
    if (is_safe && first_safe_angle == -1.0f) {
      first_safe_angle = angle;
    }

    if (!is_safe && first_safe_angle != -1.0f && last_unsafe_angle == -1.0f) {
      last_unsafe_angle = angle;
    }

    if (first_safe_angle != -1.0f && last_unsafe_angle != -1.0f) {
      if (final_safe_angle == 0.0f || final_unsafe_angle == 0.0f) {
        final_safe_angle = first_safe_angle;
        final_unsafe_angle = last_unsafe_angle;
        if (first_safe_angle == start_angle) {
          first_unsafe_angle = last_unsafe_angle;
        }
      } else {
        if (last_unsafe_angle - first_safe_angle > final_unsafe_angle - final_safe_angle) {
          final_safe_angle = first_safe_angle;
          final_unsafe_angle = last_unsafe_angle;
        }
      }
      first_safe_angle = -1.0f;
      last_unsafe_angle = -1.0f;
    }
  }

  if (first_safe_angle != -1.0f && last_unsafe_angle == -1.0f) {
    last_safe_angle = first_safe_angle;
  }

  if (std::fabs(final_safe_angle - final_unsafe_angle) < 1e-6 &&
      !last_safe_angle.has_value()) {
    RCLCPP_WARN(logger_, "No free space found");
    return std::nullopt;
  }

  if (last_safe_angle.has_value() && *last_safe_angle == start_angle && !first_unsafe_angle.has_value()) {
    best_angle = pose.theta + M_PI;
  } else if(last_safe_angle.has_value()) {
    float last_range = end_angle - *last_safe_angle;
    float middle_angle = (*last_safe_angle + end_angle) / 2.0f;
    if (first_unsafe_angle.has_value() && angles::normalize_angle(start_angle - end_angle) < 1e-6) {
      last_range += *first_unsafe_angle - start_angle;
      middle_angle = angles::normalize_angle((*last_safe_angle + 2*M_PI + *first_unsafe_angle) / 2.0f);
    }

    if (std::fabs(final_safe_angle - final_unsafe_angle) < 1e-6) {
      best_angle = middle_angle;
    } else {
      if (last_range > std::fabs(final_safe_angle - final_unsafe_angle)) {
        best_angle = middle_angle;
      } else {
        best_angle = (final_safe_angle + final_unsafe_angle) / 2.0f;
      }
    }
  } else {
    best_angle = (final_safe_angle + final_unsafe_angle) / 2.0f;
  }

  if (visualize_) {
    visualize(pose, radius, final_safe_angle, final_unsafe_angle);
  }
  
  return angles::normalize_angle(best_angle);
}

void BackUpFreeSpace::findAngleList(
  const nav2_msgs::msg::Costmap & costmap, geometry_msgs::msg::Pose2D pose, float start_angle,
  float end_angle, float radius, float angle_increment, std::vector<double> & angle_list)
{
  float resolution = costmap.metadata.resolution;
  float origin_x = costmap.metadata.origin.position.x;
  float origin_y = costmap.metadata.origin.position.y;
  int size_x = costmap.metadata.size_x;
  int size_y = costmap.metadata.size_y;

  float map_min_x = origin_x;
  float map_max_x = origin_x + (size_x * resolution);
  float map_min_y = origin_y;
  float map_max_y = origin_y + (size_y * resolution);

  for (float angle = start_angle; angle <= end_angle; angle += angle_increment) {
    bool is_safe = true;
    for (float r = 0; r <= radius; r += resolution) {
      float x = pose.x + r * std::cos(angles::normalize_angle(angle + pose.theta));
      float y = pose.y + r * std::sin(angles::normalize_angle(angle + pose.theta));

      if (x >= map_min_x && x <= map_max_x && y >= map_min_y && y <= map_max_y) {
        int i = static_cast<int>((x - origin_x) / resolution);
        int j = static_cast<int>((y - origin_y) / resolution);

        if (i >= 0 && i < size_x && j >= 0 && j < size_y) {
          if (costmap.data[i + j * size_x] >= 253) {
            is_safe = false;
            // RCLCPP_WARN(logger_, "angle: %f, is_safe: %d, x: %f, y: %f, cost: %d", angle / M_PI * 180.0, is_safe, x, y, costmap.data[i + j * size_x]);
            break;
          }
        } else {
          is_safe = false;
          // RCLCPP_WARN(logger_, "angle: %f, is_safe: %d, out of size", angle / M_PI * 180.0, is_safe);
          break;
        }
      } else {
        is_safe = false;
        // RCLCPP_WARN(logger_, "angle: %f, is_safe: %d, out of range", angle / M_PI * 180.0, is_safe);
        break;
      }
    }
    if (is_safe) {
      angle_list.push_back(angles::normalize_angle(angle));
    }
  }
}

std::vector<geometry_msgs::msg::Point> BackUpFreeSpace::gatherFreePoints(
  const nav2_msgs::msg::Costmap & costmap, geometry_msgs::msg::Pose2D pose, float radius)
{
  std::vector<geometry_msgs::msg::Point> results;
  for (unsigned int i = 0; i < costmap.metadata.size_x; i++) {
    for (unsigned int j = 0; j < costmap.metadata.size_y; j++) {
      auto idx = i + j * costmap.metadata.size_x;
      auto x = i * costmap.metadata.resolution + costmap.metadata.origin.position.x;
      auto y = j * costmap.metadata.resolution + costmap.metadata.origin.position.y;
      if (std::hypot(x - pose.x, y - pose.y) <= radius && costmap.data[idx] == 0) {
        geometry_msgs::msg::Point p;
        p.x = x;
        p.y = y;
        results.push_back(p);
      }
    }
  }
  return results;
}

void BackUpFreeSpace::visualize(
  geometry_msgs::msg::Pose2D pose, float radius, float first_safe_angle, float last_unsafe_angle)
{
  visualization_msgs::msg::MarkerArray markers;

  visualization_msgs::msg::Marker sector_marker;
  sector_marker.header.frame_id = global_frame_;
  sector_marker.header.stamp = clock_->now();
  sector_marker.ns = "direction";
  sector_marker.id = 0;
  sector_marker.type = visualization_msgs::msg::Marker::TRIANGLE_LIST;
  sector_marker.action = visualization_msgs::msg::Marker::ADD;
  sector_marker.scale.x = 1.0;
  sector_marker.scale.y = 1.0;
  sector_marker.scale.z = 1.0;
  sector_marker.color.r = 0.0f;
  sector_marker.color.g = 1.0f;
  sector_marker.color.b = 0.0f;
  sector_marker.color.a = 0.2f;

  const float angle_step = 0.05f;
  for (float angle = first_safe_angle; angle <= last_unsafe_angle; angle += angle_step) {
    const float next_angle = std::min(angle + angle_step, last_unsafe_angle);

    geometry_msgs::msg::Point origin;
    origin.x = pose.x;
    origin.y = pose.y;
    origin.z = 0.0;

    geometry_msgs::msg::Point p1;
    p1.x = pose.x + radius * std::cos(angle);
    p1.y = pose.y + radius * std::sin(angle);
    p1.z = 0.0;

    geometry_msgs::msg::Point p2;
    p2.x = pose.x + radius * std::cos(next_angle);
    p2.y = pose.y + radius * std::sin(next_angle);
    p2.z = 0.0;

    sector_marker.points.push_back(origin);
    sector_marker.points.push_back(p1);
    sector_marker.points.push_back(p2);
  }
  markers.markers.push_back(sector_marker);

  auto create_arrow = [&](float angle, int id, float r, float g, float b) {
    visualization_msgs::msg::Marker arrow;
    arrow.header.frame_id = global_frame_;
    arrow.header.stamp = clock_->now();
    arrow.ns = "direction";
    arrow.id = id;
    arrow.type = visualization_msgs::msg::Marker::ARROW;
    arrow.action = visualization_msgs::msg::Marker::ADD;
    arrow.scale.x = 0.05;
    arrow.scale.y = 0.1;
    arrow.scale.z = 0.1;
    arrow.color.r = r;
    arrow.color.g = g;
    arrow.color.b = b;
    arrow.color.a = 1.0;

    geometry_msgs::msg::Point start;
    start.x = pose.x;
    start.y = pose.y;
    start.z = 0.0;

    geometry_msgs::msg::Point end;
    end.x = start.x + radius * std::cos(angle);
    end.y = start.y + radius * std::sin(angle);
    end.z = 0.0;

    arrow.points.push_back(start);
    arrow.points.push_back(end);
    return arrow;
  };

  markers.markers.push_back(create_arrow(first_safe_angle, 1, 0.0f, 0.0f, 1.0f));
  markers.markers.push_back(create_arrow(last_unsafe_angle, 2, 0.0f, 0.0f, 1.0f));

  const float best_angle = (first_safe_angle + last_unsafe_angle) / 2.0f;
  markers.markers.push_back(create_arrow(best_angle, 3, 0.0f, 1.0f, 0.0f));

  marker_pub_->publish(markers);
}

bool BackUpFreeSpace::canRotateInPlace()
{
  try {
    geometry_msgs::msg::PoseStamped current_pose;
    if (!nav2_util::getCurrentPose(
          current_pose, *tf_, global_frame_, robot_base_frame_, transform_tolerance_)) {
      RCLCPP_WARN(logger_, "Failed to get current robot pose for rotation check");
      return false;
    }

    const double full_rotation = 2.0 * M_PI;
    const double angle_step = rotation_check_angle_step_;
    bool fetch_data = true;
    
    for (double angle = 0.0; angle <= full_rotation; angle += angle_step) {
      geometry_msgs::msg::Pose2D rotated_pose;
      rotated_pose.x = current_pose.pose.position.x;
      rotated_pose.y = current_pose.pose.position.y;
      rotated_pose.theta = angles::normalize_angle(tf2::getYaw(current_pose.pose.orientation) + angle);
      
      if (!collision_checker_->isCollisionFree(rotated_pose, fetch_data)) {
        RCLCPP_DEBUG(logger_, 
          "Rotation check failed at angle %.2f degrees (%.2f rad)", 
          angle * 180.0 / M_PI, angle);
        return false;
      }
      fetch_data = false;
    }
    
    RCLCPP_INFO(logger_, "Robot can rotate 360 degrees at current position without collision");
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_WARN(logger_, "Exception in canRotateInPlace: %s", e.what());
    return false;
  }
}

}  // namespace pb_nav2_behaviors

#include "pluginlib/class_list_macros.hpp"
PLUGINLIB_EXPORT_CLASS(pb_nav2_behaviors::BackUpFreeSpace, nav2_core::Behavior)
