
<!--
  This BT has all the functionalities of navigate_to_pose_w_replanning_and_recovery.xml,
  with an additional feature to cancel the control closer to the goal proximity and 
  make the robot wait for a specific time, to see if the obstacle clears out before 
  navigating along a significantly longer path to reach the goal location.
-->
<root main_tree_to_execute="MainTree">
  <BehaviorTree ID="MainTree">
    <Sequence name="UndockAndNavigate">
      <Inverter>
        <GoalUnreachable goal="{goal}"/>
      </Inverter>
      <Inverter>
        <IsBatteryManualCharging battery_topic="/battery_status"/>
      </Inverter>
      <Fallback name="UndockIfCharging">
        <Inverter>
          <IsBatteryAutoCharging battery_topic="/battery_status"/>
        </Inverter>
        <RetryUntilSuccessful num_attempts="3">
          <UndockRobot dock_type="simple_charging_dock" max_undocking_time="30.0"/>
        </RetryUntilSuccessful>
      </Fallback>
      <Fallback name="UndockIfUnderShelf">
        <Inverter>
          <IsUnderShelf service_name="/detect_shelf" service_timeout="1.0"/>
        </Inverter>
        <RetryUntilSuccessful num_attempts="200">
          <SequenceStar name="UndockAndWait">
            <UndockRobot dock_type="shelf_dock" max_undocking_time="30.0"/>
            <Wait wait_duration="1"/>
          </SequenceStar>
        </RetryUntilSuccessful>
      </Fallback>
      <RecoveryNode number_of_retries="20" name="NavigateRecovery">
        <PipelineSequence name="NavigateWithReplanning">
          <RateController hz="2.0">
            <Sequence name="ComputeAndEvaluate">
              <RetryUntilSuccessful num_attempts="5" name="ComputePathToPose">
                <ComputePathToPose goal="{goal}" path="{new_path}" planner_id="GridBased"/>
              </RetryUntilSuccessful>
              <PathEvaluator new_path="{new_path}" current_path="{path}" selected_path="{path}" length_factor="1.2" obstacle_num_threshold="10" path_obstacle_dist_threshold="3.0"/>
            </Sequence>
          </RateController>
          <RateController hz="1.0">
            <PathMonitorNode path="{path}" cost_threshold="100.0" recover_cost_threshold="10.0"/>
          </RateController>
          <ReactiveSequence name="MonitorAndFollowPath">
            <PathLongerOnApproach path="{path}" prox_len="3.0" length_factor="2.0">
              <RetryUntilSuccessful num_attempts="1">
                <SequenceStar name="CancelingControlAndWait">
                  <CancelControl name="ControlCancel"/>
                  <Wait wait_duration="1"/>
                </SequenceStar>
              </RetryUntilSuccessful>
            </PathLongerOnApproach>
            <RecoveryNode number_of_retries="1" name="FollowPath">
              <Sequence name="NavigationWithSpin">
                <FollowPath path="{path}" controller_id="FollowPath"/>
                <SpinToGoal goal_pose="{goal}" server_name="/spin" alignment_tolerance="0.015"/>
              </Sequence>
              <Fallback name="DistanceBasedRecovery1">
                <Sequence name="WaitAndRetry">
                  <GoalUnreachable goal="{goal}"/>
                  <DistanceToGoal goal="{goal}" distance_threshold="1.5"/>
                  <Wait wait_duration="0.5"/>
                  <AlwaysSuccess name="RetryNavigation"/>
                </Sequence>
                <Sequence name="BackUpRecovery">
                  <BackUp backup_dist="0.6" backup_speed="0.2"/>
                </Sequence>
              </Fallback>
            </RecoveryNode>
          </ReactiveSequence>
        </PipelineSequence>
        <ReactiveFallback name="RecoveryFallback">
          <GlobalUpdatedGoal/>
          <Sequence name="RecoveryActions">
            <ClearEntireCostmap name="ClearGlobalCostmap-Subtree" service_name="global_costmap/clear_entirely_global_costmap"/>
            <Fallback name="DistanceBasedRecovery2">
              <Sequence name="WaitAndRetry">
                <DistanceToGoal goal="{goal}" distance_threshold="1.5"/>
                <Wait wait_duration="0.5"/>
                <AlwaysSuccess name="RetryNavigation"/>
              </Sequence>
              <Sequence name="BackUpRecovery">
                <BackUp backup_dist="1.0" backup_speed="0.2"/>
              </Sequence>
            </Fallback>
          </Sequence>
        </ReactiveFallback>
      </RecoveryNode>
    </Sequence>
  </BehaviorTree>
</root>
