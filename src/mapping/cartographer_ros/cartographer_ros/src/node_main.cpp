/*
 * Copyright 2016 The Cartographer Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "absl/memory/memory.h"
#include "cartographer/mapping/map_builder.h"
#include "cartographer_ros/node.h"
#include "cartographer_ros/node_options.h"
#include "cartographer_ros/ros_log_sink.h"
#include "gflags/gflags.h"
#include "tf2_ros/transform_listener.h"
#include "tf2/LinearMath/Quaternion.h"
#include "cartographer_ros_msgs/action/relocalize.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include <core/geometry_rectangle.h>
#include <atomic>
#include <chrono>
#include <condition_variable>
#include <future>
#include <mutex>
#include <thread>

DEFINE_bool(collect_metrics, false,
            "Activates the collection of runtime metrics. If activated, the "
            "metrics can be accessed via a ROS service.");
DEFINE_string(configuration_directory, "",
              "First directory in which configuration files are searched, "
              "second is always the Cartographer installation to allow "
              "including files from there.");
DEFINE_string(configuration_basename, "",
              "Basename, i.e. not containing any directory prefix, of the "
              "configuration file.");
DEFINE_string(load_state_filename, "",
              "If non-empty, filename of a .pbstream file to load, containing "
              "a saved SLAM state.");
DEFINE_bool(load_frozen_state, true,
            "Load the saved state as frozen (non-optimized) trajectories.");
DEFINE_bool(
    start_trajectory_with_default_topics, true,
    "Enable to immediately start the first trajectory with default topics.");
DEFINE_string(
    save_state_filename, "",
    "If non-empty, serialize state and write it to disk before shutting down.");

namespace cartographer_ros {
namespace {

std::shared_ptr<cartographer_ros::Node> node_handle;
std::shared_ptr<cartographer_ros::TrajectoryOptions> trajectory_options_handle;
std::shared_ptr<cartographer_ros::NodeOptions> node_options_handle;

using RelocalizeAction = cartographer_ros_msgs::action::Relocalize;
using GoalHandleRelocalize = rclcpp_action::ServerGoalHandle<RelocalizeAction>;

rclcpp_action::Server<RelocalizeAction>::SharedPtr relocalize_action_server_;

void do_init_pose_callback(const geometry_msgs::msg::PoseWithCovarianceStamped::SharedPtr msg, const std::string &config_file)
{
    LOG(INFO) << "Init pose set, start trajectory with config file: " << config_file;
    node_handle->FinishAllTrajectories();
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    auto start_request = std::make_shared<cartographer_ros_msgs::srv::StartTrajectory::Request>();
    auto start_response = std::make_shared<cartographer_ros_msgs::srv::StartTrajectory::Response>();
    start_request->configuration_directory = FLAGS_configuration_directory;
    start_request->configuration_basename = config_file;
    start_request->use_initial_pose = true;
    start_request->initial_pose = msg->pose.pose; 
    int trajectory_id = 0;
    if(node_handle->FindValidTrajectory(trajectory_id)){
      start_request->relative_to_trajectory_id = trajectory_id;
    }
    else{
      start_request->use_initial_pose = false;
      LOG(ERROR) << "No valid trajectory found, start trajectory without initial pose";
    }
    node_handle->StartTrajectory(start_request, start_response);
}

void init_pose_callback(const geometry_msgs::msg::PoseWithCovarianceStamped::SharedPtr msg)
{
    std::string config_file = node_handle->isLocalizationMode() ? "localization_2d.lua" : "mapping_2d.lua";
    do_init_pose_callback(msg, config_file);
}

rclcpp_action::GoalResponse handle_relocalize_goal(
  const rclcpp_action::GoalUUID & uuid,
  std::shared_ptr<const RelocalizeAction::Goal> goal)
{
  (void)uuid;
  LOG(INFO) << "Relocalization action goal received with bounds: ["
            << goal->bounding_rect.x << ", " << goal->bounding_rect.y << "] x ["
            << goal->bounding_rect.width << ", " << goal->bounding_rect.height << "]";
  return rclcpp_action::GoalResponse::ACCEPT_AND_EXECUTE;
}

rclcpp_action::CancelResponse handle_relocalize_cancel(
  const std::shared_ptr<GoalHandleRelocalize> goal_handle)
{
  LOG(INFO) << "Relocalization action cancel requested";
  // Simplified cancel handling - the cancellation will be detected 
  // in the is_cancelled lambda function in the execution thread
  return rclcpp_action::CancelResponse::ACCEPT;
}

void handle_relocalize_accepted(const std::shared_ptr<GoalHandleRelocalize> goal_handle)
{
  std::thread{[goal_handle]() {
    auto start_time = std::chrono::steady_clock::now();
    
    const auto goal = goal_handle->get_goal();
    auto result = std::make_shared<RelocalizeAction::Result>();
    
    // Initialize simple cancel flag
    std::atomic<bool> cancel_requested{false};
    
    std::string config_file = node_handle->isLocalizationMode() ? "localization_2d.lua" : "mapping_2d.lua";
    // Set timeout
    double effective_timeout = goal->timeout_seconds;
    if (effective_timeout <= 0.0) {
      effective_timeout = node_options_handle->relocalization_timeout_sec;
    }
    bool has_timeout = effective_timeout > 0.0;
    auto timeout_end = std::chrono::steady_clock::now() + std::chrono::duration<double>(effective_timeout);
    
    LOG(INFO) << "Starting relocalization with timeout: " 
              << (has_timeout ? std::to_string(effective_timeout) + "s" : "none")
              << (goal->timeout_seconds <= 0.0 ? " (using default from config)" : " (from goal)");
    
    cartographer::transform::Rigid2d last_known_pose = cartographer::transform::Rigid2d::Identity();
    bool has_last_pose = node_handle->GetLastKnownPose(&last_known_pose);
    if (has_last_pose) {
      LOG(INFO) << "Stored last known pose: " << last_known_pose.DebugString();
    } else {
      LOG(INFO) << "No last known pose available";
    }
    
    cartographer::transform::Rigid2d best_pose_estimate;
    float best_score = 0.0f;
    rpos_common::core::RectangleF search_rect(
        static_cast<float>(goal->bounding_rect.x),
        static_cast<float>(goal->bounding_rect.y),
        static_cast<float>(goal->bounding_rect.width),
        static_cast<float>(goal->bounding_rect.height)
    );
    
    bool found = false;
    bool cancelled = false;
    bool timed_out = false;
    
    // Perform the relocalization with simple cancellation check
    auto relocalization_start = std::chrono::steady_clock::now();
    LOG(INFO) << "Starting core relocalization algorithm";
    
    try {
      // Create a background thread to monitor cancellation and timeout
      std::thread cancellation_monitor([&cancel_requested, &goal_handle, has_timeout, timeout_end]() {
        while (!cancel_requested.load()) {
          // Check external cancellation
          if (goal_handle->is_canceling()) {
            cancel_requested.store(true);
            break;
          }
          
          // Check timeout
          if (has_timeout && std::chrono::steady_clock::now() >= timeout_end) {
            cancel_requested.store(true);
            break;
          }
          
          std::this_thread::sleep_for(std::chrono::milliseconds(10)); // Check every 10ms
        }
      });
      
      // Call the simplified relocalization method with atomic bool pointer
      found = node_handle->global_relocate_with_cancel(search_rect, &best_pose_estimate, &best_score, &cancel_requested);
      
      // Stop the cancellation monitor
      cancel_requested.store(true);  // Signal the monitor to stop
      cancellation_monitor.join();
      
      // Reset cancel_requested to check actual cancellation reason
      bool was_cancelled = false;
      if (!found) {
        if (goal_handle->is_canceling()) {
          cancelled = true;
          was_cancelled = true;
          LOG(INFO) << "Relocalization was cancelled externally";
        } else if (has_timeout && std::chrono::steady_clock::now() >= timeout_end) {
          timed_out = true;
          LOG(INFO) << "Relocalization timed out";
        } else {
          // Reset flag if not actually cancelled
          cancel_requested.store(false);
        }
      }
    } catch (const std::exception& e) {
      LOG(ERROR) << "Relocalization threw exception: " << e.what();
      found = false;
    }
    
    auto relocalization_end = std::chrono::steady_clock::now();
    auto relocalization_duration = std::chrono::duration_cast<std::chrono::milliseconds>(relocalization_end - relocalization_start);
    
    auto end_time = std::chrono::steady_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Log performance statistics with detailed breakdown
    LOG(INFO) << "=== RELOCALIZATION TIMING STATISTICS ===";
    LOG(INFO) << "Core relocalization time: " << relocalization_duration.count() << " ms";
    LOG(INFO) << "Total action time: " << total_duration.count() << " ms";
    auto trajectory_management_time = total_duration.count() - relocalization_duration.count();
    LOG(INFO) << "Trajectory management overhead: " << trajectory_management_time << " ms";
    
    // Restart trajectory regardless of relocalization result
    cartographer::transform::Rigid2d recovery_pose = last_known_pose;
    bool use_initial_pose = false;
    
    if (found) {
      recovery_pose = best_pose_estimate;
      use_initial_pose = true;
      LOG(INFO) << "=== RELOCALIZATION SUCCESS === Will restart with new pose";
      LOG(INFO) << "Score: " << best_score << ", Pose: " << best_pose_estimate.DebugString();
    } else if (has_last_pose) {
      use_initial_pose = true;
      LOG(INFO) << "=== RELOCALIZATION FAILED === Will restart with last known pose";
      LOG(INFO) << "Last known pose: " << last_known_pose.DebugString();
    } else {
      use_initial_pose = false;
      LOG(INFO) << "=== RELOCALIZATION FAILED === Will restart without initial pose";
    }
    
    // Restart trajectory with appropriate pose
      auto trajectory_restart_start = std::chrono::steady_clock::now();
    LOG(INFO) << "Starting trajectory restart";
      
    try {
      // Wait a moment to ensure all trajectories are properly finished
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
      
      geometry_msgs::msg::Pose pose_msg;
      if (use_initial_pose) {
        pose_msg.position.x = recovery_pose.translation().x();
        pose_msg.position.y = recovery_pose.translation().y();
        pose_msg.position.z = 0.0;
        double theta = recovery_pose.rotation().angle();
        tf2::Quaternion q;
        q.setRPY(0, 0, theta);
        pose_msg.orientation.x = q.x();
        pose_msg.orientation.y = q.y();
        pose_msg.orientation.z = q.z();
        pose_msg.orientation.w = q.w();
      }
      
      LOG(INFO) << "Do relocalization, start trajectory with config file: " << config_file;
      auto start_request = std::make_shared<cartographer_ros_msgs::srv::StartTrajectory::Request>();
      auto start_response = std::make_shared<cartographer_ros_msgs::srv::StartTrajectory::Response>();
      start_request->configuration_directory = FLAGS_configuration_directory;
      start_request->configuration_basename = config_file;
      start_request->use_initial_pose = use_initial_pose;
      if (use_initial_pose) {
        start_request->initial_pose = pose_msg;
      } 
      int trajectory_id = 0;
      if(node_handle->FindValidTrajectory(trajectory_id)){
        start_request->relative_to_trajectory_id = trajectory_id;
      }
      else{
        start_request->use_initial_pose = false;  
        LOG(ERROR) << "No valid trajectory found, start trajectory without initial pose";
      }

      node_handle->StartTrajectory(start_request, start_response);
      
      auto trajectory_restart_end = std::chrono::steady_clock::now();
      auto trajectory_restart_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
          trajectory_restart_end - trajectory_restart_start);
      LOG(INFO) << "Trajectory restart completed in " << trajectory_restart_duration.count() << " ms";
      
      // Update final timing
      auto final_end_time = std::chrono::steady_clock::now();
      auto final_total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
          final_end_time - start_time);
      LOG(INFO) << "=== FINAL PERFORMANCE SUMMARY ===";
      LOG(INFO) << "Total relocalization action time: " << final_total_duration.count() << " ms";
      LOG(INFO) << "Performance improvement: " << (final_total_duration.count() < 2000 ? "GOOD" : "NEEDS_OPTIMIZATION");
      
    } catch (const std::exception& e) {
      LOG(ERROR) << "Error during trajectory restart: " << e.what();
      if (found) {
        result->success = false;
        result->message = "Relocalization succeeded but trajectory restart failed: " + std::string(e.what());
        goal_handle->abort(result);
        return;
      }
      // If relocalization failed, we still consider the recovery attempt as partial success
      LOG(WARNING) << "Trajectory restart failed, but this was expected after relocalization failure";
    }
    
    // Set final result and handle trajectory restart
    result->success = found;
    
    // Prepare recovery pose message for init_pose_callback
    auto recovery_pose_msg = std::make_shared<geometry_msgs::msg::PoseWithCovarianceStamped>();
    recovery_pose_msg->header.stamp = rclcpp::Clock().now();  
    recovery_pose_msg->header.frame_id = node_options_handle->map_frame;
    
    bool should_set_recovery_pose = false;
    
    if (cancelled) {
      result->message = "Relocalization cancelled";
      LOG(INFO) << "=== RELOCALIZATION CANCELLED === (Total time: " << total_duration.count() << " ms)";
      goal_handle->canceled(result);
    } else if (timed_out) {
      result->message = "Relocalization timed out after " + std::to_string(effective_timeout) + " seconds";
      LOG(INFO) << "=== RELOCALIZATION TIMED OUT === (Total time: " << total_duration.count() << " ms)";
      
      // Set recovery pose to last known pose if available
      if (has_last_pose) {
        recovery_pose_msg->pose.pose.position.x = last_known_pose.translation().x();
        recovery_pose_msg->pose.pose.position.y = last_known_pose.translation().y();
        recovery_pose_msg->pose.pose.position.z = 0.0;
        double theta = last_known_pose.rotation().angle();
        tf2::Quaternion q;
        q.setRPY(0, 0, theta);
        recovery_pose_msg->pose.pose.orientation.x = q.x();
        recovery_pose_msg->pose.pose.orientation.y = q.y();
        recovery_pose_msg->pose.pose.orientation.z = q.z();
        recovery_pose_msg->pose.pose.orientation.w = q.w();
        should_set_recovery_pose = true;
        LOG(INFO) << "Will restart with last known pose after timeout";
      }
      
      goal_handle->abort(result);
    } else if (found) {
      result->message = "Relocalization successful";
      LOG(INFO) << "=== RELOCALIZATION SUCCESS === (Total time: " << total_duration.count() << " ms)";
      
      // Set recovery pose to relocalization result
      recovery_pose_msg->pose.pose.position.x = best_pose_estimate.translation().x();
      recovery_pose_msg->pose.pose.position.y = best_pose_estimate.translation().y();
      recovery_pose_msg->pose.pose.position.z = 0.0;
      double theta = best_pose_estimate.rotation().angle();
      tf2::Quaternion q;
      q.setRPY(0, 0, theta);
      recovery_pose_msg->pose.pose.orientation.x = q.x();
      recovery_pose_msg->pose.pose.orientation.y = q.y();
      recovery_pose_msg->pose.pose.orientation.z = q.z();
      recovery_pose_msg->pose.pose.orientation.w = q.w();
      should_set_recovery_pose = true;
      LOG(INFO) << "Will restart with relocalized pose";
      
      goal_handle->succeed(result);
    } else {
      result->message = "Relocalization failed, trajectory restarted with recovery";
      LOG(WARNING) << "=== RELOCALIZATION FAILED === (Total time: " << total_duration.count() << " ms)";
      
      // Set recovery pose to last known pose if available
      if (has_last_pose) {
        recovery_pose_msg->pose.pose.position.x = last_known_pose.translation().x();
        recovery_pose_msg->pose.pose.position.y = last_known_pose.translation().y();
        recovery_pose_msg->pose.pose.position.z = 0.0;
        double theta = last_known_pose.rotation().angle();
        tf2::Quaternion q;
        q.setRPY(0, 0, theta);
        recovery_pose_msg->pose.pose.orientation.x = q.x();
        recovery_pose_msg->pose.pose.orientation.y = q.y();
        recovery_pose_msg->pose.pose.orientation.z = q.z();
        recovery_pose_msg->pose.pose.orientation.w = q.w();
        should_set_recovery_pose = true;
        LOG(INFO) << "Will restart with last known pose after failure";
      }
      
      goal_handle->succeed(result);  // Still succeed because we recovered properly
    }
    
    // Apply recovery pose using init_pose_callback if we have a valid pose
    if (should_set_recovery_pose) {
      LOG(INFO) << "Setting recovery pose: [" 
                << recovery_pose_msg->pose.pose.position.x << ", " 
                << recovery_pose_msg->pose.pose.position.y << ", " 
                << recovery_pose_msg->pose.pose.orientation.w << "]";
      
      // Use init_pose_callback to restart trajectory with recovery pose
      try {
        do_init_pose_callback(recovery_pose_msg, config_file);
        LOG(INFO) << "Successfully applied recovery pose via init_pose_callback";
      } catch (const std::exception& e) {
        LOG(ERROR) << "Failed to apply recovery pose: " << e.what();
      }
    } else {
      LOG(WARNING) << "No recovery pose available, trajectory will need to be manually restarted";
    }
  }}.detach();
}

void Run() {
  rclcpp::Node::SharedPtr cartographer_node = rclcpp::Node::make_shared("cartographer_node");
  constexpr double kTfBufferCacheTimeInSeconds = 10.;

  std::shared_ptr<tf2_ros::Buffer> tf_buffer =
      std::make_shared<tf2_ros::Buffer>(
        cartographer_node->get_clock(),
        tf2::durationFromSec(kTfBufferCacheTimeInSeconds),
        cartographer_node);

  std::shared_ptr<tf2_ros::TransformListener> tf_listener =
      std::make_shared<tf2_ros::TransformListener>(*tf_buffer);

  NodeOptions node_options;
  TrajectoryOptions trajectory_options;
  std::tie(node_options, trajectory_options) =
      LoadOptions(FLAGS_configuration_directory, FLAGS_configuration_basename);

  auto map_builder =
    cartographer::mapping::CreateMapBuilder(node_options.map_builder_options);
  auto node = std::make_shared<cartographer_ros::Node>(
    node_options, std::move(map_builder), tf_buffer, cartographer_node,
    FLAGS_collect_metrics);

  // for relocation
  trajectory_options_handle = std::make_shared<TrajectoryOptions>(trajectory_options);
  node_options_handle = std::make_shared<NodeOptions>(node_options);
  node_handle = (node);
  rclcpp::Subscription<geometry_msgs::msg::PoseWithCovarianceStamped>::SharedPtr init_pose_sub_ = 
    cartographer_node->create_subscription<geometry_msgs::msg::PoseWithCovarianceStamped>(
        "/initialpose", 10, std::bind(&init_pose_callback, std::placeholders::_1));
  
  relocalize_action_server_ = rclcpp_action::create_server<RelocalizeAction>(
    cartographer_node,
    "relocalize",
    std::bind(&handle_relocalize_goal, std::placeholders::_1, std::placeholders::_2),
    std::bind(&handle_relocalize_cancel, std::placeholders::_1),
    std::bind(&handle_relocalize_accepted, std::placeholders::_1)
  );

  if (!FLAGS_load_state_filename.empty()) {
    node->LoadState(FLAGS_load_state_filename, FLAGS_load_frozen_state);
  }

  if (FLAGS_start_trajectory_with_default_topics) {
    node->StartTrajectoryWithDefaultTopics(trajectory_options);
  }

  rclcpp::spin(cartographer_node);

  node->FinishAllTrajectories();
  node->RunFinalOptimization();

  if (!FLAGS_save_state_filename.empty()) {
    node->SerializeState(FLAGS_save_state_filename,
                        true /* include_unfinished_submaps */);
  }
}

}  // namespace
}  // namespace cartographer_ros

int main(int argc, char** argv) {
  // Init rclcpp first because gflags reorders command line flags in argv
  rclcpp::init(argc, argv);

  google::AllowCommandLineReparsing();
  google::InitGoogleLogging(argv[0]);
  google::ParseCommandLineFlags(&argc, &argv, false);

  CHECK(!FLAGS_configuration_directory.empty())
      << "-configuration_directory is missing.";
  CHECK(!FLAGS_configuration_basename.empty())
      << "-configuration_basename is missing.";

  cartographer_ros::ScopedRosLogSink ros_log_sink;
  cartographer_ros::Run();
  ::rclcpp::shutdown();
}
