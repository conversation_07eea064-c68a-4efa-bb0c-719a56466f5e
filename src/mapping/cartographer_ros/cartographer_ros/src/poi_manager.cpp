#include "cartographer_ros/poi_manager.h"

#include "cartographer/transform/transform.h"
#include "cartographer/sensor/landmark_data.h"
#include "cartographer_ros/time_conversion.h"

namespace cartographer_ros {

POIManager::P<PERSON>IManager(cartographer::mapping::PoseGraphInterface* pose_graph,
                      cartographer::mapping::MapBuilderInterface* map_builder)
    : pose_graph_(pose_graph), map_builder_(map_builder) {}

bool POIManager::AddPOI(const cartographer_ros_msgs::msg::POI& poi,
                        std::string* message) {
  absl::MutexLock lock(&mutex_);

  LOG(INFO) << "POIManager::AddPOI() called for POI ID: " << poi.id << ", name: " << poi.name;

  // Check if POI with this ID already exists
  if (pois_.find(poi.id) != pois_.end()) {
    *message = "POI with ID '" + poi.id + "' already exists.";
    LOG(WARNING) << *message;
    return false;
  }

  // Validate POI data
  if (poi.id.empty()) {
    *message = "POI ID cannot be empty.";
    LOG(WARNING) << *message;
    return false;
  }

  if (poi.name.empty()) {
    *message = "POI name cannot be empty.";
    LOG(WARNING) << *message;
    return false;
  }

  // Add POI to the map
  pois_[poi.id] = poi;
  
  cartographer::transform::Rigid3d poi_pose_3d = 
      cartographer::transform::Embed3D(ToRigid2d(poi.pose));
      
  pose_graph_->SetLandmarkPose(poi.id, poi_pose_3d, false);
  
  LOG(INFO) << "POI added to both internal map and pose graph. Total POIs: " << pois_.size();

  *message = "POI added successfully.";
  return true;
}

bool POIManager::RemovePOI(const std::string& id, std::string* message) {
  absl::MutexLock lock(&mutex_);

  auto it = pois_.find(id);
  if (it == pois_.end()) {
    *message = "POI with ID '" + id + "' not found.";
    return false;
  }

  // Remove POI from storage
  pois_.erase(it);
  
  // Note: Cartographer doesn't provide a direct way to remove landmarks
  // We mark it as frozen and set an invalid pose to effectively disable it
  cartographer::transform::Rigid3d invalid_pose = 
      cartographer::transform::Rigid3d::Identity();
  pose_graph_->SetLandmarkPose(id, invalid_pose, true);
  
  *message = "POI removed successfully from both storage and pose graph.";
  return true;
}

bool POIManager::RemoveAllPOI(std::string* message) {
  absl::MutexLock lock(&mutex_);

  if (pois_.empty()) {
    *message = "No POIs to remove.";
    return true;
  }

  size_t poi_count = pois_.size();
  
  // Remove all POIs from storage and pose graph
  for (const auto& pair : pois_) {
    const std::string& poi_id = pair.first;
    
    // Mark each POI as frozen and set an invalid pose to effectively disable it
    cartographer::transform::Rigid3d invalid_pose = 
        cartographer::transform::Rigid3d::Identity();
    pose_graph_->SetLandmarkPose(poi_id, invalid_pose, true);
  }
  
  // Clear all POIs from storage
  pois_.clear();
  
  *message = "Successfully removed all " + std::to_string(poi_count) + " POIs from both storage and pose graph.";
  LOG(INFO) << *message;
  return true;
}

bool POIManager::GetPOI(const std::string& id, cartographer_ros_msgs::msg::POI* poi, std::string* message) const {
  absl::MutexLock lock(&mutex_);

  auto it = pois_.find(id);
  if (it == pois_.end()) {
    *message = "POI with ID '" + id + "' not found.";
    return false;
  }

  *poi = it->second;
  *message = "POI retrieved successfully.";
  return true;
}

bool POIManager::UpdatePOI(const cartographer_ros_msgs::msg::POI& poi,
                          std::string* message) {
  absl::MutexLock lock(&mutex_);

  auto it = pois_.find(poi.id);
  if (it == pois_.end()) {
    *message = "POI with ID '" + poi.id + "' not found.";
    return false;
  }

  // Update POI data
  it->second = poi;
  
  // Update landmark pose in pose graph
  cartographer::transform::Rigid3d poi_pose_3d = 
      cartographer::transform::Embed3D(ToRigid2d(poi.pose));
      
  // Update the landmark pose, keeping it unfrozen for optimization
  pose_graph_->SetLandmarkPose(poi.id, poi_pose_3d, false);
  
  *message = "POI updated successfully in both storage and pose graph.";
  return true;
}

std::vector<cartographer_ros_msgs::msg::POI> POIManager::ListPOI() const {
  absl::MutexLock lock(&mutex_);

  std::vector<cartographer_ros_msgs::msg::POI> result;
  for (const auto& pair : pois_) {
    result.push_back(pair.second);
  }
  
  //LOG(INFO) << "POIManager::ListPOI() returning " << result.size() << " POIs";
  return result;
}

void POIManager::UpdatePOIPosesAfterOptimization() {
  absl::MutexLock lock(&mutex_);

  // Get optimized landmark poses from pose graph
  const auto& landmark_poses = pose_graph_->GetLandmarkPoses();
  
  // Update each POI's pose based on the optimized landmark poses
  for (auto& pair : pois_) {
    auto& poi = pair.second;
    const std::string& poi_id = poi.id;
    
    // Find the corresponding landmark pose
    auto landmark_it = landmark_poses.find(poi_id);
    if (landmark_it != landmark_poses.end()) {
      // Extract the optimized 3D pose and convert to 2D
      const cartographer::transform::Rigid3d& optimized_pose_3d = landmark_it->second;
      cartographer::transform::Rigid2d optimized_pose_2d = 
          cartographer::transform::Project2D(optimized_pose_3d);
      
             // Update the POI's pose with optimized values
       poi.pose = ToPose2D(optimized_pose_2d);
       
       RCLCPP_DEBUG(rclcpp::get_logger("POIManager"), 
                    "Updated POI '%s' pose after optimization: x=%.3f, y=%.3f, theta=%.3f",
                    poi_id.c_str(), poi.pose.x, poi.pose.y, poi.pose.theta);
    } else {
      RCLCPP_WARN(rclcpp::get_logger("POIManager"),
                  "POI '%s' not found in optimized landmark poses", poi_id.c_str());
    }
  }
}

void POIManager::AddPOIObservation(const std::string& poi_id, 
                                  int trajectory_id,
                                  const cartographer::common::Time& time,
                                  const cartographer::transform::Rigid3d& landmark_to_tracking_transform,
                                  double translation_weight,
                                  double rotation_weight) {
  // Create landmark observation
  cartographer::sensor::LandmarkObservation observation;
  observation.id = poi_id;
  observation.landmark_to_tracking_transform = landmark_to_tracking_transform;
  observation.translation_weight = translation_weight;
  observation.rotation_weight = rotation_weight;

  // Create landmark data with the observation
  cartographer::sensor::LandmarkData landmark_data;
  landmark_data.time = time;
  landmark_data.landmark_observations.push_back(observation);

  // Add landmark data through trajectory builder
  auto trajectory_builder = map_builder_->GetTrajectoryBuilder(trajectory_id);
  if (trajectory_builder) {
    trajectory_builder->AddSensorData("poi_" + poi_id, landmark_data);
    RCLCPP_DEBUG(rclcpp::get_logger("POIManager"),
                 "Added POI observation for '%s' at trajectory %d",
                 poi_id.c_str(), trajectory_id);
  } else {
    RCLCPP_WARN(rclcpp::get_logger("POIManager"),
                "Failed to get trajectory builder for trajectory %d",
                trajectory_id);
  }
}

cartographer::transform::Rigid2d POIManager::ToRigid2d(
    const geometry_msgs::msg::Pose2D& pose) const {
  return cartographer::transform::Rigid2d(
      {pose.x, pose.y},
      pose.theta);
}

geometry_msgs::msg::Pose2D POIManager::ToPose2D(
    const cartographer::transform::Rigid2d& pose) const {
  geometry_msgs::msg::Pose2D result;
  result.x = pose.translation().x();
  result.y = pose.translation().y();
  result.theta = pose.rotation().angle();
  return result;
}

}  // namespace cartographer_ros