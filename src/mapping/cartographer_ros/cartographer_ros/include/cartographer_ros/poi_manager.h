#ifndef CARTOGRAPHER_ROS_CARTOGRAPHER_ROS_POI_MANAGER_H_
#define CARTOGRAPHER_ROS_CARTOGRAPHER_ROS_POI_MANAGER_H_

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "cartographer/mapping/pose_graph_interface.h"
#include "cartographer/mapping/map_builder_interface.h"
#include "cartographer_ros_msgs/msg/poi.hpp"
#include "geometry_msgs/msg/pose2_d.h"
#include "absl/synchronization/mutex.h"

namespace cartographer_ros {

class POIManager {
 public:
  explicit POIManager(cartographer::mapping::PoseGraphInterface* pose_graph,
                     cartographer::mapping::MapBuilderInterface* map_builder);

  // Add a new POI to the manager
  bool AddPOI(const cartographer_ros_msgs::msg::POI& poi, std::string* message);

  // Delete a POI by ID
  bool RemovePOI(const std::string& id, std::string* message);

  // Delete all POIs
  bool RemoveAllPOI(std::string* message);

  // Update an existing POI
  bool UpdatePOI(const cartographer_ros_msgs::msg::POI& poi, std::string* message);

  bool GetPOI(const std::string& id, cartographer_ros_msgs::msg::POI* poi, std::string* message) const;

  // List all POIs
  std::vector<cartographer_ros_msgs::msg::POI> ListPOI() const;

  // Update POI poses after optimization
  void UpdatePOIPosesAfterOptimization();

  // Add a POI observation for optimization
  void AddPOIObservation(const std::string& poi_id, 
                        int trajectory_id,
                        const cartographer::common::Time& time,
                        const cartographer::transform::Rigid3d& landmark_to_tracking_transform,
                        double translation_weight = 1.0,
                        double rotation_weight = 1.0);

 private:
  // Convert between ROS and Cartographer types
  cartographer::transform::Rigid2d ToRigid2d(const geometry_msgs::msg::Pose2D& pose) const;
  geometry_msgs::msg::Pose2D ToPose2D(const cartographer::transform::Rigid2d& pose) const;

  // The pose graph interface for accessing optimization results
  cartographer::mapping::PoseGraphInterface* pose_graph_;
  
  // The map builder interface for adding sensor data
  cartographer::mapping::MapBuilderInterface* map_builder_;

  // Mutex for thread safety
  mutable absl::Mutex mutex_;

  // Storage for POIs
  std::unordered_map<std::string, cartographer_ros_msgs::msg::POI> pois_
      GUARDED_BY(mutex_);
};

}  // namespace cartographer_ros

#endif  // CARTOGRAPHER_ROS_CARTOGRAPHER_ROS_POI_MANAGER_H_ 