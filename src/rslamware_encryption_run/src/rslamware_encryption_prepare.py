#!/usr/bin/env python3
"""
RSlamware Encryption Preparation Script

This script prepares the rslamware launch files and configurations for encryption by:
1. Copying all launch files recursively used in run_rslamware.sh and run_simulator.sh
2. Copying all config files and combining them into one combined_config.yaml
3. Optionally deleting individual config files (controlled by --delete-individual-configs parameter)
4. Refining launch files to use the combined config
5. Creating a compressed tar.gz archive
6. Creating backup copies
7. Removing original files from install directories
"""

import os
import sys
import shutil
import yaml
import tarfile
import re
from pathlib import Path
from typing import Dict, List, Set, Any
import argparse


class RSlamwareEncryptionPrepare:
    def __init__(self, workspace_root: str, delete_individual_configs: bool = False):
        self.workspace_root = Path(workspace_root)
        self.install_dir = self.workspace_root / "install"
        self.launch_dir = self.workspace_root / "launch"
        self.config_dir = self.workspace_root / "config"
        self.backup_dir = self.workspace_root / "rslamware_encryption_backup"
        self.delete_individual_configs = delete_individual_configs

        # Key packages and launch files from the scripts
        self.key_launch_files = [
            "rslamware_bringup/rslamware.launch.py",
            "cartographer_ros/mapping.launch.py",
            "nav2_bringup/bringup_launch.py"
        ]

        # Track all discovered files
        self.all_launch_files: Set[str] = set()
        self.all_config_files: Set[str] = set()
        self.combined_config: Dict[str, Any] = {}
        self.processed_launch_files: Set[str] = set()
        
    def create_directories(self):
        """Create necessary directories"""

        if self.launch_dir.exists():
            shutil.rmtree(self.launch_dir)
        if self.config_dir.exists():
            shutil.rmtree(self.config_dir)
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)

        self.launch_dir.mkdir(exist_ok=True)
        self.config_dir.mkdir(exist_ok=True)
        self.backup_dir.mkdir(exist_ok=True)
        print(f"Created directories: {self.launch_dir}, {self.config_dir}, {self.backup_dir}")
        
    def copy_key_launch_files(self):
        """Copy the 3 key launch files to launch directory first"""
        print("Copying key launch files...")

        launch_file = self.install_dir / "rslamware_bringup/share/rslamware_bringup/launch/rslamware.launch.py"
        
        dst_path = self.launch_dir / "rslamware.launch.py"
        shutil.copy2(launch_file, dst_path)
        print(f"Copied key launch file: {launch_file.name}")

        launch_file = self.install_dir / "cartographer_ros/share/cartographer_ros/launch/mapping.launch.py"
        dst_path = self.launch_dir / "mapping.launch.py"
        shutil.copy2(launch_file, dst_path)
        print(f"Copied key launch file: {launch_file.name}")

        launch_file = self.install_dir / "nav2_bringup/share/nav2_bringup/launch/bringup_launch.py"
        dst_path = self.launch_dir / "bringup_launch.py"
        shutil.copy2(launch_file, dst_path)
        print(f"Copied key launch file: {launch_file.name}")
        
        
        print("Key launch files copied")
        
    def parse_launch_file_dependencies(self,launch_file_path: Path):
        """解析launch文件以查找对其他launch文件的依赖"""
        dependencies = set()
        print(f"Parsing launch file dependencies for {launch_file_path}")

        try:
            with open(launch_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 直接搜索所有以launch.py结尾的字符串模式
            # 匹配规则：任意字符直到遇到launch.py结尾
            pattern = r'[^\s\'"\)]+launch\.py'
            matches = re.findall(pattern, content)
            
            for match in matches:
                # 提取最后一个斜杠后的部分
                filename = match.split('/')[-1]
                
                # 验证是否以launch.py结尾
                if filename.endswith('launch.py'):
                    # 修复常见拼写错误
                    filename = filename.replace('launch.py', 'launch.py')
                    
                if filename.endswith('launch.py'):
                    if filename not in dependencies:
                        dependencies.add(filename)
                        print(f"Found dependency: {filename}")
            
            return dependencies

        except Exception as e:
            print(f"Error parsing {launch_file_path}: {str(e)}")
            return dependencies

    def recursively_find_launch_dependencies(self):
        """Recursively find and copy all launch file dependencies"""
        print("Recursively finding launch file dependencies...")

        # Start with key launch files already copied
        to_process = set()
        for launch_file in self.launch_dir.glob("*launch.py"):
            to_process.add(launch_file.name)

        while to_process:
            current_file = to_process.pop()
            if current_file in self.processed_launch_files:
                continue

            self.processed_launch_files.add(current_file)
            current_path = self.launch_dir / current_file

            if not current_path.exists():
                # Try to find and copy this file from install directory
                found = False
                for package_dir in self.install_dir.iterdir():
                    if not package_dir.is_dir():
                        continue

                    share_dir = package_dir / "share" / package_dir.name / "launch"
                    if share_dir.exists():
                        for launch_file in share_dir.rglob("*launch.py"):
                            if launch_file.name == current_file:
                                shutil.copy2(launch_file, self.launch_dir / current_file)
                                current_path = self.launch_dir / current_file
                                found = True
                                print(f"Found and copied dependency: {current_file}")
                                break
                    if found:
                        break

            if current_path.exists():
                # Parse this file for more dependencies
                dependencies = self.parse_launch_file_dependencies(current_path)
                for dep in dependencies:
                    dep_filename = dep.split('/')[-1]  # Get just the filename
                    if dep_filename not in self.processed_launch_files:
                        to_process.add(dep_filename)

        print(f"Processed {len(self.processed_launch_files)} launch files")
        
    def parse_launch_files_for_configs(self):
        """Parse all launch files in launch directory to find config file dependencies"""
        print("Parsing launch files for config dependencies...")

        for launch_file in self.launch_dir.glob("*launch.py"):
            
            print(f"Parsing launch file config files for {launch_file}")

            try:
                with open(launch_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 直接搜索所有以.yaml或.yml结尾的字符串模式
                # 匹配规则：任意字符直到遇到.yaml或.yml结尾
                pattern = r'[^\s\'"\)]+\.ya?ml'
                matches = re.findall(pattern, content)
                
                for match in matches:
                    # 提取最后一个斜杠后的部分
                    filename = re.split(r'[\\/]', match)[-1]
                    
                    # 验证是否以yaml或yml结尾
                    if filename.endswith('.yaml') or filename.endswith('.yml'):
                        print(f"Found config file: {filename}")
                        self.find_and_add_config_file(filename)
            except Exception as e:
                print(f"Warning: Could not parse {launch_file}: {e}")

        print(f"Found {len(self.all_config_files)} config files from launch files")

    def find_and_add_config_file(self, config_filename: str):
        """Find a config file in install directory and add to collection"""
        found = False
        for package_dir in self.install_dir.iterdir():
            if not package_dir.is_dir():
                continue

            share_dir = package_dir / "share" / package_dir.name
            if not share_dir.exists():
                continue

            # Check config and params directories
            for subdir in ["config"]:
                config_share_dir = share_dir / subdir
                if config_share_dir.exists() and config_share_dir.is_dir():
                    # 同时搜索 .yaml 和 .yml 文件
                    for ext in ["*.yaml", "*.yml"]:
                        for config_file in config_share_dir.rglob(ext):
                            if config_file.is_file() and config_file.name == config_filename:
                                rel_path = config_file.relative_to(self.install_dir)
                                self.all_config_files.add(str(rel_path))
                                found = True
        
        if not found:
            print(f"Warning: Config file {config_filename} not found in any package")
        
    def create_startup_launch_files(self):
        """Create rslamware_startup_mapping.launch.py and rslamware_startup_localization.launch.py"""
        print("Creating startup launch files...")

        # Create mapping startup launch file
        mapping_content = '''#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
import os

def generate_launch_description():
    # Get the directory where this launch file is located
    launch_dir = os.path.dirname(os.path.realpath(__file__))

    return LaunchDescription([
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(launch_dir, 'rslamware.launch.py')
            )
        ),
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(launch_dir, 'mapping.launch.py')
            )
        )
    ])
'''

        # Create localization startup launch file
        localization_content = '''#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
import os

def generate_launch_description():
    # Get the directory where this launch file is located
    launch_dir = os.path.dirname(os.path.realpath(__file__))

    return LaunchDescription([
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(launch_dir, 'rslamware.launch.py')
            )
        ),
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(launch_dir, 'bringup_launch.py')
            )
        )
    ])
'''

        # Write the files
        mapping_path = self.launch_dir / "rslamware_startup_mapping.launch.py"
        localization_path = self.launch_dir / "rslamware_startup_localization.launch.py"

        with open(mapping_path, 'w') as f:
            f.write(mapping_content)

        with open(localization_path, 'w') as f:
            f.write(localization_content)

        print("Created startup launch files")


    def deep_merge_dict(self, target, source):
        """递归深度合并两个字典"""
        for key, value in source.items():
            # 如果键在目标字典中且两个值都是字典，则递归合并
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self.deep_merge_dict(target[key], value)
            else:
                # 否则直接赋值（覆盖）
                target[key] = value

    def copy_and_combine_config_files(self):
        """复制所有配置文件并深度合并为一个YAML文件"""
        print("Copying and combining config files...")
        self.combined_config = {}  # 确保每次调用时重置
        
        # 首先复制所有文件
        for config_file_rel in self.all_config_files:
            src_path = self.install_dir / config_file_rel
            
            # 保留原始路径结构复制文件
            dst_path = self.config_dir / config_file_rel
            dst_path.parent.mkdir(parents=True, exist_ok=True)  # 创建必要的目录结构
            shutil.copy2(src_path, dst_path)
            
            # 加载并深度合并到组合配置
            try:
                with open(src_path, 'r') as f:
                    config_data = yaml.safe_load(f)
                    if config_data is None:  # 空文件处理
                        print(f"Warning: Empty config file {config_file_rel}")
                        continue
                        
                    if not self.combined_config:  # 如果是第一个文件
                        self.combined_config = config_data
                    elif isinstance(config_data, dict) and isinstance(self.combined_config, dict):
                        # 递归深度合并字典
                        self.deep_merge_dict(self.combined_config, config_data)
                    else:
                        # 处理非字典内容（列表、字符串等）
                        # 创建特殊键或直接覆盖（根据需求选择）
                        print(f"Warning: Non-dict config in {config_file_rel}, overwriting")
                        self.combined_config = config_data
            except Exception as e:
                print(f"Error processing {config_file_rel}: {str(e)}")

        # 写入合并后的配置
        combined_config_path = self.config_dir / "combined_config.yaml"
        try:
            with open(combined_config_path, 'w') as f:
                yaml.dump(
                    self.combined_config, 
                    f, 
                    default_flow_style=False, 
                    indent=2,
                    sort_keys=False,  # 按键排序使输出更一致
                    allow_unicode=True  # 正确处理Unicode字符
                )
            print(f"Successfully created combined config with {len(self.all_config_files)} files merged")
            print(f"Combined config written to: {combined_config_path}")
        except Exception as e:
            print(f"Failed to write combined config: {str(e)}")

        # Delete all individual config files except combined_config.yaml if requested
        if self.delete_individual_configs:
            deleted_count = 0
            
            # Delete copied config files from config directory
            for config_file in self.config_dir.glob("**/*.yaml"):
                if config_file.name != "combined_config.yaml":
                    config_file.unlink()
                    deleted_count += 1
            for config_file in self.config_dir.glob("**/*.yml"):
                config_file.unlink()
                deleted_count += 1
            
            # Delete original config files from install directory
            original_deleted_count = 0
            for config_file_rel in self.all_config_files:
                original_file = self.install_dir / config_file_rel
                if original_file.exists():
                    original_file.unlink()
                    original_deleted_count += 1
                    print(f"Deleted original config file: {config_file_rel}")
            
            # Remove empty directories from config directory
            for root, dirs, files in os.walk(self.config_dir, topdown=False):
                for dir_name in dirs:
                    dir_path = Path(root) / dir_name
                    if dir_path.exists() and not any(dir_path.iterdir()):
                        dir_path.rmdir()
            
            print(f"Deleted {deleted_count} config files from config directory")
            print(f"Deleted {original_deleted_count} original config files from install directory")
            print("Kept only combined_config.yaml")
        else:
            print("Individual config files preserved alongside combined_config.yaml")
    def refine_launch_files(self):
        """Refine launch files to use combined_config.yaml and local launch file paths"""
        print("Refining launch files to use combined config and local paths...")

        # combined_config_path = "os.path.dirname(os.path.realpath(__file__))/../config/combined_config.yaml"

        for launch_file in self.launch_dir.glob("*launch.py"):
            try:
                with open(launch_file, 'r') as f:
                    content = f.read()

                modified = False

                if "ENCRYPTION_MODE=False" in content:
                    content = content.replace("ENCRYPTION_MODE=False", "ENCRYPTION_MODE=True")
                    modified = True

                if modified:
                    with open(launch_file, 'w') as f:
                        f.write(content)
                    print(f"Modified launch file: {launch_file.name}")

            except Exception as e:
                print(f"Warning: Could not refine {launch_file}: {e}")

        print("Launch file refinement completed")

    def create_tar_archive(self):
        """Create tar.gz archive of launch and config directories"""
        print("Creating tar.gz archive...")

        archive_path = self.workspace_root / "install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware.tar.gz"

        with tarfile.open(archive_path, 'w:gz') as tar:
            tar.add(self.launch_dir, arcname='launch')
            tar.add(self.config_dir, arcname='config')

        print(f"Created archive: {archive_path}")
        return archive_path

    def create_backup(self, archive_path: Path):
        """Create backup copies in rslamware_encryption_backup"""
        print("Creating backup copies...")

        # Copy archive
        shutil.copy2(archive_path, self.backup_dir / "rslamware.tar.gz")

        # Copy launch and config directories
        if (self.backup_dir / "launch").exists():
            shutil.rmtree(self.backup_dir / "launch")
        if (self.backup_dir / "config").exists():
            shutil.rmtree(self.backup_dir / "config")

        shutil.copytree(self.launch_dir, self.backup_dir / "launch")
        shutil.copytree(self.config_dir, self.backup_dir / "config")

        print(f"Backup created in: {self.backup_dir}")

    def remove_original_files(self):
        """Remove original launch and config files from install directories"""
        print("Removing original files from install directories...")

        removed_launch = 0
        removed_config = 0

        # Remove all launch directories that have launch files
        for package_dir in self.install_dir.iterdir():
            if not package_dir.is_dir():
                continue

            share_dir = package_dir / "share" / package_dir.name
            if not share_dir.exists():
                continue

            launch_dir = share_dir / "launch"
            if launch_dir.exists():
                try:
                    shutil.rmtree(launch_dir)
                    removed_launch += 1
                    print(f"Removed launch directory: {launch_dir}")
                except Exception as e:
                    print(f"Warning: Could not remove {launch_dir}: {e}")

            # Remove config directories
            for config_subdir in ["config", "params"]:
                config_dir = share_dir / config_subdir
                if config_dir.exists():
                    try:
                        shutil.rmtree(config_dir)
                        removed_config += 1
                        print(f"Removed config directory: {config_dir}")
                    except Exception as e:
                        print(f"Warning: Could not remove {config_dir}: {e}")

        # Remove specific directories mentioned in requirements
        specific_dirs = [
            self.install_dir / "rslamware_bringup/share/rslamware_bringup/launch/controller",
            self.install_dir / "nav2_bringup/share/nav2_bringup/params"
        ]

        for specific_dir in specific_dirs:
            if specific_dir.exists():
                try:
                    shutil.rmtree(specific_dir)
                    print(f"Removed specific directory: {specific_dir}")
                except Exception as e:
                    print(f"Warning: Could not remove {specific_dir}: {e}")

        print(f"Removed {removed_launch} launch directories and {removed_config} config directories")

    def run(self):
        """Execute the complete preparation process"""
        print("Starting RSlamware encryption preparation...")

        try:
            # 1.1 Copy key launch files first
            self.create_directories()
            self.copy_key_launch_files()

            # 1.2 Parse and copy all launch file dependencies recursively
            self.recursively_find_launch_dependencies()

            # 1.3 Parse launch files and copy config files
            self.parse_launch_files_for_configs()

            # 1.4 & 1.5 Create startup launch files
            self.create_startup_launch_files()

            # 1.6 & 1.7 Copy and combine config files, delete individuals
            self.copy_and_combine_config_files()

            # 1.8 Refine launch files to use combined config
            self.refine_launch_files()

            # 1.8 Create tar archive
            archive_path = self.create_tar_archive()

            # 1.9 Create backup
            self.create_backup(archive_path)

            # 1.10 Remove original files from install directories
            if self.delete_individual_configs:
                self.remove_original_files()

            print("RSlamware encryption preparation completed successfully!")
            print(f"Archive created: {archive_path}")
            print(f"Backup available in: {self.backup_dir}")

        except Exception as e:
            print(f"Error during preparation: {e}")
            sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description='Prepare RSlamware files for encryption')
    parser.add_argument('--workspace', default='.',
                       help='Workspace root directory (default: current directory)')
    parser.add_argument('--delete-configs', action='store_true',
                       help='Delete individual config files after combining (default: False)')

    args = parser.parse_args()

    workspace_root = os.path.abspath(args.workspace)
    if not os.path.exists(os.path.join(workspace_root, 'install')):
        print(f"Error: install directory not found in {workspace_root}")
        print("Please run this script from the workspace root or specify --workspace")
        sys.exit(1)

    preparer = RSlamwareEncryptionPrepare(workspace_root, args.delete_configs)
    preparer.run()


if __name__ == "__main__":
    main()
