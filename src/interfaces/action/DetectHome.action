#goal definition
# Expected charging dock pose (global coordinate system, required)
geometry_msgs/Pose expected_home_pose

# Detection timeout (milliseconds)
uint32 timeout_ms

---
#result definition
# Whether charging dock was successfully detected
bool success

# Detection time (milliseconds)
uint64 detection_time_ms

# Error message
string error_message

# Detected charging dock pose (global coordinate system)
geometry_msgs/Pose home_pose

---
#feedback definition
# Current detection status
uint8 status
uint8 STATUS_PENDING=0
uint8 STATUS_RUNNING=1
uint8 STATUS_SUCCEED=2
uint8 STATUS_FAILED=3
uint8 STATUS_CANCELED=4

# Detection progress (0-100)
uint8 progress_percent

# Current number of detected candidates
uint32 candidate_count

# Vote count of current best candidate
uint32 best_candidate_votes

# Status description
string status_message