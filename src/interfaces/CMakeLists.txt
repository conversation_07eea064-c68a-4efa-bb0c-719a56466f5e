cmake_minimum_required(VERSION 3.8)
project(interfaces)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(rosidl_default_runtime REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(action_msgs REQUIRED)

# uncomment the following section in order to fill in
# further dependencies manually.
# find_package(<dependency> REQUIRED)

rosidl_generate_interfaces(${PROJECT_NAME}
  "msg/BaseErrorInfo.msg"
  "msg/ComponentHealthInfo.msg"
  "msg/RobotHealthInfo.msg"
  "msg/SystemEvent.msg"
  "msg/FusionAndPerScan.msg"
  "msg/Rectangle2D.msg"
  "msg/WeightPose.msg"
  "msg/RectangleF.msg"
  "msg/Line.msg"
  "msg/HoughLine.msg"
  "msg/ScanData.msg"
  "msg/ShelfInfo.msg"
  "msg/VirtualLine.msg"
  "srv/DetectShelf.srv"
  "action/DetectHome.action"
  DEPENDENCIES std_msgs sensor_msgs geometry_msgs action_msgs
)

# Create health_provider static library
add_library(health_provider STATIC
  src/health_provider.cpp
)

# Include directories
target_include_directories(health_provider PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include/${PROJECT_NAME}>
)

# Link dependencies
ament_target_dependencies(health_provider
  rclcpp
  rosidl_default_runtime
)

# Link against the generated interfaces using the new method
rosidl_get_typesupport_target(cpp_typesupport_target ${PROJECT_NAME} "rosidl_typesupport_cpp")
target_link_libraries(health_provider ${cpp_typesupport_target})

# Install the library
install(TARGETS health_provider
  EXPORT health_provider_targets
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

# Export the targets
install(EXPORT health_provider_targets
  FILE health_provider_targets.cmake
  NAMESPACE ${PROJECT_NAME}::
  DESTINATION lib/cmake/${PROJECT_NAME}
)

install(
  DIRECTORY include/
  DESTINATION include/${PROJECT_NAME}
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

# Export dependencies
ament_export_targets(health_provider_targets HAS_LIBRARY_TARGET)
ament_export_dependencies(rclcpp rosidl_default_runtime)

ament_package()
