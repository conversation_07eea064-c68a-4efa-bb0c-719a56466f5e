#ifndef SL_VCU_ALL_BUMPER_SENSOR_HPP
#define SL_VCU_ALL_BUMPER_SENSOR_HPP

#include "rclcpp/rclcpp.hpp"
#include "sl_vcu_all/msg/bumper_state.hpp"
#include "health_provider/health_provider.h"
#include <chrono>
#include <functional>
#include <string>
#include <memory>
#include <thread>
#include <atomic>
#include <fstream>
#include <fcntl.h>
#include <poll.h>
#include <unistd.h>
#include <linux/input.h>

namespace sl_vcu_all
{

class BumperSensor : public rclcpp::Node
{
public:
    explicit BumperSensor(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    virtual ~BumperSensor();

private:
    // Methods
    void initParameters();
    bool setupInputDevice();
    void monitorInputEvents();
    void publishBumperState();
    
    // Publisher
    rclcpp::Publisher<sl_vcu_all::msg::BumperState>::SharedPtr bumper_state_pub_;
    
    // Input device parameters
    std::string input_device_path_;
    int input_device_fd_;
    int poll_timeout_ms_;
    int front_bumper_code_;
    int back_bumper_code_;
    int triggered_value_;
    
    // Bumper state
    std::atomic<bool> front_bumper_triggered_;
    std::atomic<bool> back_bumper_triggered_;
    std::atomic<uint32_t> bumper_status_;
    
    // Input event monitoring thread
    std::thread input_thread_;
    std::atomic<bool> keep_monitoring_;
    
    // Timer for publishing
    rclcpp::TimerBase::SharedPtr publish_timer_;
    int publish_rate_ms_;
    std::string bumper_topic_;

    // Health provider for emergency stop alerts
    std::unique_ptr<rslamware::health::HealthProvider> health_provider_;

    // Emergency stop error codes
    static constexpr int32_t EMERGENCY_STOP_FRONT_ERROR = 0x02010001;  // Level 2, Component 1, Error 1
    static constexpr int32_t EMERGENCY_STOP_BACK_ERROR = 0x02010002;   // Level 2, Component 1, Error 2
};

} // namespace sl_vcu_all

#endif // SL_VCU_ALL_BUMPER_SENSOR_HPP 