#include "sl_vcu_all/bumper_sensor.hpp"
#include <iostream>
#include <fstream>
#include <string>
#include <chrono>
#include <cstring>
#include <system_error>
#include <linux/input.h>
#include <sys/ioctl.h>

using namespace std::chrono_literals;

// Macros for bit testing
#define BITS_PER_LONG (sizeof(long) * 8)
#define NLONGS(x) (((x) + BITS_PER_LONG - 1) / BITS_PER_LONG)
#define test_bit(bit, array) ((array)[(bit) / BITS_PER_LONG] & (1L << ((bit) % BITS_PER_LONG)))

namespace sl_vcu_all
{

BumperSensor::BumperSensor(const rclcpp::NodeOptions & options)
: Node("bumper_sensor", options),
    input_device_fd_(-1),
    front_bumper_triggered_(false),
    back_bumper_triggered_(false),
    bumper_status_(0),
    keep_monitoring_(true)
{
    // Initialize parameters
    initParameters();

    RCLCPP_INFO(this->get_logger(), "Initializing Bumper Sensor");

    // Initialize health provider
    health_provider_ = std::make_unique<rslamware::health::HealthProvider>(
        shared_from_this(), "bumper_sensor");

    // Create publisher
    bumper_state_pub_ = this->create_publisher<sl_vcu_all::msg::BumperState>(bumper_topic_, 10);

    // Setup input device
    bool setup_ok = setupInputDevice();

    if (!setup_ok) {
        RCLCPP_ERROR(this->get_logger(), "Failed to setup input device, bumpers may not function");
    }

    // Start input event monitoring thread
    input_thread_ = std::thread(&BumperSensor::monitorInputEvents, this);
    
    // Create timer for publishing state
    publish_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(publish_rate_ms_),
        std::bind(&BumperSensor::publishBumperState, this));
    
    RCLCPP_INFO(this->get_logger(), "Bumper Sensor initialized");
}

BumperSensor::~BumperSensor()
{
    // Stop monitoring thread
    keep_monitoring_ = false;

    if (input_thread_.joinable()) {
        input_thread_.join();
    }

    // Close input device file descriptor
    if (input_device_fd_ >= 0) {
        close(input_device_fd_);
    }
}

void BumperSensor::initParameters()
{
    // Input device parameters
    this->declare_parameter("input_device_path", "/dev/input/event2"); // Default input device path
    this->declare_parameter("poll_timeout_ms", 100); // Default poll timeout in milliseconds
    this->declare_parameter("front_bumper_code", 59); // Default front bumper event code
    this->declare_parameter("back_bumper_code", 60); // Default back bumper event code
    this->declare_parameter("triggered_value", 1); // Default triggered event value
    this->declare_parameter("publish_rate_ms", 20); // Default publish period
    this->declare_parameter("bumper_topic", "bumper_state");

    // Get parameter values
    input_device_path_ = this->get_parameter("input_device_path").as_string();
    poll_timeout_ms_ = this->get_parameter("poll_timeout_ms").as_int();
    front_bumper_code_ = this->get_parameter("front_bumper_code").as_int();
    back_bumper_code_ = this->get_parameter("back_bumper_code").as_int();
    triggered_value_ = this->get_parameter("triggered_value").as_int();
    publish_rate_ms_ = this->get_parameter("publish_rate_ms").as_int();
    bumper_topic_ = this->get_parameter("bumper_topic").as_string();

    RCLCPP_INFO(this->get_logger(), "Parameters initialized:");
    RCLCPP_INFO(this->get_logger(), "  Input device path: %s", input_device_path_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Poll timeout: %d ms", poll_timeout_ms_);
    RCLCPP_INFO(this->get_logger(), "  Front bumper code: %d", front_bumper_code_);
    RCLCPP_INFO(this->get_logger(), "  Back bumper code: %d", back_bumper_code_);
    RCLCPP_INFO(this->get_logger(), "  Triggered value: %d", triggered_value_);
    RCLCPP_INFO(this->get_logger(), "  Publish rate: %d ms", publish_rate_ms_);
    RCLCPP_INFO(this->get_logger(), "  Bumper topic: %s", bumper_topic_.c_str());
}

bool BumperSensor::setupInputDevice()
{
    try {
        // Open input device
        input_device_fd_ = open(input_device_path_.c_str(), O_RDONLY | O_NONBLOCK);
        if (input_device_fd_ < 0) {
            RCLCPP_ERROR(this->get_logger(), "Failed to open input device %s: %s",
                       input_device_path_.c_str(), strerror(errno));
            return false;
        }

        // Check if device supports the events we need
        unsigned long evbit[NLONGS(EV_MAX)] = {0};
        if (ioctl(input_device_fd_, EVIOCGBIT(0, EV_MAX), evbit) < 0) {
            RCLCPP_ERROR(this->get_logger(), "Failed to get event types from input device: %s", strerror(errno));
            close(input_device_fd_);
            input_device_fd_ = -1;
            return false;
        }

        // Check if device supports key events
        if (!test_bit(EV_KEY, evbit)) {
            RCLCPP_ERROR(this->get_logger(), "Input device does not support key events");
            close(input_device_fd_);
            input_device_fd_ = -1;
            return false;
        }

        RCLCPP_INFO(this->get_logger(), "Successfully setup input device %s", input_device_path_.c_str());
        return true;
    }
    catch (const std::exception & e) {
        RCLCPP_ERROR(this->get_logger(), "Exception during input device setup: %s", e.what());
        return false;
    }
}

void BumperSensor::monitorInputEvents()
{
    // Exit if no valid file descriptor
    if (input_device_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "No valid input device file descriptor to monitor");
        return;
    }

    // Setup polling
    struct pollfd fds[1];
    fds[0].fd = input_device_fd_;
    fds[0].events = POLLIN;

    // Buffer for input events
    struct input_event events[64];

    while (keep_monitoring_.load()) {
        // Poll input device with timeout
        int ret = poll(fds, 1, poll_timeout_ms_);

        if (ret < 0) {
            RCLCPP_ERROR(this->get_logger(), "Poll error: %s", strerror(errno));
            std::this_thread::sleep_for(std::chrono::seconds(1));
            continue;
        }

        if (ret == 0) {
            // Timeout, continue monitoring
            // front_bumper_triggered_.store(false);
            // back_bumper_triggered_.store(false);
            // bumper_status_.store(0);
            // RCLCPP_DEBUG(this->get_logger(), "Poll timeout, no events received");

            continue;
        }

        // Read input events
        if (fds[0].revents & POLLIN) {
            ssize_t bytes_read = read(input_device_fd_, events, sizeof(events));
            if (bytes_read < 0) {
                if (errno != EAGAIN && errno != EWOULDBLOCK) {
                    RCLCPP_ERROR(this->get_logger(), "Read error: %s", strerror(errno));
                }
                continue;
            }

            int num_events = bytes_read / sizeof(struct input_event);
            for (int i = 0; i < num_events; i++) {
                struct input_event &ev = events[i];

                // Only process key events
                if (ev.type == EV_KEY) {
                    bool is_triggered = (ev.value == triggered_value_);
                    bool is_released = (ev.value == 0);

                    if (ev.code == front_bumper_code_) { // Front bumper
                        if (is_triggered || is_released) {
                            // Only log state changes to avoid flooding
                            if (is_triggered != front_bumper_triggered_.load()) {
                                RCLCPP_INFO(this->get_logger(), "Front bumper %s",
                                          is_triggered ? "TRIGGERED" : "RELEASED");

                                // Report health status for emergency stop
                                if (is_triggered) {
                                    health_provider_->reportHealth(EMERGENCY_STOP_FRONT_ERROR,
                                                                 "Front emergency stop button triggered");
                                } else {
                                    health_provider_->removeHealth(EMERGENCY_STOP_FRONT_ERROR);
                                }
                            }

                            front_bumper_triggered_.store(is_triggered);

                            // Update bumper status
                            if (is_triggered) {
                                bumper_status_.fetch_or(0x01);
                            } else {
                                bumper_status_.fetch_and(~0x01);
                            }
                        }
                    } else if (ev.code == back_bumper_code_) { // Back bumper
                        if (is_triggered || is_released) {
                            // Only log state changes to avoid flooding
                            if (is_triggered != back_bumper_triggered_.load()) {
                                RCLCPP_INFO(this->get_logger(), "Back bumper %s",
                                          is_triggered ? "TRIGGERED" : "RELEASED");

                                // Report health status for emergency stop
                                if (is_triggered) {
                                    health_provider_->reportHealth(EMERGENCY_STOP_BACK_ERROR,
                                                                 "Back emergency stop button triggered");
                                } else {
                                    health_provider_->removeHealth(EMERGENCY_STOP_BACK_ERROR);
                                }
                            }

                            back_bumper_triggered_.store(is_triggered);

                            // Update bumper status
                            if (is_triggered) {
                                bumper_status_.fetch_or(0x02);
                            } else {
                                bumper_status_.fetch_and(~0x02);
                            }
                        }
                    }
                }
            }
        }
        else
        {
            RCLCPP_DEBUG(this->get_logger(), "No input events to process");
        }
    }
}

void BumperSensor::publishBumperState()
{
    auto msg = std::make_unique<sl_vcu_all::msg::BumperState>();
    
    msg->header.stamp = this->now();
    msg->header.frame_id = "bumper_link";
    msg->front_bumper_triggered = front_bumper_triggered_.load();
    msg->back_bumper_triggered = back_bumper_triggered_.load();
    msg->bumper_status = bumper_status_.load();
    
    bumper_state_pub_->publish(std::move(msg));
}

} // namespace sl_vcu_all

// Main entry point
int main(int argc, char ** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<sl_vcu_all::BumperSensor>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
} 