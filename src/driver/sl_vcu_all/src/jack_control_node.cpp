#include "sl_vcu_all/jack_control_node.hpp"
#include <cstring>
#include <functional>

namespace sl_vcu_all
{

JackControlNode::JackControlNode(const rclcpp::NodeOptions & options)
    : Node("jack_control", options),
      socket_fd_(-1),
      running_(false),
      current_stage_(JACK_STAGE_INIT),
      request_stage_(JACK_REQUEST_STAGE_WAIT_TO_SEND),
      current_position_(0),
      current_status_(0),
      current_alarm_(0),
      target_position_(0),
      target_speed_(0),
      awaiting_response_(false),
      expected_response_index_(0),
      expected_response_subindex_(0),
      response_received_(false),
      last_send_time_(std::chrono::steady_clock::now())
{
    RCLCPP_INFO(this->get_logger(), "Initializing Jack Control Node");

    // Initialize parameters
    initParameters();

    // Initialize health provider
    health_provider_ = std::make_unique<rslamware::health::HealthProvider>(
        shared_from_this(), "jack_control");

    // Check if node is enabled
    if (!enable_) {
        RCLCPP_INFO(this->get_logger(), "Jack Control Node is disabled. Exiting without initialization.");
        return;
    }

    // Initialize SocketCAN
    if (!initSocketCan()) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize SocketCAN");
        return;
    }
    
    // Create action server
    action_server_ = rclcpp_action::create_server<JackControlAction>(
        this,
        "jack_control",
        std::bind(&JackControlNode::handle_goal, this, std::placeholders::_1, std::placeholders::_2),
        std::bind(&JackControlNode::handle_cancel, this, std::placeholders::_1),
        std::bind(&JackControlNode::handle_accepted, this, std::placeholders::_1));
    
    // Create control timer
    control_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(control_cycle_ms_),
        std::bind(&JackControlNode::controlTimerCallback, this));
    
    // Start receive and send threads
    running_ = true;
    receive_thread_ = std::thread(&JackControlNode::receiveCanFramesThread, this);
    send_thread_ = std::thread(&JackControlNode::sendCanFramesThread, this);

    // Initialize timing
    last_heartbeat_time_ = this->now();
    last_request_time_ = this->now();
    stage_start_time_ = this->now();

    RCLCPP_INFO(this->get_logger(), "Jack Control Node initialized successfully");

    // Start automatic base detection if enabled
    if (auto_detect_base_on_start_) {
        RCLCPP_INFO(this->get_logger(), "Automatic base detection on startup is enabled");
        // Start automatic base detection after a short delay to ensure everything is ready
        auto auto_detect_timer = this->create_wall_timer(
            std::chrono::milliseconds(2000),  // 2 second delay
            [this]() {
                this->startAutomaticBaseDetection();
            });

        // Make it a one-shot timer by storing it and letting it self-destruct
        auto_detect_timer_ = auto_detect_timer;
    } else {
        RCLCPP_INFO(this->get_logger(), "Automatic base detection on startup is disabled");
    }
}

JackControlNode::~JackControlNode()
{
    shutdown();
}

void JackControlNode::shutdown()
{
    static std::atomic<bool> shutdown_called{false};

    // Prevent multiple shutdown calls
    if (shutdown_called.exchange(true)) {
        return;
    }

    RCLCPP_INFO(this->get_logger(), "Shutting down Jack Control Node");

    // Stop threads
    running_ = false;

    // Cancel any active goals to prevent feedback publishing
    {
        std::lock_guard<std::mutex> lock(action_mutex_);
        if (current_goal_handle_ && current_goal_handle_->is_active()) {
            try {
                auto result = std::make_shared<JackControlAction::Result>();
                result->success = false;
                result->message = "Node shutting down";
                current_goal_handle_->abort(result);
            } catch (const std::exception& e) {
                RCLCPP_DEBUG(this->get_logger(), "Exception while aborting goal during shutdown: %s", e.what());
            }
            current_goal_handle_.reset();
        }
    }

    // Close SocketCAN first to interrupt blocking read() calls
    closeSocketCan();

    // Notify send thread to wake up and exit
    send_queue_cv_.notify_all();

    // Wait for threads to finish with timeout
    if (receive_thread_.joinable()) {
        RCLCPP_INFO(this->get_logger(), "Waiting for CAN receive thread to stop...");
        receive_thread_.join();
        RCLCPP_INFO(this->get_logger(), "CAN receive thread stopped");
    }

    if (send_thread_.joinable()) {
        RCLCPP_INFO(this->get_logger(), "Waiting for CAN send thread to stop...");
        send_thread_.join();
        RCLCPP_INFO(this->get_logger(), "CAN send thread stopped");
    }

    RCLCPP_INFO(this->get_logger(), "Jack Control Node shutdown complete");
}

bool JackControlNode::isEnabled() const
{
    return enable_;
}

void JackControlNode::initParameters()
{
    // Node enable parameter
    this->declare_parameter("enable", true);
    enable_ = this->get_parameter("enable").as_bool();

    // CAN interface
    this->declare_parameter("can_interface", "can0");
    can_interface_ = this->get_parameter("can_interface").as_string();
    
    // Control timing
    this->declare_parameter("control_cycle_ms", 50);
    control_cycle_ms_ = this->get_parameter("control_cycle_ms").as_int();
    
    this->declare_parameter("response_timeout_ms", 1000);
    response_timeout_ms_ = this->get_parameter("response_timeout_ms").as_int();
    
    this->declare_parameter("heartbeat_period_ms", 100);
    heartbeat_period_ms_ = this->get_parameter("heartbeat_period_ms").as_int();

    this->declare_parameter("min_send_interval_ms", 5);
    min_send_interval_ms_ = this->get_parameter("min_send_interval_ms").as_int();

    // Jack parameters
    this->declare_parameter("default_speed", 1000);  // RPM
    default_speed_ = this->get_parameter("default_speed").as_int();

    this->declare_parameter("max_speed", 3000);  // RPM
    max_speed_ = this->get_parameter("max_speed").as_int();
    
    this->declare_parameter("max_position", 26000000);
    max_position_ = this->get_parameter("max_position").as_int();
    
    this->declare_parameter("min_position", 0);
    min_position_ = this->get_parameter("min_position").as_int();

    this->declare_parameter("position_tolerance", 1000);
    position_tolerance_ = this->get_parameter("position_tolerance").as_int();

    // Timeout parameters
    this->declare_parameter("up_stage_timeout_s", 90);
    up_stage_timeout_s_ = this->get_parameter("up_stage_timeout_s").as_int();

    this->declare_parameter("down_stage_timeout_s", 90);
    down_stage_timeout_s_ = this->get_parameter("down_stage_timeout_s").as_int();

    this->declare_parameter("base_stage_timeout_s", 45);
    base_stage_timeout_s_ = this->get_parameter("base_stage_timeout_s").as_int();

    this->declare_parameter("movement_start_delay_s", 3);
    movement_start_delay_s_ = this->get_parameter("movement_start_delay_s").as_int();

    this->declare_parameter("movement_check_tolerance", 100);
    movement_check_tolerance_ = this->get_parameter("movement_check_tolerance").as_int();

    this->declare_parameter("status_update_delay_s", 2);
    status_update_delay_s_ = this->get_parameter("status_update_delay_s").as_int();

    this->declare_parameter("auto_detect_base_on_start", true);
    auto_detect_base_on_start_ = this->get_parameter("auto_detect_base_on_start").as_bool();

    RCLCPP_INFO(this->get_logger(), "Parameters initialized:");
    RCLCPP_INFO(this->get_logger(), "  Enable: %s", enable_ ? "true" : "false");
    RCLCPP_INFO(this->get_logger(), "  CAN interface: %s", can_interface_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Control cycle: %d ms", control_cycle_ms_);
    RCLCPP_INFO(this->get_logger(), "  Default speed: %d RPM", default_speed_);
    RCLCPP_INFO(this->get_logger(), "  Max speed: %d RPM", max_speed_);
    RCLCPP_INFO(this->get_logger(), "  Position range: %d - %d", min_position_, max_position_);
    RCLCPP_INFO(this->get_logger(), "  Position tolerance: %d", position_tolerance_);
    RCLCPP_INFO(this->get_logger(), "  Movement start delay: %d seconds", movement_start_delay_s_);
    RCLCPP_INFO(this->get_logger(), "  Movement check tolerance: %d", movement_check_tolerance_);
    RCLCPP_INFO(this->get_logger(), "  Status update delay: %d seconds", status_update_delay_s_);
    RCLCPP_INFO(this->get_logger(), "  Auto detect base on start: %s", auto_detect_base_on_start_ ? "true" : "false");
}

bool JackControlNode::initSocketCan()
{
    // Create socket
    socket_fd_ = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (socket_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to create CAN socket: %s", strerror(errno));
        return false;
    }
    
    // Get interface index
    struct ifreq ifr;
    strcpy(ifr.ifr_name, can_interface_.c_str());
    if (ioctl(socket_fd_, SIOCGIFINDEX, &ifr) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to get interface index for %s: %s", 
                     can_interface_.c_str(), strerror(errno));
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }
    
    // Bind socket
    struct sockaddr_can addr;
    memset(&addr, 0, sizeof(addr));
    addr.can_family = AF_CAN;
    addr.can_ifindex = ifr.ifr_ifindex;
    
    if (bind(socket_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to bind CAN socket: %s", strerror(errno));
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }
    
    // Set up CAN filter for jack responses
    struct can_filter filter;
    filter.can_id = JACK_DATA_RESPONSE_ID;
    filter.can_mask = CAN_SFF_MASK;  // Standard frame format mask
    
    if (setsockopt(socket_fd_, SOL_CAN_RAW, CAN_RAW_FILTER, &filter, sizeof(filter)) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to set CAN filter: %s", strerror(errno));
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }

    // Set receive timeout to allow graceful shutdown
    struct timeval timeout;
    timeout.tv_sec = 0;
    timeout.tv_usec = 100000;  // 100ms timeout
    if (setsockopt(socket_fd_, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        RCLCPP_WARN(this->get_logger(), "Failed to set socket receive timeout: %s", strerror(errno));
        // Continue anyway, this is not critical
    }

    RCLCPP_INFO(this->get_logger(), "SocketCAN initialized on interface %s", can_interface_.c_str());
    return true;
}

void JackControlNode::closeSocketCan()
{
    if (socket_fd_ >= 0) {
        close(socket_fd_);
        socket_fd_ = -1;
        RCLCPP_INFO(this->get_logger(), "SocketCAN closed");
    }
}

bool JackControlNode::sendCanFrame(const jack_bk_protocol_t& protocol)
{
    if (socket_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "CAN socket not initialized");
        return false;
    }
    
    struct can_frame frame;
    memset(&frame, 0, sizeof(frame));
    
    frame.can_id = JACK_DATA_REQUEST_ID;
    frame.can_dlc = sizeof(jack_bk_protocol_t);
    memcpy(frame.data, &protocol, sizeof(jack_bk_protocol_t));
    
    ssize_t bytes_sent = write(socket_fd_, &frame, sizeof(frame));
    if (bytes_sent != sizeof(frame)) {
        RCLCPP_ERROR(this->get_logger(), "Failed to send CAN frame: %s", strerror(errno));
        return false;
    }
    
    RCLCPP_DEBUG(this->get_logger(), "Sent CAN frame: cmd=0x%02X, index=0x%04X, sub=0x%02X, data=0x%08X",
                 protocol.cmd, protocol.index, protocol.sub_index, protocol.data);
    return true;
}

bool JackControlNode::queueCanFrameForSend(const jack_bk_protocol_t& protocol)
{
    {
        std::lock_guard<std::mutex> lock(send_queue_mutex_);
        send_queue_.push(protocol);
    }
    send_queue_cv_.notify_one();

    RCLCPP_DEBUG(this->get_logger(), "CAN frame queued for send: cmd=0x%02X, index=0x%04X, sub=0x%02X, data=0x%08X",
                 protocol.cmd, protocol.index, protocol.sub_index, protocol.data);
    return true;
}

void JackControlNode::sendCanFramesThread()
{
    RCLCPP_INFO(this->get_logger(), "CAN send thread started");

    while (running_) {
        std::unique_lock<std::mutex> lock(send_queue_mutex_);

        // Wait for frames to send or shutdown signal
        send_queue_cv_.wait(lock, [this] { return !send_queue_.empty() || !running_; });

        if (!running_) {
            break;
        }

        while (!send_queue_.empty() && running_) {
            auto protocol = send_queue_.front();
            send_queue_.pop();
            lock.unlock();

            // Check timing interval
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::microseconds>(now - last_send_time_).count();

            if (elapsed < min_send_interval_ms_ * 1000) {
                // Too soon, wait for the remaining time
                auto wait_time = min_send_interval_ms_ * 1000 - elapsed;
                RCLCPP_DEBUG(this->get_logger(), "Send interval too small (%ld us), waiting %ld us", elapsed, wait_time);
                std::this_thread::sleep_for(std::chrono::microseconds(wait_time));
            }

            // Send the frame
            sendCanFrame(protocol);
            last_send_time_ = std::chrono::steady_clock::now();

            lock.lock();
        }
    }

    RCLCPP_INFO(this->get_logger(), "CAN send thread stopped");
}

void JackControlNode::receiveCanFramesThread()
{
    RCLCPP_INFO(this->get_logger(), "CAN receive thread started");

    while (running_ && socket_fd_ >= 0) {
        struct can_frame frame;
        ssize_t bytes_received = read(socket_fd_, &frame, sizeof(frame));

        if (bytes_received == sizeof(frame)) {
            processCanFrame(frame);
        } else if (bytes_received < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                // Timeout or no data available, continue (this is normal with SO_RCVTIMEO)
                continue;
            } else if (errno == EBADF || errno == ENOTSOCK) {
                // Socket closed, exit gracefully
                RCLCPP_DEBUG(this->get_logger(), "CAN socket closed, receive thread exiting");
                break;
            } else if (running_) {
                // Only log error if we're still supposed to be running
                RCLCPP_ERROR(this->get_logger(), "Failed to receive CAN frame: %s", strerror(errno));
                break;
            }
        } else if (bytes_received == 0) {
            // Socket closed by peer
            RCLCPP_DEBUG(this->get_logger(), "CAN socket closed by peer");
            break;
        }
    }

    RCLCPP_INFO(this->get_logger(), "CAN receive thread stopped");
}

void JackControlNode::processCanFrame(const struct can_frame& frame)
{
    if (frame.can_id != JACK_DATA_RESPONSE_ID || frame.can_dlc != sizeof(jack_bk_protocol_t)) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(jack_mutex_);
    
    memcpy(&last_response_, frame.data, sizeof(jack_bk_protocol_t));
    response_received_ = true;
    
    RCLCPP_DEBUG(this->get_logger(), "Received CAN response: cmd=0x%02X, index=0x%04X, sub=0x%02X, data=0x%08X",
                 last_response_.cmd, last_response_.index, last_response_.sub_index, last_response_.data);
    
    // Process response based on register
    if (last_response_.index == JACK_INDEX_STATUS) {
        current_status_ = last_response_.data;
        RCLCPP_DEBUG(this->get_logger(), "Status updated: 0x%04X", current_status_);
    } else if (last_response_.index == JACK_INDEX_ALARM) {
        current_alarm_ = last_response_.data;
        RCLCPP_DEBUG(this->get_logger(), "Alarm updated: 0x%04X", current_alarm_);
    } else if (last_response_.index == JACK_INDEX_ACTUAL_POS) {
        int32_t old_position = current_position_;
        current_position_ = static_cast<int32_t>(last_response_.data);
        RCLCPP_DEBUG(this->get_logger(), "Position updated: %d (was %d)", current_position_, old_position);

        // Log warning if position seems invalid
        if (current_position_ > max_position_ + 1000000) {
            RCLCPP_WARN(this->get_logger(), "Received potentially invalid position: %d (max expected: %d)",
                       current_position_, max_position_);
        }
    }
    
    // Check if this is the response we're waiting for
    if (awaiting_response_ && 
        last_response_.index == expected_response_index_ && 
        last_response_.sub_index == expected_response_subindex_) {
        
        if (last_response_.cmd == JACK_CMD_READ_RESPONSE_ERROR || 
            last_response_.cmd == JACK_CMD_WRITE_RESPONSE_ERROR) {
            request_stage_ = JACK_REQUEST_STAGE_RESPONSE_ERROR;
        } else {
            request_stage_ = JACK_REQUEST_STAGE_RESPONSE_OK;
        }
        awaiting_response_ = false;
    }
}

bool JackControlNode::sendJackCommand(uint16_t index, uint8_t sub_index, uint32_t data, uint8_t cmd)
{
    std::lock_guard<std::mutex> lock(jack_mutex_);
    return sendJackCommandUnlocked(index, sub_index, data, cmd);
}

bool JackControlNode::sendJackCommandUnlocked(uint16_t index, uint8_t sub_index, uint32_t data, uint8_t cmd)
{
    jack_bk_protocol_t protocol;
    memset(&protocol, 0, sizeof(protocol));

    protocol.cmd = cmd;
    protocol.index = index;
    protocol.sub_index = sub_index;
    protocol.data = data;

    if (!queueCanFrameForSend(protocol)) {
        return false;
    }

    // Set up response tracking
    awaiting_response_ = true;
    expected_response_index_ = index;
    expected_response_subindex_ = sub_index;
    request_stage_ = JACK_REQUEST_STAGE_WAIT_RESPONSE;
    last_request_time_ = this->now();

    return true;
}

bool JackControlNode::waitForResponse(uint32_t timeout_ms)
{
    auto start_time = this->now();

    while (rclcpp::ok() && running_) {
        {
            std::lock_guard<std::mutex> lock(jack_mutex_);
            if (request_stage_ == JACK_REQUEST_STAGE_RESPONSE_OK) {
                return true;
            } else if (request_stage_ == JACK_REQUEST_STAGE_RESPONSE_ERROR) {
                return false;
            }
        }

        auto elapsed = (this->now() - start_time).nanoseconds() / 1000000;  // Convert to ms
        if (elapsed > timeout_ms) {
            RCLCPP_WARN(this->get_logger(), "Response timeout after %d ms", timeout_ms);
            std::lock_guard<std::mutex> lock(jack_mutex_);
            awaiting_response_ = false;
            request_stage_ = JACK_REQUEST_STAGE_RESPONSE_ERROR;
            return false;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    return false;
}

void JackControlNode::updateJackStatus()
{
    // Request current position
    sendJackCommand(JACK_INDEX_ACTUAL_POS, JACK_SUB_INDEX_ACTUAL_POS, 0, JACK_CMD_READ_REQUEST);

    // Request status
    sendJackCommand(JACK_INDEX_STATUS, JACK_SUB_INDEX_STATUS, 0, JACK_CMD_READ_REQUEST);

    // Request alarm status
    sendJackCommand(JACK_INDEX_ALARM, JACK_SUB_INDEX_ALARM, 0, JACK_CMD_READ_REQUEST);
}

void JackControlNode::controlTimerCallback()
{
    auto now = this->now();

    // Update jack status periodically (outside of jack_mutex_ to avoid deadlock)
    auto elapsed_heartbeat = (now - last_heartbeat_time_).nanoseconds() / 1000000;
    if (elapsed_heartbeat >= heartbeat_period_ms_) {
        updateJackStatus();
        last_heartbeat_time_ = now;
    }

    // Process state machine
    std::lock_guard<std::mutex> lock(jack_mutex_);

    switch (current_stage_) {
        case JACK_STAGE_INIT:
            processStageInit();
            break;
        case JACK_STAGE_DETECTING_BASE:
            processStageDetectingBase();
            break;
        case JACK_STAGE_BASE_STOP:
            processStageBaseStop();
            break;
        case JACK_STAGE_LIFTING_UP:
            processStageLiftingUp();
            break;
        case JACK_STAGE_LIFTING_DOWN:
            processStageLiftingDown();
            break;
        case JACK_STAGE_TOP_STOP:
            processStageTopStop();
            break;
        case JACK_STAGE_MIDDLE_STOP:
            processStageMiddleStop();
            break;
        default:
            processStageMiddleStop();
            break;
    }

    // Send feedback if we have an active goal
    if (current_goal_handle_ && current_goal_handle_->is_active()) {
        try {
            auto feedback = std::make_shared<JackControlAction::Feedback>();
            feedback->current_stage = stageToString(current_stage_);
            feedback->current_position = current_position_;
            feedback->current_status = current_status_;
            feedback->current_alarm = current_alarm_;
            feedback->progress = calculateProgress();

            current_goal_handle_->publish_feedback(feedback);
        } catch (const std::exception& e) {
            RCLCPP_DEBUG(this->get_logger(), "Failed to publish feedback: %s", e.what());
            // Clear the goal handle if it's invalid
            std::lock_guard<std::mutex> lock(action_mutex_);
            current_goal_handle_.reset();
        }
    }
}

rclcpp_action::GoalResponse JackControlNode::handle_goal(
    const rclcpp_action::GoalUUID & uuid,
    std::shared_ptr<const JackControlAction::Goal> goal)
{
    (void)uuid;

    RCLCPP_INFO(this->get_logger(), "Received goal request: command=%s", goal->command.c_str());

    // Validate command
    if (goal->command != "detect_base" &&
        goal->command != "lift_up" &&
        goal->command != "lift_down" &&
        goal->command != "stop" &&
        goal->command != "clear_alarm") {
        RCLCPP_WARN(this->get_logger(), "Invalid command: %s", goal->command.c_str());
        return rclcpp_action::GoalResponse::REJECT;
    }

    // Check if another goal is already active
    std::lock_guard<std::mutex> action_lock(action_mutex_);
    std::lock_guard<std::mutex> jack_lock(jack_mutex_);

    if (current_goal_handle_ && current_goal_handle_->is_active()) {
        // Special case: if this is a stop command, cancel the current goal and accept the new one
        if (goal->command == "stop") {
            RCLCPP_INFO(this->get_logger(), "Stop command received - canceling current goal");
            try {
                auto result = std::make_shared<JackControlAction::Result>();
                result->success = false;
                result->message = "Cancelled by stop command";
                current_goal_handle_->abort(result);
            } catch (const std::exception& e) {
                RCLCPP_DEBUG(this->get_logger(), "Exception while aborting goal for stop: %s", e.what());
            }
            current_goal_handle_.reset();
            current_command_.clear();
            // Continue to accept the stop goal
        } else {
            RCLCPP_WARN(this->get_logger(), "Another goal is already active");
            return rclcpp_action::GoalResponse::REJECT;
        }
    }

    // Log current system state for debugging
    RCLCPP_INFO(this->get_logger(), "Accepting goal '%s' - current stage: %s, position: %d, status: 0x%04X",
               goal->command.c_str(), stageToString(current_stage_).c_str(), current_position_, current_status_);

    return rclcpp_action::GoalResponse::ACCEPT_AND_EXECUTE;
}

rclcpp_action::CancelResponse JackControlNode::handle_cancel(
    const std::shared_ptr<GoalHandleJackControl> goal_handle)
{
    RCLCPP_INFO(this->get_logger(), "Received request to cancel goal");

    // Use consistent lock ordering: action_mutex_ first, then jack_mutex_
    std::lock_guard<std::mutex> action_lock(action_mutex_);
    std::lock_guard<std::mutex> jack_lock(jack_mutex_);

    if (current_goal_handle_ == goal_handle) {
        std::string cancelled_command = current_command_;
        jack_stage_t cancelled_stage = current_stage_;

        // Send stop command to jack - critical for safety during lift operations
        current_command_ = "stop";
        current_stage_ = JACK_STAGE_MIDDLE_STOP;

        // Send the brake command to stop the jack immediately
        sendJackCommandUnlocked(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_BRAKE, JACK_CMD_WRITE_REQUEST_2BYTE);

        RCLCPP_INFO(this->get_logger(), "Goal cancelled - command: '%s', stage: %s, brake command sent",
                   cancelled_command.c_str(), stageToString(cancelled_stage).c_str());
    }

    return rclcpp_action::CancelResponse::ACCEPT;
}

void JackControlNode::handle_accepted(const std::shared_ptr<GoalHandleJackControl> goal_handle)
{
    // Start executing the goal in a separate thread
    std::thread{std::bind(&JackControlNode::execute_goal, this, goal_handle)}.detach();
}

void JackControlNode::execute_goal(const std::shared_ptr<GoalHandleJackControl> goal_handle)
{
    RCLCPP_INFO(this->get_logger(), "Executing goal: %s", goal_handle->get_goal()->command.c_str());

    {
        std::lock_guard<std::mutex> lock(action_mutex_);
        current_goal_handle_ = goal_handle;
        current_command_ = goal_handle->get_goal()->command;
        action_start_time_ = this->now();
    }

    auto goal = goal_handle->get_goal();
    auto result = std::make_shared<JackControlAction::Result>();

    // Reset system state and set target parameters
    {
        std::lock_guard<std::mutex> lock(jack_mutex_);

        // Reset any error states and ensure clean start
        awaiting_response_ = false;
        request_stage_ = JACK_REQUEST_STAGE_WAIT_TO_SEND;

        // Set target parameters
        if (goal->target_position > 0) {
            target_position_ = std::min(static_cast<int32_t>(goal->target_position), max_position_);
        } else {
            // Set default target position based on command
            if (goal->command == "lift_down") {
                target_position_ = min_position_;  // Default to min for lift_down
            } else {
                target_position_ = max_position_;  // Default to max for lift_up
            }
        }

        if (goal->speed > 0) {
            if (validateSpeed(goal->speed)) {
                target_speed_ = goal->speed;
            } else {
                target_speed_ = default_speed_;
                RCLCPP_WARN(this->get_logger(), "Invalid speed %d, using default speed %d", goal->speed, default_speed_);
            }
        } else {
            target_speed_ = default_speed_;
        }

        RCLCPP_INFO(this->get_logger(), "Action parameters set: target_position=%d, target_speed=%d, current_stage=%s",
                   target_position_, target_speed_, stageToString(current_stage_).c_str());
    }

    // Execute command
    bool success = false;
    std::string message;

    if (goal->command == "detect_base") {
        success = executeDetectBase();
        message = success ? "Base detection completed successfully" : "Base detection failed";
    } else if (goal->command == "lift_up") {
        success = executeLiftUp();
        message = success ? "Lift up completed successfully" : "Lift up failed";
    } else if (goal->command == "lift_down") {
        success = executeLiftDown();
        message = success ? "Lift down completed successfully" : "Lift down failed";
    } else if (goal->command == "stop") {
        success = executeStop();
        message = success ? "Stop completed successfully" : "Stop failed";
    } else if (goal->command == "clear_alarm") {
        success = executeClearAlarm();
        message = success ? "Alarm cleared successfully" : "Clear alarm failed";
    }

    // Prepare result
    {
        std::lock_guard<std::mutex> lock(jack_mutex_);
        result->success = success;
        result->message = message;
        result->final_position = current_position_;
        result->final_status = current_status_;
        result->alarm_code = current_alarm_;
    }

    // Complete the goal and cleanup state
    {
        std::lock_guard<std::mutex> jack_lock(jack_mutex_);
        std::lock_guard<std::mutex> action_lock(action_mutex_);

        // Reset any pending response states
        awaiting_response_ = false;
        request_stage_ = JACK_REQUEST_STAGE_WAIT_TO_SEND;

        // Complete the goal
        if (success) {
            goal_handle->succeed(result);
            RCLCPP_INFO(this->get_logger(), "Goal succeeded: %s", message.c_str());
        } else {
            goal_handle->abort(result);
            RCLCPP_ERROR(this->get_logger(), "Goal aborted: %s", message.c_str());

            // On failure, ensure jack is in a safe state
            if (current_stage_ != JACK_STAGE_BASE_STOP && current_stage_ != JACK_STAGE_TOP_STOP) {
                RCLCPP_INFO(this->get_logger(), "Setting stage to middle_stop after failed operation");
                current_stage_ = JACK_STAGE_MIDDLE_STOP;
            }
        }

        // Clear current goal
        current_goal_handle_.reset();
        current_command_.clear();
    }
}

bool JackControlNode::executeDetectBase()
{
    RCLCPP_INFO(this->get_logger(), "Starting base detection");

    // Set mode to detect base
    if (!sendJackCommand(JACK_INDEX_MODE, JACK_SUB_INDEX_MODE, JACK_MODE_DETECT_BASE, JACK_CMD_WRITE_REQUEST_1BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Start detect base command
    if (!sendJackCommand(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_DETECT_BASE, JACK_CMD_WRITE_REQUEST_2BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Set stage to detecting base and start timing
    {
        std::lock_guard<std::mutex> lock(jack_mutex_);
        current_stage_ = JACK_STAGE_DETECTING_BASE;
        stage_start_time_ = this->now();
    }

    // Wait for base detection to complete
    while (rclcpp::ok() && running_) {
        {
            std::lock_guard<std::mutex> lock(jack_mutex_);
            // Check for base detection using JACK_BK_STATUS_GET_BASE
            if (current_status_ & JACK_STATUS_GET_BASE) {
                current_stage_ = JACK_STAGE_BASE_STOP;
                RCLCPP_INFO(this->get_logger(), "Base detected successfully (status: 0x%04X, position: %d)", current_status_, current_position_);
                return true;
            }

            // Also check if we reached target position (alternative success condition)
            if (current_status_ & JACK_STATUS_REACH_TRAGET) {
                // For base detection, we expect to be near the minimum position
                if (current_position_ <= min_position_ + position_tolerance_ * 10) {  // More lenient for base detection
                    current_stage_ = JACK_STAGE_BASE_STOP;
                    RCLCPP_INFO(this->get_logger(), "Base position reached (status: 0x%04X, position: %d)", current_status_, current_position_);
                    return true;
                } else {
                    RCLCPP_DEBUG(this->get_logger(), "Target reached but not at base position: current=%d, min=%d", current_position_, min_position_);
                }
            }

            // Check for stage timeout
            if (checkStageTimeout()) {
                return false;
            }
        }

        // Check for cancellation
        if (current_goal_handle_ && current_goal_handle_->is_canceling()) {
            RCLCPP_INFO(this->get_logger(), "Base detection cancelled");
            return false;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    return false;
}

bool JackControlNode::executeLiftUp()
{
    uint32_t speed_can_value = convertRpmToCanValue(target_speed_);
    RCLCPP_INFO(this->get_logger(), "Starting lift up to position %d with speed %d RPM (CAN value: %d)",
                target_position_, target_speed_, speed_can_value);

    // Validate speed
    if (!validateSpeed(target_speed_)) {
        RCLCPP_ERROR(this->get_logger(), "Invalid speed for lift up: %d RPM", target_speed_);
        return false;
    }

    // Set speed (convert RPM to CAN value)
    if (!sendJackCommand(JACK_INDEX_TARGET_SPEED, JACK_SUB_INDEX_TARGET_SPEED, speed_can_value, JACK_CMD_WRITE_REQUEST_4BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Set target position
    if (!sendJackCommand(JACK_INDEX_TARGET_POS, JACK_SUB_INDEX_TARGET_POS, target_position_, JACK_CMD_WRITE_REQUEST_4BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Set mode to position mode
    if (!sendJackCommand(JACK_INDEX_MODE, JACK_SUB_INDEX_MODE, JACK_MODE_POS, JACK_CMD_WRITE_REQUEST_1BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Start lift up (brake release)
    RCLCPP_INFO(this->get_logger(), "Releasing brake for lift up operation");
    health_provider_->reportHealth(BRAKE_RELEASE_ERROR, "Brake released for jack lift up operation");

    if (!sendJackCommand(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_AUTO_POS_CHG, JACK_CMD_WRITE_REQUEST_2BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Set stage to lifting up and start timing
    int32_t initial_position;
    {
        std::lock_guard<std::mutex> lock(jack_mutex_);
        current_stage_ = JACK_STAGE_LIFTING_UP;
        stage_start_time_ = this->now();
        initial_position = current_position_;
    }

    // Check if movement has started after delay
    if (!checkMovementStarted(initial_position)) {
        RCLCPP_ERROR(this->get_logger(), "Jack failed to start moving up");
        return false;
    }

    // Wait for lift to complete
    return waitForPositionReached();
}

bool JackControlNode::executeLiftDown()
{
    uint32_t speed_can_value = convertRpmToCanValue(target_speed_);
    RCLCPP_INFO(this->get_logger(), "Starting lift down to position %d with speed %d RPM (CAN value: %d)",
                min_position_, target_speed_, speed_can_value);

    // Validate speed
    if (!validateSpeed(target_speed_)) {
        RCLCPP_ERROR(this->get_logger(), "Invalid speed for lift down: %d RPM", target_speed_);
        return false;
    }

    // Set speed (convert RPM to CAN value)
    if (!sendJackCommand(JACK_INDEX_TARGET_SPEED, JACK_SUB_INDEX_TARGET_SPEED, speed_can_value, JACK_CMD_WRITE_REQUEST_4BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Set target position to minimum (base)
    if (!sendJackCommand(JACK_INDEX_TARGET_POS, JACK_SUB_INDEX_TARGET_POS, min_position_, JACK_CMD_WRITE_REQUEST_4BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Set mode to position mode
    if (!sendJackCommand(JACK_INDEX_MODE, JACK_SUB_INDEX_MODE, JACK_MODE_POS, JACK_CMD_WRITE_REQUEST_1BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Start lift down (brake release)
    RCLCPP_INFO(this->get_logger(), "Releasing brake for lift down operation");
    health_provider_->reportHealth(BRAKE_RELEASE_ERROR, "Brake released for jack lift down operation");

    if (!sendJackCommand(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_AUTO_POS_CHG, JACK_CMD_WRITE_REQUEST_2BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Set stage to lifting down and start timing
    int32_t initial_position;
    {
        std::lock_guard<std::mutex> lock(jack_mutex_);
        current_stage_ = JACK_STAGE_LIFTING_DOWN;
        stage_start_time_ = this->now();
        initial_position = current_position_;
        // Update target_position_ to match the actual target for lift down
        target_position_ = min_position_;
    }

    // Check if movement has started after delay
    if (!checkMovementStarted(initial_position)) {
        RCLCPP_ERROR(this->get_logger(), "Jack failed to start moving down");
        return false;
    }

    // Wait for lift to complete
    return waitForPositionReached();
}

bool JackControlNode::executeStop()
{
    RCLCPP_INFO(this->get_logger(), "Stopping jack");

    // Send brake command and clear brake release health alert
    health_provider_->removeHealth(BRAKE_RELEASE_ERROR);

    if (!sendJackCommand(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_BRAKE, JACK_CMD_WRITE_REQUEST_2BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Set stage to middle stop
    {
        std::lock_guard<std::mutex> lock(jack_mutex_);
        current_stage_ = JACK_STAGE_MIDDLE_STOP;
    }

    return true;
}

bool JackControlNode::executeClearAlarm()
{
    RCLCPP_INFO(this->get_logger(), "Clearing alarm");

    // Send clear alarm command
    if (!sendJackCommand(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_ALARM_CLS, JACK_CMD_WRITE_REQUEST_2BYTE)) {
        return false;
    }
    if (!waitForResponse()) return false;

    // Wait a bit for alarm to clear
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // Check if alarm is cleared
    updateJackStatus();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    {
        std::lock_guard<std::mutex> lock(jack_mutex_);
        if (current_alarm_ == 0) {
            RCLCPP_INFO(this->get_logger(), "Alarm cleared successfully");
            return true;
        } else {
            RCLCPP_WARN(this->get_logger(), "Alarm still present: 0x%04X", current_alarm_);
            return false;
        }
    }
}

bool JackControlNode::waitForPositionReached()
{
    // Add initial delay to allow CAN communication to update status and position
    // when movement just started
    RCLCPP_DEBUG(this->get_logger(), "Starting position monitoring with %d second delay for status updates", status_update_delay_s_);
    auto start_time = this->now();
    bool initial_delay_passed = false;

    while (rclcpp::ok() && running_) {
        // Wait for initial delay before checking status flags and position
        if (!initial_delay_passed) {
            auto elapsed_seconds = (this->now() - start_time).nanoseconds() / 1000000000;
            if (elapsed_seconds < status_update_delay_s_) {
                // Check for cancellation during initial delay
                if (current_goal_handle_ && current_goal_handle_->is_canceling()) {
                    RCLCPP_INFO(this->get_logger(), "Position movement cancelled during initial delay - sending brake command");

                    // Send brake command to stop the motor immediately for safety
                    std::lock_guard<std::mutex> lock(jack_mutex_);
                    sendJackCommandUnlocked(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_BRAKE, JACK_CMD_WRITE_REQUEST_2BYTE);
                    current_stage_ = JACK_STAGE_MIDDLE_STOP;

                    return false;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                continue;
            } else {
                initial_delay_passed = true;
                RCLCPP_DEBUG(this->get_logger(), "Initial delay passed, starting position monitoring");
            }
        }

        {
            std::lock_guard<std::mutex> lock(jack_mutex_);

            // Check if target position is reached via status flag
            if (current_status_ & JACK_STATUS_REACH_TRAGET) {
                // Determine the correct target position based on current stage and target_position_
                int32_t target_pos;
                if (current_stage_ == JACK_STAGE_LIFTING_DOWN) {
                    target_pos = min_position_;
                } else if (current_stage_ == JACK_STAGE_LIFTING_UP) {
                    target_pos = target_position_;
                } else {
                    // For other stages, use the current target_position_
                    target_pos = target_position_;
                }

                RCLCPP_INFO(this->get_logger(), "Position reached flag detected: stage=%s, target_pos=%d, current_pos=%d, target_position_=%d",
                           stageToString(current_stage_).c_str(), target_pos, current_position_, target_position_);

                // Check if position reading seems valid (not extremely large)
                bool position_valid = (current_position_ <= max_position_ + 1000000);

                if (position_valid && checkPositionTolerance(target_pos, current_position_)) {
                    if (current_stage_ == JACK_STAGE_LIFTING_UP) {
                        current_stage_ = JACK_STAGE_TOP_STOP;
                    } else if (current_stage_ == JACK_STAGE_LIFTING_DOWN) {
                        current_stage_ = JACK_STAGE_BASE_STOP;
                    }
                    RCLCPP_INFO(this->get_logger(), "Target position reached: current=%d, target=%d", current_position_, target_pos);
                    return true;
                } else if (!position_valid) {
                    RCLCPP_WARN(this->get_logger(), "Position reached flag set but position reading invalid: current=%d, target=%d",
                               current_position_, target_pos);
                    // Request position update
                    sendJackCommand(JACK_INDEX_ACTUAL_POS, JACK_SUB_INDEX_ACTUAL_POS, 0, JACK_CMD_READ_REQUEST);
                } else {
                    RCLCPP_WARN(this->get_logger(), "Position reached flag set but position not within tolerance: current=%d, target=%d, tolerance=%d, stage=%s",
                               current_position_, target_pos, position_tolerance_, stageToString(current_stage_).c_str());

                    // If we're very far from target, there might be a communication issue
                    int32_t diff = (target_pos > current_position_) ? (target_pos - current_position_) : (current_position_ - target_pos);
                    if (diff > max_position_ / 2) {
                        RCLCPP_ERROR(this->get_logger(), "Position difference too large, possible communication error. Aborting operation.");

                        // For base_stop stage with wrong target, consider it successful if position is near base
                        if (current_stage_ == JACK_STAGE_BASE_STOP && current_position_ <= min_position_ + position_tolerance_ * 10) {
                            RCLCPP_INFO(this->get_logger(), "Jack is at base position despite target mismatch, considering operation successful");
                            return true;
                        }

                        // Send brake command to stop the jack safely
                        sendJackCommandUnlocked(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_BRAKE, JACK_CMD_WRITE_REQUEST_2BYTE);
                        current_stage_ = JACK_STAGE_MIDDLE_STOP;
                        return false;
                    } else {
                        RCLCPP_INFO(this->get_logger(), "Position difference within reasonable range (%d), continuing to wait", diff);
                    }
                }
            }

            // Check for stage timeout
            if (checkStageTimeout()) {
                return false;
            }
        }

        // Check for cancellation
        if (current_goal_handle_ && current_goal_handle_->is_canceling()) {
            RCLCPP_INFO(this->get_logger(), "Position movement cancelled - sending brake command");

            // Send brake command to stop the motor immediately for safety
            // Use unlocked version since we already have the jack_mutex_
            sendJackCommandUnlocked(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_BRAKE, JACK_CMD_WRITE_REQUEST_2BYTE);
            current_stage_ = JACK_STAGE_MIDDLE_STOP;

            return false;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    return false;
}

// State machine processing methods
void JackControlNode::processStageInit()
{
    // Send brake command to initialize
    if (request_stage_ == JACK_REQUEST_STAGE_WAIT_TO_SEND) {
        health_provider_->removeHealth(BRAKE_RELEASE_ERROR);
        sendJackCommandUnlocked(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_BRAKE, JACK_CMD_WRITE_REQUEST_2BYTE);
    } else if (request_stage_ == JACK_REQUEST_STAGE_RESPONSE_OK) {
        current_stage_ = JACK_STAGE_MIDDLE_STOP;
        request_stage_ = JACK_REQUEST_STAGE_WAIT_TO_SEND;
    }
}

void JackControlNode::processStageDetectingBase()
{
    // Check if base is detected using JACK_BK_STATUS_GET_BASE
    if (current_status_ & JACK_STATUS_GET_BASE) {
        current_stage_ = JACK_STAGE_BASE_STOP;
        RCLCPP_INFO(this->get_logger(), "Base detected in state machine (status: 0x%04X)", current_status_);
    }

    // Check for timeout
    if (checkStageTimeout()) {
        RCLCPP_ERROR(this->get_logger(), "Base detection timeout in state machine");
        current_stage_ = JACK_STAGE_MIDDLE_STOP;  // Move to safe state
    }
}

void JackControlNode::processStageBaseStop()
{
    // Jack is at base position and stopped
    // No action needed, waiting for next command
}

void JackControlNode::processStageLiftingUp()
{
    // Check if target position is reached
    if (current_status_ & JACK_STATUS_REACH_TRAGET) {
        // Verify position is within tolerance
        if (checkPositionTolerance(target_position_, current_position_)) {
            current_stage_ = JACK_STAGE_TOP_STOP;
            RCLCPP_INFO(this->get_logger(), "Lift up completed in state machine");
        }
    }

    // Check for timeout
    if (checkStageTimeout()) {
        RCLCPP_ERROR(this->get_logger(), "Lift up timeout in state machine");
        current_stage_ = JACK_STAGE_MIDDLE_STOP;  // Move to safe state
    }
}

void JackControlNode::processStageLiftingDown()
{
    // Check if target position is reached
    if (current_status_ & JACK_STATUS_REACH_TRAGET) {
        // Verify position is within tolerance
        if (checkPositionTolerance(min_position_, current_position_)) {
            current_stage_ = JACK_STAGE_BASE_STOP;
            RCLCPP_INFO(this->get_logger(), "Lift down completed in state machine");
        }
    }

    // Check for timeout
    if (checkStageTimeout()) {
        RCLCPP_ERROR(this->get_logger(), "Lift down timeout in state machine");
        current_stage_ = JACK_STAGE_MIDDLE_STOP;  // Move to safe state
    }
}

void JackControlNode::processStageTopStop()
{
    // Jack is at top position and stopped
    // No action needed, waiting for next command
}

void JackControlNode::processStageMiddleStop()
{
    // Jack is stopped at some middle position
    // No action needed, waiting for next command
}

std::string JackControlNode::stageToString(jack_stage_t stage)
{
    switch (stage) {
        case JACK_STAGE_INIT: return "init";
        case JACK_STAGE_DETECTING_BASE: return "detecting_base";
        case JACK_STAGE_BASE_STOP: return "base_stop";
        case JACK_STAGE_LIFTING_UP: return "lifting_up";
        case JACK_STAGE_LIFTING_DOWN: return "lifting_down";
        case JACK_STAGE_TOP_STOP: return "top_stop";
        case JACK_STAGE_MIDDLE_STOP: return "middle_stop";
        default: return "unknown";
    }
}

float JackControlNode::calculateProgress()
{
    if (current_command_.empty()) {
        return 0.0f;
    }

    if (current_command_ == "detect_base") {
        return (current_stage_ == JACK_STAGE_BASE_STOP) ? 100.0f : 50.0f;
    } else if (current_command_ == "lift_up") {
        if (target_position_ > min_position_) {
            float progress = static_cast<float>(current_position_ - min_position_) /
                           static_cast<float>(target_position_ - min_position_) * 100.0f;
            return std::min(100.0f, std::max(0.0f, progress));
        }
    } else if (current_command_ == "lift_down") {
        if (current_position_ > min_position_) {
            float progress = static_cast<float>(target_position_ - current_position_) /
                           static_cast<float>(target_position_ - min_position_) * 100.0f;
            return std::min(100.0f, std::max(0.0f, progress));
        }
    } else if (current_command_ == "stop" || current_command_ == "clear_alarm") {
        return 100.0f;
    }

    return 0.0f;
}

bool JackControlNode::validateSpeed(uint32_t speed)
{
    if (speed > max_speed_) {
        RCLCPP_WARN(this->get_logger(), "Speed %d RPM exceeds maximum allowed speed %d RPM", speed, max_speed_);
        return false;
    }
    return true;
}

bool JackControlNode::checkPositionTolerance(int32_t target_pos, int32_t current_pos)
{
    // Handle potential overflow/underflow in position values
    if (current_pos > max_position_ + 1000000) {
        RCLCPP_WARN(this->get_logger(), "Current position %d seems invalid (too large)", current_pos);
        return false;
    }

    // Calculate absolute difference safely
    int32_t diff;
    if (target_pos > current_pos) {
        diff = target_pos - current_pos;
    } else {
        diff = current_pos - target_pos;
    }

    bool within_tolerance = diff <= position_tolerance_;

    RCLCPP_DEBUG(this->get_logger(), "Position tolerance check: target=%d, current=%d, diff=%d, tolerance=%d, result=%s",
                target_pos, current_pos, diff, position_tolerance_, within_tolerance ? "PASS" : "FAIL");

    return within_tolerance;
}

bool JackControlNode::checkStageTimeout()
{
    auto now = this->now();
    auto elapsed_seconds = (now - stage_start_time_).nanoseconds() / 1000000000;  // Convert to seconds

    switch (current_stage_) {
        case JACK_STAGE_DETECTING_BASE:
            if (elapsed_seconds > base_stage_timeout_s_) {
                RCLCPP_ERROR(this->get_logger(), "Base detection stage timeout after %ld seconds", elapsed_seconds);
                return true;
            }
            break;
        case JACK_STAGE_LIFTING_UP:
            if (elapsed_seconds > up_stage_timeout_s_) {
                RCLCPP_ERROR(this->get_logger(), "Lifting up stage timeout after %ld seconds", elapsed_seconds);
                return true;
            }
            break;
        case JACK_STAGE_LIFTING_DOWN:
            if (elapsed_seconds > down_stage_timeout_s_) {
                RCLCPP_ERROR(this->get_logger(), "Lifting down stage timeout after %ld seconds", elapsed_seconds);
                return true;
            }
            break;
        default:
            // No timeout for other stages
            break;
    }
    return false;
}

void JackControlNode::startAutomaticBaseDetection()
{
    RCLCPP_INFO(this->get_logger(), "Starting automatic base detection on first startup");

    // Cancel the timer to make it one-shot
    if (auto_detect_timer_) {
        auto_detect_timer_->cancel();
        auto_detect_timer_.reset();
    }

    // Check if we already have an active goal
    {
        std::lock_guard<std::mutex> lock(action_mutex_);
        if (current_goal_handle_ && current_goal_handle_->is_active()) {
            RCLCPP_INFO(this->get_logger(), "Goal already active, skipping automatic base detection");
            return;
        }
    }

    // Check current status first - if base is already detected, skip
    {
        std::lock_guard<std::mutex> lock(jack_mutex_);
        if (current_status_ & JACK_STATUS_GET_BASE) {
            RCLCPP_INFO(this->get_logger(), "Base already detected (status: 0x%04X), skipping automatic detection", current_status_);
            current_stage_ = JACK_STAGE_BASE_STOP;
            return;
        }
    }

    // Execute base detection in a separate thread
    std::thread([this]() {
        RCLCPP_INFO(this->get_logger(), "Executing automatic base detection");

        {
            std::lock_guard<std::mutex> lock(action_mutex_);
            current_command_ = "detect_base";
            action_start_time_ = this->now();
        }

        // Set default parameters
        {
            std::lock_guard<std::mutex> lock(jack_mutex_);
            target_speed_ = default_speed_;
        }

        // Execute base detection
        bool success = executeDetectBase();

        if (success) {
            RCLCPP_INFO(this->get_logger(), "Automatic base detection completed successfully");
        } else {
            RCLCPP_WARN(this->get_logger(), "Automatic base detection failed");
        }

        // Clear current command
        {
            std::lock_guard<std::mutex> lock(action_mutex_);
            current_command_.clear();
        }
    }).detach();
}

bool JackControlNode::checkMovementStarted(int32_t initial_position)
{
    RCLCPP_INFO(this->get_logger(), "Checking if movement has started from position %d", initial_position);

    // Wait for the specified delay before checking movement
    auto start_time = this->now();
    while (rclcpp::ok() && running_) {
        auto elapsed_seconds = (this->now() - start_time).nanoseconds() / 1000000000;

        if (elapsed_seconds >= movement_start_delay_s_) {
            break;
        }

        // Check for cancellation during delay
        if (current_goal_handle_ && current_goal_handle_->is_canceling()) {
            RCLCPP_INFO(this->get_logger(), "Movement check cancelled - sending brake command");

            // Send brake command to stop the motor immediately for safety
            std::lock_guard<std::mutex> lock(jack_mutex_);
            sendJackCommandUnlocked(JACK_INDEX_CTRL, JACK_SUB_INDEX_CTRL, JACK_CTRL_BRAKE, JACK_CMD_WRITE_REQUEST_2BYTE);
            current_stage_ = JACK_STAGE_MIDDLE_STOP;

            return false;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // Check if position has changed significantly
    {
        std::lock_guard<std::mutex> lock(jack_mutex_);
        int32_t position_diff = (current_position_ > initial_position) ?
                                (current_position_ - initial_position) :
                                (initial_position - current_position_);

        bool movement_detected = position_diff >= movement_check_tolerance_;

        RCLCPP_INFO(this->get_logger(), "Movement check: initial=%d, current=%d, diff=%d, tolerance=%d, result=%s",
                   initial_position, current_position_, position_diff, movement_check_tolerance_,
                   movement_detected ? "MOVING" : "NOT_MOVING");

        if (!movement_detected) {
            RCLCPP_WARN(this->get_logger(), "Jack does not appear to be moving after %d seconds", movement_start_delay_s_);
            return false;
        }
    }

    RCLCPP_INFO(this->get_logger(), "Movement confirmed - jack is moving");
    return true;
}

uint32_t JackControlNode::convertRpmToCanValue(uint32_t rpm)
{
    // Convert RPM to CAN value using formula: rpm / 1875 * 512 * 131072
    // This can be simplified to: rpm * 512 * 131072 / 1875 = rpm * 36700.16
    // Using integer arithmetic to avoid floating point errors

    if (rpm == 0) {
        return 0;
    }

    // Calculate: rpm * 512 * 131072 / 1875
    uint64_t temp = static_cast<uint64_t>(rpm) * 512 * 131072;
    uint32_t can_value = static_cast<uint32_t>(temp / 1875);

    RCLCPP_DEBUG(this->get_logger(), "Speed conversion: %d RPM -> %d CAN value", rpm, can_value);

    return can_value;
}

}  // namespace sl_vcu_all

// Global node pointer for signal handler
std::shared_ptr<sl_vcu_all::JackControlNode> g_node = nullptr;

// Signal handler for graceful shutdown
void signalHandler(int signum)
{
    RCLCPP_INFO(rclcpp::get_logger("jack_control"), "Received signal %d, shutting down gracefully...", signum);
    if (g_node) {
        g_node->shutdown();
    }
    rclcpp::shutdown();
}

// Main entry point
int main(int argc, char ** argv)
{
    rclcpp::init(argc, argv);

    // Set up signal handlers
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    g_node = std::make_shared<sl_vcu_all::JackControlNode>();

    // Check if node is enabled before spinning
    if (!g_node->isEnabled()) {
        RCLCPP_INFO(rclcpp::get_logger("jack_control"), "Jack Control Node is disabled, exiting gracefully");
    } else {
        try {
            rclcpp::spin(g_node);
        } catch (const std::exception& e) {
            RCLCPP_ERROR(rclcpp::get_logger("jack_control"), "Exception in main loop: %s", e.what());
        }
    }

    // Clean shutdown
    if (g_node) {
        g_node->shutdown();
        g_node.reset();
    }

    rclcpp::shutdown();
    return 0;
}
