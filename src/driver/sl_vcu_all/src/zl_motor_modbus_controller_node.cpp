#include "sl_vcu_all/zl_motor_modbus_controller.hpp"
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <tf2/LinearMath/Matrix3x3.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <cstring>
#include <chrono>
#include <functional>
#include <sstream>
#include <iomanip>

using namespace std::chrono_literals;
using std::placeholders::_1;

namespace sl_vcu_all
{

ZLMotorModbusController::ZLMotorModbusController(const rclcpp::NodeOptions & options)
    : Node("zl_motor_modbus_controller", options),
    modbus_ctx_(nullptr),
    modbus_connected_(false),
    motors_enabled_(false),
    left_pos_(0),
    right_pos_(0),
    left_pos_prev_(0),
    right_pos_prev_(0),
    is_last_left_pos_init_(false),
    is_last_right_pos_init_(false),
    left_speed_(0),
    right_speed_(0),
    left_current_(0),
    right_current_(0),
    gpio_status_(0),
    left_temp_(0),
    right_temp_(0),
    driver_temp_(0),
    left_alarm_(0),
    right_alarm_(0),
    x_(0.0),
    y_(0.0),
    theta_(0.0),
    cycle_counter_(0),
    control_state_(ControlState::INIT),
    state_counter_(0),
    cmd_vel_timeout_ms_(500),
    bumper_timeout_ms_(5000),
    response_timeout_ms_(1000),
    status_update_cycle_ms_(1000),
    publish_motor_info_(true)
{
    // Initialize parameters
    initParameters();

    RCLCPP_INFO(this->get_logger(), "Initializing ZL Motor Modbus Controller");
    last_cmd_vel_time_ = this->now();
    last_bumper_time_ = this->now();

    // Initialize bumper states to false (not triggered)
    latest_bumper_state_.front_bumper_triggered = false;
    latest_bumper_state_.back_bumper_triggered = false;

    // Create publishers
    odom_pub_ = this->create_publisher<nav_msgs::msg::Odometry>(odom_topic_, 10);
    motor_info_pub_ = this->create_publisher<sl_vcu_all::msg::MotorInfo>(motor_info_topic_, 10);
    motor_state_pub_ = this->create_publisher<sl_vcu_all::msg::MotorState>(motor_state_topic_, 10);

    // Create subscribers
    cmd_vel_sub_ = this->create_subscription<geometry_msgs::msg::Twist>(
        cmd_vel_topic_, 10,
        std::bind(&ZLMotorModbusController::cmdVelCallback, this, std::placeholders::_1));

    bumper_sub_ = this->create_subscription<sl_vcu_all::msg::BumperState>(
        bumper_topic_, 10,
        std::bind(&ZLMotorModbusController::bumperCallback, this, std::placeholders::_1));

    alarm_clear_sub_ = this->create_subscription<std_msgs::msg::Bool>(
        "clear_alarm", 10,
        std::bind(&ZLMotorModbusController::clearAlarmCallback, this, std::placeholders::_1));

    filtered_imu_sub_ = this->create_subscription<sensor_msgs::msg::Imu>(
        "sl_vcu_all/imu_data_filtered", 10,
        std::bind(&ZLMotorModbusController::filteredImuCallback, this, std::placeholders::_1));

    // Create TF broadcaster if enabled
    if (publish_tf_) {
        tf_broadcaster_ = std::make_unique<tf2_ros::TransformBroadcaster>(this);
    }

    // Set up the control timer with the specified control cycle
    control_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(control_cycle_ms_),
        std::bind(&ZLMotorModbusController::controlTimerCallback, this));

#if 0
    // Set up the status timer for temperature and current updates
    status_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(status_update_cycle_ms_),
        std::bind(&ZLMotorModbusController::statusTimerCallback, this));
#endif
    last_odom_time_ = this->now();

    RCLCPP_INFO(this->get_logger(), "ZL Motor Modbus Controller initialized");
}

ZLMotorModbusController::~ZLMotorModbusController()
{
    // Disable motors when node is shutting down
    disableMotors();
    
    // Close Modbus connection
    closeModbus();
}

void ZLMotorModbusController::initParameters()
{
    // Modbus communication parameters
    this->declare_parameter("serial_device", "/dev/ttyUSB0");
    this->declare_parameter("baud_rate", 115200);
    this->declare_parameter("parity", "N");
    this->declare_parameter("data_bits", 8);
    this->declare_parameter("stop_bits", 1);
    this->declare_parameter("slave_address", 1);

    // Robot physical parameters
    this->declare_parameter("wheel_diameter_left", 0.140);  // meters
    this->declare_parameter("wheel_diameter_right", 0.140);  // meters
    this->declare_parameter("wheel_separation", 0.390);  // meters
    this->declare_parameter("gear_ratio", 1.0);  // motor gear ratio
    this->declare_parameter("encoder_resolution", 16384.0);  // encoder ticks per revolution

    // Frame IDs
    this->declare_parameter("odom_frame_id", "odom");
    this->declare_parameter("base_frame_id", "base_link");
    this->declare_parameter("publish_tf", true);

    // Topic names
    this->declare_parameter("cmd_vel_topic", "cmd_vel");
    this->declare_parameter("odom_topic", "odom");
    this->declare_parameter("publish_motor_info", false);  // Whether to publish motor info
    this->declare_parameter("motor_info_topic", "motor_info");
    this->declare_parameter("motor_state_topic", "motor_state");
    this->declare_parameter("bumper_topic", "bumper_state");

    // Control parameters
    this->declare_parameter("control_cycle_ms", 20);
    this->declare_parameter("print_status_out", false);
    this->declare_parameter("status_update_cycle_ms", 1000);  // Status update cycle for temps and currents
    this->declare_parameter("cmd_vel_timeout_ms", 500);  // Command velocity timeout in ms
    this->declare_parameter("bumper_timeout_ms", 5000);  // Bumper state timeout in ms
    this->declare_parameter("response_timeout_ms", 1000);  // Response timeout in ms

    // Get parameter values
    serial_device_ = this->get_parameter("serial_device").as_string();
    baud_rate_ = this->get_parameter("baud_rate").as_int();
    std::string parity_str = this->get_parameter("parity").as_string();
    parity_ = parity_str.empty() ? 'N' : parity_str[0];
    data_bits_ = this->get_parameter("data_bits").as_int();
    stop_bits_ = this->get_parameter("stop_bits").as_int();
    slave_address_ = this->get_parameter("slave_address").as_int();

    wheel_diameter_left_ = this->get_parameter("wheel_diameter_left").as_double();
    wheel_diameter_right_ = this->get_parameter("wheel_diameter_right").as_double();
    wheel_radius_left_ = wheel_diameter_left_ / 2.0;
    wheel_radius_right_ = wheel_diameter_right_ / 2.0;
    wheel_separation_ = this->get_parameter("wheel_separation").as_double();
    gear_ratio_ = this->get_parameter("gear_ratio").as_double();
    encoder_resolution_ = this->get_parameter("encoder_resolution").as_double();

    odom_frame_id_ = this->get_parameter("odom_frame_id").as_string();
    base_frame_id_ = this->get_parameter("base_frame_id").as_string();
    publish_tf_ = this->get_parameter("publish_tf").as_bool();
    print_status_ = this->get_parameter("print_status_out").as_bool();

    cmd_vel_topic_ = this->get_parameter("cmd_vel_topic").as_string();
    odom_topic_ = this->get_parameter("odom_topic").as_string();
    motor_info_topic_ = this->get_parameter("motor_info_topic").as_string();
    motor_state_topic_ = this->get_parameter("motor_state_topic").as_string();
    bumper_topic_ = this->get_parameter("bumper_topic").as_string();
    cmd_vel_timeout_ms_ = this->get_parameter("cmd_vel_timeout_ms").as_int();

    control_cycle_ms_ = this->get_parameter("control_cycle_ms").as_int();
    status_update_cycle_ms_ = this->get_parameter("status_update_cycle_ms").as_int();
    bumper_timeout_ms_ = this->get_parameter("bumper_timeout_ms").as_int();
    response_timeout_ms_ = this->get_parameter("response_timeout_ms").as_int();
    publish_motor_info_ = this->get_parameter("publish_motor_info").as_bool();

    RCLCPP_INFO(this->get_logger(), "Parameters initialized:");
    RCLCPP_INFO(this->get_logger(), "  Serial device: %s", serial_device_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Baud rate: %d", baud_rate_);
    RCLCPP_INFO(this->get_logger(), "  Parity: %c", parity_);
    RCLCPP_INFO(this->get_logger(), "  Data bits: %d", data_bits_);
    RCLCPP_INFO(this->get_logger(), "  Stop bits: %d", stop_bits_);
    RCLCPP_INFO(this->get_logger(), "  Slave address: %d", slave_address_);
    RCLCPP_INFO(this->get_logger(), "  Wheel separation: %.4f m", wheel_separation_);
    RCLCPP_INFO(this->get_logger(), "  Gear ratio: %.2f", gear_ratio_);
    RCLCPP_INFO(this->get_logger(), "  Encoder resolution: %.2f ticks/rev", encoder_resolution_);
    RCLCPP_INFO(this->get_logger(), "  Control cycle: %d ms", control_cycle_ms_);
    RCLCPP_INFO(this->get_logger(), "  Status update cycle: %d ms", status_update_cycle_ms_);
    RCLCPP_INFO(this->get_logger(), "  Publish TF: %s", publish_tf_ ? "true" : "false");
    RCLCPP_INFO(this->get_logger(), "  Cmd vel timeout: %d ms", cmd_vel_timeout_ms_);
    RCLCPP_INFO(this->get_logger(), "  Bumper timeout: %d ms", bumper_timeout_ms_);
    RCLCPP_INFO(this->get_logger(), "  Response timeout: %d ms", response_timeout_ms_);
    RCLCPP_INFO(this->get_logger(), "  Publish motor info: %s", publish_motor_info_ ? "true" : "false");
}

void ZLMotorModbusController::cmdVelCallback(const geometry_msgs::msg::Twist::SharedPtr msg)
{
    // Store the twist message for processing in the control loop
    {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        latest_cmd_vel_ = *msg;
    }
    last_cmd_vel_time_ = this->now();
}

void ZLMotorModbusController::bumperCallback(const sl_vcu_all::msg::BumperState::SharedPtr msg)
{
    bool front_state_changed = false;
    bool back_state_changed = false;

    {
        // Check for state changes (compare with current latest_bumper_state_)
        front_state_changed = (latest_bumper_state_.front_bumper_triggered != msg->front_bumper_triggered);
        back_state_changed = (latest_bumper_state_.back_bumper_triggered != msg->back_bumper_triggered);

        // Update states
        latest_bumper_state_ = *msg;
    }

    // Log state changes
    if (front_state_changed) {
        RCLCPP_INFO(this->get_logger(), "Front bumper state changed: %s",
                   msg->front_bumper_triggered ? "TRIGGERED" : "RELEASED");
    }

    if (back_state_changed) {
        RCLCPP_INFO(this->get_logger(), "Back bumper state changed: %s",
                   msg->back_bumper_triggered ? "TRIGGERED" : "RELEASED");
    }

    last_bumper_time_ = this->now();
}

void ZLMotorModbusController::clearAlarmCallback(const std_msgs::msg::Bool::SharedPtr msg)
{
    if(msg->data)
    {
        clearAlarm();
        RCLCPP_INFO(this->get_logger(), "Clear alarm command received");
    }
}

void ZLMotorModbusController::filteredImuCallback(const sensor_msgs::msg::Imu::SharedPtr msg)
{
    // Extract orientation quaternion and convert to Euler angles
    double roll, pitch, yaw;

    tf2::Quaternion tf_quat;
    tf2::fromMsg(msg->orientation, tf_quat);
    tf_quat.normalize();

    tf2::Matrix3x3(tf_quat).getRPY(roll, pitch, yaw);

    {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        // Update theta with the yaw from filtered IMU
        theta_ = yaw;
        // Normalize to [-pi, pi]
        theta_ = normalizeAngle(theta_);
    }

    RCLCPP_DEBUG(this->get_logger(), "Updated theta from filtered IMU: %.4f", theta_);
}

bool ZLMotorModbusController::initModbus()
{
    std::lock_guard<std::mutex> lock(modbus_mutex_);

    if (modbus_ctx_ != nullptr) {
        modbus_close(modbus_ctx_);
        modbus_free(modbus_ctx_);
        modbus_ctx_ = nullptr;
    }

    // Create new RTU context
    modbus_ctx_ = modbus_new_rtu(serial_device_.c_str(), baud_rate_, parity_, data_bits_, stop_bits_);
    if (modbus_ctx_ == nullptr) {
        RCLCPP_ERROR(this->get_logger(), "Failed to create Modbus RTU context");
        return false;
    }

    // Set slave address
    if (modbus_set_slave(modbus_ctx_, slave_address_) == -1) {
        RCLCPP_ERROR(this->get_logger(), "Failed to set slave address: %s", modbus_strerror(errno));
        modbus_free(modbus_ctx_);
        modbus_ctx_ = nullptr;
        return false;
    }

    // Set response timeout
    if (modbus_set_response_timeout(modbus_ctx_, 0, response_timeout_ms_*1000) == -1) {  // 500ms timeout
        RCLCPP_ERROR(this->get_logger(), "Failed to set response timeout: %s", modbus_strerror(errno));
        modbus_free(modbus_ctx_);
        modbus_ctx_ = nullptr;
        return false;
    }

    // Connect to the device
    if (modbus_connect(modbus_ctx_) == -1) {
        RCLCPP_ERROR(this->get_logger(), "Failed to connect to Modbus device: %s", modbus_strerror(errno));
        modbus_free(modbus_ctx_);
        modbus_ctx_ = nullptr;
        return false;
    }

    modbus_connected_ = true;
    RCLCPP_INFO(this->get_logger(), "Modbus RTU connection established on %s", serial_device_.c_str());
    return true;
}

void ZLMotorModbusController::closeModbus()
{
    std::lock_guard<std::mutex> lock(modbus_mutex_);

    if (modbus_ctx_ != nullptr) {
        modbus_close(modbus_ctx_);
        modbus_free(modbus_ctx_);
        modbus_ctx_ = nullptr;
        modbus_connected_ = false;
        RCLCPP_INFO(this->get_logger(), "Modbus connection closed");
    }
}

bool ZLMotorModbusController::writeRegister(uint16_t address, uint16_t value)
{
    std::lock_guard<std::mutex> lock(modbus_mutex_);

    if (!modbus_connected_ || modbus_ctx_ == nullptr) {
        RCLCPP_ERROR(this->get_logger(), "Modbus not connected");
        return false;
    }

    int rc = modbus_write_register(modbus_ctx_, address, value);
    if (rc == -1) {
        RCLCPP_ERROR(this->get_logger(), "Failed to write register 0x%04X: %s", address, modbus_strerror(errno));
        return false;
    }

    RCLCPP_DEBUG(this->get_logger(), "Wrote register 0x%04X = 0x%04X", address, value);
    return true;
}

bool ZLMotorModbusController::writeRegisters(uint16_t address, uint16_t count, uint16_t* values)
{
    std::lock_guard<std::mutex> lock(modbus_mutex_);

    if (!modbus_connected_ || modbus_ctx_ == nullptr) {
        RCLCPP_ERROR(this->get_logger(), "Modbus not connected");
        return false;
    }

    int rc = modbus_write_registers(modbus_ctx_, address, count, values);
    if (rc == -1) {
        RCLCPP_ERROR(this->get_logger(), "Failed to write registers 0x%04X (count %d): %s",
                     address, count, modbus_strerror(errno));
        return false;
    }

    RCLCPP_DEBUG(this->get_logger(), "Wrote %d registers starting from 0x%04X", count, address);
    return true;
}


bool ZLMotorModbusController::readRegisters(uint16_t address, uint16_t count, uint16_t* dest)
{
    std::lock_guard<std::mutex> lock(modbus_mutex_);

    if (!modbus_connected_ || modbus_ctx_ == nullptr) {
        RCLCPP_ERROR(this->get_logger(), "Modbus not connected");
        return false;
    }

    int rc = modbus_read_registers(modbus_ctx_, address, count, dest);
    if (rc == -1) {
        RCLCPP_ERROR(this->get_logger(), "Failed to read registers 0x%04X (count %d): %s",
                     address, count, modbus_strerror(errno));
        return false;
    }

    RCLCPP_DEBUG(this->get_logger(), "Read %d registers starting from 0x%04X", count, address);
    return true;
}

bool ZLMotorModbusController::readSingleRegister(uint16_t address, uint16_t* value)
{
    return readRegisters(address, 1, value);
}

void ZLMotorModbusController::enableMotors()
{
    if (writeRegister(ZL_MODBUS_REG_CONTROL, ZL_MODBUS_CTRL_ENABLE)) {
        motors_enabled_ = true;
        RCLCPP_INFO(this->get_logger(), "Motors enabled");
    } else {
        RCLCPP_ERROR(this->get_logger(), "Failed to enable motors");
    }
}

void ZLMotorModbusController::disableMotors()
{
    if (writeRegister(ZL_MODBUS_REG_CONTROL, ZL_MODBUS_CTRL_DISABLE)) {
        motors_enabled_ = false;
        RCLCPP_INFO(this->get_logger(), "Motors disabled");
    } else {
        RCLCPP_ERROR(this->get_logger(), "Failed to disable motors");
    }
}

void ZLMotorModbusController::clearAlarm()
{
    if (writeRegister(ZL_MODBUS_REG_CONTROL, ZL_MODBUS_CTRL_CLEAR_ALARM)) {
        RCLCPP_INFO(this->get_logger(), "Alarm cleared");
        control_state_ = ControlState::CLEAR_ALARM;
        // Reset alarm codes
        left_alarm_ = 0;
        right_alarm_ = 0;
    } else {
        RCLCPP_ERROR(this->get_logger(), "Failed to clear alarm");
    }
}

void ZLMotorModbusController::setMotorSpeeds(double left_speed_rpm, double right_speed_rpm)
{
    if (control_state_ != ControlState::RUNNING) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000, "Motors not in running state");
        return;
    }

    if (!motors_enabled_) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000, "Motors not enabled");
        return;
    }

    // Convert RPM to Modbus speed format 
    int16_t left_modbus_speed = rpmToModbusSpeed(left_speed_rpm);
    int16_t right_modbus_speed = rpmToModbusSpeed(-right_speed_rpm);  // Right motor inverted

    // Write speed to registers
    uint16_t values[2] = {static_cast<uint16_t>(left_modbus_speed & 0xFFFF), static_cast<uint16_t>(right_modbus_speed & 0xFFFF)};
    if (writeRegisters(ZL_MODBUS_REG_SPEED_SET, 2, values)) {
        RCLCPP_DEBUG(this->get_logger(), "Set motor speeds: L=%.2f RPM, R=%.2f RPM",
                     left_speed_rpm, right_speed_rpm);
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to set motor speeds: L=%.2f RPM, R=%.2f RPM",
                     left_speed_rpm, right_speed_rpm);
    }
}


void ZLMotorModbusController::updateMotorAllData()
{
    uint16_t motor_data[13];  // 2 registers each for left and right position

    if (readRegisters(ZL_MODBUS_REG_MOTOR_TEMPS, 13, motor_data)) 
    {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        left_temp_ = static_cast<int16_t>(static_cast<int8_t>((motor_data[0] >> 8) & 0xFF));
        right_temp_ = static_cast<int16_t>(static_cast<int8_t>(motor_data[0] & 0xFF));
        left_alarm_ = motor_data[1];
        right_alarm_ = motor_data[2];
        left_pos_ = static_cast<int32_t>((static_cast<int32_t>(motor_data[3]) << 16) | motor_data[4]);
        right_pos_ = static_cast<int32_t>((static_cast<int32_t>(motor_data[5]) << 16) | motor_data[6]);

        left_speed_ = static_cast<int16_t>(motor_data[7]);
        right_speed_ = static_cast<int16_t>(motor_data[8]);
        left_current_ = static_cast<int16_t>(motor_data[9]);
        right_current_ = static_cast<int16_t>(motor_data[10]);

        driver_temp_ = static_cast<int16_t>(motor_data[12]);
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to update motor all data");
    }


    RCLCPP_DEBUG(this->get_logger(), "Motor all data updated");
    RCLCPP_DEBUG(this->get_logger(), "Left temp: %d, Right temp: %d", left_temp_, right_temp_);
    RCLCPP_DEBUG(this->get_logger(), "Left alarm: 0x%04X, Right alarm: 0x%04X", left_alarm_, right_alarm_);
    RCLCPP_DEBUG(this->get_logger(), "Left pos: %d, Right pos: %d", left_pos_, right_pos_);
    RCLCPP_DEBUG(this->get_logger(), "Left speed: %d, Right speed: %d", left_speed_, right_speed_);
    RCLCPP_DEBUG(this->get_logger(), "Left current: %d, Right current: %d", left_current_, right_current_);
    RCLCPP_DEBUG(this->get_logger(), "Driver temp: %d", driver_temp_);
}


void ZLMotorModbusController::updateMotorPositions()
{
    uint16_t pos_data[4];  // 2 registers each for left and right position

    if (readRegisters(ZL_MODBUS_REG_LEFT_POS, 4, pos_data)) {
        // Combine 16-bit registers into 32-bit positions
        int32_t new_left_pos = (static_cast<int32_t>(pos_data[0]) << 16) | pos_data[1];
        int32_t new_right_pos = (static_cast<int32_t>(pos_data[2]) << 16) | pos_data[3];

        std::lock_guard<std::mutex> lock(motor_mutex_);
        left_pos_prev_ = left_pos_;
        right_pos_prev_ = right_pos_;
        left_pos_ = new_left_pos;
        right_pos_ = new_right_pos;

        if (!is_last_left_pos_init_) {
            left_pos_prev_ = left_pos_;
            is_last_left_pos_init_ = true;
        }
        if (!is_last_right_pos_init_) {
            right_pos_prev_ = right_pos_;
            is_last_right_pos_init_ = true;
        }
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to update motor positions");
    }
}

void ZLMotorModbusController::updateMotorSpeeds()
{
    uint16_t speed_data[2];

    if (readRegisters(ZL_MODBUS_REG_LEFT_SPEED_FB, 2, speed_data)) {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        left_speed_ = static_cast<int16_t>(speed_data[0]);
        right_speed_ = static_cast<int16_t>(speed_data[1]);
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to update motor speeds");
    }
}

void ZLMotorModbusController::updateMotorCurrents()
{
    uint16_t current_data[2];

    if (readRegisters(ZL_MODBUS_REG_LEFT_CURRENT, 2, current_data)) {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        left_current_ = static_cast<int16_t>(current_data[0]);
        right_current_ = static_cast<int16_t>(current_data[1]);
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to update motor currents");
    }
}

void ZLMotorModbusController::updateMotorTemperatures()
{
    uint16_t motor_temp;  // left, right 1 degC, driver 0.1 degC

    if (readSingleRegister(ZL_MODBUS_REG_MOTOR_TEMPS, &motor_temp)) {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        left_temp_ = static_cast<int16_t>(static_cast<int8_t>((motor_temp >> 8) & 0xFF));
        right_temp_ = static_cast<int16_t>(static_cast<int8_t>(motor_temp & 0xFF));
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to update motor temperatures");
    }

    // Read driver temperature separately
    uint16_t driver_temp;
    if (readSingleRegister(ZL_MODBUS_REG_DRIVER_TEMP, &driver_temp)) {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        driver_temp_ = static_cast<int16_t>(driver_temp);
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to update driver temperature");
    }
}

void ZLMotorModbusController::updateGpioStatus()
{
    uint16_t gpio_data;
    
    uint32_t old_gpio_status = gpio_status_;
        
    if (readSingleRegister(ZL_MODBUS_REG_GPIO_STATUS, &gpio_data)) {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        gpio_status_ = gpio_data;
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to update GPIO status");
    }

    
    // Check for changes in specific GPIO bits
    bool emergency_bit0_changed = ((old_gpio_status & GPIO_EMERGENCY_BIT0) != (gpio_status_ & GPIO_EMERGENCY_BIT0));
    bool emergency_bit1_changed = ((old_gpio_status & GPIO_EMERGENCY_BIT1) != (gpio_status_ & GPIO_EMERGENCY_BIT1));
    bool brake_release_changed = ((old_gpio_status & GPIO_BRAKE_RELEASE) != (gpio_status_ & GPIO_BRAKE_RELEASE));
    bool power_off_button_changed = ((old_gpio_status & GPIO_POWER_OFF_BUTTON) != (gpio_status_ & GPIO_POWER_OFF_BUTTON));

    // Log state changes for emergency signals (bit0 and bit1)
    if (emergency_bit0_changed) {
        bool is_triggered = (gpio_status_ & GPIO_EMERGENCY_BIT0) != 0;
        RCLCPP_INFO(this->get_logger(), "Emergency signal bit0 changed: %s",
                    is_triggered ? "TRIGGERED" : "RELEASED");
    }

    if (emergency_bit1_changed) {
        bool is_triggered = (gpio_status_ & GPIO_EMERGENCY_BIT1) != 0;
        RCLCPP_INFO(this->get_logger(), "Emergency signal bit1 changed: %s",
                    is_triggered ? "TRIGGERED" : "RELEASED");
    }

    // Log state changes for brake release signal (bit2)
    if (brake_release_changed) {
        bool is_triggered = (gpio_status_ & GPIO_BRAKE_RELEASE) != 0;
        RCLCPP_INFO(this->get_logger(), "Brake release signal changed: %s",
                    is_triggered ? "TRIGGERED" : "RELEASED");
    }

    // Log state changes for power off button signal (bit3)
    if (power_off_button_changed) {
        bool is_pressed = (gpio_status_ & GPIO_POWER_OFF_BUTTON) != 0;
        RCLCPP_INFO(this->get_logger(), "Power off button changed: %s",
                    is_pressed ? "PRESSED" : "RELEASED");

        // Log additional warning when power off button is pressed
        if (is_pressed) {
            RCLCPP_WARN(this->get_logger(), "POWER OFF BUTTON PRESSED - System shutdown may be initiated");
        }
    }

    // Log warnings every 1 second if emergency signals are triggered
    if (gpio_status_ & GPIO_EMERGENCY_BIT0) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
            "Emergency signal bit0 is triggered");
    }

    if (gpio_status_ & GPIO_EMERGENCY_BIT1) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
            "Emergency signal bit1 is triggered");
    }

    if (gpio_status_ & GPIO_BRAKE_RELEASE) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
            "Brake release signal is triggered");
    }

    // Log warning every 1 second if power off button is pressed
    if (gpio_status_ & GPIO_POWER_OFF_BUTTON) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000,
            "Power off button is currently pressed - System may shutdown");
    }




    RCLCPP_DEBUG(this->get_logger(), "GPIO status: 0x%04X", gpio_status_);
}

void ZLMotorModbusController::updateAlarmCodes()
{
    uint16_t alarm_data[2];

    if (readRegisters(ZL_MODBUS_REG_LEFT_ALARM, 2, alarm_data)) {

        bool is_changed = false;

        {
            if ((left_alarm_ != alarm_data[0]) || right_alarm_ != alarm_data[1])
            {
                is_changed = true;
            }
            RCLCPP_DEBUG(this->get_logger(), "Last alarm code updated successfully Left: 0x%04X, Right: 0x%04X", alarm_data[0], alarm_data[1]);
        } 

        if (is_changed)
        {
            RCLCPP_INFO(this->get_logger(), "Last alarm code changed, Left: 0x%04X -> 0x%04X Right: 0x%04X -> 0x%04X", 
            left_alarm_, alarm_data[0], right_alarm_, alarm_data[1]);
            is_changed = false;
        }

        {
            std::lock_guard<std::mutex> lock(motor_mutex_);
            left_alarm_ = alarm_data[0];
            right_alarm_ = alarm_data[1];
        }

        if (left_alarm_ != 0 || right_alarm_ != 0) {
            RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000, "Motor alarm detected - Left: 0x%04X, Right: 0x%04X",
                         left_alarm_, right_alarm_);
        }
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to update alarm codes");
    }
}

void ZLMotorModbusController::controlTimerCallback()
{
    // Check for cmd_vel timeout
    if ((control_state_ == ControlState::RUNNING) && motors_enabled_) {
        auto now = this->now();
        auto elapsed = (now - last_cmd_vel_time_).nanoseconds() / 1000000;  // Convert to ms

        if (elapsed > cmd_vel_timeout_ms_)
        {
            RCLCPP_DEBUG(this->get_logger(), "Command velocity timeout, stopping motors");
            latest_cmd_vel_.linear.x = 0.0;
            latest_cmd_vel_.angular.z = 0.0;
        }
    }
    else
    {
        latest_cmd_vel_.linear.x = 0.0;
        latest_cmd_vel_.angular.z = 0.0;
    }

    // Check for bumper timeout
    {
        auto now = this->now();
        auto elapsed = (now - last_bumper_time_).nanoseconds() / 1000000;  // Convert to ms

        if (elapsed > bumper_timeout_ms_) {
            RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                "Bumper state timeout, assuming safe state");
            // Reset bumper state to safe (not triggered) when timeout occurs
            latest_bumper_state_.front_bumper_triggered = false;
            latest_bumper_state_.back_bumper_triggered = false;
        }
    }

    // Process the latest cmd_vel if in running state
    if ((control_state_ == ControlState::RUNNING) && motors_enabled_)
    {
        // Convert twist message to differential drive wheel speeds
        double linear_velocity = latest_cmd_vel_.linear.x;  // m/s
        double angular_velocity = latest_cmd_vel_.angular.z;  // rad/s

        // Calculate wheel velocities using differential drive kinematics
        double left_wheel_linear = linear_velocity - (angular_velocity * wheel_separation_ / 2.0);
        double right_wheel_linear = linear_velocity + (angular_velocity * wheel_separation_ / 2.0);

        // Convert linear velocity to angular velocity (rad/s)
        double left_wheel_angular = left_wheel_linear / wheel_radius_left_;
        double right_wheel_angular = right_wheel_linear / wheel_radius_right_;

        // Convert to RPM
        double left_rpm = radPerSecToRpm(left_wheel_angular);
        double right_rpm = radPerSecToRpm(right_wheel_angular);

        // Check for emergency stop or brake release - filter speed to 0
        bool emergency_stop = (gpio_status_ & GPIO_EMERGENCY_BIT0) || (gpio_status_ & GPIO_EMERGENCY_BIT1);
        bool brake_release = (gpio_status_ & GPIO_BRAKE_RELEASE);

        if (emergency_stop || brake_release) {
            left_rpm = 0.0;
            right_rpm = 0.0;
        }

        // Set motor speeds
        // check bumper state and log warnings if speed is limited
        bool front_speed_limited = false;
        bool back_speed_limited = false;

        if (latest_bumper_state_.front_bumper_triggered)
        {
            if (left_rpm > 0)
            {
                left_rpm = 0;
                front_speed_limited = true;
            }
            if (right_rpm > 0)
            {
                right_rpm = 0;
                front_speed_limited = true;
            }
        }
        if (latest_bumper_state_.back_bumper_triggered)
        {
            if (left_rpm < 0)
            {
                left_rpm = 0;
                back_speed_limited = true;
            }
            if (right_rpm < 0)
            {
                right_rpm = 0;
                back_speed_limited = true;
            }
        }

        // Log warnings every 1 second if speed is limited due to bumper triggers
        if (front_speed_limited) {
            RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000,
                "Forward movement limited due to front bumper trigger");
        }

        if (back_speed_limited) {
            RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000,
                "Backward movement limited due to back bumper trigger");
        }

        setMotorSpeeds(left_rpm, right_rpm);
        // wait for 5ms
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }

    // State machine for initialization and control
    switch (control_state_) {
        case ControlState::INIT:
        {
            // Initial state, wait for a few cycles before starting
            if (state_counter_++ > 10) {
                control_state_ = ControlState::CONNECTING;
                state_counter_ = 0;
                RCLCPP_INFO(this->get_logger(), "Initializing Modbus connection");
            }
        }
        break;
        case ControlState::CONNECTING:
        {
            // Try to establish Modbus connection
            if (initModbus()) {
                control_state_ = ControlState::SET_MODE;
                state_counter_ = 0;
                RCLCPP_INFO(this->get_logger(), "Modbus connection established");
            } else {
                state_counter_++;
                if (state_counter_ > 50) { 
                    RCLCPP_ERROR(this->get_logger(), "Timeout connecting to Modbus device");
                    control_state_ = ControlState::ERROR;
                    state_counter_ = 0;
                }
            }
        }
        break;
        case ControlState::SET_MODE:
        {
            // Set speed mode (register 0x200D with data 3 as per requirements)
            if (writeRegister(ZL_MODBUS_REG_MODE, ZL_MODBUS_MODE_SPEED)) {
                control_state_ = ControlState::ENABLE_MOTORS;
                state_counter_ = 0;
                RCLCPP_INFO(this->get_logger(), "Speed mode set");
            } else {
                state_counter_++;
                if (state_counter_ > 5) {
                    control_state_ = ControlState::ERROR;
                    state_counter_ = 0;
                }
            }
        }
        break;
        case ControlState::ENABLE_MOTORS:
        {
            // Enable motors
            enableMotors();
            if (motors_enabled_) {
                control_state_ = ControlState::RUNNING;
                state_counter_ = 0;
                RCLCPP_INFO(this->get_logger(), "Motors enabled, entering running state");
            } else {
                state_counter_++;
                if (state_counter_ > 5) {
                    control_state_ = ControlState::ERROR;
                    state_counter_ = 0;
                }
            }
        }
        break;
        case ControlState::RUNNING:
        {
#if 0
            // Normal operation - update status and odometry
            // Always update positions every cycle
            updateMotorPositions();

            // Update speeds every cycle
            updateMotorSpeeds();
#endif
            
            updateMotorAllData();

            // wait for 5ms
            std::this_thread::sleep_for(std::chrono::milliseconds(5));
            // Update GPIO status every cycle
            updateGpioStatus();
#if 0
            // Update alarm codes every cycle
            updateAlarmCodes();
#endif
            // Update and publish odometry
            updateOdometry();
            publishOdometry();

            if (publish_motor_info_) 
            {
                publishMotorInfo();
            }
    
            // Always publish motor state (regardless of running state)
            publishMotorState();

            // Increment cycle counter
            cycle_counter_ = 0; // Reset cycle counter
        }
        break;
        case ControlState::CLEAR_ALARM:
        {
            control_state_ = ControlState::SET_MODE;
            state_counter_ = 0;
            disableMotors();
        }
        break;
        case ControlState::ERROR:
        {
            // Error state, try to recover after a delay
            if (state_counter_++ > 100) {  // Wait longer in error state
                control_state_ = ControlState::INIT;
                state_counter_ = 0;
                disableMotors();
                closeModbus();
                RCLCPP_INFO(this->get_logger(), "Attempting to recover from error");
            }
        }
        break;
        default:
        {
            RCLCPP_ERROR(this->get_logger(), "Unknown control state: %d", static_cast<int>(control_state_));
        }
    }
}

void ZLMotorModbusController::statusTimerCallback()
{

    // Only update temperatures and currents when in running state
    if (control_state_ == ControlState::RUNNING)
    {
        // Update motor currents
        updateMotorCurrents();

        // Update motor temperatures
        updateMotorTemperatures();

        // Publish motor info if enabled
        if (publish_motor_info_) {
            publishMotorInfo();
        }
    }

    // Always publish motor state (regardless of running state)
    publishMotorState();
}

void ZLMotorModbusController::updateOdometry()
{
    std::lock_guard<std::mutex> lock(motor_mutex_);

    if (!is_last_left_pos_init_ || !is_last_right_pos_init_) {
        return;  // Wait until we have valid position data
    }

    auto current_time = this->now();
    double dt = (current_time - last_odom_time_).seconds();

    if (dt <= 0.0) {
        return;  // Avoid division by zero
    }

    // Calculate wheel displacements
    int32_t left_delta = left_pos_ - left_pos_prev_;
    int32_t right_delta = right_pos_ - right_pos_prev_;

    // Convert encoder ticks to radians
    double left_wheel_angle = encoderToRadians(left_delta);
    double right_wheel_angle = encoderToRadians(right_delta);

    // Calculate wheel distances
    double left_distance = left_wheel_angle * wheel_radius_left_;
    double right_distance = right_wheel_angle * wheel_radius_right_;

    // Calculate robot displacement and rotation
    double distance = (left_distance + right_distance) / 2.0;
    double delta_theta = (right_distance - left_distance) / wheel_separation_;

    // Update robot pose
    double delta_x = distance * cos(theta_ + delta_theta / 2.0);
    double delta_y = distance * sin(theta_ + delta_theta / 2.0);

    x_ += delta_x;
    y_ += delta_y;
    theta_ += delta_theta;
    theta_ = normalizeAngle(theta_);

    last_odom_time_ = current_time;
}

void ZLMotorModbusController::publishOdometry()
{
    auto odom_msg = nav_msgs::msg::Odometry();
    odom_msg.header.stamp = this->now();
    odom_msg.header.frame_id = odom_frame_id_;
    odom_msg.child_frame_id = base_frame_id_;

    // Set position
    odom_msg.pose.pose.position.x = x_;
    odom_msg.pose.pose.position.y = y_;
    odom_msg.pose.pose.position.z = 0.0;

    // Set orientation
    tf2::Quaternion q;
    q.setRPY(0, 0, theta_);
    odom_msg.pose.pose.orientation.x = q.x();
    odom_msg.pose.pose.orientation.y = q.y();
    odom_msg.pose.pose.orientation.z = q.z();
    odom_msg.pose.pose.orientation.w = q.w();

    // Set velocity (calculate from wheel speeds)
    double left_wheel_vel = modbusSpeedToRpm(left_speed_) * 2.0 * M_PI / 60.0 * wheel_radius_left_;
    double right_wheel_vel = modbusSpeedToRpm(right_speed_) * 2.0 * M_PI / 60.0 * wheel_radius_right_;

    double linear_vel = (left_wheel_vel + right_wheel_vel) / 2.0;
    double angular_vel = (right_wheel_vel - left_wheel_vel) / wheel_separation_;

    odom_msg.twist.twist.linear.x = linear_vel;
    odom_msg.twist.twist.linear.y = 0.0;
    odom_msg.twist.twist.angular.z = angular_vel;

    // Publish odometry
    odom_pub_->publish(odom_msg);

    // Publish TF if enabled
    if (publish_tf_) {
        geometry_msgs::msg::TransformStamped transform;
        transform.header.stamp = odom_msg.header.stamp;
        transform.header.frame_id = odom_frame_id_;
        transform.child_frame_id = base_frame_id_;
        transform.transform.translation.x = x_;
        transform.transform.translation.y = y_;
        transform.transform.translation.z = 0.0;
        transform.transform.rotation = odom_msg.pose.pose.orientation;

        tf_broadcaster_->sendTransform(transform);
    }
}

void ZLMotorModbusController::publishMotorInfo()
{
    auto msg = sl_vcu_all::msg::MotorInfo();
    msg.header.stamp = this->now();
    msg.left_current = left_current_ / 10.0;  // Convert to actual current units
    msg.right_current = right_current_ / 10.0;
    msg.left_temp = left_temp_;  
    msg.right_temp = right_temp_;
    msg.driver_temp = driver_temp_ / 10.0;  // Convert from 0.1°C units
    msg.left_pos_encoder = left_pos_;
    msg.right_pos_encoder = right_pos_;
    msg.alarm_code = (static_cast<uint32_t>(left_alarm_) << 16) | right_alarm_;
    motor_info_pub_->publish(msg);
}

void ZLMotorModbusController::publishMotorState()
{
    auto msg = sl_vcu_all::msg::MotorState();
    msg.header.stamp = this->now();

    // Set brake and emergency status based on GPIO bits
    msg.brake_release = (gpio_status_ & GPIO_BRAKE_RELEASE) != 0;
    msg.emergency_stop = ((gpio_status_ & GPIO_EMERGENCY_BIT0) != 0) || ((gpio_status_ & GPIO_EMERGENCY_BIT1) != 0);

    // Set motor state based on control state and alarm status
    if (left_alarm_ != 0 || right_alarm_ != 0) {
        msg.state = "error";
        msg.error_code = static_cast<int32_t>((static_cast<uint32_t>(left_alarm_) << 16) | right_alarm_);

        // Format alarm code as hex string
        std::stringstream ss;
        ss << "Motor alarm detected: Left=0x" << std::hex << std::uppercase << left_alarm_
           << ", Right=0x" << right_alarm_;
        msg.error_info = ss.str();
    } else if (control_state_ == ControlState::RUNNING && motors_enabled_) {
        msg.state = "running";
        msg.error_code = 0;
        msg.error_info = "";
    } else {
        msg.state = "error";
        msg.error_code = 0;
        msg.error_info = "Motor not running or not enabled";
    }

    motor_state_pub_->publish(msg);
}

// Utility methods
double ZLMotorModbusController::rpmToRadPerSec(double rpm)
{
    return rpm * 2.0 * M_PI / 60.0;
}

double ZLMotorModbusController::radPerSecToRpm(double rad_per_sec)
{
    return rad_per_sec * 60.0 / (2.0 * M_PI);
}

int16_t ZLMotorModbusController::rpmToModbusSpeed(double rpm)
{
    // Convert RPM to Modbus speed format (0.1 RPM units as per requirements)
    //return static_cast<int16_t>(rpm * 10.0);
    return static_cast<int16_t>(rpm);
}

double ZLMotorModbusController::modbusSpeedToRpm(int16_t modbus_speed)
{
    // Convert from Modbus speed format (0.1 RPM units) to RPM
    return static_cast<double>(modbus_speed) / 10.0;
}

double ZLMotorModbusController::encoderToRadians(int32_t encoder_ticks)
{
    return (static_cast<double>(encoder_ticks) / encoder_resolution_) * 2.0 * M_PI / gear_ratio_;
}

double ZLMotorModbusController::normalizeAngle(double angle)
{
    while (angle > M_PI) angle -= 2.0 * M_PI;
    while (angle < -M_PI) angle += 2.0 * M_PI;
    return angle;
}

}  // namespace sl_vcu_all

// Main function
int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    rclcpp::spin(std::make_shared<sl_vcu_all::ZLMotorModbusController>());
    rclcpp::shutdown();
    return 0;
}
