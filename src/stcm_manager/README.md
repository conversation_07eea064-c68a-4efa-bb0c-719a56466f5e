# STCM Manager

A ROS2 service package for managing STCM (SlamTec Composite Map) files. This package provides comprehensive map management services including STCM file operations, map conversion tools, and map storage management.

## Features

- **Map Management**: Complete map lifecycle management with MapManager
- **STCM File Operations**: Upload and download STCM files with binary data transfer
- **Map Conversion**: Convert between different map formats (PGM, YAML, pbstream, STCM)
- **Known Area Service**: Get actual map dimensions and bounds from loaded maps
- **Map Clearing**: Comprehensive cleanup of map files and related data
- **Map Saving**: Save current maps with automatic file generation
- **File Analysis**: Read and analyze STCM files with detailed layer information
- **Configurable Storage**: Configurable storage paths based on operation mode

## Services

### GetStcmFile
Retrieves the current STCM file from the map storage.

**Request:**
- No parameters required

**Response:**
- `success` (bool): Whether the operation was successful
- `message` (string): Status message or error description
- `file_data` (uint8[])`: Binary data of the STCM file

### UploadStcmFile
Uploads an STCM file to the map storage.

**Request:**
- `file_name` (string): Name for the file to be saved
- `file_data` (uint8[])`: Binary data of the STCM file

**Response:**
- `success` (bool): Whether the operation was successful
- `message` (string): Status message or error description

### GetKnownArea
Returns the known area bounds from the current map.

**Request:**
- No parameters required

**Response:**
- `known_area` (Rectangle2D): Map bounds with x_min, y_min, width, height in meters

### ClearMap
Clears the current map and related files.

**Request:**
- No parameters required

**Response:**
- `success` (bool): Whether the operation was successful

### SaveMap
Saves the current map to the storage location.

**Request:**
- No parameters required

**Response:**
- `success` (bool): Whether the operation was successful
- `message` (string): Status message or error description
- `file_path` (string): Path where the map was saved

## Installation

1. Make sure you have ROS2 and the required dependencies installed:
   ```bash
   # Install ROS2 dependencies
   sudo apt install ros-humble-nav2-map-server
   sudo apt install ros-humble-cartographer-ros
   ```

2. Build the package:
   ```bash
   cd /path/to/your/ros2_workspace
   colcon build --packages-select stcm_manager
   ```

3. Source the workspace:
   ```bash
   source install/setup.bash
   ```

## Usage

### Starting the Service

```bash
# Start with default parameters
ros2 launch stcm_manager stcm_manager.launch.py

# Start with default parameters
ros2 launch stcm_manager stcm_manager.launch.py

# Start with custom storage path
ros2 launch stcm_manager stcm_manager.launch.py map_storage_path:=/custom/path

# When launched from rslamware.launch.py, the map_storage_path is automatically set based on mode:
# - real mode: /opt/rslamware_data/maps/
# - simulation/replay mode: <simulator_package>/maps/
```

### Using the Test Client

```bash
# Upload a file
ros2 run stcm_manager test_stcm_client upload /path/to/your/file.stcm

# Upload with custom name
ros2 run stcm_manager test_stcm_client upload /path/to/your/file.stcm custom_name.stcm

# Download a file
ros2 run stcm_manager test_stcm_client get filename.stcm

# Test upload then download
ros2 run stcm_manager test_stcm_client test /path/to/your/file.stcm

# Test integrated map converter (pbstream + pgm/yaml -> robot_map.stcm)
ros2 run stcm_manager test_map_converter /path/to/map/folder

# Test STCM file reader and layer information extraction
ros2 run stcm_manager test_composite_map_reader /path/to/file.stcm
```

### Map Converter Tool

The `test_map_converter` tool integrates multiple map formats into a single STCM file:

```bash
# Convert maps from a folder containing .pbstream, .pgm, and .yaml files
ros2 run stcm_manager test_map_converter /path/to/map/folder
```

**Input folder should contain:**
- `*.pbstream` file (cartographer output for ImageFeaturesMapLayer)
- `*.pgm` file (occupancy grid image for GridMapLayer)
- `*.yaml` file (map metadata for GridMapLayer)

**Output:**
- `robot_map.stcm` file created in the same folder with both layers

### STCM File Reader Tool

The `test_composite_map_reader` tool reads and analyzes STCM files, extracting detailed information from all map layers:

```bash
# Read and analyze an STCM file
ros2 run stcm_manager test_composite_map_reader /path/to/file.stcm
```

**Supported layer types:**
- **GridMapLayer**: Occupancy grid maps with statistics
- **ImageFeaturesMapLayer**: Image features from SLAM systems (Cartographer, DBoW, etc.)
- **LineMapLayer**: Line-based map features
- **PoseMapLayer**: Named poses and waypoints
- **PointsMapLayer**: Point cloud data
- **RectangleAreaMapLayer**: Rectangular area definitions
- **PolygonAreaMapLayer**: Polygon area definitions

**ImageFeaturesMapLayer information includes:**
- Feature type (Cartographer, DBoW ORB, VSLAM, etc.)
- Number of feature observations
- Camera poses for each observation
- Features data size and statistics
- Sample observation details
```
### Using ROS2 Service Commands

```bash
# Upload a file (you'll need to encode the binary data)
ros2 service call /upload_stcm_file stcm_manager/srv/UploadStcmFile \
  "{file_name: 'test.stcm', file_data: [/* binary data */]}"

# Get the current STCM file
ros2 service call /get_stcm_file stcm_manager/srv/GetStcmFile "{}"

# Get known area (returns Rectangle2D with actual map bounds)
ros2 service call /get_known_area stcm_manager/srv/GetKnownArea "{}"

# Clear map and related files
ros2 service call /clear_map stcm_manager/srv/ClearMap "{}"

# Save current map
ros2 service call /save_map stcm_manager/srv/SaveMap "{}"
```

**Service Details:**

**GetKnownArea**: Returns actual map dimensions from:
- **Loaded STCM file**: Extracts dimensions and origin from GridMapLayer
- **Subscribed map**: Uses dimensions and origin from received OccupancyGrid message
- **Returns Rectangle2D**: `x_min`, `y_min`, `width`, `height` in meters
- **Fallback**: Returns default 20x20m area if map data is unavailable

**ClearMap**: Performs comprehensive cleanup:
- Deletes the main STCM map file (`robot_map.stcm`)
- Scans the map directory and removes related files with extensions:
  - `.pbstream` (Cartographer map files)
  - `.pgm` (Occupancy grid images)
  - `.yaml` and `.yml` (Map metadata files)
- Cancels any active map subscriptions
- Resets internal map state

**SaveMap**: Saves the current map to the fixed location:
- Copies the current map file to `storage_path/robot_map.stcm`
- Returns the saved file name in the response

## Configuration

The service can be configured using parameters:

- `map_storage_path`: Directory for storing STCM files (default: `/tmp/stcm_files`)
  - When launched via `rslamware.launch.py`, this is automatically set based on mode:
    - `real` mode: `/opt/rslamware_data/maps/`
    - `simulation`/`replay` mode: `<simulator_package>/maps/`
- `allowed_extensions`: List of allowed file extensions (default: `[".stcm"]`)

Configuration can be set via:
1. Launch file parameters
2. Parameter file (`config/stcm_service_params.yaml`)
3. ROS2 parameter commands

## Dependencies

- ROS2 (tested with Humble)
- rpos_common (STCM library)
- nav2_map_server (for PGM map saving)
- cartographer_ros (for pbstream file generation)
- Standard C++17 libraries

## MapManager Features

The `MapManager` class provides comprehensive map management:

- **Map Loading**: Automatically loads STCM files or subscribes to map topics
- **Map Storage**: Manages map files in configurable storage directories
- **Format Support**: Handles STCM, PGM, YAML, and pbstream formats
- **Map Bounds**: Extracts actual map dimensions from loaded data
- **Map Clearing**: Comprehensive cleanup of all related map files
- **Map Saving**: Saves current maps with proper file naming

## Security Considerations

- File paths are sanitized to prevent directory traversal attacks
- Only files with allowed extensions can be uploaded/downloaded
- Storage paths are configurable and validated
- Binary data transfer is handled safely

## Troubleshooting

### Service Not Available
```bash
# Check if the service is running
ros2 service list | grep stcm

# Check service node status
ros2 node list | grep stcm_service_node
```

### Permission Issues
Make sure the storage directory has proper write permissions:
```bash
sudo chmod 755 /tmp/stcm_files
```

### Large File Issues
Increase the file size limit or check available disk space:
```bash
df -h /tmp/stcm_files
```

## Map Conversion and Processing

### Integrated Map Converter

The `test_map_converter` tool combines multiple map formats into a single STCM file:

**Features:**
- Converts Cartographer pbstream files to ImageFeatureMapLayer
- Converts PGM/YAML files to GridMapLayer
- Combines both layers into a single STCM file
- Automatic file detection and processing
- Comprehensive error handling and validation

**Usage:**
```bash
# Convert maps from a folder containing .pbstream, .pgm, and .yaml files
ros2 run stcm_manager test_map_converter /path/to/map/folder
```

**Input Requirements:**
- `*.pbstream` file (Cartographer output)
- `*.pgm` file (occupancy grid image)
- `*.yaml` file (map metadata)

**Output:**
- `robot_map.stcm` file with both ImageFeatureMapLayer and GridMapLayer

### STCM File Analysis

The `test_composite_map_reader` tool provides detailed analysis of STCM files:

**Features:**
- Reads and analyzes complete STCM file structure
- Extracts information from all map layers
- Displays layer statistics and metadata
- Supports all STCM layer types

**Usage:**
```bash
# Analyze an STCM file
ros2 run stcm_manager test_composite_map_reader /path/to/file.stcm
```

**Supported Layer Types:**
- **GridMapLayer**: Occupancy grid maps with statistics
- **ImageFeaturesMapLayer**: Image features from SLAM systems
- **LineMapLayer**: Line-based map features
- **PoseMapLayer**: Named poses and waypoints
- **PointsMapLayer**: Point cloud data
- **RectangleAreaMapLayer**: Rectangular area definitions
- **PolygonAreaMapLayer**: Polygon area definitions

### Map Generation with savePgmMap

The MapManager includes a `savePgmMap` function that generates maps using ROS2 service clients:

**Process:**
1. **Generate PGM/YAML**: Uses `nav2_msgs/srv/SaveMap` service client to call `/map_saver/save_map`
2. **Generate pbstream**: Uses `cartographer_ros_msgs/srv/WriteState` service client to call `/write_state`
3. **Create STCM**: Uses `test_map_converter` to combine files into `robot_map.stcm`

**Service Integration:**
- **SaveMap Service**: Calls nav2_map_server with parameters (map_topic="/map", free_thresh=0.196, etc.)
- **WriteState Service**: Calls cartographer to save pbstream file
- **Timeout Handling**: 30-second timeout for each service call with proper error handling
- **Asynchronous Calls**: Uses `async_send_request` with `spin_until_future_complete`

**Generated Files:**
- `map.pgm`: Occupancy grid image
- `map.yaml`: Map metadata (resolution, origin, thresholds)
- `map.pbstream`: Cartographer state file
- `robot_map.stcm`: Combined STCM file with both layers

**Usage:**
This function is called internally by the MapManager when saving maps from live SLAM data.

### Occupancy Value Mapping

The system uses the following mapping for occupancy values:
- **-127**: Obstacle/occupied space (black pixels in PGM)
- **127**: Free space (white pixels in PGM)
- **0**: Unknown areas (gray pixels in PGM)

This mapping ensures compatibility between different map formats and tools.
