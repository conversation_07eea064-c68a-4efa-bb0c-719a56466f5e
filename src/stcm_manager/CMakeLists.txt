cmake_minimum_required(VERSION 3.8)
project(stcm_manager)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

add_compile_options(-Wno-unused-parameter)  # GCC/Clang

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(std_srvs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(interfaces REQUIRED)
find_package(rpos_common REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(jsoncpp REQUIRED)
find_package(cartographer_ros_msgs REQUIRED)

# Use Eigen from 3rdparty directory instead of system Eigen
get_filename_component(PROJECT_ROOT_DIR ${CMAKE_SOURCE_DIR}/../.. ABSOLUTE)
set(EIGEN3_INCLUDE_DIR ${PROJECT_ROOT_DIR}/3rdparty/eigen)
message(STATUS "Using Eigen from: ${EIGEN3_INCLUDE_DIR}")

# jsoncpp_static target is already available from rpos_common dependency

# Generate service interfaces
rosidl_generate_interfaces(${PROJECT_NAME}
  "srv/GetStcmFile.srv"
  "srv/UploadStcmFile.srv"
  "srv/GetKnownArea.srv"
  "srv/ClearMap.srv"
  "srv/SaveMap.srv"
  "srv/AddVirtualWall.srv"
  "srv/DeleteVirtualWall.srv"
  "srv/ModifyVirtualWall.srv"
  "srv/ClearVirtualWalls.srv"
  "srv/GetVirtualWalls.srv"
  DEPENDENCIES std_msgs geometry_msgs interfaces
)

# Create library for PGM conversion, pbstream conversion, and map management
add_library(${PROJECT_NAME}_lib
  src/pgm_to_grid_converter.cpp
  src/pbstream_to_maplayer_converter.cpp
  src/map_manager.cpp
  src/temp_directory_manager.cpp
  src/virtual_wall_manager.cpp
)

target_include_directories(${PROJECT_NAME}_lib PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
  ${EIGEN3_INCLUDE_DIR}
)

ament_target_dependencies(${PROJECT_NAME}_lib
  rclcpp
  nav_msgs
  rpos_common
  cartographer_ros_msgs
)
target_link_libraries(${PROJECT_NAME}_lib jsoncpp)

# Create executable
add_executable(stcm_manager_node src/stcm_service_node.cpp)
ament_target_dependencies(stcm_manager_node
  rclcpp
  std_msgs
  std_srvs
  geometry_msgs
  nav_msgs
  interfaces
)

# Link the generated interfaces
rosidl_get_typesupport_target(cpp_typesupport_target ${PROJECT_NAME} "rosidl_typesupport_cpp")
target_link_libraries(stcm_manager_node "${cpp_typesupport_target}" ${PROJECT_NAME}_lib)

# Create test client executable
add_executable(test_stcm_client test/test_stcm_client.cpp)
ament_target_dependencies(test_stcm_client
  rclcpp
  std_msgs
  std_srvs
  geometry_msgs
)
target_link_libraries(test_stcm_client "${cpp_typesupport_target}")

# PGM converter test integrated into test_map_converter

# pbstream_to_maplayer executable temporarily disabled (missing main file)

# Create composite map reader test executable
add_executable(test_composite_map_reader test/test_composite_map_reader.cpp)
target_include_directories(test_composite_map_reader PRIVATE
  ${EIGEN3_INCLUDE_DIR}
)
ament_target_dependencies(test_composite_map_reader
  rpos_common
)
# Link jsoncpp_static to resolve symbols used by rpos_common
target_link_libraries(test_composite_map_reader jsoncpp)

# pbstream converter test temporarily disabled (private method access issues)

# Create map converter test executable (integrates pbstream, pgm, yaml conversion)
add_executable(test_map_converter test/test_map_converter.cpp)
target_link_libraries(test_map_converter ${PROJECT_NAME}_lib)
ament_target_dependencies(test_map_converter
  rpos_common
)

# Install targets
install(TARGETS
  ${PROJECT_NAME}_lib
  stcm_manager_node
  test_stcm_client
  test_composite_map_reader
  test_map_converter
  EXPORT ${PROJECT_NAME}_targets
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION lib/${PROJECT_NAME}
  INCLUDES DESTINATION include
)

# Install launch files
install(DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}/
)

# Config files installation disabled (no config directory)

# Install include directory
install(DIRECTORY include/
  DESTINATION include/
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_export_dependencies(rosidl_default_runtime)
ament_package()
