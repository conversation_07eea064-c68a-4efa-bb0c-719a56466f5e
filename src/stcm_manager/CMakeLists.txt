cmake_minimum_required(VERSION 3.8)
project(stcm_manager)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

add_compile_options(-Wno-unused-parameter)  # GCC/Clang

# Search for Slamware SDK
find_path(slamware_sdk_INCLUDE_DIR rpos/rpos.h ${PROJECT_SOURCE_DIR}/../slamkit_ros_bridge/slamware_sdk/include)
find_path(slamware_sdk_LIBRARY librpos_framework.a ${PROJECT_SOURCE_DIR}/../slamkit_ros_bridge/slamware_sdk/lib)
if(slamware_sdk_INCLUDE_DIR AND slamware_sdk_LIBRARY)
  set(SLTC_SDK_INC_DIR "${slamware_sdk_INCLUDE_DIR}")
  set(SLTC_SDK_LIB_DIR "${slamware_sdk_LIBRARY}")
else()
  message(FATAL_ERROR "Slamware SDK directory not found")
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(std_srvs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(visualization_msgs REQUIRED)
find_package(interfaces REQUIRED)
find_package(rpos_common REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(cartographer_ros_msgs REQUIRED)
find_package(opennav_docking_msgs REQUIRED)
find_package(nav2_util REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(nav2_msgs REQUIRED)
find_package(nav2_map_server REQUIRED)

# Use Eigen from 3rdparty directory instead of system Eigen
get_filename_component(PROJECT_ROOT_DIR ${CMAKE_SOURCE_DIR}/../.. ABSOLUTE)
set(EIGEN3_INCLUDE_DIR ${PROJECT_ROOT_DIR}/3rdparty/eigen)
message(STATUS "Using Eigen from: ${EIGEN3_INCLUDE_DIR}")

# jsoncpp_static target is already available from rpos_common dependency

# Generate service interfaces
rosidl_generate_interfaces(${PROJECT_NAME}
  "srv/GetStcmFile.srv"
  "srv/UploadStcmFile.srv"
  "srv/GetKnownArea.srv"
  "srv/ClearMap.srv"
  "srv/SaveMap.srv"
  "srv/AddVirtualWall.srv"
  "srv/DeleteVirtualWall.srv"
  "srv/ModifyVirtualWall.srv"
  "srv/ClearVirtualWalls.srv"
  "srv/GetVirtualWalls.srv"
  "srv/AddSensorDisableArea.srv"
  "srv/DeleteSensorDisableArea.srv"
  "srv/ModifySensorDisableArea.srv"
  "srv/ClearSensorDisableAreas.srv"
  "srv/GetSensorDisableAreas.srv"
  "srv/AddDangerousArea.srv"
  "srv/DeleteDangerousArea.srv"
  "srv/ModifyDangerousArea.srv"
  "srv/ClearDangerousAreas.srv"
  "srv/GetDangerousAreas.srv"
  DEPENDENCIES std_msgs geometry_msgs interfaces
)

# Create library for PGM conversion, pbstream conversion, and map management
add_library(${PROJECT_NAME}_lib
  src/pgm_to_grid_converter.cpp
  src/pbstream_to_maplayer_converter.cpp
  src/map_manager.cpp
  src/virtual_wall_manager.cpp
  src/sensor_disable_area_manager.cpp
  src/dangerous_area_manager.cpp
  src/poi_manager.cpp
)

target_include_directories(${PROJECT_NAME}_lib PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
  ${EIGEN3_INCLUDE_DIR}
  ${nav2_map_server_INCLUDE_DIRS}
  ${SLTC_SDK_INC_DIR}
)

ament_target_dependencies(${PROJECT_NAME}_lib
  rclcpp
  nav_msgs
  rpos_common
  cartographer_ros_msgs
  opennav_docking_msgs
  nav2_util
  tf2_ros
  tf2_geometry_msgs
  visualization_msgs
  nav2_msgs
  nav2_map_server
)

# Link the generated interfaces to the library
rosidl_get_typesupport_target(cpp_typesupport_target_lib ${PROJECT_NAME} "rosidl_typesupport_cpp")
target_link_libraries(${PROJECT_NAME}_lib "${cpp_typesupport_target_lib}"
  ${SLTC_SDK_LIB_DIR}/librpos_robotplatforms_rpslamware.a
  ${SLTC_SDK_LIB_DIR}/librpos_framework.a
  ${SLTC_SDK_LIB_DIR}/libbase64.a
  ${SLTC_SDK_LIB_DIR}/librlelib.a
  -Wl,--whole-archive
  ${SLTC_SDK_LIB_DIR}/libjsoncpp.a
  -Wl,--no-whole-archive
  ${SLTC_SDK_LIB_DIR}/libcurl.a
  ${SLTC_SDK_LIB_DIR}/libcares.a
  ${SLTC_SDK_LIB_DIR}/libssl.a
  ${SLTC_SDK_LIB_DIR}/libcrypto.a
  ${SLTC_SDK_LIB_DIR}/libboost_atomic.a
  ${SLTC_SDK_LIB_DIR}/libboost_chrono.a
  ${SLTC_SDK_LIB_DIR}/libboost_date_time.a
  ${SLTC_SDK_LIB_DIR}/libboost_regex.a 
  ${SLTC_SDK_LIB_DIR}/libboost_filesystem.a
  ${SLTC_SDK_LIB_DIR}/libboost_system.a
  ${SLTC_SDK_LIB_DIR}/libboost_thread.a
  ${SLTC_SDK_LIB_DIR}/libboost_random.a
  ${SLTC_SDK_LIB_DIR}/libz.a
  pthread
  dl
  rt
)

# Create executable
add_executable(stcm_manager_node src/stcm_service_node.cpp)
ament_target_dependencies(stcm_manager_node
  rclcpp
  std_msgs
  std_srvs
  geometry_msgs
  cartographer_ros_msgs
  visualization_msgs
  nav_msgs
  interfaces
  nav2_msgs
  tf2_ros
  nav2_map_server
)

target_include_directories(stcm_manager_node
  PRIVATE ${SLTC_SDK_INC_DIR}
)

# Link the generated interfaces
rosidl_get_typesupport_target(cpp_typesupport_target ${PROJECT_NAME} "rosidl_typesupport_cpp")
target_link_libraries(stcm_manager_node
  "${cpp_typesupport_target}"
  -Wl,--allow-multiple-definition
  ${PROJECT_NAME}_lib
  pthread
  dl
  rt)
# target_link_libraries(dangerous_area_manager_node "${cpp_typesupport_target}" ${PROJECT_NAME}_lib)

# Create test client executable
add_executable(test_stcm_client test/test_stcm_client.cpp)
ament_target_dependencies(test_stcm_client
  rclcpp
  std_msgs
  std_srvs
  geometry_msgs
)
target_link_libraries(test_stcm_client "${cpp_typesupport_target}")

# PGM converter test integrated into test_map_converter

# pbstream_to_maplayer executable temporarily disabled (missing main file)

# Create composite map reader test executable
add_executable(test_composite_map_reader test/test_composite_map_reader.cpp)
target_include_directories(test_composite_map_reader PRIVATE
  ${EIGEN3_INCLUDE_DIR}
)
ament_target_dependencies(test_composite_map_reader
  rpos_common
)
# Link jsoncpp_static to resolve symbols used by rpos_common
target_link_libraries(test_composite_map_reader
  -Wl,--allow-multiple-definition
  -Wl,--whole-archive
  ${SLTC_SDK_LIB_DIR}/libjsoncpp.a
  -Wl,--no-whole-archive
)

# pbstream converter test temporarily disabled (private method access issues)

# Create map converter test executable (integrates pbstream, pgm, yaml conversion)
add_executable(test_map_converter test/test_map_converter.cpp)
target_link_libraries(test_map_converter
  -Wl,--allow-multiple-definition
  ${PROJECT_NAME}_lib)
ament_target_dependencies(test_map_converter
  rpos_common
)

# Install targets
install(TARGETS
  ${PROJECT_NAME}_lib
  stcm_manager_node
  test_stcm_client
  test_composite_map_reader
  test_map_converter
  EXPORT ${PROJECT_NAME}_targets
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION lib/${PROJECT_NAME}
  INCLUDES DESTINATION include
)

# Install launch files
install(DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}/
)

# Config files installation disabled (no config directory)

# Install include directory
install(DIRECTORY include/
  DESTINATION include/
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_export_dependencies(rosidl_default_runtime)
ament_package()
