// Copyright (c) 2024 Slamtec
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef STCM_MANAGER__VIRTUAL_WALL_MANAGER_HPP_
#define STCM_MANAGER__VIRTUAL_WALL_MANAGER_HPP_

#include <map>
#include <memory>
#include <mutex>
#include <vector>

#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/point.hpp"
#include "nav_msgs/msg/occupancy_grid.hpp"
#include "stcm/line_map_layer.h"
#include "stcm_manager/srv/add_virtual_wall.hpp"
#include "stcm_manager/srv/delete_virtual_wall.hpp"
#include "stcm_manager/srv/modify_virtual_wall.hpp"
#include "stcm_manager/srv/clear_virtual_walls.hpp"
#include "stcm_manager/srv/get_virtual_walls.hpp"

#include "core/geometry.h"

namespace stcm_manager
{

/**
 * @brief Virtual wall manager for STCM maps
 * 
 * This class manages virtual walls in STCM maps, providing services to add,
 * delete, modify, and clear virtual walls. Virtual walls are represented
 * as Line objects from rpos_common.
 */
class VirtualWallManager
{
public:
  /**
   * @brief Constructor
   * @param node ROS2 node for service creation
   */
  explicit VirtualWallManager(std::shared_ptr<rclcpp::Node> node,
                  const std::string& virtual_wall_mask_topic);

  /**
   * @brief Destructor
   */
  ~VirtualWallManager();

  /**
   * @brief Initialize the virtual wall manager
   * @return true if initialization successful
   */
  bool initialize();

  void setGridMapSize( uint32_t width, uint32_t height, float resolution, float origin_x, float origin_y);
  
  void loadFromMapLayer(std::shared_ptr<rpos::stcm::LineMapLayer> map_layer);

  /**
   * @brief Add a virtual wall
   * @param start_point Start point of the wall
   * @param end_point End point of the wall
   * @param wall_id Requested wall ID (0 for auto-assign)
   * @return Assigned wall ID, -1 if failed
   */
  int32_t addVirtualWall(
    const geometry_msgs::msg::Point & start_point,
    const geometry_msgs::msg::Point & end_point,
    int32_t wall_id = 0);

  /**
   * @brief Delete a virtual wall
   * @param wall_id ID of the wall to delete
   * @return true if deletion successful
   */
  bool deleteVirtualWall(int32_t wall_id);

  /**
   * @brief Modify a virtual wall
   * @param wall_id ID of the wall to modify
   * @param start_point New start point
   * @param end_point New end point
   * @return true if modification successful
   */
  bool modifyVirtualWall(
    int32_t wall_id,
    const geometry_msgs::msg::Point & start_point,
    const geometry_msgs::msg::Point & end_point);

  /**
   * @brief Clear all virtual walls
   * @return Number of walls cleared
   */
  int32_t clearVirtualWalls();

  /**
   * @brief Get all virtual walls
   * @param start_points Output array of start points
   * @param end_points Output array of end points
   * @param wall_ids Output array of wall IDs
   */
  void getVirtualWalls(
    std::vector<geometry_msgs::msg::Point> & start_points,
    std::vector<geometry_msgs::msg::Point> & end_points,
    std::vector<int32_t> & wall_ids) const;

  /**
   * @brief Get virtual wall as rpos::core::Line
   * @param wall_id ID of the wall
   * @return Pointer to Line object, nullptr if not found
   */
  std::shared_ptr<rpos::core::Line> getVirtualWallAsLine(int32_t wall_id) const;

  /**
   * @brief Get all virtual walls as rpos::core::Line objects
   * @return Vector of Line objects
   */
  std::vector<std::shared_ptr<rpos::core::Line>> getAllVirtualWallsAsLines() const;

  void publishVirtualWalls( nav_msgs::msg::OccupancyGrid::SharedPtr gridmap);
private:

  /**
   * @brief Service callback for adding virtual wall
   */
  void addVirtualWallCallback(
    const std::shared_ptr<void> request,
    std::shared_ptr<void> response);

  /**
   * @brief Service callback for deleting virtual wall
   */
  void deleteVirtualWallCallback(
    const std::shared_ptr<void> request,
    std::shared_ptr<void> response);

  /**
   * @brief Service callback for modifying virtual wall
   */
  void modifyVirtualWallCallback(
    const std::shared_ptr<void> request,
    std::shared_ptr<void> response);

  /**
   * @brief Service callback for clearing virtual walls
   */
  void clearVirtualWallsCallback(
    const std::shared_ptr<void> request,
    std::shared_ptr<void> response);

  /**
   * @brief Service callback for getting virtual walls
   */
  void getVirtualWallsCallback(
    const std::shared_ptr<void> request,
    std::shared_ptr<void> response);

  /**
   * @brief Convert geometry_msgs::Point to rpos::core::Point
   */
  rpos::core::Point convertToRposPoint(const geometry_msgs::msg::Point & point) const;

  /**
   * @brief Convert rpos::core::Point to geometry_msgs::Point
   */
  geometry_msgs::msg::Point convertToGeometryPoint(const rpos::core::Point & point) const;

  /**
   * @brief Generate next available wall ID
   */
  int32_t generateNextWallId();

  std::shared_ptr<rclcpp::Node> node_;
  // Virtual wall services
  rclcpp::Service<stcm_manager::srv::AddVirtualWall>::SharedPtr add_virtual_wall_service_;
  rclcpp::Service<stcm_manager::srv::DeleteVirtualWall>::SharedPtr delete_virtual_wall_service_;
  rclcpp::Service<stcm_manager::srv::ModifyVirtualWall>::SharedPtr modify_virtual_wall_service_;
  rclcpp::Service<stcm_manager::srv::ClearVirtualWalls>::SharedPtr clear_virtual_walls_service_;
  rclcpp::Service<stcm_manager::srv::GetVirtualWalls>::SharedPtr get_virtual_walls_service_;

  // Virtual walls storage
  std::map<int32_t, std::shared_ptr<rpos::core::Line>> virtual_walls_;
  
  // Thread safety
  mutable std::mutex walls_mutex_;
  
  // Next available wall ID
  int32_t next_wall_id_;

  rclcpp::Publisher<nav_msgs::msg::OccupancyGrid>::SharedPtr virtual_wall_publisher_;
  std::string virtual_wall_mask_topic_;

  // Map parameters for occupancy grid generation
  float map_resolution_;
  uint32_t map_width_;
  uint32_t map_height_;
  double map_origin_x_;
  double map_origin_y_;
};

}  // namespace stcm_manager

#endif  // STCM_MANAGER__VIRTUAL_WALL_MANAGER_HPP_ 