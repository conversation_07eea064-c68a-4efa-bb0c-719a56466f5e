// Copyright (c) 2024 Slamtec
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef STCM_MANAGER__POI_MANAGER_HPP_
#define STCM_MANAGER__POI_MANAGER_HPP_

// Forward declaration of MapManager
namespace rslamware { namespace stcm_manager {
    class MapManager;
}} // namespace rslamware::stcm_manager

#include "interfaces/srv/add_poi.hpp"
#include "interfaces/srv/remove_poi.hpp"
#include "interfaces/srv/get_poi.hpp"
#include "interfaces/srv/list_poi.hpp"
#include "interfaces/srv/remove_all_poi.hpp"
#include "interfaces/srv/update_poi.hpp"
#include "interfaces/msg/poi.hpp"

#include <map>
#include <memory>
#include <mutex>
#include <vector>
#include <string>

#include <rclcpp/rclcpp.hpp>

namespace stcm_manager
{
/**
 * @brief POI (Point of Interest) manager for STCM maps
 * 
 * This class manages POIs in STCM maps, providing services to add,
 * delete, modify, and query POIs.
 */
class POIManager
{
public:
  /**
   * @brief Constructor
   * @param node ROS2 node for service creation
   * @param map_manager Reference to MapManager
   * @param slamkit_platform_mutex Mutex for SlamwareCorePlatform access
   */
  explicit POIManager(std::shared_ptr<rclcpp::Node> node, rslamware::stcm_manager::MapManager& map_manager,
    std::shared_ptr<std::recursive_mutex> slamkit_platform_mutex);

  /**
   * @brief Destructor
   */
  ~POIManager();

  /**
   * @brief Initialize the POI manager
   * @return true if initialization successful
   */
  bool initialize();

  /**
   * @brief Add a POI
   * @param poi The POI to add
   * @param error_msg Output parameter message
   * @return true if addition successful
   */
  bool addPOI(const interfaces::msg::POI & poi, std::string & error_msg);

  /**
   * @brief Delete a POI
   * @param id ID of the POI to delete
   * @param error_msg Output parameter message
   * @return true if deletion successful
   */
  bool deletePOI(const std::string & id, std::string & error_msg);

  /**
   * @brief Update a POI
   * @param poi The updated POI data
   * @param error_msg Output parameter message
   * @return true if update successful
   */
  bool updatePOI(const interfaces::msg::POI & poi, std::string & error_msg);

  /**
   * @brief Clear all POIs
   * @param error_msg Output parameter message
   * @return true if clear successful
   */
  bool clearAllPOIs(std::string & error_msg);


  /**
   * @brief Get a POI by ID
   * @param id ID of the POI to retrieve
   * @param poi Output parameter to store the retrieved POI
   * @return true if POI found
   */
  bool getPOI(const std::string & id, interfaces::msg::POI & poi) const;

  /**
   * @brief Get all POIs
   * @param pois Output vector to store the POIs
   */
  void getAllPOIs(std::vector<interfaces::msg::POI> & pois) const;

private:
  /**
   * @brief Service callback for adding POI
   */
  void addPOICallback(
    const std::shared_ptr<interfaces::srv::AddPOI::Request> request,
    std::shared_ptr<interfaces::srv::AddPOI::Response> response);

  /**
   * @brief Service callback for deleting POI
   */
  void deletePOICallback(
    const std::shared_ptr<interfaces::srv::RemovePOI::Request> request,
    std::shared_ptr<interfaces::srv::RemovePOI::Response> response);

  /**
   * @brief Service callback for updating POI
   */
  void updatePOICallback(
    const std::shared_ptr<interfaces::srv::UpdatePOI::Request> request,
    std::shared_ptr<interfaces::srv::UpdatePOI::Response> response);

  /**
   * @brief Service callback for clearing all POIs
   */
  void clearAllPOIsCallback(
    const std::shared_ptr<interfaces::srv::RemoveAllPOI::Request> request,
    std::shared_ptr<interfaces::srv::RemoveAllPOI::Response> response);

  /**
   * @brief Service callback for getting POI
   */
  void getPOICallback(
    const std::shared_ptr<interfaces::srv::GetPOI::Request> request,
    std::shared_ptr<interfaces::srv::GetPOI::Response> response);

  /**
   * @brief Service callback for listing all POIs
   */
  void listPOICallback(
    const std::shared_ptr<interfaces::srv::ListPOI::Request> request,
    std::shared_ptr<interfaces::srv::ListPOI::Response> response);

  // Member variables
  std::shared_ptr<rclcpp::Node> node_;
  rslamware::stcm_manager::MapManager& map_manager_;
  std::shared_ptr<std::recursive_mutex> slamkit_platform_mutex_;

  // POI services
  rclcpp::Service<interfaces::srv::AddPOI>::SharedPtr add_poi_service_;
  rclcpp::Service<interfaces::srv::RemovePOI>::SharedPtr delete_poi_service_;
  rclcpp::Service<interfaces::srv::UpdatePOI>::SharedPtr update_poi_service_;
  rclcpp::Service<interfaces::srv::RemoveAllPOI>::SharedPtr clear_all_poi_service_;
  rclcpp::Service<interfaces::srv::GetPOI>::SharedPtr get_poi_service_;
  rclcpp::Service<interfaces::srv::ListPOI>::SharedPtr list_poi_service_;

  // POI storage
  std::map<std::string, interfaces::msg::POI> pois_;
  mutable std::mutex pois_mutex_;
};

}  // namespace stcm_manager

#endif  // STCM_MANAGER__POI_MANAGER_HPP_