#pragma once

#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/occupancy_grid.hpp>
#include <string>
#include <memory>
#include <filesystem>
#include <thread>
#include <atomic>
#include <stcm/composite_map.h>
#include <stcm/grid_map_layer.h>
#include <stcm/pose_map_layer.h>
#include <stcm/line_map_layer.h>
#include <stcm/rectangle_area_map_layer.h>
#include "cartographer_ros_msgs/srv/add_poi.hpp"
#include "cartographer_ros_msgs/srv/list_poi.hpp"
#include "cartographer_ros_msgs/msg/poi.hpp"
#include "stcm_manager/virtual_wall_manager.hpp"

namespace rslamware { namespace stcm_manager {

    class MapManager
    {
    public:
        MapManager(rclcpp::Node::SharedPtr node,
                  const std::string& virtual_wall_topic = "/keepout_filter_mask",
                  const std::string& map_server_topic = "/map");
        ~MapManager();

        /**
         * @brief Initialize map manager asynchronously
         * Check if map file exists, if yes load STCM, if no subscribe to map_server/map
         * This function returns immediately and performs initialization in background thread
         * @param map_file_path Path to the STCM map file
         */
        void initialize(const std::string& map_file_path);

        /**
         * @brief Clear map data and delete map file
         * @return true if clear successful, false otherwise
         */
        bool clearMap();

        /**
         * @brief Get the current map file path
         * @return Current map file path
         */
        const std::string& getMapFilePath() const { return map_file_path_; }

        /**
         * @brief Check if map is loaded
         * @return true if map is loaded, false otherwise
         */
        bool isMapLoaded() const { return map_loaded_; }

        /**
         * @brief Get known area as Rectangle2D
         * Returns map bounds from loaded STCM file or received map topic
         * @param x_min Output parameter for minimum x coordinate
         * @param y_min Output parameter for minimum y coordinate
         * @param width Output parameter for map width in meters
         * @param height Output parameter for map height in meters
         * @return true if map bounds are available, false otherwise
         */
        bool getKnownArea(float& x_min, float& y_min, float& width, float& height) const;

        /**
         * @brief Save current map as STCM file
         * @param file_path Full path where to save the map file
         * @return true if map saved successfully, false otherwise
         */
        bool saveMap(const std::string& file_path);

        bool onStcmFileUploaded(const std::string& file_path, std::string& error_msg);

    private:
        bool savePgmMap(const std::string &map_base, const std::string& pbstream);

        bool loadLineMapLayer(const std::shared_ptr<rpos::stcm::LineMapLayer>& layer);
        bool loadPoseEntryMapLayer(const std::shared_ptr<rpos::stcm::PoseMapLayer>& layer);
        bool loadRectangleAreaMapLayer(const std::shared_ptr<rpos::stcm::RectangleAreaMapLayer>& layer);

        std::shared_ptr<rpos::stcm::PoseMapLayer> createPOILayer(); 
        std::shared_ptr<rpos::stcm::LineMapLayer> createVirtualWallLayer();
        std::shared_ptr<rpos::stcm::LineMapLayer> createVirtualTrackLayer();
        /**
         * @brief Load STCM map file
         * @param file_path Path to STCM file
         * @return true if load successful, false otherwise
         */
        bool loadStcmFile(const std::string& file_path, bool createPgm, std::string& error_msg);

        /**
         * @brief Subscribe to map topic from map_server
         */
        void subscribeToMapServer();

        /**
         * @brief Callback for map topic subscription
         * @param msg Occupancy grid message
         */
        void mapCallback(const nav_msgs::msg::OccupancyGrid::SharedPtr msg);
 
        /**
         * @brief Async initialization worker function
         * @param map_file_path Path to the STCM map file
         */
        void asyncInitializeWorker(const std::string& map_file_path);

        // Member variables
        rclcpp::Node::SharedPtr node_;
        std::string map_file_path_;

        // Configuration parameters
        std::string virtual_wall_topic_;
        std::string map_server_topic_;
        bool map_loaded_;
        bool subscribed_to_map_server_;

        // Map dimension and origin information
        uint32_t map_width_;
        uint32_t map_height_;
        float map_resolution_;
        float map_origin_x_;
        float map_origin_y_;
        bool map_dimensions_available_;

        // ROS2 subscription and publisher
        rclcpp::Subscription<nav_msgs::msg::OccupancyGrid>::SharedPtr map_subscription_; 

        // POI service clients
        rclcpp::Client<cartographer_ros_msgs::srv::AddPOI>::SharedPtr add_poi_client_;
        rclcpp::Client<cartographer_ros_msgs::srv::ListPOI>::SharedPtr list_poi_client_;

        // Timer for checking map updates
        rclcpp::TimerBase::SharedPtr check_timer_;
        std::shared_ptr<rpos::stcm::CompositeMap> composite_map_;

        // Async initialization
        std::thread initialization_thread_;
        std::atomic<bool> initialization_in_progress_;
        std::atomic<bool> initialization_completed_;
        ::stcm_manager::VirtualWallManager wallManager_;
    };

}} // namespace rslamware::stcm_manager