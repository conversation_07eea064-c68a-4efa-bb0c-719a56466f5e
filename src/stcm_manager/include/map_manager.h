#pragma once

#include "stcm_manager/virtual_wall_manager.hpp"
#include "stcm_manager/sensor_disable_area_manager.hpp"
#include "stcm_manager/dangerous_area_manager.hpp"
#include "stcm_manager/poi_manager.hpp"
#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/occupancy_grid.hpp>
#include <stcm/composite_map.h>
#include <stcm/grid_map_layer.h>
#include <stcm/pose_map_layer.h>
#include <stcm/line_map_layer.h>
#include <stcm/rectangle_area_map_layer.h>
#include <opennav_docking_msgs/srv/set_docks.hpp>
#include <opennav_docking_msgs/srv/get_all_docks.hpp>
#include <opennav_docking_msgs/srv/delete_dock.hpp>
#include <cartographer_ros_msgs/srv/add_poi.hpp>
#include <cartographer_ros_msgs/srv/list_poi.hpp>
#include <cartographer_ros_msgs/msg/poi.hpp>
#include <cartographer_ros_msgs/srv/start_trajectory.hpp>
#include <cartographer_ros_msgs/srv/finish_all_trajectories.hpp>
#include <cartographer_ros_msgs/srv/get_trajectory_states.hpp>
#include <cartographer_ros_msgs/srv/set_map_topic.hpp>
#include <cartographer_ros_msgs/srv/load_state.hpp>
#include <cartographer_ros_msgs/srv/write_state.hpp>
#include <cartographer_ros_msgs/srv/set_localization_mode.hpp>
#include <cartographer_ros_msgs/srv/set_mapping_mode.hpp>
#include <cartographer_ros_msgs/srv/get_mode.hpp>
#include <rpos/robot_platforms/slamware_core_platform.h>
#include <rpos/robot_platforms/objects/composite_map.h>
#include <interfaces/msg/system_event.h>

#include <std_msgs/msg/int32.hpp>
#include <tf2_ros/buffer.h>
#include <tf2_ros/transform_listener.h>
#include <geometry_msgs/msg/pose_with_covariance_stamped.hpp>
#include <rclcpp/rclcpp.hpp>
#include <string>
#include <memory>
#include <filesystem>
#include <thread>
#include <atomic>
#include <mutex>

namespace rslamware { namespace stcm_manager {

    class MapManager
    {
    public:
        MapManager(rclcpp::Node::SharedPtr node,
                  const std::string& virtual_wall_topic = "/keepout_filter_mask",
                  const std::string& dangerous_area_topic = "/speed_filter_mask",
                  const std::string& map_server_topic = "/map",
                  const std::string& quality_topic = "/localization_quality",
                  const std::string& robot_events_topic = "/robot/events",
                  const std::string& map_server_type = "cartographer",
                  const std::string& platform_host = "127.0.0.1",
                  int platform_port = 1445);
        ~MapManager();

        /**
         * @brief Initialize map manager asynchronously
         * Check if map file exists, if yes load STCM, if no subscribe to map_server/map
         * This function returns immediately and performs initialization in background thread
         * @param map_file_path Path to the STCM map file
         */
        void initialize(const std::string& map_file_path);

        /**
         * @brief Clear map data and delete map file
         * @return true if clear successful, false otherwise
         */
        bool clearMap(std::string& error_msg);

        /**
         * @brief Get the current map file path
         * @return Current map file path
         */
        const std::string& getMapFilePath() const { return map_file_path_; }

        /**
         * @brief Get known area as Rectangle2D
         * Returns map bounds from loaded STCM file or received map topic
         * @param x_min Output parameter for minimum x coordinate
         * @param y_min Output parameter for minimum y coordinate
         * @param width Output parameter for map width in meters
         * @param height Output parameter for map height in meters
         * @return true if map bounds are available, false otherwise
         */
        bool getKnownArea(float& x_min, float& y_min, float& width, float& height) const;

        /**
         * @brief Save current map as STCM file
         * @param file_path Full path where to save the map file
         * @return true if map saved successfully, false otherwise
         */
        bool saveMap(const std::string& file_path, std::string& error_msg);

        bool savePgmMap(const std::string &map_base, const std::string& pbstream, std::string& error_msg, bool lite_version);

        bool onStcmFileUploaded(const std::string& file_path, std::string& error_msg);

        std::shared_ptr<rpos_common::stcm::CompositeMap> createCompositeMapFromPgm(const std::string& dir_path, bool lite_version, std::string& error_msg);

        std::shared_ptr<rpos_common::stcm::CompositeMap> createCompositeMapFromSlamkit(std::string& error_msg);

        bool setSLAMMode(const std::string& mode, bool reload_map, std::string& message);

        bool getSLAMMode(std::string& mode, std::string& error_msg);

        bool hasMapUpdated();
        
        /**
         * @brief Update planner parameters for different modes
         * @param is_mapping_mode Whether switching to mapping mode
         * @param error_msg Error message if failed
         * @return true if successful, false otherwise
         */
        bool updatePlannerParameters(bool is_mapping_mode, std::string& error_msg);

        /**
         * @brief Get the SlamwareCorePlatform instance
         * @return Shared pointer to SlamwareCorePlatform
         */
        std::shared_ptr<rpos::robot_platforms::SlamwareCorePlatform> getSlamwarePlatform();

    private:
        template<typename T>
        bool waitForFuture(std::future<T>& future, T& result, int timeout_ms = 2000)
        {
            auto start_time = std::chrono::steady_clock::now();
            auto timeout_duration = std::chrono::milliseconds(timeout_ms);

            while (rclcpp::ok()) {
                auto status = future.wait_for(std::chrono::milliseconds(100));
                if (status == std::future_status::ready) {
                    result = future.get();
                    return true;
                } 

                auto elapsed = std::chrono::steady_clock::now() - start_time;
                if (elapsed >= timeout_duration) {
                    return false;
                } 
            }
            return false;
        }
        bool waitForServiceAvailable(int timeout_ms);
        bool finishCurrentTrajectory(std::string& errMsg);
        bool writeStateToFile(const std::string& filename, std::string& errMsg);
        bool getRobotPose(geometry_msgs::msg::PoseWithCovarianceStamped& pose);
        void publishLocalPgmMap();
        void publishEmptyMap();
        bool switchMapPublisher(bool is_mapping_mode, bool need_publish_local_map, std::string& errMsg);


        bool loadLineMapLayer(const std::shared_ptr<rpos_common::stcm::LineMapLayer>& layer);
        bool loadPoseEntryMapLayer(const std::shared_ptr<rpos_common::stcm::PoseMapLayer>& layer);
        bool loadRectangleAreaMapLayer(const std::shared_ptr<rpos_common::stcm::RectangleAreaMapLayer>& layer);

        std::shared_ptr<rpos_common::stcm::PoseMapLayer> createPOILayer(); 
        std::shared_ptr<rpos_common::stcm::PoseMapLayer> createHomeDockLayer();
        std::shared_ptr<rpos_common::stcm::LineMapLayer> createVirtualWallLayer();
        std::shared_ptr<rpos_common::stcm::LineMapLayer> createVirtualTrackLayer();
        std::shared_ptr<rpos_common::stcm::RectangleAreaMapLayer> createRectangleAreaLayer();
        /**
         * @brief Load STCM map file
         * @param file_path Path to STCM file
         * @return true if load successful, false otherwise
         */
        bool loadStcmFile(const std::string& file_path, bool createPgm, std::string& error_msg);

        /**
         * @brief Subscribe to map topic from map_server
         */
        void subscribeToMapServer();

        /**
         * @brief Callback for map topic subscription
         * @param msg Occupancy grid message
         */
        void mapCallback(const nav_msgs::msg::OccupancyGrid::SharedPtr msg);
 
        /**
         * @brief Async initialization worker function
         * @param map_file_path Path to the STCM map file
         */
        void asyncInitializeWorker(const std::string& map_file_path);

        /**
         * @brief Slamkit worker function
         */
        void slamkitWorker();

        /**
         * @brief Timer callback for periodic mode checking
         */
        void modeCheckTimerCallback();

        /**
         * @brief Publish localization quality
         */
        void publishLocalizationQuality_();

        /**
         * @brief Publish robot event
         */
        void publishRobotEvent_();

    private:
        // Member variables
        rclcpp::Node::SharedPtr node_;
        std::string map_file_path_;

        // Configuration parameters
        std::string virtual_wall_topic_;
        std::string map_server_topic_;
        std::string quality_topic_;
        std::string robot_events_topic_;

        // Map dimension and origin information
        mutable std::mutex map_mutex_;
        nav_msgs::msg::OccupancyGrid::SharedPtr current_map_; 
        uint32_t map_width_;
        uint32_t map_height_;
        float map_resolution_;
        float map_origin_x_;
        float map_origin_y_;
        bool map_dimensions_available_;
        std::atomic<bool> has_map_updated_= false;

        // ROS2 subscription and publisher
        rclcpp::Subscription<nav_msgs::msg::OccupancyGrid>::SharedPtr map_subscription_; 
        rclcpp::Publisher<nav_msgs::msg::OccupancyGrid>::SharedPtr map_publisher_;
        rclcpp::Publisher<std_msgs::msg::Int32>::SharedPtr quality_publisher_;
        rclcpp::Publisher<interfaces::msg::SystemEvent>::SharedPtr robot_events_publisher_;

        // POI service clients
        rclcpp::Client<cartographer_ros_msgs::srv::AddPOI>::SharedPtr add_poi_client_;
        rclcpp::Client<cartographer_ros_msgs::srv::ListPOI>::SharedPtr list_poi_client_;
        // HomeDock service clients
        rclcpp::Client<opennav_docking_msgs::srv::SetDocks>::SharedPtr set_home_docks_client_;
        rclcpp::Client<opennav_docking_msgs::srv::GetAllDocks>::SharedPtr get_all_home_docks_client_;
        rclcpp::Client<opennav_docking_msgs::srv::DeleteDock>::SharedPtr delete_home_dock_client_;
    
        // Cartographer trajectory service clients
        rclcpp::Client<cartographer_ros_msgs::srv::StartTrajectory>::SharedPtr start_trajectory_client_;
        rclcpp::Client<cartographer_ros_msgs::srv::FinishAllTrajectories>::SharedPtr finish_all_trajectories_client_;
        rclcpp::Client<cartographer_ros_msgs::srv::GetTrajectoryStates>::SharedPtr get_trajectory_states_client_;
        rclcpp::Client<cartographer_ros_msgs::srv::SetMapTopic>::SharedPtr set_map_topic_client_;
        rclcpp::Client<cartographer_ros_msgs::srv::LoadState>::SharedPtr load_state_client_;
        rclcpp::Client<cartographer_ros_msgs::srv::WriteState>::SharedPtr write_state_client_;
        rclcpp::Client<cartographer_ros_msgs::srv::SetLocalizationMode>::SharedPtr set_localization_mode_client_;
        rclcpp::Client<cartographer_ros_msgs::srv::SetMappingMode>::SharedPtr set_mapping_mode_client_;
        rclcpp::Client<cartographer_ros_msgs::srv::GetMode>::SharedPtr get_mode_client_;
        
        rclcpp::Client<rcl_interfaces::srv::SetParameters>::SharedPtr planner_param_client_;

        // Timer for checking map updates
        std::shared_ptr<rpos_common::stcm::CompositeMap> composite_map_;
        boost::shared_ptr<rpos::robot_platforms::objects::CompositeMap> slamkit_composite_map_;

        // Async initialization
        std::thread initialization_thread_;
        std::thread slamkit_thread_;
        std::atomic<bool> initialization_in_progress_;
        std::atomic<bool> initialization_completed_;
        ::stcm_manager::VirtualWallManager wallManager_;
        ::stcm_manager::SensorDisableAreaManager sensorDisableAreaManager_;
        ::stcm_manager::DangerousAreaManager dangerousAreaManager_;
        std::unique_ptr<::stcm_manager::POIManager> poi_manager_;
        rclcpp::CallbackGroup::SharedPtr client_callback_group_;
        // TF buffer and listener
        std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
        std::shared_ptr<tf2_ros::TransformListener> tf_listener_;
        
        // Timer for periodic mode checking
        rclcpp::TimerBase::SharedPtr mode_check_timer_;
        int max_trajectory_nodes_;
 
        std::string map_server_type_;
        std::string platform_host_;
        int platform_port_;
        std::shared_ptr<rpos::robot_platforms::SlamwareCorePlatform> slamkit_platform_;
        std::shared_ptr<std::recursive_mutex> slamkit_platform_mutex_;

        std::string slam_mode_;
    };

}} // namespace rslamware::stcm_manager