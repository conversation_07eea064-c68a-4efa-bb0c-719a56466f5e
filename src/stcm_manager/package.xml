<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>stcm_manager</name>
  <version>1.0.0</version>
  <description>ROS2 service for comprehensive STCM map management - provides map storage, conversion, analysis, and lifecycle management with MapManager integration</description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>std_msgs</depend>
  <depend>std_srvs</depend>
  <depend>geometry_msgs</depend>
  <depend>visualization_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>tf2_ros</depend>
  <depend>interfaces</depend>
  <depend>rpos_common</depend>
  <depend>Eigen3</depend>
  <depend>cartographer_ros_msgs</depend>
  <depend>opennav_docking_msgs</depend>
  <depend>nav2_util</depend>
  <depend>nav2_map_server</depend>
  
  <build_depend>rosidl_default_generators</build_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>
  <member_of_group>rosidl_interface_packages</member_of_group>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
