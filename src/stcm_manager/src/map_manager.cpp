#include "map_manager.h"
#include "stcm_converter/pbstream_to_maplayer_converter.h"
#include "stcm_converter/pgm_to_grid_converter.h" 
#include <stcm/composite_map_reader.h>
#include <stcm/composite_map_writer.h>
#include <cartographer_ros_msgs/msg/trajectory_states.hpp>
#include <cartographer_ros_msgs/msg/status_code.hpp>
#include <nav2_map_server/map_io.hpp>
#include <ament_index_cpp/get_package_share_directory.hpp>
#include <rpos/system/exception.h>
#include <rpos/features/location_provider/map.h>
#include <rpos/core/pose.h>
#include <rpos/robot_platforms/objects/composite_map_reader.h>
#include <rpos/robot_platforms/objects/composite_map_writer.h>
#include <rpos/robot_platforms/objects/grid_map_layer.h>
#include <rpos/core/diagnosis_info.h>
#include <interfaces/events.h>
#include <fstream>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <thread>
#include <cstdlib>
#include <cmath>
#include <future>
#include <nav2_util/geometry_utils.hpp>
#include <tf2/utils.h>

namespace fs = std::filesystem;
namespace cartographer = cartographer_ros_msgs::srv;

namespace rslamware { namespace stcm_manager {

MapManager::MapManager(rclcpp::Node::SharedPtr node,
                      const std::string& virtual_wall_topic,
                      const std::string& dangerous_area_topic,
                      const std::string& map_server_topic,
                      const std::string& quality_topic,
                      const std::string& robot_events_topic,
                      const std::string& map_server_type,
                      const std::string& platform_host,
                      int platform_port)
    : node_(node)
    , virtual_wall_topic_(virtual_wall_topic)
    , map_server_topic_(map_server_topic)
    , quality_topic_(quality_topic)
    , robot_events_topic_(robot_events_topic)
    , map_width_(0)
    , map_height_(0)
    , map_resolution_(0.0f)
    , map_origin_x_(0.0f)
    , map_origin_y_(0.0f)
    , map_dimensions_available_(false)
    , initialization_in_progress_(false)
    , initialization_completed_(false)
    , wallManager_(node, virtual_wall_topic)
    , sensorDisableAreaManager_(node)
    , dangerousAreaManager_(node, dangerous_area_topic)
    , map_server_type_(map_server_type)
    , platform_host_(platform_host)
    , platform_port_(platform_port)
{    
    // Initialize POI service clients 
    client_callback_group_ = node_->create_callback_group(
        rclcpp::CallbackGroupType::Reentrant);

    // Initialize HomeDock service clients
    set_home_docks_client_ = node_->create_client<opennav_docking_msgs::srv::SetDocks>(
        "/docking_server/set_docks", rmw_qos_profile_services_default, client_callback_group_);
    get_all_home_docks_client_ = node_->create_client<opennav_docking_msgs::srv::GetAllDocks>(
        "/docking_server/get_all_docks", rmw_qos_profile_services_default, client_callback_group_);
    delete_home_dock_client_ = node_->create_client<opennav_docking_msgs::srv::DeleteDock>(
        "/docking_server/delete_dock", rmw_qos_profile_services_default, client_callback_group_);
    
    if(map_server_type_ == "slamkit")
    {
        try {
            slamkit_platform_ = std::make_shared<rpos::robot_platforms::SlamwareCorePlatform>(rpos::robot_platforms::SlamwareCorePlatform::connect(platform_host_.c_str(), platform_port_));
            RCLCPP_INFO(node_->get_logger(), "Connected to SlamwareCorePlatform");
        } catch (const rpos::system::detail::ExceptionBase& e) {
            slamkit_platform_ = nullptr;
            RCLCPP_ERROR(node_->get_logger(), "Failed to connect to SlamwareCorePlatform: %s", e.toString().c_str());
        }
        poi_manager_ = std::make_unique<::stcm_manager::POIManager>(node_, *this);
    }
    else if(map_server_type_ == "cartographer")
    {
        start_trajectory_client_ = node_->create_client<cartographer::StartTrajectory>(
            "/start_trajectory", rmw_qos_profile_services_default, client_callback_group_);
        finish_all_trajectories_client_ = node_->create_client<cartographer::FinishAllTrajectories>(
            "/finish_all_trajectories", rmw_qos_profile_services_default, client_callback_group_);
        get_trajectory_states_client_ = node_->create_client<cartographer::GetTrajectoryStates>(
            "/get_trajectory_states", rmw_qos_profile_services_default, client_callback_group_);
        set_map_topic_client_ = node_->create_client<cartographer::SetMapTopic>(
            "/set_map_topic", rmw_qos_profile_services_default, client_callback_group_);
        load_state_client_ = node_->create_client<cartographer::LoadState>(
            "/load_state", rmw_qos_profile_services_default, client_callback_group_);
        write_state_client_ = node_->create_client<cartographer::WriteState>(
            "/write_state", rmw_qos_profile_services_default, client_callback_group_); 
        set_localization_mode_client_ = node_->create_client<cartographer::SetLocalizationMode>(
            "/set_localization_mode", rmw_qos_profile_services_default, client_callback_group_);
        set_mapping_mode_client_ = node_->create_client<cartographer::SetMappingMode>(
            "/set_mapping_mode", rmw_qos_profile_services_default, client_callback_group_);
        get_mode_client_ = node_->create_client<cartographer::GetMode>(
            "/get_mode", rmw_qos_profile_services_default, client_callback_group_);
        add_poi_client_ = node_->create_client<cartographer_ros_msgs::srv::AddPOI>(
            "add_poi", rmw_qos_profile_services_default, client_callback_group_);
        list_poi_client_ = node_->create_client<cartographer_ros_msgs::srv::ListPOI>(
            "list_poi", rmw_qos_profile_services_default, client_callback_group_);
    }

    planner_param_client_ = node_->create_client<rcl_interfaces::srv::SetParameters>(
        "/planner_server/set_parameters", rmw_qos_profile_services_default, client_callback_group_);
        
    // Create timer for periodic mode checking (30 seconds interval)
    max_trajectory_nodes_ = node_->get_parameter("max_trajectory_nodes").as_int();
    if(map_server_type_ == "cartographer" && max_trajectory_nodes_ > 0)
    {
        RCLCPP_INFO(node_->get_logger(), "Set max trajectory nodes to %d", max_trajectory_nodes_);
        mode_check_timer_ = node_->create_wall_timer(
        std::chrono::seconds(30),
        std::bind(&MapManager::modeCheckTimerCallback, this),
        client_callback_group_);
    }
}

MapManager::~MapManager()
{
    // Wait for initialization thread to complete
    if (initialization_thread_.joinable()) {
        initialization_thread_.join();
    }

    // Wait for localization quality thread to complete
    if (slamkit_thread_.joinable()) {
        slamkit_thread_.join();
    }

    if (node_) {
        RCLCPP_INFO(node_->get_logger(), "MapManager destroyed");
    }
    if (map_server_type_ == "slamkit" && slamkit_platform_)
    {
        slamkit_platform_->disconnect();
        slamkit_platform_ = nullptr;
    }
}

void MapManager::initialize(const std::string& map_file_path)
{
    // Check if initialization is already in progress
    if (initialization_in_progress_.exchange(true)) {
        RCLCPP_WARN(node_->get_logger(), "MapManager initialization already in progress");
        return;
    }

    RCLCPP_INFO(node_->get_logger(), "Starting async MapManager initialization");

    // Start initialization in background thread
    initialization_thread_ = std::thread(&MapManager::asyncInitializeWorker, this, map_file_path);

    if (map_server_type_ == "slamkit")
    {
        // Start localization quality thread
        slamkit_thread_ = std::thread(&MapManager::slamkitWorker, this);
    }    
}

bool MapManager::clearMap(std::string& error_msg)
{
    RCLCPP_INFO(node_->get_logger(), "Clearing map data...");

    double mapping_speed = node_->get_parameter("mapping_speed").as_double();
    try {
        // Extract directory path and delete related map files
        fs::path map_path(map_file_path_);
        fs::remove(map_file_path_);
   
        fs::path map_dir = map_path.parent_path(); 
        if (fs::exists(map_dir) && fs::is_directory(map_dir)) {
            RCLCPP_INFO(node_->get_logger(), "Cleaning map directory: %s", map_dir.c_str());

            // Define file extensions to delete
            const std::unordered_set<std::string> extensions_to_delete = {".pbstream", ".pgm", ".yaml"};
 
            for (const auto& entry : fs::directory_iterator(map_dir)) {
                if (!entry.is_regular_file()) {
                    continue;
                }
                std::string file_extension = entry.path().extension().string();
                if(extensions_to_delete.count(file_extension))
                {
                    fs::remove(entry.path());
                    RCLCPP_INFO(node_->get_logger(), "Deleted file: %s", entry.path().c_str()); 
                }
            }   
        }
        // Reset map dimension and origin information
        has_map_updated_ = true;
        map_width_ = 0;
        map_height_ = 0;
        map_resolution_ = 0.0f;
        map_origin_x_ = 0.0f;
        map_origin_y_ = 0.0f;
        map_dimensions_available_ = false;
        wallManager_.clearVirtualWalls();
        if(delete_home_dock_client_->service_is_ready())
        {
            RCLCPP_INFO(node_->get_logger(), "Clear home docks");
            auto delete_home_dock_req = std::make_shared<opennav_docking_msgs::srv::DeleteDock::Request>();
            delete_home_dock_req->delete_all = true;
            auto delete_home_dock_future = delete_home_dock_client_->async_send_request(delete_home_dock_req);
            opennav_docking_msgs::srv::DeleteDock::Response::SharedPtr delete_home_dock_result;
            if(!waitForFuture(delete_home_dock_future.future, delete_home_dock_result, 2000))
            {
                RCLCPP_ERROR(node_->get_logger(), "Clear home docks timeout");
                error_msg = "Clear home docks timeout";
                return false;
            }
            if(!delete_home_dock_result->success)
            {
                RCLCPP_ERROR(node_->get_logger(), "Clear home docks failed: %s", delete_home_dock_result->message.c_str());
                error_msg = "Clear home docks failed: " + delete_home_dock_result->message;
                return false;
            }
        }

        if (map_server_type_ == "slamkit")
        {
            try {
                std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
                auto slamkit_platform = getSlamwarePlatform();
                if (!slamkit_platform)
                {
                    RCLCPP_ERROR(node_->get_logger(), "Failed to get SlamwareCorePlatform");
                    error_msg = "Failed to get SlamwareCorePlatform";
                    return false;
                }
                if (!slamkit_platform->clearMap())
                {
                    RCLCPP_ERROR(node_->get_logger(), "Failed to clear map");
                    error_msg = "Failed to clear map";
                    return false;
                }
            } catch (const rpos::system::detail::ExceptionBase& e) {
                RCLCPP_ERROR(node_->get_logger(), "Failed to clear map: %s", e.toString().c_str());
                error_msg = "Failed to clear map: " + e.toString();
                return false;
            }
            if(!updatePlannerParameters(true, error_msg))
            {
                RCLCPP_WARN(node_->get_logger(), "UpdatePlannerParameters after clear map failed: %s", error_msg.c_str()); 
            }
        }
        else if(map_server_type_ == "cartographer")
        {
            if(set_mapping_mode_client_->service_is_ready() && set_map_topic_client_->service_is_ready())
            {
                auto set_mapping_mode_req = std::make_shared<cartographer::SetMappingMode::Request>();
                set_mapping_mode_req->clear_map = true;
                auto set_mapping_mode_future = set_mapping_mode_client_->async_send_request(set_mapping_mode_req);
                cartographer::SetMappingMode::Response::SharedPtr set_mapping_mode_result;
                if(!waitForFuture(set_mapping_mode_future.future, set_mapping_mode_result, 5000))
                {
                    RCLCPP_ERROR(node_->get_logger(), "SetMappingMode timeout");
                    error_msg = "SetMappingMode timeout";
                    return false;
                }
                if(set_mapping_mode_result->status.code != cartographer_ros_msgs::msg::StatusCode::OK)
                {
                    RCLCPP_ERROR(node_->get_logger(), "SetMappingMode failed: %s(code:%d)",
                        set_mapping_mode_result->status.message.c_str(), set_mapping_mode_result->status.code);
                    error_msg = "SetMappingMode failed: " + set_mapping_mode_result->status.message;
                    return false;
                } 

                if(!updatePlannerParameters(true, error_msg))
                {
                    RCLCPP_WARN(node_->get_logger(), "UpdatePlannerParameters after clear map failed: %s", error_msg.c_str()); 
                }

                auto set_map_topic_req = std::make_shared<cartographer::SetMapTopic::Request>();
                set_map_topic_req->update_public_map = true;
                auto set_map_topic_future = set_map_topic_client_->async_send_request(set_map_topic_req);
                cartographer::SetMapTopic::Response::SharedPtr set_map_topic_result;
                if(!waitForFuture(set_map_topic_future.future, set_map_topic_result, 2000))
                {
                    RCLCPP_ERROR(node_->get_logger(), "SetMapTopic timeout");
                    error_msg = "SetMapTopic timeout";
                    return false;
                }
                if(!set_map_topic_result->success)
                {
                    RCLCPP_ERROR(node_->get_logger(), "SetMapTopic failed: %s", set_map_topic_result->message.c_str());
                    error_msg = "SetMapTopic failed: " + set_map_topic_result->message;
                    return false;
                }
            }
        }
        wallManager_.publishVirtualWallMask(); 
        dangerousAreaManager_.setForceSpeedLimit(mapping_speed);
    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Error clearing map: %s", e.what());
        error_msg = "Error clearing map: " + std::string(e.what());
        return false;
    }
    return true;
}

bool MapManager::loadLineMapLayer(const std::shared_ptr<rpos_common::stcm::LineMapLayer>& layer)
{
    if(!layer){
        return false;
    }
    if(layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_VIRTUAL_WALL)
    {
        wallManager_.loadFromMapLayer(layer);
    }
    else if (layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_VIRTUAL_TRACK)
    {
        //TODO
    }
    
    return true;
}
        
bool MapManager::loadPoseEntryMapLayer(const std::shared_ptr<rpos_common::stcm::PoseMapLayer>& layer)
{
    if(!layer){
        return false;
    }
    if(layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_HOME_DOCK_POSE){
        auto & home_docks = layer->poses();
        RCLCPP_INFO(node_->get_logger(), "Home dock layer found with %zu home docks", home_docks.size());
        
        auto start_time = std::chrono::steady_clock::now();
        const auto timeout = std::chrono::seconds(10);
        bool service_available = false;

        while (!service_available && (std::chrono::steady_clock::now() - start_time) < timeout) {
            if (set_home_docks_client_->service_is_ready()) {
                service_available = true;
                RCLCPP_INFO(node_->get_logger(), "set_docks service is now available");
                break;
            }
            if (!set_home_docks_client_->wait_for_service(std::chrono::milliseconds(500))) {
                auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::steady_clock::now() - start_time).count();
                RCLCPP_INFO_THROTTLE(node_->get_logger(), *node_->get_clock(), 2000,
                                    "Still waiting for set_docks service... (%ld seconds elapsed)", elapsed);
            } else {
                service_available = true;
                RCLCPP_INFO(node_->get_logger(), "set_docks service is now available");
                break;
            }
        }
        if (!service_available) {
            RCLCPP_WARN(node_->get_logger(), "set_docks service not available after 10 seconds timeout, skipping home dock set");
            return false;
        } else {
            auto req = std::make_shared<opennav_docking_msgs::srv::SetDocks::Request>();
            req->clear_existing = true;
            for (const auto& home_dock_pair : home_docks) {
                const auto& home_dock_id = home_dock_pair.first;
                const auto& home_dock_entry = home_dock_pair.second;

                opennav_docking_msgs::msg::Dock dock;
                dock.dock_id = home_dock_id;
                home_dock_entry.metadata.tryGet("display_name", dock.display_name);
                dock.pose.position.x = home_dock_entry.pose.x();
                dock.pose.position.y = home_dock_entry.pose.y();
                dock.pose.orientation = nav2_util::geometry_utils::orientationAroundZAxis(home_dock_entry.pose.yaw());
                RCLCPP_INFO(node_->get_logger(), "Setting home dock: %s name: %s at (%.2f, %.2f, %.2f)", 
                            dock.dock_id.c_str(), dock.display_name.c_str(), dock.pose.position.x, dock.pose.position.y, tf2::getYaw(dock.pose.orientation));
                req->docks.push_back(dock);
            }

            auto future = set_home_docks_client_->async_send_request(req);
            if (future.wait_for(std::chrono::seconds(5)) == std::future_status::ready) {
                try {
                    auto response = future.get();
                    if (response->success) {
                        RCLCPP_INFO(node_->get_logger(), "Successfully set home docks");
                    } else {
                        RCLCPP_WARN(node_->get_logger(), "Failed to set home docks");
                        return false;
                    }
                } catch (const std::exception& e) {
                    RCLCPP_ERROR(node_->get_logger(), "Exception while setting home docks: %s", e.what());
                    return false;
                }
            } else {
                RCLCPP_WARN(node_->get_logger(), "Timeout setting home docks via service");
                return false;
            }
        }
        RCLCPP_INFO(node_->get_logger(), "Finished processing %zu home docks from STCM layer", home_docks.size());
    }
    else if(layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_POI){
        auto & pois = layer->poses();
        RCLCPP_INFO(node_->get_logger(), "POI layer found with %zu POIs", pois.size());

        if (map_server_type_ == "cartograph")
        {
            auto start_time = std::chrono::steady_clock::now();
            const auto timeout = std::chrono::seconds(10);
            bool service_available = false;

            while (!service_available &&
                   (std::chrono::steady_clock::now() - start_time) < timeout) {

                if (add_poi_client_->service_is_ready()) {
                    service_available = true;
                    RCLCPP_INFO(node_->get_logger(), "POI service is now available");
                    break;
                }

                // Wait for service to become available
                if (!add_poi_client_->wait_for_service(std::chrono::milliseconds(500))) {
                    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                        std::chrono::steady_clock::now() - start_time).count();
                    RCLCPP_INFO_THROTTLE(node_->get_logger(), *node_->get_clock(), 2000,
                                        "Still waiting for POI service... (%ld seconds elapsed)", elapsed);
                } else {
                    service_available = true;
                    RCLCPP_INFO(node_->get_logger(), "POI service is now available");
                    break;
                }
            }

            if (!service_available) {
                RCLCPP_WARN(node_->get_logger(), "POI service not available after 10 seconds timeout, skipping POI loading");
                return false;
            }
        }
        
        // Add each POI through the service
        for (const auto& poi_pair : pois) {
            const auto& poi_name = poi_pair.first;
            const auto& poi_entry = poi_pair.second;
            
            if (map_server_type_ == "slamkit")
            {
                // Create POI message
                interfaces::msg::POI poi_msg;
                poi_msg.id = poi_name;
                poi_entry.metadata.tryGet("display_name", poi_msg.name);
                poi_entry.metadata.tryGet("type", poi_msg.type);
                poi_msg.pose.x = poi_entry.pose.x();
                poi_msg.pose.y = poi_entry.pose.y();
                poi_msg.pose.theta = poi_entry.pose.yaw();
                std::string error_msg;
                if (!poi_manager_->addPOI(poi_msg, error_msg)) {
                    RCLCPP_ERROR(node_->get_logger(), "Failed to add POI %s: %s", poi_name.c_str(), error_msg.c_str());
                }
            }
            else if (map_server_type_ == "cartographer")
            {
                // Create POI message
                cartographer_ros_msgs::msg::POI poi_msg;
                poi_msg.id = poi_name;
                poi_entry.metadata.tryGet("display_name", poi_msg.name);
                poi_entry.metadata.tryGet("type", poi_msg.type);
                poi_msg.pose.x = poi_entry.pose.x();
                poi_msg.pose.y = poi_entry.pose.y();
                poi_msg.pose.theta = poi_entry.pose.yaw();

                // Create service request
                auto request = std::make_shared<cartographer_ros_msgs::srv::AddPOI::Request>();
                request->poi = poi_msg;
            
                // Send request asynchronously
                auto future = add_poi_client_->async_send_request(request);
            
                // Wait for response with timeout
                if (future.wait_for(std::chrono::seconds(5)) == std::future_status::ready) {
                    try {
                        auto response = future.get();
                        if (response->success) {
                            RCLCPP_INFO(node_->get_logger(), "Successfully added POI: %s at (%.2f, %.2f, %.2f)", 
                                       poi_name.c_str(), poi_msg.pose.x, poi_msg.pose.y, poi_msg.pose.theta);
                        } else {
                            RCLCPP_WARN(node_->get_logger(), "Failed to add POI %s: %s", 
                                       poi_name.c_str(), response->message.c_str());
                        }
                    } catch (const std::exception& e) {
                        RCLCPP_ERROR(node_->get_logger(), "Exception while adding POI %s: %s", 
                                    poi_name.c_str(), e.what());
                    }
                } else {
                    RCLCPP_WARN(node_->get_logger(), "Timeout while adding POI: %s", poi_name.c_str());
                }
            }
        }
        
        RCLCPP_INFO(node_->get_logger(), "Finished processing %zu POIs from STCM layer", pois.size());
    } 
    return true;
}
        
bool MapManager::loadRectangleAreaMapLayer(const std::shared_ptr<rpos_common::stcm::RectangleAreaMapLayer>& layer)
{
    if(!layer){
        return false;
    }

    for(auto & rectArea : layer->areas())
    {
        switch(rectArea.usage)
        {
            case rpos_common::core::ArtifactUsage::ArtifactUsageDangerousArea:
                // Dangerous areas are now handled by dangerousAreaManager_
                // Load dangerous areas into the dangerous area manager
                dangerousAreaManager_.loadFromMapLayer(layer);
                RCLCPP_DEBUG(node_->get_logger(), "Loaded dangerous area with ID %d", rectArea.id);
                break;
            case rpos_common::core::ArtifactUsage::ArtifactUsageSensorDisableArea:
                sensorDisableAreaManager_.loadFromMapLayer(layer);
                break;
            default:
                break;
        }
    }
    return true;
}

bool MapManager::loadStcmFile(const std::string& file_path, bool createPgm, std::string& error_msg)
{
    RCLCPP_INFO(node_->get_logger(), "Loading STCM file: %s", file_path.c_str());

    try {
        // Check file extension
        if (fs::path(file_path).extension() != ".stcm") {
            error_msg = "Invalid file extension. Expected .stcm file";
            return false;
        }
        fs::path dir_path = fs::path(file_path).parent_path();

        // Check file size
        auto file_size = fs::file_size(file_path);
        if (file_size == 0) {
            error_msg = "STCM file is empty";
            return false;
        }

        // Load STCM file and extract map dimensions
        rpos_common::stcm::CompositeMapReader reader;
        composite_map_ = reader.loadFile(file_path);

        if (!composite_map_) {
            error_msg = "Failed to load STCM file";
            return false;
        }

        // Find GridMapLayer to extract dimensions
        const auto& layers = composite_map_->maps();
        bool found_grid_layer = false;

        for (const auto& layer : layers) {
            if (layer->getType() == rpos_common::stcm::GridMapLayer::Type
                && layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_EXPLORE){
                auto grid_layer = std::dynamic_pointer_cast<rpos_common::stcm::GridMapLayer>(layer);
                if(!grid_layer){
                    continue; 
                }
                // Extract map dimensions and origin
                const auto& dimension = grid_layer->getDimension();
                const auto& resolution = grid_layer->getResolution();
                const auto& origin = grid_layer->getOrigin();

                map_width_ = static_cast<uint32_t>(dimension.x());
                map_height_ = static_cast<uint32_t>(dimension.y());
                map_resolution_ = resolution.x(); // Assuming square pixels
                map_origin_x_ = static_cast<float>(origin.x());
                map_origin_y_ = static_cast<float>(origin.y());
                map_dimensions_available_ = true;
                found_grid_layer = true;
                if(createPgm)
                {
                    PgmToGridConverter converter;
                    converter.saveGridMapToPgm(grid_layer, (dir_path/"map.pgm").string(), (dir_path/"map.yaml").string());
                }
                wallManager_.setGridMapSize(map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);
                sensorDisableAreaManager_.setGridMapSize(map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);
                dangerousAreaManager_.setGridMapSize(map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);
                RCLCPP_INFO(node_->get_logger(), "Extracted map info: %ux%u pixels, resolution: %.3f m/pixel, origin: (%.3f, %.3f)",
                           map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_); 
            }
            else if(layer->getType() == rpos_common::stcm::ImageFeaturesMapLayer::Type
                && layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_CARTOGRAPHER
                && map_server_type_ == "cartographer"){
                auto image_layer = std::dynamic_pointer_cast<rpos_common::stcm::ImageFeaturesMapLayer>(layer);
                if(!image_layer || image_layer->featureObs().empty()){
                    continue; 
                }
                auto & obs = image_layer->featureObs()[0];
                std::ofstream file((dir_path/"map.pbstream").string(), std::ios::binary);
                if (file.is_open()) {
                    file.write(reinterpret_cast<const char*>(obs.features.data()), obs.features.size());
                    file.close();
                }
            }
            else if(layer->getType() == rpos_common::stcm::LineMapLayer::Type){
                loadLineMapLayer(std::dynamic_pointer_cast<rpos_common::stcm::LineMapLayer>(layer));
            }
            else if(layer->getType() == rpos_common::stcm::PoseMapLayer::Type){
                loadPoseEntryMapLayer(std::dynamic_pointer_cast<rpos_common::stcm::PoseMapLayer>(layer));
            }
            else if(layer->getType() == rpos_common::stcm::RectangleAreaMapLayer::Type){
                loadRectangleAreaMapLayer(std::dynamic_pointer_cast<rpos_common::stcm::RectangleAreaMapLayer>(layer));
            }
        }

        if (!found_grid_layer) {
            RCLCPP_WARN(node_->get_logger(), "No GridMapLayer found in STCM file, dimensions not available");
            map_dimensions_available_ = false;
        }

        if (map_server_type_ == "slamkit")
        {
            // Load STCM file and extract map dimensions
            rpos::robot_platforms::objects::CompositeMapReader reader;
            auto slamkit_composite_map_ = reader.loadFile(file_path);

            if (!slamkit_composite_map_) {
                error_msg = "Failed to load STCM file";
                return false;
            }
            try {
                std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
                auto slamkit_platform = getSlamwarePlatform();
                if (!slamkit_platform)
                {
                    RCLCPP_ERROR(node_->get_logger(), "Failed to get SlamwareCorePlatform");
                    error_msg = "Failed to get SlamwareCorePlatform";
                    return false;
                }
                rpos::core::Pose pose;
                if (!slamkit_platform->setCompositeMap(*slamkit_composite_map_, pose))
                {
                    error_msg = "Failed to set composite map";
                    return false;
                }
            } catch (const rpos::system::detail::ExceptionBase& e) {
                error_msg = "Failed to set composite map: " + e.toString();
                return false;
            }
        }

        RCLCPP_INFO(node_->get_logger(), "STCM file loaded successfully, file size: %zu bytes", file_size);

        return true;

    } catch (const std::exception& e) {
        error_msg = "Error loading STCM file: " + std::string(e.what());
        return false;
    }
}

bool MapManager::onStcmFileUploaded(const std::string& file_path, std::string& error_msg)
{
    return loadStcmFile(file_path, map_server_type_ == "cartographer", error_msg);
}

void MapManager::subscribeToMapServer()
{
    RCLCPP_INFO(node_->get_logger(), "Subscribing to %s topic...", map_server_topic_.c_str());

    try {
        // Create subscription to map topic
        map_subscription_ = node_->create_subscription<nav_msgs::msg::OccupancyGrid>(
            map_server_topic_,
            rclcpp::QoS(1).transient_local(),  // Use transient_local for map data
            std::bind(&MapManager::mapCallback, this, std::placeholders::_1)
        );
 
        map_publisher_ = node_->create_publisher<nav_msgs::msg::OccupancyGrid>(map_server_topic_,
            rclcpp::QoS(rclcpp::KeepLast(1)).transient_local().reliable());

        RCLCPP_INFO(node_->get_logger(), "Successfully subscribed to %s", map_server_topic_.c_str());

    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Error subscribing to map_server: %s", e.what());
    }
}

void MapManager::mapCallback(const nav_msgs::msg::OccupancyGrid::SharedPtr msg)
{
    try {
        // Extract and store map dimensions and origin from received map
        {
            std::lock_guard<std::mutex> lock(map_mutex_);
            current_map_ = msg;
        }
        map_width_ = msg->info.width;
        map_height_ = msg->info.height;
        map_resolution_ = msg->info.resolution;
        map_origin_x_ = static_cast<float>(msg->info.origin.position.x);
        map_origin_y_ = static_cast<float>(msg->info.origin.position.y);
        map_dimensions_available_ = true; 
        wallManager_.setGridMapSize(map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);
        sensorDisableAreaManager_.setGridMapSize(map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);
        dangerousAreaManager_.setGridMapSize(map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);
        RCLCPP_DEBUG(node_->get_logger(), "Stored map info: %ux%u pixels, resolution: %.3f m/pixel, origin: (%.3f, %.3f)",
                   map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);

    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Error processing map callback: %s", e.what());
    }
}

bool MapManager::getKnownArea(float& x_min, float& y_min, float& width, float& height) const
{
    if (!map_dimensions_available_) {
        RCLCPP_WARN(node_->get_logger(), "Map dimensions not available");
        return false;
    }

    // Calculate physical dimensions
    float physical_width = map_width_ * map_resolution_;
    float physical_height = map_height_ * map_resolution_;

    // Calculate bounds based on origin and dimensions
    // The origin represents the position of the bottom-left corner of the map
    x_min = map_origin_x_;
    y_min = map_origin_y_;
    width = physical_width;
    height = physical_height;

    RCLCPP_DEBUG(node_->get_logger(), "Returning known area: x_min=%.3f, y_min=%.3f, width=%.3f, height=%.3f",
                x_min, y_min, width, height);
        RCLCPP_DEBUG(node_->get_logger(), "Map info: %ux%u pixels, resolution: %.3f m/pixel, origin: (%.3f, %.3f)",
                map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);
 
    return true;
}

std::shared_ptr<rpos_common::stcm::PoseMapLayer> MapManager::createPOILayer()
{
    RCLCPP_INFO(node_->get_logger(), "Creating POI layer from list_poi service");

    std::vector<interfaces::msg::POI> pois;
    if (map_server_type_ == "slamkit")
    {
        poi_manager_->getAllPOIs(pois);
    }
    else if (map_server_type_ == "cartographer")
    {
        // Check if list_poi service is available
        if (!list_poi_client_->service_is_ready()) {
            RCLCPP_ERROR(node_->get_logger(), "List POI service not available");
            return nullptr;
        }

        // Create service request
        auto request = std::make_shared<cartographer_ros_msgs::srv::ListPOI::Request>();

        // Send synchronous request
        auto future = list_poi_client_->async_send_request(request);

        // Wait for response with timeout (10 seconds total)
        auto start_time = std::chrono::steady_clock::now();
        auto timeout_duration = std::chrono::seconds(5);

        while (rclcpp::ok()) {
            auto status = future.wait_for(std::chrono::milliseconds(100));
            if (status == std::future_status::ready) {
                break;
            }

            // Check if total timeout exceeded
            auto elapsed = std::chrono::steady_clock::now() - start_time;
            if (elapsed >= timeout_duration) {
                RCLCPP_ERROR(node_->get_logger(), "List POI service call timed out after %ld seconds",
                            std::chrono::duration_cast<std::chrono::seconds>(elapsed).count());
                return nullptr;
            }
        }

        auto response = future.get();
        if (!response) {
            RCLCPP_ERROR(node_->get_logger(), "Received null response from list POI service");
            return nullptr;
        }

        if (!response->success) {
            RCLCPP_ERROR(node_->get_logger(), "List POI service failed: %s", response->message.c_str());
            return nullptr;
        }

        for (const auto& poi : response->pois) {
            interfaces::msg::POI tmp_poi;
            tmp_poi.id = poi.id;
            tmp_poi.name = poi.name;
            tmp_poi.type = poi.type;
            tmp_poi.pose = poi.pose;
            pois.push_back(tmp_poi);
        }
    }

    // Create POI layer
    auto poi_layer = std::make_shared<rpos_common::stcm::PoseMapLayer>();
    poi_layer->setType(rpos_common::stcm::PoseMapLayer::Type);
    poi_layer->setUsage(RPOS_COMPOSITEMAP_USAGE_POI);

    // Convert POIs to STCM format and add to layer
    for (const auto& poi : pois) {
        rpos_common::core::PoseEntry pose_entry;

        // Set pose
        pose_entry.pose = rpos_common::core::Pose(
            rpos_common::core::Location(poi.pose.x, poi.pose.y),
            rpos_common::core::Rotation(poi.pose.theta)
        );

        pose_entry.id = poi.id;

        // Set metadata
        pose_entry.metadata.set("display_name", poi.name);
        pose_entry.metadata.set("type", poi.type);

        // Add to layer with POI ID as key
        poi_layer->poses()[poi.id] = pose_entry;

        RCLCPP_DEBUG(node_->get_logger(),
                    "Added POI '%s' (%s) at (%.3f, %.3f, %.3f) to layer",
                    poi.name.c_str(), poi.id.c_str(),
                    poi.pose.x, poi.pose.y, poi.pose.theta);
    }

    RCLCPP_INFO(node_->get_logger(), "Created POI layer with %zu POIs", poi_layer->poses().size());
    return poi_layer;
}

std::shared_ptr<rpos_common::stcm::PoseMapLayer> MapManager::createHomeDockLayer()
{
    RCLCPP_INFO(node_->get_logger(), "Creating HomeDock layer from get_all_docks service");

    // Check if get_all_docks service is available
    if (!get_all_home_docks_client_->service_is_ready()) {
        RCLCPP_ERROR(node_->get_logger(), "get_all_docks service not available");
        return nullptr;
    }

    // Create service request
    auto request = std::make_shared<opennav_docking_msgs::srv::GetAllDocks::Request>();

    // Send synchronous request
    auto future = get_all_home_docks_client_->async_send_request(request);

    // Wait for response with timeout (5 seconds total)
    auto start_time = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::seconds(5);

    while (rclcpp::ok()) {
        auto status = future.wait_for(std::chrono::milliseconds(100));
        if (status == std::future_status::ready) {
            break;
        }

        // Check if total timeout exceeded
        auto elapsed = std::chrono::steady_clock::now() - start_time;
        if (elapsed >= timeout_duration) {
            RCLCPP_ERROR(node_->get_logger(), "get_all_docks service call timed out after %ld seconds",
                        std::chrono::duration_cast<std::chrono::seconds>(elapsed).count());
            return nullptr;
        }
    }

    auto response = future.get();
    if (!response) {
        RCLCPP_ERROR(node_->get_logger(), "Received null response from get_all_docks service");
        return nullptr;
    }

    if (!response->success) {
        RCLCPP_ERROR(node_->get_logger(), "get_all_docks service failed: %s", response->message.c_str());
        return nullptr;
    }

    // Create HomeDock layer
    auto maplayer = std::make_shared<rpos_common::stcm::PoseMapLayer>();
    maplayer->setType(rpos_common::stcm::PoseMapLayer::Type);
    maplayer->setUsage(RPOS_COMPOSITEMAP_USAGE_HOME_DOCK_POSE);

    for (const auto& dock : response->docks) {
        rpos_common::core::PoseEntry pose_entry;
        pose_entry.pose = rpos_common::core::Pose(
            rpos_common::core::Location(dock.pose.position.x, dock.pose.position.y),
            rpos_common::core::Rotation(tf2::getYaw(dock.pose.orientation))
        );
        pose_entry.id = dock.dock_id;
        pose_entry.metadata.set("display_name", dock.display_name);
        maplayer->poses()[dock.dock_id] = pose_entry;

        RCLCPP_INFO(node_->get_logger(), "Added home dock: %s name: %s at (%.2f, %.2f, %.2f)", 
                    dock.dock_id.c_str(), dock.display_name.c_str(), dock.pose.position.x, dock.pose.position.y, tf2::getYaw(dock.pose.orientation));
    }

    RCLCPP_INFO(node_->get_logger(), "Created HomeDock layer with %zu docks", response->docks.size());
    return maplayer;
}

std::shared_ptr<rpos_common::stcm::LineMapLayer> MapManager::createVirtualWallLayer()
{
    auto maplayer = std::make_shared<rpos_common::stcm::LineMapLayer>();
    maplayer->setType(rpos_common::stcm::LineMapLayer::Type);
    maplayer->setUsage(RPOS_COMPOSITEMAP_USAGE_VIRTUAL_WALL);
    auto virtual_walls = wallManager_.getAllVirtualWalls();
    auto & mapLines = maplayer->lines();
    for(const auto& virtual_wall : virtual_walls)
    {
        rpos_common::stcm::Line line;
        line.name = std::to_string(virtual_wall->id());
        line.start = rpos_common::core::Location(virtual_wall->startP().x(), virtual_wall->startP().y());
        line.end = rpos_common::core::Location(virtual_wall->endP().x(), virtual_wall->endP().y());
        line.metadata = virtual_wall->metadata();
        mapLines[line.name] = line;
    }
    return maplayer;
}

std::shared_ptr<rpos_common::stcm::LineMapLayer> MapManager::createVirtualTrackLayer()
{
    auto maplayer = std::make_shared<rpos_common::stcm::LineMapLayer>();
    maplayer->setType(rpos_common::stcm::LineMapLayer::Type);
    maplayer->setUsage(RPOS_COMPOSITEMAP_USAGE_VIRTUAL_TRACK);
    
    return maplayer;
}

std::shared_ptr<rpos_common::stcm::RectangleAreaMapLayer> MapManager::createRectangleAreaLayer()
{
    auto maplayer = std::make_shared<rpos_common::stcm::RectangleAreaMapLayer>();
    maplayer->setType(rpos_common::stcm::RectangleAreaMapLayer::Type);
    maplayer->setUsage(RPOS_COMPOSITEMAP_USAGE_RECTANGLE_AREA);
    
    // Add sensor disable areas to the layer
    auto sensor_disable_areas = sensorDisableAreaManager_.getAllSensorDisableAreas();
    auto& areas = maplayer->areas();
    for (const auto& area : sensor_disable_areas) {
        areas.push_back(*area);
    }
    
    RCLCPP_INFO(node_->get_logger(), "Created rectangle area layer with %zu sensor disable areas", sensor_disable_areas.size());

    // Add dangerous areas from the dangerous area manager
    auto dangerous_areas = dangerousAreaManager_.getAllDangerousAreas();
    auto & mapAreas = maplayer->areas();
    for(const auto& dangerous_area : dangerous_areas)
    {
        mapAreas.push_back(*dangerous_area);
    }

    return maplayer;
}

std::shared_ptr<rpos_common::stcm::CompositeMap> MapManager::createCompositeMapFromPgm(const std::string& dir_path, bool lite_version, std::string& error_msg)
{
    fs::path dir(dir_path);
    std::string mapfile_pbstream = (dir / "map.pbstream").string();  
    std::string mapfile_pgm = (dir / "map.pgm").string();  
    std::string mapfile_yaml = (dir / "map.yaml").string();  

    auto composite_map = std::make_shared<rpos_common::stcm::CompositeMap>();
    if (!composite_map) {
        error_msg = "failed to create composite map";
        RCLCPP_ERROR(node_->get_logger(),"failed to create composite map");
        return nullptr;
    }

    if(!lite_version)
    {
        PbstreamToMapLayerConverter pbstream_converter; 
        auto image_layer = pbstream_converter.convertPbstreamToMapLayer(mapfile_pbstream, "cartographer_features");
        if (!image_layer) { 
            error_msg = "failed to creat map layer for pbstream";
            RCLCPP_ERROR(node_->get_logger(),"failed to create image feature map layer");
            return nullptr;
        } 
        composite_map->maps().push_back(image_layer);
    }
   
    PgmToGridConverter pgm_converter;
    if (!pgm_converter.loadPgmAndParams(mapfile_pgm, mapfile_yaml)
      || !pgm_converter.convertToGridMap()) {
        error_msg = "failed to convert pgm to grid map";
        RCLCPP_ERROR(node_->get_logger(),"failed to convert pgm to grid map");
        return nullptr;
    }
    
    auto grid_layer = pgm_converter.createGridMapLayer();
    if (!grid_layer) {
        error_msg = "failed to create grid map layer";
        RCLCPP_ERROR(node_->get_logger(),"failed to create grid map layer");
        return nullptr;
    }
    composite_map->maps().push_back(grid_layer);
 
    std::shared_ptr<rpos_common::stcm::PoseMapLayer> poi_layer = createPOILayer(); 
    if(poi_layer){
        composite_map->maps().push_back(poi_layer);
    }

    auto home_dock_layer = createHomeDockLayer();
    if(home_dock_layer){
        composite_map->maps().push_back(home_dock_layer);
    }

    auto rectangle_area_layer = createRectangleAreaLayer();
    if(rectangle_area_layer){
        composite_map->maps().push_back(rectangle_area_layer);
    }

    auto wall_layer= createVirtualWallLayer();
    if(wall_layer){
        composite_map->maps().push_back(wall_layer);
    }
    
    auto track_layer= createVirtualTrackLayer();
    if(track_layer){
        composite_map->maps().push_back(track_layer);
    }
    return composite_map;
}

std::shared_ptr<rpos_common::stcm::CompositeMap> MapManager::createCompositeMapFromSlamkit(std::string& error_msg)
{
    auto composite_map = std::make_shared<rpos_common::stcm::CompositeMap>();
    if (!composite_map) {
        error_msg = "failed to create composite map";
        RCLCPP_ERROR(node_->get_logger(),"failed to create composite map");
        return nullptr;
    }
    try {
        std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
        auto slamkit_platform = getSlamwarePlatform();
        if (!slamkit_platform)
        {
            RCLCPP_ERROR(node_->get_logger(), "Failed to get SlamwareCorePlatform");
            error_msg = "Failed to get SlamwareCorePlatform";
            return nullptr;
        }
        auto tmp_composite_map = std::move(slamkit_platform->getCompositeMap());

        const auto& layers = tmp_composite_map.maps();
        for (const auto& layer : layers) {
            if (layer->getType() == rpos::robot_platforms::objects::GridMapLayer::Type
                && layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_EXPLORE) {
                auto tmp_grid_map_layer = boost::dynamic_pointer_cast<rpos::robot_platforms::objects::GridMapLayer>(layer);
                auto grid_layer = std::make_shared<rpos_common::stcm::GridMapLayer>();
                grid_layer->setType(rpos_common::stcm::GridMapLayer::Type);
                grid_layer->setUsage(RPOS_COMPOSITEMAP_USAGE_EXPLORE);
                grid_layer->setName("grid_map");
                grid_layer->metadata().dict() = tmp_grid_map_layer->metadata().dict();
                grid_layer->setOrigin(rpos_common::core::Location(tmp_grid_map_layer->getOrigin().x(), tmp_grid_map_layer->getOrigin().y()));
                grid_layer->setDimension(rpos_common::core::Vector2i(tmp_grid_map_layer->getDimension().x(), tmp_grid_map_layer->getDimension().y()));
                grid_layer->setResolution(rpos_common::core::Vector2f(tmp_grid_map_layer->getResolution().x(), tmp_grid_map_layer->getResolution().y()));
                grid_layer->mapData() = tmp_grid_map_layer->mapData();
                composite_map->maps().push_back(grid_layer);
                break;
            }
        }
    }  catch (const rpos::system::detail::ExceptionBase& e) {
        error_msg = "Failed to get composite map from slamkit: " + std::string(e.what());
        RCLCPP_ERROR(node_->get_logger(), "Failed to get composite map from slamkit: %s", e.what());
        return nullptr;
    }
 
    std::shared_ptr<rpos_common::stcm::PoseMapLayer> poi_layer = createPOILayer(); 
    if(poi_layer){
        composite_map->maps().push_back(poi_layer);
    }

    auto home_dock_layer = createHomeDockLayer();
    if(home_dock_layer){
        composite_map->maps().push_back(home_dock_layer);
    }

    auto rectangle_area_layer = createRectangleAreaLayer();
    if(rectangle_area_layer){
        composite_map->maps().push_back(rectangle_area_layer);
    }

    auto wall_layer= createVirtualWallLayer();
    if(wall_layer){
        composite_map->maps().push_back(wall_layer);
    }
    
    auto track_layer= createVirtualTrackLayer();
    if(track_layer){
        composite_map->maps().push_back(track_layer);
    }
    return composite_map;
}

bool MapManager::saveMap(const std::string& file_path, std::string& error_msg)
{
    std::shared_ptr<rpos_common::stcm::CompositeMap> composite_map;
    if (map_server_type_ == "slamkit")
    {
        try {
            composite_map = createCompositeMapFromSlamkit(error_msg);
        } catch (const rpos::system::detail::ExceptionBase& e) {
            error_msg = "Failed to save map: " + std::string(e.what());
            RCLCPP_ERROR(node_->get_logger(), "Failed to save map: %s", e.what());
            return false;
        }
    }
    else if (map_server_type_ == "cartographer")
    {
        std::filesystem::path dir_path = std::filesystem::path(file_path).parent_path();
       
        std::string mapfile_base = (dir_path / "map").string();
        std::string mapfile_pbstream = (dir_path / "map.pbstream").string();  
 
        if(!savePgmMap(mapfile_base, mapfile_pbstream, error_msg, false))
        {
            RCLCPP_ERROR(node_->get_logger(), "Failed to save PGM map: %s", error_msg.c_str());
            return false;
        }
        auto composite_map = createCompositeMapFromPgm(dir_path.string(), false, error_msg);
    }
    if(!composite_map)
    { 
        RCLCPP_ERROR(node_->get_logger(), "Failed to create composite map from PGM map");
        return false;
    }
    try {
        rpos_common::stcm::CompositeMapWriter writer;
        writer.saveFile(file_path, *composite_map);
        return true;
    } catch (const std::exception& e) {
        error_msg = "write composite map exeception: " + std::string(e.what());
        RCLCPP_ERROR(node_->get_logger(),error_msg.c_str()); 
        for(auto map: composite_map->maps())
        {
            RCLCPP_INFO(node_->get_logger(), "type:%s", map->getType().c_str());
        }
    }
    return false;
}

bool MapManager::savePgmMap(const std::string &map_base, const std::string& pbstream, std::string& error_msg, bool lite_version)
{
    try { 
        if(!lite_version)
        {
            if(!writeStateToFile(pbstream, error_msg))
            {
                RCLCPP_ERROR(node_->get_logger(), "Failed to write state: %s", error_msg.c_str()); 
                return false;
            } 
            RCLCPP_INFO(node_->get_logger(), "write PBStream to %s", pbstream.c_str()); 
        }
        bool savePgmSuccess =false;
        {
            nav2_map_server::SaveParameters parameters;
            parameters.free_thresh = 0.196;
            parameters.occupied_thresh = 0.65;
            parameters.map_file_name = map_base;
            std::lock_guard<std::mutex> lock(map_mutex_); 
            if(nav2_map_server::saveMapToFile( *current_map_, parameters))
            {
                savePgmSuccess = true;
                RCLCPP_INFO(node_->get_logger(), "save PGM map to %s", map_base.c_str());
            }
        }
        if(!savePgmSuccess)
        {
            RCLCPP_WARN(node_->get_logger(), "Failed to save PGM map via map server lib, retry with system command");
            std::string map_saver_cmd = "ros2 run nav2_map_server map_saver_cli --free 0.196 -f " + map_base;
            RCLCPP_INFO(node_->get_logger(), "Executing: %s", map_saver_cmd.c_str());
            int result = std::system(map_saver_cmd.c_str());
            if (result != 0) {
                RCLCPP_ERROR(node_->get_logger(), "Failed to save PGM map, exit code: %d", result);
                error_msg = "Failed to save PGM map, exit code: " + std::to_string(result);
                return false;
            }
            RCLCPP_INFO(node_->get_logger(), "map_saver_cli save PGM map successfully");  
        } 

    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "save pmg file exception: %s", e.what());
        error_msg = "save pmg file exception: " + std::string(e.what());
        return false;
    }
    return true;
}

void MapManager::asyncInitializeWorker(const std::string& map_file_path)
{
    map_file_path_ = map_file_path;

    RCLCPP_INFO(node_->get_logger(), "Async initialization worker started");
    bool auto_load_map = node_->get_parameter("auto_load_map").as_bool();
    float mapping_speed = node_->get_parameter("mapping_speed").as_double();

    // Initialize TF buffer and listener
    tf_buffer_ = std::make_shared<tf2_ros::Buffer>(node_->get_clock());
    tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
 
    wallManager_.initialize();
    sensorDisableAreaManager_.initialize();
    dangerousAreaManager_.initialize();
    if (map_server_type_ == "slamkit")
    {
        poi_manager_->initialize();
    }
    subscribeToMapServer(); 
    try {
        fs::path dir_path = fs::path(map_file_path_).parent_path();
        std::string pbstream_file_path = (dir_path / "map.pbstream").string();
        std::string pgm_file_path = (dir_path / "map.pgm").string();
        std::string yaml_file_path = (dir_path / "map.yaml").string();
        bool check_file_success = false;
        if (map_server_type_ == "slamkit")
        {
            check_file_success = fs::exists(map_file_path_);
        }
        else if (map_server_type_ == "cartographer")
        {
            check_file_success = fs::exists(pbstream_file_path) && fs::exists(pgm_file_path) && fs::exists(yaml_file_path) && fs::exists(map_file_path_);
        }
        if(check_file_success) 
        {
            do{
                if(!waitForServiceAvailable(20000))
                {
                    RCLCPP_ERROR(node_->get_logger(), "service not available");
                    break;
                }
                if(auto_load_map)
                {
                    std::string error_msg;
                    if(!loadStcmFile(map_file_path_, false, error_msg))
                    {
                        RCLCPP_ERROR(node_->get_logger(), "Failed to load STCM file: %s", error_msg.c_str());
                        break;
                    }
                    if(getSLAMMode(slam_mode_, error_msg))
                    {
                        if(slam_mode_ == "localization")
                        {
                            RCLCPP_INFO(node_->get_logger(), "SLAM mode is localization, skip auto load map"); 
                            if(map_server_type_ == "cartographer" && !switchMapPublisher(false, true, error_msg))
                            {
                                RCLCPP_ERROR(node_->get_logger(), "Failed to switch map publisher: %s", error_msg.c_str());
                                break;
                            }
                        }
                        else
                        {
                            RCLCPP_INFO(node_->get_logger(), "SLAM mode is mapping, load map and set mode to localization");
                            if(!setSLAMMode("localization", true, error_msg))
                            {
                                RCLCPP_ERROR(node_->get_logger(), "Failed to set SLAM mode: %s", error_msg.c_str());
                                break;
                            } 
                        }
                    }
                }
                else{
                    RCLCPP_WARN(node_->get_logger(), "Auto load map disabled");
                    dangerousAreaManager_.setForceSpeedLimit(mapping_speed);
                    has_map_updated_= true;
                }
            }while(false); 
        } 
        else{
            RCLCPP_INFO(node_->get_logger(), "Map file not found, staying in mapping mode."); 
            dangerousAreaManager_.setForceSpeedLimit(mapping_speed);
            has_map_updated_= true;
        }
        initialization_completed_.store(true);
        RCLCPP_INFO(node_->get_logger(), "Async MapManager initialization completed successfully");

    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Error during async initialization: %s", e.what());
        initialization_completed_.store(true); // Mark as completed even on error
    }

    initialization_in_progress_.store(false);
}

void MapManager::slamkitWorker()
{
    quality_publisher_ = node_->create_publisher<std_msgs::msg::Int32>(quality_topic_, 10);
    robot_events_publisher_ = node_->create_publisher<interfaces::msg::SystemEvent>(robot_events_topic_, 10);
    while (rclcpp::ok())
    {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        publishLocalizationQuality_();
        publishRobotEvent_();
    }
}

void MapManager::publishLocalizationQuality_()
{
    if (slam_mode_.empty())
    {
        std::string error_msg;
        if (!getSLAMMode(slam_mode_, error_msg))
        {
            RCLCPP_ERROR(node_->get_logger(), "Failed to get SLAM mode: %s", error_msg.c_str());
            return;
        }
    }
    if (slam_mode_ == "localization")
    {
        std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
        auto slamkit_platform = getSlamwarePlatform();
        if (!slamkit_platform)
        {
            RCLCPP_ERROR(node_->get_logger(), "Failed to get SlamwareCorePlatform");
            return;
        }
        int quality = slamkit_platform->getLocalizationQuality();
        if (quality > 0)
        {
            std_msgs::msg::Int32 quality_msg;
            quality_msg.data = quality;
            quality_publisher_->publish(quality_msg);
        }
    }
}

void MapManager::publishRobotEvent_()
{
    std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
    auto slamkit_platform = getSlamwarePlatform();
    if (!slamkit_platform)
    {
        RCLCPP_ERROR(node_->get_logger(), "Failed to get SlamwareCorePlatform");
        return;
    }
    auto systemEventProvider = slamkit_platform->createSystemEventProvider();
    if (!systemEventProvider)
    {
        RCLCPP_ERROR(node_->get_logger(), "Failed to create system event provider.");
        return;
    }

    std::vector<rpos::core::DiagnosisInfoInternalSystemEvent> events;
    systemEventProvider->readEvents(events);
    for (size_t i = 0; i < events.size(); ++i)
    {
        if (rpos::core::InternalSystemEvent::InternalSystemEventMapLoopClosure == events[i].internalSystemEvent)
        {
            interfaces::msg::SystemEvent event_msg;
            event_msg.type = rslamware::SystemEventMapLoopClosure;
            event_msg.source = "slamkit";
            robot_events_publisher_->publish(event_msg);
            RCLCPP_INFO(node_->get_logger(), "Map loop closure detected and publish");
        }
    }
}

bool MapManager::getRobotPose(geometry_msgs::msg::PoseWithCovarianceStamped& current_pose)
{
    try {
        geometry_msgs::msg::TransformStamped transform = 
            tf_buffer_->lookupTransform("map", "base_link", tf2::TimePointZero);
        
        current_pose.header.frame_id = "map";
        current_pose.header.stamp = node_->now();
        current_pose.pose.pose.position.x = transform.transform.translation.x;
        current_pose.pose.pose.position.y = transform.transform.translation.y;
        current_pose.pose.pose.position.z = transform.transform.translation.z;
        current_pose.pose.pose.orientation = transform.transform.rotation;
        
        // Set covariance (assuming reasonable uncertainty)
        for (int i = 0; i < 36; ++i) {
            current_pose.pose.covariance[i] = 0.0;
        }
        current_pose.pose.covariance[0] = 0.1;   // x variance
        current_pose.pose.covariance[7] = 0.1;   // y variance
        current_pose.pose.covariance[35] = 0.1;  // yaw variance
         
    } catch (tf2::TransformException &ex) {
        RCLCPP_WARN(node_->get_logger(), "Could not get current robot pose: %s", ex.what());
        return false;
    }
    return true;
}

void MapManager::publishEmptyMap()
{
    nav_msgs::msg::OccupancyGrid grid_map;
    grid_map.header.frame_id = "map";
    grid_map.header.stamp = node_->now();
    grid_map.info.resolution = 0.05;
    grid_map.info.width = 0;
    grid_map.info.height = 0;
    grid_map.info.origin.position.x = 0;
    grid_map.info.origin.position.y = 0;
    grid_map.info.origin.orientation.w = 1;
    grid_map.info.origin.orientation.x = 0;
    grid_map.info.origin.orientation.y = 0;
    grid_map.info.origin.orientation.z = 0;
    grid_map.data.resize(grid_map.info.width * grid_map.info.height);
    map_publisher_->publish(grid_map);
}

void MapManager::publishLocalPgmMap()
{ 
    fs::path dir = fs::path(map_file_path_).parent_path(); 
    std::string mapfile_yaml = (dir / "map.yaml").string();  
    nav_msgs::msg::OccupancyGrid grid_map;
    auto ret = nav2_map_server::loadMapFromYaml(mapfile_yaml, grid_map);
    if(ret == nav2_map_server::LOAD_MAP_SUCCESS)
    {
        grid_map.header.frame_id = "map";
        grid_map.header.stamp = node_->now();
        map_publisher_->publish(grid_map);
    } 
    else{
        RCLCPP_ERROR(node_->get_logger(), "Failed to load map from yaml and pgm, status:%d, publish empty map", ret);
        publishEmptyMap();
    }
}

bool MapManager::waitForServiceAvailable(int timeout_ms)
{
    auto start_time = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::milliseconds(timeout_ms);
    bool common_service_ready = false;
    while(rclcpp::ok())
    {
        if (!common_service_ready
            && set_home_docks_client_->service_is_ready()
            && get_all_home_docks_client_->service_is_ready()
            && delete_home_dock_client_->service_is_ready()
            && planner_param_client_->service_is_ready())
        {
            common_service_ready = true;
        }

        if (common_service_ready)
        {
            if (map_server_type_ == "slamkit")
            {
                std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
                if (slamkit_platform_)
                {
                    return true;
                }
                try {
                    slamkit_platform_ = std::make_shared<rpos::robot_platforms::SlamwareCorePlatform>(rpos::robot_platforms::SlamwareCorePlatform::connect(platform_host_.c_str(), platform_port_));
                    RCLCPP_INFO(node_->get_logger(), "Connected to SlamwareCorePlatform");
                    return true;
                } catch (const rpos::system::detail::ExceptionBase& e) {
                    slamkit_platform_ = nullptr;
                    RCLCPP_ERROR(node_->get_logger(), "Failed to connect to SlamwareCorePlatform: %s", e.toString().c_str());
                }
            }
            else if (map_server_type_ == "cartographer")
            {
                if (start_trajectory_client_->service_is_ready()
                    && finish_all_trajectories_client_->service_is_ready()
                    && get_trajectory_states_client_->service_is_ready()
                    && set_map_topic_client_->service_is_ready()
                    && load_state_client_->service_is_ready()
                    && write_state_client_->service_is_ready()
                    && set_localization_mode_client_->service_is_ready()
                    && set_mapping_mode_client_->service_is_ready()
                    && get_mode_client_->service_is_ready()
                    && add_poi_client_->service_is_ready()
                    && list_poi_client_->service_is_ready())
                {
                    return true;
                }
            }
        }

        auto elapsed = std::chrono::steady_clock::now() - start_time;
        if(elapsed >= timeout_duration)
        {
            return false;   
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    return false;
}

bool MapManager::finishCurrentTrajectory(std::string& errMsg)
{
    if( !finish_all_trajectories_client_->service_is_ready() ){
        errMsg = "Service not available";
        return false;
    }

    auto finish_req = std::make_shared<cartographer::FinishAllTrajectories::Request>();
    auto finish_future = finish_all_trajectories_client_->async_send_request(finish_req);
    cartographer::FinishAllTrajectories::Response::SharedPtr finish_result;
    if(!waitForFuture(finish_future.future, finish_result, 2000))
    {
        errMsg = "FinishAllTrajectories Timeout";
        return false; 
    }
    if(finish_result->status.code != cartographer_ros_msgs::msg::StatusCode::OK)
    {
        errMsg = "FinishAllTrajectories failed";
        return false;
    }
    return true;
}

bool MapManager::writeStateToFile(const std::string& filename, std::string& errMsg)
{
    auto write_req = std::make_shared<cartographer::WriteState::Request>();
    write_req->filename = filename;
    write_req->include_unfinished_submaps = true;
    auto write_future = write_state_client_->async_send_request(write_req);
    cartographer::WriteState::Response::SharedPtr write_result;
    if(!waitForFuture(write_future.future, write_result, 5000))
    {
        errMsg = "WriteState Timeout";
        return false;
    }
    if(write_result->status.code != cartographer_ros_msgs::msg::StatusCode::OK)
    {
        errMsg = write_result->status.message;
        return false;
    }
    return true;
}

bool MapManager::setSLAMMode(const std::string& mode, bool reload_map, std::string& message)
{
    if (map_server_type_ == "slamkit")
    {
        try {
            std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
            auto slamkit_platform = getSlamwarePlatform();
            if (!slamkit_platform)
            {
                RCLCPP_ERROR(node_->get_logger(), "Failed to get SlamwareCorePlatform");
                message = "Failed to get SlamwareCorePlatform";
                return false;
            }
            if (reload_map)
            {
                rpos::core::Pose pose;
                if (!slamkit_platform->setCompositeMap(*slamkit_composite_map_, pose))
                {
                    RCLCPP_ERROR(node_->get_logger(), "Failed to set composite map");
                    message = "Failed to set composite map";
                    return false;
                }
                if (mode == "mapping")
                {
                    slamkit_platform->setMapUpdate(true, rpos::features::location_provider::EXPLORERMAP);
                    // Update planner parameters for mapping mode
                    std::string planner_error;
                    if(!updatePlannerParameters(true, planner_error))
                    {
                        RCLCPP_WARN(node_->get_logger(), "Failed to update planner parameters for mapping mode: %s", planner_error.c_str());
                        // Don't fail the mode switch, just log the warning
                    }
                    double mapping_speed = node_->get_parameter("mapping_speed").as_double();
                    dangerousAreaManager_.setForceSpeedLimit(mapping_speed);
                    has_map_updated_= true;
                }
            }
            else
            {
                if (mode == "mapping")
                {
                    slamkit_platform->setMapUpdate(true, rpos::features::location_provider::EXPLORERMAP);
                    // Update planner parameters for mapping mode
                    std::string planner_error;
                    if(!updatePlannerParameters(true, planner_error))
                    {
                        RCLCPP_WARN(node_->get_logger(), "Failed to update planner parameters for mapping mode: %s", planner_error.c_str());
                        // Don't fail the mode switch, just log the warning
                    }
                    double mapping_speed = node_->get_parameter("mapping_speed").as_double();
                    dangerousAreaManager_.setForceSpeedLimit(mapping_speed);
                    has_map_updated_= true;
                }
                else
                {
                    slamkit_platform->setMapUpdate(false, rpos::features::location_provider::EXPLORERMAP);
                    // Update planner parameters for localization mode
                    std::string planner_error;
                    if(!updatePlannerParameters(false, planner_error))
                    {
                        RCLCPP_WARN(node_->get_logger(), "Failed to update planner parameters for localization mode: %s", planner_error.c_str());
                        // Don't fail the mode switch, just log the warning
                    }
                    dangerousAreaManager_.setForceSpeedLimit(0.0);
                }
            }
        } catch (const rpos::system::detail::ExceptionBase& e) {
            RCLCPP_ERROR(node_->get_logger(), "Failed to set SLAM mode: %s", e.toString().c_str());
            message = "Failed to set SLAM mode: " + e.toString();
            return false;
        }
    } 
    else if (map_server_type_ == "cartographer")
    {   
        if(!get_mode_client_->service_is_ready()
            || !set_map_topic_client_->service_is_ready()
            || !set_localization_mode_client_->service_is_ready() 
            || !set_mapping_mode_client_->service_is_ready()
        )
        {
            RCLCPP_ERROR(node_->get_logger(), "service client not initialized"); 
            message = "service client not initialized";
            return false;
        }
 
        bool require_mapping_mode = (mode == "mapping"); 
        auto get_mode_req = std::make_shared<cartographer::GetMode::Request>();
        auto get_mode_future = get_mode_client_->async_send_request(get_mode_req);
        cartographer::GetMode::Response::SharedPtr mode_result;
        if(waitForFuture(get_mode_future.future, mode_result))
        {
            if(mode_result->is_localization_mode == !require_mapping_mode && !reload_map)
            {
                RCLCPP_INFO(node_->get_logger(), "Already in %s mode", mode.c_str()); 
                message = "Already in " + mode + " mode";
                return true;
            }
        }

        fs::path pbstream = (fs::path(map_file_path_).parent_path() / "map.pbstream");
        if(reload_map && !fs::exists(pbstream))
        {
            message = "local map not exist, can not reload";
            return false;
        }

        // Get current robot pose before finishing trajectory
        geometry_msgs::msg::PoseWithCovarianceStamped current_pose;
        bool pose_available = getRobotPose(current_pose);

        tf2::Quaternion q(
        current_pose.pose.pose.orientation.x,
        current_pose.pose.pose.orientation.y,
        current_pose.pose.pose.orientation.z,
        current_pose.pose.pose.orientation.w);
        double roll, pitch, yaw;
        tf2::Matrix3x3(q).getRPY(roll, pitch, yaw);
        RCLCPP_INFO(node_->get_logger(), "current pose, x:%.4f, y:%.4f, yaw:%.4f",
            current_pose.pose.pose.position.x, current_pose.pose.pose.position.y, yaw);
        if(require_mapping_mode)
        {
            auto set_mapping_mode_req = std::make_shared<cartographer_ros_msgs::srv::SetMappingMode::Request>();
            set_mapping_mode_req->clear_map = false;
            set_mapping_mode_req->initial_pose = current_pose.pose.pose;
            auto set_mapping_mode_future = set_mapping_mode_client_->async_send_request(set_mapping_mode_req);
            cartographer_ros_msgs::srv::SetMappingMode::Response::SharedPtr set_mapping_mode_result;
            if(!waitForFuture(set_mapping_mode_future.future, set_mapping_mode_result, 2000))
            {
                message = "SetMappingMode timeout";
                return false;
            }
            if(set_mapping_mode_result->status.code != cartographer_ros_msgs::msg::StatusCode::OK)
            {
                message = "SetMappingMode failed, status:" + std::to_string(set_mapping_mode_result->status.code) + ", " + set_mapping_mode_result->status.message;
                return false;
            }
        
            // Update planner parameters for mapping mode
            std::string planner_error;
            if(!updatePlannerParameters(true, planner_error))
            {
                RCLCPP_WARN(node_->get_logger(), "Failed to update planner parameters for mapping mode: %s", planner_error.c_str());
                // Don't fail the mode switch, just log the warning
            }
        
            double mapping_speed = node_->get_parameter("mapping_speed").as_double();
            dangerousAreaManager_.setForceSpeedLimit(mapping_speed);
            has_map_updated_= true;
        }
        else
        {
            std::string pbstream_file_path = (reload_map) ? pbstream.string() : "";

            auto set_localization_mode_req = std::make_shared<cartographer_ros_msgs::srv::SetLocalizationMode::Request>();
            set_localization_mode_req->use_initial_pose = pose_available && !reload_map;
            set_localization_mode_req->initial_pose = current_pose.pose.pose;
            set_localization_mode_req->state_file = pbstream_file_path;
            auto set_localization_mode_future = set_localization_mode_client_->async_send_request(set_localization_mode_req);
            cartographer_ros_msgs::srv::SetLocalizationMode::Response::SharedPtr set_localization_mode_result;
            if(!waitForFuture(set_localization_mode_future.future, set_localization_mode_result, 2000))
            {
                message = "SetLocalizationMode timeout";
                return false;
            }
            if(set_localization_mode_result->status.code != cartographer_ros_msgs::msg::StatusCode::OK)
            {
                message = "SetLocalizationMode failed, status:" + std::to_string(set_localization_mode_result->status.code) + ", " + set_localization_mode_result->status.message;
                return false;
            }
        
            // Update planner parameters for localization mode
            std::string planner_error;
            if(!updatePlannerParameters(false, planner_error))
            {
                RCLCPP_WARN(node_->get_logger(), "Failed to update planner parameters for localization mode: %s", planner_error.c_str());
                // Don't fail the mode switch, just log the warning
            }
        
            dangerousAreaManager_.setForceSpeedLimit(0.0);
        }

        bool need_publish_local_map = reload_map && !require_mapping_mode;
        if(!switchMapPublisher(require_mapping_mode, need_publish_local_map, message))
        {
            return false;
        } 
    }

    RCLCPP_INFO(node_->get_logger(), "Successfully switched to %s mode", mode.c_str());
    slam_mode_ = mode;
    message = "Successfully switched to " + mode + " mode";
    return true;
}

bool MapManager::switchMapPublisher(bool is_mapping_mode, bool need_publish_local_map, std::string& errMsg)
{
    auto set_map_topic_req = std::make_shared<cartographer::SetMapTopic::Request>();
    set_map_topic_req->update_public_map = is_mapping_mode;
    auto set_map_topic_future = set_map_topic_client_->async_send_request(set_map_topic_req);
    cartographer::SetMapTopic::Response::SharedPtr set_map_topic_result;
    if(!waitForFuture(set_map_topic_future.future, set_map_topic_result, 2000))
    {
        errMsg = "SetMapTopic timeout";
        return false;
    }
    if(!set_map_topic_result->success)
    {
        errMsg = "SetMapTopic failed:" + set_map_topic_result->message;
        return false;
    }
    if(need_publish_local_map)
    {
        RCLCPP_INFO(node_->get_logger(), "Reload map in localization mode, publish local map"); 
        publishLocalPgmMap();
    }
    return true;
}

bool MapManager::getSLAMMode(std::string& mode, std::string& error_msg)
{
    if (map_server_type_ == "slamkit")
    {
        try {
            std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
            auto slamkit_platform = getSlamwarePlatform();
            if (!slamkit_platform)
            {
                RCLCPP_ERROR(node_->get_logger(), "Failed to get SlamwareCorePlatform");
                error_msg = "Failed to get SlamwareCorePlatform";
                return false;
            }
            mode = slamkit_platform->getMapUpdate(rpos::features::location_provider::EXPLORERMAP) ? "mapping" : "localization";
        } catch (const rpos::system::detail::ExceptionBase& e) {
            RCLCPP_ERROR(node_->get_logger(), "Failed to get SLAM mode: %s", e.toString().c_str());
            error_msg = "Failed to get SLAM mode: %s" + e.toString();
            return false;
        }
    }
    else if (map_server_type_ == "cartographer")
    {
        if(!get_mode_client_->service_is_ready())
        {
            RCLCPP_ERROR(node_->get_logger(), "GetMode client not initialized");
            error_msg = "GetMode client not initialized";
            return false;
        }
        auto get_mode_req = std::make_shared<cartographer_ros_msgs::srv::GetMode::Request>();
        auto get_mode_future = get_mode_client_->async_send_request(get_mode_req);
        cartographer_ros_msgs::srv::GetMode::Response::SharedPtr mode_result;
        if(!waitForFuture(get_mode_future.future, mode_result, 2000))
        {
            error_msg = "GetMode Timeout";
            return false;
        }
        mode = mode_result->is_localization_mode ? "localization" : "mapping";
    }
    return true;
}

bool MapManager::hasMapUpdated()
{
    return has_map_updated_.load();
}

bool MapManager::updatePlannerParameters(bool is_mapping_mode, std::string& error_msg)
{
    try { 
        auto set_param_req = std::make_shared<rcl_interfaces::srv::SetParameters::Request>();
        set_param_req->parameters.push_back(rclcpp::Parameter("GridBased.allow_unknown", is_mapping_mode).to_parameter_msg());
        auto set_param_future = planner_param_client_->async_send_request(set_param_req);
        rcl_interfaces::srv::SetParameters::Response::SharedPtr set_param_result;
        if(!waitForFuture(set_param_future.future, set_param_result, 2000))
        {
            error_msg = "SetPlannerParameters Timeout";
            return false;
        }
        for (const auto & r : set_param_result->results) {
            if(!r.successful)
            {
                error_msg = "SetPlannerParameters failed: " + r.reason;
                return false;
            }
        }
        RCLCPP_INFO(node_->get_logger(), "Successfully updated planner parameters");
        return true; 
    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Exception during planner parameter update: %s", e.what());
        error_msg = "Exception during planner parameter update: " + std::string(e.what());
        return false;
    }
}

std::shared_ptr<rpos::robot_platforms::SlamwareCorePlatform> MapManager::getSlamwarePlatform()
{
    try {
        if (!slamkit_platform_)
        {
            slamkit_platform_ = std::make_shared<rpos::robot_platforms::SlamwareCorePlatform>(rpos::robot_platforms::SlamwareCorePlatform::connect(platform_host_.c_str(), platform_port_));
            RCLCPP_INFO(node_->get_logger(), "Connected to SlamwareCorePlatform");
        }
    } catch (const rpos::system::detail::ExceptionBase& e) {
        RCLCPP_ERROR(node_->get_logger(), "Failed to connect to SlamwareCorePlatform: %s", e.toString().c_str());
        slamkit_platform_ = nullptr;
        return nullptr;
    }

    return slamkit_platform_;
}

void MapManager::modeCheckTimerCallback()
{
    // Check if get_mode service is available
    if (!get_mode_client_->service_is_ready()) { 
        return;
    }
    
    // Create service request
    auto request = std::make_shared<cartographer_ros_msgs::srv::GetMode::Request>();
    
    // Send asynchronous request
    auto future = get_mode_client_->async_send_request(request);
    cartographer_ros_msgs::srv::GetMode::Response::SharedPtr mode_result;
    if(!waitForFuture(future.future, mode_result, 2000))
    {
        RCLCPP_WARN(node_->get_logger(), "GetMode service call timed out during periodic check");
        return;
    } 
    if(!mode_result->is_localization_mode && mode_result->active_trajectory_nodes > max_trajectory_nodes_)
    {
        RCLCPP_WARN(node_->get_logger(), "Active trajectory nodes exceed max trajectory nodes, switching to localization mode");
        std::string errMsg;
        if(!setSLAMMode("localization", false, errMsg))
        {
            RCLCPP_ERROR(node_->get_logger(), "Failed to switch to localization mode: %s", errMsg.c_str());
        }
    }
    else
    {
        RCLCPP_DEBUG(node_->get_logger(), "Current active trajectory nodes: %d", mode_result->active_trajectory_nodes);
    }
}

}} // namespace rslamware::stcm_manager