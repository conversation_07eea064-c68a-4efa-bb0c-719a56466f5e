#include "map_manager.h"
#include "stcm_converter/pbstream_to_maplayer_converter.h"
#include "stcm_converter/pgm_to_grid_converter.h" 
#include <stcm/composite_map_reader.h>
#include <stcm/composite_map_writer.h>
#include <fstream>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <thread>
#include <cstdlib>
#include <cmath>
#include <future>

namespace fs = std::filesystem;

namespace rslamware { namespace stcm_manager {

MapManager::MapManager(rclcpp::Node::SharedPtr node,
                      const std::string& virtual_wall_topic,
                      const std::string& map_server_topic)
    : node_(node)
    , virtual_wall_topic_(virtual_wall_topic)
    , map_server_topic_(map_server_topic)
    , map_loaded_(false)
    , subscribed_to_map_server_(false)
    , map_width_(0)
    , map_height_(0)
    , map_resolution_(0.0f)
    , map_origin_x_(0.0f)
    , map_origin_y_(0.0f)
    , map_dimensions_available_(false)
    , initialization_in_progress_(false)
    , initialization_completed_(false)
    , wallManager_(node, virtual_wall_topic)
{    
    // Initialize POI service clients 
    client_callback_group_ = node_->create_callback_group(
        rclcpp::CallbackGroupType::Reentrant);

    add_poi_client_ = node_->create_client<cartographer_ros_msgs::srv::AddPOI>(
        "add_poi", rmw_qos_profile_services_default, client_callback_group_);
    list_poi_client_ = node_->create_client<cartographer_ros_msgs::srv::ListPOI>(
        "list_poi", rmw_qos_profile_services_default, client_callback_group_);
    
    RCLCPP_INFO(node_->get_logger(), "MapManager initialized with POI service client");
}

MapManager::~MapManager()
{
    // Wait for initialization thread to complete
    if (initialization_thread_.joinable()) {
        initialization_thread_.join();
    }

    if (node_) {
        RCLCPP_INFO(node_->get_logger(), "MapManager destroyed");
    }
}

void MapManager::initialize(const std::string& map_file_path)
{
    // Check if initialization is already in progress
    if (initialization_in_progress_.exchange(true)) {
        RCLCPP_WARN(node_->get_logger(), "MapManager initialization already in progress");
        return;
    }

    RCLCPP_INFO(node_->get_logger(), "Starting async MapManager initialization");

    // Start initialization in background thread
    initialization_thread_ = std::thread(&MapManager::asyncInitializeWorker, this, map_file_path);
}

bool MapManager::clearMap()
{
    RCLCPP_INFO(node_->get_logger(), "Clearing map data...");

    try {
        // Extract directory path and delete related map files
        if (!map_file_path_.empty()) {
            // Delete stcm file if it exists
            if(fs::exists(map_file_path_)) {
                fs::remove(map_file_path_);
            }

            fs::path map_path(map_file_path_);
            fs::path map_dir = map_path.parent_path(); 
            if (fs::exists(map_dir) && fs::is_directory(map_dir)) {
                RCLCPP_INFO(node_->get_logger(), "Cleaning map directory: %s", map_dir.c_str());

                // Define file extensions to delete
                std::vector<std::string> extensions_to_delete = {".pbstream", ".pgm", ".yaml"};

                try {
                    for (const auto& entry : fs::directory_iterator(map_dir)) {
                        if (entry.is_regular_file()) {
                            std::string file_extension = entry.path().extension().string();

                            // Check if file has one of the target extensions
                            for (const auto& ext : extensions_to_delete) {
                                if (file_extension == ext) {
                                    try {
                                        if (fs::remove(entry.path())) {
                                            RCLCPP_INFO(node_->get_logger(), "Deleted file: %s", entry.path().c_str());
                                        } else {
                                            RCLCPP_WARN(node_->get_logger(), "Failed to delete file: %s", entry.path().c_str());
                                        }
                                    } catch (const fs::filesystem_error& e) {
                                        RCLCPP_WARN(node_->get_logger(), "Error deleting file %s: %s",
                                                   entry.path().c_str(), e.what());
                                    }
                                    break; // Found matching extension, no need to check others
                                }
                            }
                        }
                    }
                } catch (const fs::filesystem_error& e) {
                    RCLCPP_WARN(node_->get_logger(), "Error iterating directory %s: %s",
                               map_dir.c_str(), e.what());
                }
            }
        }

        // Reset internal state
        map_loaded_ = false;
        subscribed_to_map_server_ = false;

        // Reset map dimension and origin information
        map_width_ = 0;
        map_height_ = 0;
        map_resolution_ = 0.0f;
        map_origin_x_ = 0.0f;
        map_origin_y_ = 0.0f;
        map_dimensions_available_ = false;
        wallManager_.clearVirtualWalls();

        // Cancel subscription if active
        if (map_subscription_) {
            map_subscription_.reset();
            RCLCPP_INFO(node_->get_logger(), "Map subscription cancelled");
        }

        // Cancel timer if active
        if (check_timer_) {
            check_timer_.reset();
        }

        RCLCPP_INFO(node_->get_logger(), "Map cleared successfully");
        return true;

    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Error clearing map: %s", e.what());
        return false;
    }
}

bool MapManager::loadLineMapLayer(const std::shared_ptr<rpos::stcm::LineMapLayer>& layer)
{
    if(!layer){
        return false;
    }
    if(layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_VIRTUAL_WALL)
    {
        wallManager_.loadFromMapLayer(layer);
    }
    else if (layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_VIRTUAL_TRACK)
    {
        //TODO
    }
    
    return true;
}
        
bool MapManager::loadPoseEntryMapLayer(const std::shared_ptr<rpos::stcm::PoseMapLayer>& layer)
{
    if(!layer){
        return false;
    }
    if(layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_HOME_DOCK_POSE){
        //TODO set home dock
        RCLCPP_INFO(node_->get_logger(), "Home dock pose layer found, TODO: implement home dock setting");
    }
    else if(layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_POI){
        auto & pois = layer->poses();
        RCLCPP_INFO(node_->get_logger(), "POI layer found with %zu POIs", pois.size());

        auto start_time = std::chrono::steady_clock::now();
        const auto timeout = std::chrono::seconds(10);
        bool service_available = false;

        while (!service_available &&
               (std::chrono::steady_clock::now() - start_time) < timeout) {

            if (add_poi_client_->service_is_ready()) {
                service_available = true;
                RCLCPP_INFO(node_->get_logger(), "POI service is now available");
                break;
            }

            // Wait for service to become available
            if (!add_poi_client_->wait_for_service(std::chrono::milliseconds(500))) {
                auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::steady_clock::now() - start_time).count();
                RCLCPP_INFO_THROTTLE(node_->get_logger(), *node_->get_clock(), 2000,
                                    "Still waiting for POI service... (%ld seconds elapsed)", elapsed);
            } else {
                service_available = true;
                RCLCPP_INFO(node_->get_logger(), "POI service is now available");
                break;
            }
        }

        if (!service_available) {
            RCLCPP_WARN(node_->get_logger(), "POI service not available after 10 seconds timeout, skipping POI loading");
            return false;
        }
        
        // Add each POI through the service
        for (const auto& poi_pair : pois) {
            const auto& poi_name = poi_pair.first;
            const auto& poi_entry = poi_pair.second;
            
            // Create POI message
            cartographer_ros_msgs::msg::POI poi_msg;
            poi_msg.id = poi_name;
            poi_entry.metadata.tryGet("display_name", poi_msg.name);
            poi_entry.metadata.tryGet("type", poi_msg.type);
            poi_msg.pose.x = poi_entry.pose.x();
            poi_msg.pose.y = poi_entry.pose.y();
            poi_msg.pose.theta = poi_entry.pose.yaw();
            
            // Create service request
            auto request = std::make_shared<cartographer_ros_msgs::srv::AddPOI::Request>();
            request->poi = poi_msg;
            
            // Send request asynchronously
            auto future = add_poi_client_->async_send_request(request);
            
            // Wait for response with timeout
            if (future.wait_for(std::chrono::seconds(5)) == std::future_status::ready) {
                try {
                    auto response = future.get();
                    if (response->success) {
                        RCLCPP_INFO(node_->get_logger(), "Successfully added POI: %s at (%.2f, %.2f, %.2f)", 
                                   poi_name.c_str(), poi_msg.pose.x, poi_msg.pose.y, poi_msg.pose.theta);
                    } else {
                        RCLCPP_WARN(node_->get_logger(), "Failed to add POI %s: %s", 
                                   poi_name.c_str(), response->message.c_str());
                    }
                } catch (const std::exception& e) {
                    RCLCPP_ERROR(node_->get_logger(), "Exception while adding POI %s: %s", 
                                poi_name.c_str(), e.what());
                }
            } else {
                RCLCPP_WARN(node_->get_logger(), "Timeout while adding POI: %s", poi_name.c_str());
            }
        }
        
        RCLCPP_INFO(node_->get_logger(), "Finished processing %zu POIs from STCM layer", pois.size());
    } 
    return true;
}
        
bool MapManager::loadRectangleAreaMapLayer(const std::shared_ptr<rpos::stcm::RectangleAreaMapLayer>& layer)
{
    if(!layer){
        return false;
    }
    //TODO
    return true;
}

bool MapManager::loadStcmFile(const std::string& file_path, bool createPgm, std::string& error_msg)
{
    RCLCPP_INFO(node_->get_logger(), "Loading STCM file: %s", file_path.c_str());

    try {
        // Check file extension
        if (fs::path(file_path).extension() != ".stcm") {
            error_msg = "Invalid file extension. Expected .stcm file";
            return false;
        }
        fs::path dir_path = fs::path(file_path).parent_path();

        // Check file size
        auto file_size = fs::file_size(file_path);
        if (file_size == 0) {
            error_msg = "STCM file is empty";
            return false;
        }

        // Load STCM file and extract map dimensions
        rpos::stcm::CompositeMapReader reader;
        composite_map_ = reader.loadFile(file_path);

        if (!composite_map_) {
            error_msg = "Failed to load STCM file";
            return false;
        }

        // Find GridMapLayer to extract dimensions
        const auto& layers = composite_map_->maps();
        bool found_grid_layer = false;

        for (const auto& layer : layers) {
            if (layer->getType() == rpos::stcm::GridMapLayer::Type
                && layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_EXPLORE){
                auto grid_layer = std::dynamic_pointer_cast<rpos::stcm::GridMapLayer>(layer);
                if(!grid_layer){
                    continue; 
                }
                // Extract map dimensions and origin
                const auto& dimension = grid_layer->getDimension();
                const auto& resolution = grid_layer->getResolution();
                const auto& origin = grid_layer->getOrigin();

                map_width_ = static_cast<uint32_t>(dimension.x());
                map_height_ = static_cast<uint32_t>(dimension.y());
                map_resolution_ = resolution.x(); // Assuming square pixels
                map_origin_x_ = static_cast<float>(origin.x());
                map_origin_y_ = static_cast<float>(origin.y());
                map_dimensions_available_ = true;
                found_grid_layer = true;
                if(createPgm)
                {
                    PgmToGridConverter converter;
                    converter.saveGridMapToPgm(grid_layer, (dir_path/"map.pgm").string(), (dir_path/"map.yaml").string());
                }
                wallManager_.setGridMapSize(map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);
                RCLCPP_INFO(node_->get_logger(), "Extracted map info: %ux%u pixels, resolution: %.3f m/pixel, origin: (%.3f, %.3f)",
                           map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_); 
            }
            else if(layer->getType() == rpos::stcm::ImageFeaturesMapLayer::Type
                && layer->getUsage() == RPOS_COMPOSITEMAP_USAGE_CARTOGRAPHER){
                auto image_layer = std::dynamic_pointer_cast<rpos::stcm::ImageFeaturesMapLayer>(layer);
                if(!image_layer || image_layer->featureObs().empty()){
                    continue; 
                }
                auto & obs = image_layer->featureObs()[0];
                std::ofstream file((dir_path/"map.pbstream").string(), std::ios::binary);
                if (file.is_open()) {
                    file.write(reinterpret_cast<const char*>(obs.features.data()), obs.features.size());
                    file.close();
                }
            }
            else if(layer->getType() == rpos::stcm::LineMapLayer::Type){
                loadLineMapLayer(std::dynamic_pointer_cast<rpos::stcm::LineMapLayer>(layer));
            }
            else if(layer->getType() == rpos::stcm::PoseMapLayer::Type){
                loadPoseEntryMapLayer(std::dynamic_pointer_cast<rpos::stcm::PoseMapLayer>(layer));
            }
            else if(layer->getType() == rpos::stcm::RectangleAreaMapLayer::Type){
                loadRectangleAreaMapLayer(std::dynamic_pointer_cast<rpos::stcm::RectangleAreaMapLayer>(layer));
            }
        }

        if (!found_grid_layer) {
            RCLCPP_WARN(node_->get_logger(), "No GridMapLayer found in STCM file, dimensions not available");
            map_dimensions_available_ = false;
        }

        RCLCPP_INFO(node_->get_logger(), "STCM file loaded successfully, file size: %zu bytes", file_size);

        map_loaded_ = true;
        return true;

    } catch (const std::exception& e) {
        error_msg = "Error loading STCM file: " + std::string(e.what());
        return false;
    }
}

bool MapManager::onStcmFileUploaded(const std::string& file_path, std::string& error_msg)
{
    return loadStcmFile(file_path, true, error_msg);
}

void MapManager::subscribeToMapServer()
{
    RCLCPP_INFO(node_->get_logger(), "Subscribing to %s topic...", map_server_topic_.c_str());

    try {
        // Create subscription to map topic
        map_subscription_ = node_->create_subscription<nav_msgs::msg::OccupancyGrid>(
            map_server_topic_,
            rclcpp::QoS(1).transient_local(),  // Use transient_local for map data
            std::bind(&MapManager::mapCallback, this, std::placeholders::_1)
        );

        subscribed_to_map_server_ = true;
        RCLCPP_INFO(node_->get_logger(), "Successfully subscribed to %s", map_server_topic_.c_str());

        // Set up a timer to check for map updates
        check_timer_ = node_->create_wall_timer(
            std::chrono::seconds(5),
            [this]() {
                if (!map_loaded_) {
                    RCLCPP_INFO(node_->get_logger(), "Waiting for map data from map_server...");
                }
            }
        );

    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Error subscribing to map_server: %s", e.what());
        subscribed_to_map_server_ = false;
    }
}

void MapManager::mapCallback(const nav_msgs::msg::OccupancyGrid::SharedPtr msg)
{
    try {
        // Extract and store map dimensions and origin from received map
        map_width_ = msg->info.width;
        map_height_ = msg->info.height;
        map_resolution_ = msg->info.resolution;
        map_origin_x_ = static_cast<float>(msg->info.origin.position.x);
        map_origin_y_ = static_cast<float>(msg->info.origin.position.y);
        map_dimensions_available_ = true;
        map_loaded_ = true;
        wallManager_.setGridMapSize(map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);
        RCLCPP_DEBUG(node_->get_logger(), "Stored map info: %ux%u pixels, resolution: %.3f m/pixel, origin: (%.3f, %.3f)",
                   map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);

    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Error processing map callback: %s", e.what());
    }
}

bool MapManager::getKnownArea(float& x_min, float& y_min, float& width, float& height) const
{
    if (!map_dimensions_available_) {
        RCLCPP_WARN(node_->get_logger(), "Map dimensions not available");
        return false;
    }

    // Calculate physical dimensions
    float physical_width = map_width_ * map_resolution_;
    float physical_height = map_height_ * map_resolution_;

    // Calculate bounds based on origin and dimensions
    // The origin represents the position of the bottom-left corner of the map
    x_min = map_origin_x_;
    y_min = map_origin_y_;
    width = physical_width;
    height = physical_height;

    RCLCPP_DEBUG(node_->get_logger(), "Returning known area: x_min=%.3f, y_min=%.3f, width=%.3f, height=%.3f",
                x_min, y_min, width, height);
    RCLCPP_DEBUG(node_->get_logger(), "Map info: %ux%u pixels, resolution: %.3f m/pixel, origin: (%.3f, %.3f)",
                map_width_, map_height_, map_resolution_, map_origin_x_, map_origin_y_);

    return true;
}

std::shared_ptr<rpos::stcm::PoseMapLayer> MapManager::createPOILayer()
{
    RCLCPP_INFO(node_->get_logger(), "Creating POI layer from list_poi service");

    // Check if list_poi service is available
    if (!list_poi_client_->service_is_ready()) {
        RCLCPP_ERROR(node_->get_logger(), "List POI service not available");
        return nullptr;
    }

    // Create service request
    auto request = std::make_shared<cartographer_ros_msgs::srv::ListPOI::Request>();

    // Send synchronous request
    auto future = list_poi_client_->async_send_request(request);

    // Wait for response with timeout (10 seconds total)
    auto start_time = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::seconds(5);

    while (rclcpp::ok()) {
        auto status = future.wait_for(std::chrono::milliseconds(100));
        if (status == std::future_status::ready) {
            break;
        }

        // Check if total timeout exceeded
        auto elapsed = std::chrono::steady_clock::now() - start_time;
        if (elapsed >= timeout_duration) {
            RCLCPP_ERROR(node_->get_logger(), "List POI service call timed out after %ld seconds",
                        std::chrono::duration_cast<std::chrono::seconds>(elapsed).count());
            return nullptr;
        }
    }

    auto response = future.get();
    if (!response) {
        RCLCPP_ERROR(node_->get_logger(), "Received null response from list POI service");
        return nullptr;
    }

    if (!response->success) {
        RCLCPP_ERROR(node_->get_logger(), "List POI service failed: %s", response->message.c_str());
        return nullptr;
    }

    RCLCPP_INFO(node_->get_logger(), "Retrieved %zu POIs from service", response->pois.size());

    // Create POI layer
    auto poi_layer = std::make_shared<rpos::stcm::PoseMapLayer>();
    poi_layer->setType(rpos::stcm::PoseMapLayer::Type);
    poi_layer->setUsage(RPOS_COMPOSITEMAP_USAGE_POI);

    // Convert POIs to STCM format and add to layer
    for (const auto& poi : response->pois) {
        rpos::core::PoseEntry pose_entry;

        // Set pose
        pose_entry.pose = rpos::core::Pose(
            rpos::core::Location(poi.pose.x, poi.pose.y),
            rpos::core::Rotation(poi.pose.theta)
        );

        pose_entry.id = poi.id;

        // Set metadata
        pose_entry.metadata.set("display_name", poi.name);
        pose_entry.metadata.set("type", poi.type);

        // Add to layer with POI ID as key
        poi_layer->poses()[poi.id] = pose_entry;

        RCLCPP_DEBUG(node_->get_logger(),
                    "Added POI '%s' (%s) at (%.3f, %.3f, %.3f) to layer",
                    poi.name.c_str(), poi.id.c_str(),
                    poi.pose.x, poi.pose.y, poi.pose.theta);
    }

    RCLCPP_INFO(node_->get_logger(), "Created POI layer with %zu POIs", poi_layer->poses().size());
    return poi_layer;
}

std::shared_ptr<rpos::stcm::LineMapLayer> MapManager::createVirtualWallLayer()
{
    auto maplayer = std::make_shared<rpos::stcm::LineMapLayer>();
    maplayer->setType(rpos::stcm::LineMapLayer::Type);
    maplayer->setUsage(RPOS_COMPOSITEMAP_USAGE_VIRTUAL_WALL);
    auto virtual_walls = wallManager_.getAllVirtualWalls();
    auto & mapLines = maplayer->lines();
    for(const auto& virtual_wall : virtual_walls)
    {
        rpos::stcm::Line line;
        line.name = std::to_string(virtual_wall->id());
        line.start = rpos::core::Location(virtual_wall->startP().x(), virtual_wall->startP().y());
        line.end = rpos::core::Location(virtual_wall->endP().x(), virtual_wall->endP().y());
        line.metadata = virtual_wall->metadata();
        mapLines[line.name] = line;
    }
    return maplayer;
}

std::shared_ptr<rpos::stcm::LineMapLayer> MapManager::createVirtualTrackLayer()
{
    auto maplayer = std::make_shared<rpos::stcm::LineMapLayer>();
    maplayer->setType(rpos::stcm::LineMapLayer::Type);
    maplayer->setUsage(RPOS_COMPOSITEMAP_USAGE_VIRTUAL_TRACK);
    
    return maplayer;
}

std::shared_ptr<rpos::stcm::CompositeMap> MapManager::createCompositeMapFromPgm(const std::string& dir_path)
{
    fs::path dir(dir_path);
    std::string mapfile_pbstream = (dir / "map.pbstream").string();  
    std::string mapfile_pgm = (dir / "map.pgm").string();  
    std::string mapfile_yaml = (dir / "map.yaml").string();  

    auto composite_map = std::make_shared<rpos::stcm::CompositeMap>();
    if (!composite_map) {
        RCLCPP_ERROR(node_->get_logger(),"failed to create composite map");
        return nullptr;
    }

    PbstreamToMapLayerConverter pbstream_converter; 
    auto image_layer = pbstream_converter.convertPbstreamToMapLayer(mapfile_pbstream, "cartographer_features");
    if (!image_layer) { 
        RCLCPP_ERROR(node_->get_logger(),"failed to create image feature map layer");
        return nullptr;
    } 
    composite_map->maps().push_back(image_layer);
   
    PgmToGridConverter pgm_converter;
    if (!pgm_converter.loadPgmAndParams(mapfile_pgm, mapfile_yaml)
      || !pgm_converter.convertToGridMap()) {
        RCLCPP_ERROR(node_->get_logger(),"failed to convert pgm to grid map");
        return nullptr;
    }
    
    auto grid_layer = pgm_converter.createGridMapLayer();
    if (!grid_layer) {
        RCLCPP_ERROR(node_->get_logger(),"failed to create grid map layer");
        return nullptr;
    }
    composite_map->maps().push_back(grid_layer);
 
    std::shared_ptr<rpos::stcm::PoseMapLayer> poi_layer = createPOILayer(); 
    if(poi_layer){
        composite_map->maps().push_back(poi_layer);
    }

    auto wall_layer= createVirtualWallLayer();
    if(wall_layer){
        composite_map->maps().push_back(wall_layer);
    }
    
    auto track_layer= createVirtualTrackLayer();
    if(track_layer){
        composite_map->maps().push_back(track_layer);
    }
    return composite_map;
}

bool MapManager::saveMap(const std::string& file_path)
{
    std::filesystem::path dir_path = std::filesystem::path(file_path).parent_path();
       
    std::string mapfile_base = (dir_path / "map").string();
    std::string mapfile_pbstream = (dir_path / "map.pbstream").string();  

    if(!savePgmMap(mapfile_base, mapfile_pbstream))
    {
        return false;
    }
        
    auto composite_map = createCompositeMapFromPgm(dir_path.string());
    if(!composite_map)
        return false;
    try {
        rpos::stcm::CompositeMapWriter writer;
        writer.saveFile(file_path, *composite_map);
        return true;
    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(),"write composite map exeception:%s", e.what()); 
        for(auto map: composite_map->maps())
        {
            RCLCPP_INFO(node_->get_logger(), "type:%s", map->getType().c_str());
        }
    }
    return false;
}

bool MapManager::savePgmMap(const std::string &map_base, const std::string& pbstream)
{
    try {
        RCLCPP_INFO(node_->get_logger(), "Starting map save process...");

        // Step 1: Save PGM and YAML files using nav2_map_server
        std::string map_saver_cmd = "ros2 run nav2_map_server map_saver_cli --free 0.196 -f " + map_base;
        RCLCPP_INFO(node_->get_logger(), "Executing: %s", map_saver_cmd.c_str());

        int result1 = std::system(map_saver_cmd.c_str());
        if (result1 != 0) {
            RCLCPP_ERROR(node_->get_logger(), "Failed to save PGM map, exit code: %d", result1);
            return false;
        }

        RCLCPP_INFO(node_->get_logger(), "PGM map saved successfully");

        // Step 2: Save pbstream file using cartographer service
        std::string write_state_cmd = "ros2 service call /write_state cartographer_ros_msgs/srv/WriteState \"{filename: '" + pbstream + "'}\"";
        RCLCPP_INFO(node_->get_logger(), "Executing: %s", write_state_cmd.c_str());

        int result2 = std::system(write_state_cmd.c_str());
        if (result2 != 0) {
            RCLCPP_ERROR(node_->get_logger(), "Failed to save pbstream, exit code: %d", result2);
            return false;
        }

        RCLCPP_INFO(node_->get_logger(), "PBStream saved successfully");
        return true;

    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Error in savePgmMap: %s", e.what());
        return false;
    }
}

void MapManager::asyncInitializeWorker(const std::string& map_file_path)
{
    map_file_path_ = map_file_path;

    RCLCPP_INFO(node_->get_logger(), "Async initialization worker started");

    wallManager_.initialize();
    try {
        std::string error_msg;

        // Check if map file exists
        if (fs::exists(map_file_path_) && fs::is_regular_file(map_file_path_)) {
            RCLCPP_INFO(node_->get_logger(), "Map file exists, loading STCM file...");

            bool success = loadStcmFile(map_file_path_, false, error_msg);
            if (success) {
                RCLCPP_INFO(node_->get_logger(), "STCM file loaded successfully");
            } else {
                RCLCPP_ERROR(node_->get_logger(), "Failed to load STCM file: %s", error_msg.c_str());
            }
        } else {
           
            RCLCPP_INFO(node_->get_logger(), "Map file does not exist, subscribing to map_server/map...");
            subscribeToMapServer(); 
        }
        initialization_completed_.store(true);
        RCLCPP_INFO(node_->get_logger(), "Async MapManager initialization completed successfully");

    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Error during async initialization: %s", e.what());
        initialization_completed_.store(true); // Mark as completed even on error
    }

    initialization_in_progress_.store(false);
}

}} // namespace rslamware::stcm_manager