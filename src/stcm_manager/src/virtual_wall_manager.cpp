// Copyright (c) 2024 Slamtec
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "stcm_manager/virtual_wall_manager.hpp"

#include <algorithm>
#include <string>

namespace stcm_manager
{

VirtualWallManager::VirtualWallManager(std::shared_ptr<rclcpp::Node> node,
                  const std::string& virtual_wall_mask_topic)
: node_(node), virtual_wall_mask_topic_(virtual_wall_mask_topic), next_wall_id_(1),
  map_resolution_(0.05f), map_width_(0), map_height_(0), map_origin_x_(0.0), map_origin_y_(0.0)
{
}

VirtualWallManager::~VirtualWallManager()
{
}

bool VirtualWallManager::initialize()
{
  try {
    rclcpp::QoS qos(1);
    qos.transient_local();   // Make messages persistent for late-joining subscribers
    qos.reliable();          // Ensure reliable delivery
    qos.keep_last(1);        // Only keep the last message

    add_virtual_wall_service_ = node_->create_service<stcm_manager::srv::AddVirtualWall>(
        "add_virtual_wall", std::bind(&VirtualWallManager::addVirtualWallCallback, 
          this, std::placeholders::_1, std::placeholders::_2));

    delete_virtual_wall_service_ = node_->create_service<stcm_manager::srv::DeleteVirtualWall>(
        "delete_virtual_wall", std::bind(&VirtualWallManager::deleteVirtualWallCallback, 
          this, std::placeholders::_1, std::placeholders::_2));

    modify_virtual_wall_service_ = node_->create_service<stcm_manager::srv::ModifyVirtualWall>(
        "modify_virtual_wall", std::bind(&VirtualWallManager::modifyVirtualWallCallback, 
          this, std::placeholders::_1, std::placeholders::_2));
          
    clear_virtual_walls_service_ = node_->create_service<stcm_manager::srv::ClearVirtualWalls>(
        "clear_virtual_walls", std::bind(&VirtualWallManager::clearVirtualWallsCallback, 
          this, std::placeholders::_1, std::placeholders::_2));

    get_virtual_walls_service_ = node_->create_service<stcm_manager::srv::GetVirtualWalls>(
        "get_virtual_walls", std::bind(&VirtualWallManager::getVirtualWallsCallback, 
          this, std::placeholders::_1, std::placeholders::_2));
    
    virtual_wall_publisher_ = node_->create_publisher<nav_msgs::msg::OccupancyGrid>(
        virtual_wall_mask_topic_, qos);
    
    return true;
  } catch (const std::exception & e) {
    RCLCPP_ERROR(node_->get_logger(), "Failed to initialize virtual wall manager: %s", e.what());
    return false;
  }
}
  
void VirtualWallManager::setGridMapSize( uint32_t width, uint32_t height, float resolution, float origin_x, float origin_y)
{
  std::lock_guard<std::mutex> lock(walls_mutex_);
  map_width_ = width;
  map_height_ = height;
  map_resolution_ = resolution;
  map_origin_x_ = origin_x;
  map_origin_y_ = origin_y;
}

void VirtualWallManager::loadFromMapLayer(std::shared_ptr<rpos::stcm::LineMapLayer> layer)
{ 
    std::lock_guard<std::mutex> lock(walls_mutex_);
    virtual_walls_.clear();
    next_wall_id_ = 1;
    for (const auto& line_pair : layer->lines())
    { 
        std::stringstream ss(line_pair.first);
        int id;
        ss >> id; 
        rpos::core::Point start(line_pair.second.start.x(),line_pair.second.start.y());
        rpos::core::Point end(line_pair.second.end.x(),line_pair.second.end.y());

        auto line = std::make_shared<rpos::core::Line>(start, end, id);
        line->metadata() = line_pair.second.metadata;
        virtual_walls_[id] = line;
    } 
    publishVirtualWallMask();
}

int32_t VirtualWallManager::addVirtualWall(
  const geometry_msgs::msg::Point & start_point,
  const geometry_msgs::msg::Point & end_point,
  int32_t wall_id)
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  // Determine the wall ID
  int32_t assigned_id = wall_id;
  if (assigned_id == 0) {
    assigned_id = generateNextWallId();
  } else if (virtual_walls_.find(assigned_id) != virtual_walls_.end()) {
    RCLCPP_WARN(node_->get_logger(), "Virtual wall with ID %d already exists", assigned_id);
    return -1;
  }

  // Create the virtual wall
  auto start_rpos = convertToRposPoint(start_point);
  auto end_rpos = convertToRposPoint(end_point);
  auto line = std::make_shared<rpos::core::Line>(start_rpos, end_rpos, assigned_id);

  // Store the virtual wall
  virtual_walls_[assigned_id] = line;

  RCLCPP_INFO(
    node_->get_logger(),
    "Added virtual wall with ID %d from (%.2f, %.2f) to (%.2f, %.2f)",
    assigned_id, start_point.x, start_point.y, end_point.x, end_point.y);

  return assigned_id;
}

bool VirtualWallManager::deleteVirtualWall(int32_t wall_id)
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  auto it = virtual_walls_.find(wall_id);
  if (it == virtual_walls_.end()) { 
    return false;
  }

  virtual_walls_.erase(it);
  RCLCPP_INFO(node_->get_logger(), "Deleted virtual wall with ID %d", wall_id);
  return true;
}

bool VirtualWallManager::modifyVirtualWall(
  int32_t wall_id,
  const geometry_msgs::msg::Point & start_point,
  const geometry_msgs::msg::Point & end_point)
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  auto it = virtual_walls_.find(wall_id);
  if (it == virtual_walls_.end()) {
    RCLCPP_WARN(node_->get_logger(), "Virtual wall with ID %d not found", wall_id);
    return false;
  }

  // Update the virtual wall
  auto start_rpos = convertToRposPoint(start_point);
  auto end_rpos = convertToRposPoint(end_point);
  it->second->startP() = start_rpos;
  it->second->endP() = end_rpos;

  RCLCPP_INFO(
    node_->get_logger(),
    "Modified virtual wall with ID %d to (%.2f, %.2f) to (%.2f, %.2f)",
    wall_id, start_point.x, start_point.y, end_point.x, end_point.y);

  return true;
}

int32_t VirtualWallManager::clearVirtualWalls()
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  int32_t count = static_cast<int32_t>(virtual_walls_.size());
  virtual_walls_.clear();
  next_wall_id_ = 1;  // Reset next wall ID

  RCLCPP_INFO(node_->get_logger(), "Cleared %d virtual walls", count);
  return count;
}

void VirtualWallManager::getVirtualWalls(
  std::vector<interfaces::msg::VirtualLine> & lines) const
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  lines.clear(); 
  lines.reserve(virtual_walls_.size());
  for (const auto & pair : virtual_walls_) {
    const auto & line = pair.second;
    interfaces::msg::VirtualLine virtual_line;
    virtual_line.start = convertToGeometryPoint(line->startP());
    virtual_line.end = convertToGeometryPoint(line->endP());
    virtual_line.id = pair.first;
    lines.push_back(virtual_line);
  }
}

std::shared_ptr<rpos::core::Line> VirtualWallManager::getVirtualWallAsLine(int32_t wall_id) const
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  auto it = virtual_walls_.find(wall_id);
  if (it == virtual_walls_.end()) {
    return nullptr;
  }

  return it->second;
}

std::vector<std::shared_ptr<rpos::core::Line>> VirtualWallManager::getAllVirtualWalls() const
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  std::vector<std::shared_ptr<rpos::core::Line>> lines;
  lines.reserve(virtual_walls_.size());

  for (const auto & pair : virtual_walls_) {
    lines.push_back(pair.second);
  }

  return lines;
}

void VirtualWallManager::publishVirtualWallMask()
{
  auto grid_msg = std::make_shared<nav_msgs::msg::OccupancyGrid>();

  // Set up the grid header and info
  grid_msg->header.stamp = node_->now();
  grid_msg->header.frame_id = "map";
 
    grid_msg->info.resolution = map_resolution_;
    grid_msg->info.width = map_width_;
    grid_msg->info.height = map_height_;
    grid_msg->info.origin.position.x = map_origin_x_;
    grid_msg->info.origin.position.y = map_origin_y_;
    grid_msg->info.origin.position.z = 0.0;
    grid_msg->info.origin.orientation.w = 1.0;
    // Initialize grid with free space (0)
    grid_msg->data.resize(map_width_ * map_height_, 0);

    for (const auto& line_pair : virtual_walls_) {
        const auto& line = line_pair.second;

        // Convert world coordinates to grid coordinates
        int x0, y0, x1, y1;
        if (worldToGrid(line->startP().x(), line->startP().y(), x0, y0)
           && worldToGrid(line->endP().x(), line->endP().y(), x1, y1)) {
            // Draw line in grid (100 = occupied/obstacle)
            drawLineInGrid(grid_msg->data, map_width_, map_height_, x0, y0, x1, y1, 100); 
        }
    }

    // Publish the virtual wall occupancy grid
    if (virtual_wall_publisher_) {
        virtual_wall_publisher_->publish(*grid_msg);
    }
}

void VirtualWallManager::addVirtualWallCallback(
  const std::shared_ptr<stcm_manager::srv::AddVirtualWall::Request> request,
  std::shared_ptr<stcm_manager::srv::AddVirtualWall::Response> response)
{
  try {
    int32_t assigned_id = addVirtualWall(request->start_point, request->end_point);

    if (assigned_id > 0) {
      response->success = true;
      response->message = "Virtual wall added successfully";
      response->id = assigned_id;

      // Publish updated virtual walls
      publishVirtualWallMask();
    } else {
      response->success = false;
      response->message = "Failed to add virtual wall";
      response->id = 0;
    }
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    response->id = 0;
    RCLCPP_ERROR(node_->get_logger(), "Exception in addVirtualWallCallback: %s", e.what());
  }
}

void VirtualWallManager::deleteVirtualWallCallback(
  const std::shared_ptr<stcm_manager::srv::DeleteVirtualWall::Request> request,
  std::shared_ptr<stcm_manager::srv::DeleteVirtualWall::Response> response)
{
  try {
    bool success = deleteVirtualWall(request->id);

    if (success) {
      response->success = true;
      response->message = "Virtual wall deleted successfully";

      // Publish updated virtual walls
      publishVirtualWallMask();
    } else {
      response->success = false;
      response->message = "Virtual wall not found or failed to delete";
    }
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    RCLCPP_ERROR(node_->get_logger(), "Exception in deleteVirtualWallCallback: %s", e.what());
  }
}

void VirtualWallManager::modifyVirtualWallCallback(
  const std::shared_ptr<stcm_manager::srv::ModifyVirtualWall::Request> request,
  std::shared_ptr<stcm_manager::srv::ModifyVirtualWall::Response> response)
{
  try {
    bool success = modifyVirtualWall(request->id, request->start_point, request->end_point);

    if (success) {
      response->success = true;
      response->message = "Virtual wall modified successfully";

      // Publish updated virtual walls
      publishVirtualWallMask();
    } else {
      response->success = false;
      response->message = "Virtual wall not found or failed to modify";
    }
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    RCLCPP_ERROR(node_->get_logger(), "Exception in modifyVirtualWallCallback: %s", e.what());
  }
}

void VirtualWallManager::clearVirtualWallsCallback(
  const std::shared_ptr<stcm_manager::srv::ClearVirtualWalls::Request> request,
  std::shared_ptr<stcm_manager::srv::ClearVirtualWalls::Response> response)
{
  try {
    int32_t cleared_count = clearVirtualWalls();

    response->success = true;
    response->message = "Cleared " + std::to_string(cleared_count) + " virtual walls";

    // Publish updated virtual walls (empty)
    publishVirtualWallMask();
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    RCLCPP_ERROR(node_->get_logger(), "Exception in clearVirtualWallsCallback: %s", e.what());
  }
}

void VirtualWallManager::getVirtualWallsCallback(
  const std::shared_ptr<stcm_manager::srv::GetVirtualWalls::Request> request,
  std::shared_ptr<stcm_manager::srv::GetVirtualWalls::Response> response)
{
  try {
    std::vector<interfaces::msg::VirtualLine> lines; 
    getVirtualWalls(lines);

    response->success = true;
    response->message = "Retrieved " + std::to_string(lines.size()) + " virtual walls";
    response->lines = lines; 
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    response->lines.clear();
    RCLCPP_ERROR(node_->get_logger(), "Exception in getVirtualWallsCallback: %s", e.what());
  }
}

rpos::core::Point VirtualWallManager::convertToRposPoint(const geometry_msgs::msg::Point & point) const
{
  return rpos::core::Point(point.x, point.y);
}

geometry_msgs::msg::Point VirtualWallManager::convertToGeometryPoint(const rpos::core::Point & point) const
{
  geometry_msgs::msg::Point geometry_point;
  geometry_point.x = point.x();
  geometry_point.y = point.y();
  geometry_point.z = 0.0;
  return geometry_point;
}

void VirtualWallManager::drawLineInGrid(std::vector<int8_t>& grid, int width, int height, int x0, int y0, int x1, int y1, int8_t value)
{    
    // Bresenham's line algorithm
    int dx = std::abs(x1 - x0);
    int dy = std::abs(y1 - y0);
    int sx = (x0 < x1) ? 1 : -1;
    int sy = (y0 < y1) ? 1 : -1;
    int err = dx - dy;

    int x = x0;
    int y = y0;

    while (true) {
        // Check bounds and set pixel
        if (x >= 0 && x < width && y >= 0 && y < height) {
            int index = y * width + x;
            grid[index] = value;
        }

        // Check if we've reached the end point
        if (x == x1 && y == y1) break;

        int e2 = 2 * err;
        if (e2 > -dy) {
            err -= dy;
            x += sx;
        }
        if (e2 < dx) {
            err += dx;
            y += sy;
        }
    }
}

bool VirtualWallManager::worldToGrid(double world_x, double world_y, int& grid_x, int& grid_y) const
{
    if (map_resolution_ <= 0.0) {
        return false;
    }

    // Convert world coordinates to grid coordinates
    grid_x = static_cast<int>((world_x - map_origin_x_) / map_resolution_);
    grid_y = static_cast<int>((world_y - map_origin_y_) / map_resolution_);

    // Check bounds
    return (grid_x >= 0 && grid_x < static_cast<int>(map_width_) &&
            grid_y >= 0 && grid_y < static_cast<int>(map_height_));
}

int32_t VirtualWallManager::generateNextWallId()
{
  // Find the next available ID
  while (virtual_walls_.find(next_wall_id_) != virtual_walls_.end()) {
    next_wall_id_++;
  }

  return next_wall_id_++;
}

}  // namespace stcm_manager 