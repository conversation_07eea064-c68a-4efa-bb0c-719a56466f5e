// Copyright (c) 2024 Slamtec
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "stcm_manager/virtual_wall_manager.hpp"

#include <algorithm>
#include <string>

namespace stcm_manager
{

VirtualWallManager::VirtualWallManager(std::shared_ptr<rclcpp::Node> node,
                  const std::string& virtual_wall_mask_topic)
: node_(node), next_wall_id_(1), virtual_wall_mask_topic_(virtual_wall_mask_topic),
  map_resolution_(0.05f), map_width_(0), map_height_(0), map_origin_x_(0.0), map_origin_y_(0.0)
{
}

VirtualWallManager::~VirtualWallManager()
{
}

bool VirtualWallManager::initialize()
{
  try {
    rclcpp::QoS qos(1);
    qos.transient_local();   // Make messages persistent for late-joining subscribers
    qos.reliable();          // Ensure reliable delivery
    qos.keep_last(1);        // Only keep the last message

    virtual_wall_publisher_ = node_->create_publisher<nav_msgs::msg::OccupancyGrid>(
        virtual_wall_mask_topic_, qos);
    
    return true;
  } catch (const std::exception & e) {
    RCLCPP_ERROR(node_->get_logger(), "Failed to initialize virtual wall manager: %s", e.what());
    return false;
  }
}
  
void VirtualWallManager::setGridMapSize( uint32_t width, uint32_t height, float resolution, float origin_x, float origin_y)
{
  std::lock_guard<std::mutex> lock(walls_mutex_);
  map_width_ = width;
  map_height_ = height;
  map_resolution_ = resolution;
  map_origin_x_ = origin_x;
  map_origin_y_ = origin_y;
}

void VirtualWallManager::loadFromMapLayer(std::shared_ptr<rpos::stcm::LineMapLayer> layer)
{ 
    for (const auto& line_pair : layer->lines()) {
        
    } 
}

int32_t VirtualWallManager::addVirtualWall(
  const geometry_msgs::msg::Point & start_point,
  const geometry_msgs::msg::Point & end_point,
  int32_t wall_id)
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  // Determine the wall ID
  int32_t assigned_id = wall_id;
  if (assigned_id == 0) {
    assigned_id = generateNextWallId();
  } else if (virtual_walls_.find(assigned_id) != virtual_walls_.end()) {
    RCLCPP_WARN(node_->get_logger(), "Virtual wall with ID %d already exists", assigned_id);
    return -1;
  }

  // Create the virtual wall
  auto start_rpos = convertToRposPoint(start_point);
  auto end_rpos = convertToRposPoint(end_point);
  auto line = std::make_shared<rpos::core::Line>(start_rpos, end_rpos, assigned_id);

  // Store the virtual wall
  virtual_walls_[assigned_id] = line;

  RCLCPP_INFO(
    node_->get_logger(),
    "Added virtual wall with ID %d from (%.2f, %.2f) to (%.2f, %.2f)",
    assigned_id, start_point.x, start_point.y, end_point.x, end_point.y);

  return assigned_id;
}

bool VirtualWallManager::deleteVirtualWall(int32_t wall_id)
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  auto it = virtual_walls_.find(wall_id);
  if (it == virtual_walls_.end()) { 
    return false;
  }

  virtual_walls_.erase(it);
  RCLCPP_INFO(node_->get_logger(), "Deleted virtual wall with ID %d", wall_id);
  return true;
}

bool VirtualWallManager::modifyVirtualWall(
  int32_t wall_id,
  const geometry_msgs::msg::Point & start_point,
  const geometry_msgs::msg::Point & end_point)
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  auto it = virtual_walls_.find(wall_id);
  if (it == virtual_walls_.end()) {
    RCLCPP_WARN(node_->get_logger(), "Virtual wall with ID %d not found", wall_id);
    return false;
  }

  // Update the virtual wall
  auto start_rpos = convertToRposPoint(start_point);
  auto end_rpos = convertToRposPoint(end_point);
  it->second->startP() = start_rpos;
  it->second->endP() = end_rpos;

  RCLCPP_INFO(
    node_->get_logger(),
    "Modified virtual wall with ID %d to (%.2f, %.2f) to (%.2f, %.2f)",
    wall_id, start_point.x, start_point.y, end_point.x, end_point.y);

  return true;
}

int32_t VirtualWallManager::clearVirtualWalls()
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  int32_t count = static_cast<int32_t>(virtual_walls_.size());
  virtual_walls_.clear();
  next_wall_id_ = 1;  // Reset next wall ID

  RCLCPP_INFO(node_->get_logger(), "Cleared %d virtual walls", count);
  return count;
}

void VirtualWallManager::getVirtualWalls(
  std::vector<geometry_msgs::msg::Point> & start_points,
  std::vector<geometry_msgs::msg::Point> & end_points,
  std::vector<int32_t> & wall_ids) const
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  start_points.clear();
  end_points.clear();
  wall_ids.clear();

  for (const auto & pair : virtual_walls_) {
    const auto & line = pair.second;
    start_points.push_back(convertToGeometryPoint(line->startP()));
    end_points.push_back(convertToGeometryPoint(line->endP()));
    wall_ids.push_back(pair.first);
  }
}

std::shared_ptr<rpos::core::Line> VirtualWallManager::getVirtualWallAsLine(int32_t wall_id) const
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  auto it = virtual_walls_.find(wall_id);
  if (it == virtual_walls_.end()) {
    return nullptr;
  }

  return it->second;
}

std::vector<std::shared_ptr<rpos::core::Line>> VirtualWallManager::getAllVirtualWallsAsLines() const
{
  std::lock_guard<std::mutex> lock(walls_mutex_);

  std::vector<std::shared_ptr<rpos::core::Line>> lines;
  lines.reserve(virtual_walls_.size());

  for (const auto & pair : virtual_walls_) {
    lines.push_back(pair.second);
  }

  return lines;
}

void VirtualWallManager::publishVirtualWalls(nav_msgs::msg::OccupancyGrid::SharedPtr gridmap)
{
  
    auto grid_msg = std::make_shared<nav_msgs::msg::OccupancyGrid>();

    // Set up the grid header and info
    grid_msg->header.stamp = node_->now();
    grid_msg->header.frame_id = "map";

    grid_msg->info.resolution = map_resolution_;
    grid_msg->info.width = map_width_;
    grid_msg->info.height = map_height_;
    grid_msg->info.origin.position.x = map_origin_x_;
    grid_msg->info.origin.position.y = map_origin_y_;
    grid_msg->info.origin.position.z = 0.0;
    grid_msg->info.origin.orientation.w = 1.0;

    // Initialize grid with free space (0)
    grid_msg->data.resize(map_width_ * map_height_, 0);
 
    // Get all lines from this layer
    /*for (const auto& line_pair : layer->lines()) {
        const auto& line = line_pair.second;

        // Convert world coordinates to grid coordinates
        int x0, y0, x1, y1;
        if (worldToGrid(line.start.x(), line.start.y(), x0, y0) && worldToGrid(line.end.x(), line.end.y(), x1, y1)) {
            // Draw line in grid (100 = occupied/obstacle)
            drawLineInGrid(grid_msg->data, map_width_, map_height_, x0, y0, x1, y1, 100); 
        }
    }*/
    // Publish the virtual wall occupancy grid
    if (virtual_wall_publisher_) {
        virtual_wall_publisher_->publish(*grid_msg);
    }
}

void VirtualWallManager::addVirtualWallCallback(
  const std::shared_ptr<void> request,
  std::shared_ptr<void> response)
{
  auto req = std::static_pointer_cast<stcm_manager::srv::AddVirtualWall::Request>(request);
  auto res = std::static_pointer_cast<stcm_manager::srv::AddVirtualWall::Response>(response);

  try {
    int32_t assigned_id = addVirtualWall(req->start_point, req->end_point);

    if (assigned_id > 0) {
      res->success = true;
      res->message = "Virtual wall added successfully";
      res->id = assigned_id;

      // Publish updated virtual walls
      publishVirtualWalls(nullptr);
    } else {
      res->success = false;
      res->message = "Failed to add virtual wall";
      res->id = -1;
    }
  } catch (const std::exception& e) {
    res->success = false;
    res->message = std::string("Exception occurred: ") + e.what();
    res->id = -1;
    RCLCPP_ERROR(node_->get_logger(), "Exception in addVirtualWallCallback: %s", e.what());
  }
}

void VirtualWallManager::deleteVirtualWallCallback(
  const std::shared_ptr<void> request,
  std::shared_ptr<void> response)
{
  auto req = std::static_pointer_cast<stcm_manager::srv::DeleteVirtualWall::Request>(request);
  auto res = std::static_pointer_cast<stcm_manager::srv::DeleteVirtualWall::Response>(response);

  try {
    bool success = deleteVirtualWall(req->id);

    if (success) {
      res->success = true;
      res->message = "Virtual wall deleted successfully";

      // Publish updated virtual walls
      publishVirtualWalls(nullptr);
    } else {
      res->success = false;
      res->message = "Virtual wall not found or failed to delete";
    }
  } catch (const std::exception& e) {
    res->success = false;
    res->message = std::string("Exception occurred: ") + e.what();
    RCLCPP_ERROR(node_->get_logger(), "Exception in deleteVirtualWallCallback: %s", e.what());
  }
}

void VirtualWallManager::modifyVirtualWallCallback(
  const std::shared_ptr<void> request,
  std::shared_ptr<void> response)
{
  auto req = std::static_pointer_cast<stcm_manager::srv::ModifyVirtualWall::Request>(request);
  auto res = std::static_pointer_cast<stcm_manager::srv::ModifyVirtualWall::Response>(response);

  try {
    bool success = modifyVirtualWall(req->id, req->start_point, req->end_point);

    if (success) {
      res->success = true;
      res->message = "Virtual wall modified successfully";

      // Publish updated virtual walls
      publishVirtualWalls(nullptr);
    } else {
      res->success = false;
      res->message = "Virtual wall not found or failed to modify";
    }
  } catch (const std::exception& e) {
    res->success = false;
    res->message = std::string("Exception occurred: ") + e.what();
    RCLCPP_ERROR(node_->get_logger(), "Exception in modifyVirtualWallCallback: %s", e.what());
  }
}

void VirtualWallManager::clearVirtualWallsCallback(
  const std::shared_ptr<void> request,
  std::shared_ptr<void> response)
{
  auto req = std::static_pointer_cast<stcm_manager::srv::ClearVirtualWalls::Request>(request);
  auto res = std::static_pointer_cast<stcm_manager::srv::ClearVirtualWalls::Response>(response);

  try {
    int32_t cleared_count = clearVirtualWalls();

    res->success = true;
    res->message = "Cleared " + std::to_string(cleared_count) + " virtual walls";

    // Publish updated virtual walls (empty)
    publishVirtualWalls(nullptr);
  } catch (const std::exception& e) {
    res->success = false;
    res->message = std::string("Exception occurred: ") + e.what();
    RCLCPP_ERROR(node_->get_logger(), "Exception in clearVirtualWallsCallback: %s", e.what());
  }
}

void VirtualWallManager::getVirtualWallsCallback(
  const std::shared_ptr<void> request,
  std::shared_ptr<void> response)
{
  auto req = std::static_pointer_cast<stcm_manager::srv::GetVirtualWalls::Request>(request);
  auto res = std::static_pointer_cast<stcm_manager::srv::GetVirtualWalls::Response>(response);

  try {
    std::vector<geometry_msgs::msg::Point> start_points;
    std::vector<geometry_msgs::msg::Point> end_points;
    std::vector<int32_t> wall_ids;

    getVirtualWalls(start_points, end_points, wall_ids);

    res->success = true;
    res->message = "Retrieved " + std::to_string(wall_ids.size()) + " virtual walls";
    res->start_points = start_points;
    res->end_points = end_points;
    res->ids = wall_ids;
  } catch (const std::exception& e) {
    res->success = false;
    res->message = std::string("Exception occurred: ") + e.what();
    res->start_points.clear();
    res->end_points.clear();
    res->ids.clear();
    RCLCPP_ERROR(node_->get_logger(), "Exception in getVirtualWallsCallback: %s", e.what());
  }
}

rpos::core::Point VirtualWallManager::convertToRposPoint(const geometry_msgs::msg::Point & point) const
{
  return rpos::core::Point(point.x, point.y);
}

geometry_msgs::msg::Point VirtualWallManager::convertToGeometryPoint(const rpos::core::Point & point) const
{
  geometry_msgs::msg::Point geometry_point;
  geometry_point.x = point.x();
  geometry_point.y = point.y();
  geometry_point.z = 0.0;
  return geometry_point;
}

int32_t VirtualWallManager::generateNextWallId()
{
  // Find the next available ID
  while (virtual_walls_.find(next_wall_id_) != virtual_walls_.end()) {
    next_wall_id_++;
  }

  return next_wall_id_++;
}

}  // namespace stcm_manager 