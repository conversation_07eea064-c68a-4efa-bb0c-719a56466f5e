#include "stcm_converter/pgm_to_grid_converter.h"
#include <iostream>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <regex>

// Include rpos_common headers
#include <core/metadata.h>
#include <cctype>

namespace rslamware { namespace stcm_manager {

PgmToGridConverter::PgmToGridConverter()
    : is_loaded_(false), is_converted_(false)
{
}

PgmToGridConverter::~PgmToGridConverter()
{
}

bool PgmToGridConverter::loadPgmFile(const std::string& file_path)
{
    last_error_.clear();
    is_loaded_ = false;
    is_converted_ = false;

    std::ifstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        last_error_ = "Failed to open file: " + file_path;
        return false;
    }

    // Parse header
    if (!parsePgmHeader(file)) {
        file.close();
        return false;
    }

    // Read data
    if (!readPgmData(file)) {
        file.close();
        return false;
    }

    file.close();
    is_loaded_ = true;
    return true;
}

bool PgmToGridConverter::loadPgmAndParams(const std::string& pgm_file_path, const std::string& yaml_file_path)
{
    last_error_.clear();
    is_loaded_ = false;
    is_converted_ = false;

    // First load YAML parameters
    if (!parseYamlFile(yaml_file_path, params_)) {
        return false;
    }

    // Then load PGM file
    std::ifstream file(pgm_file_path, std::ios::binary);
    if (!file.is_open()) {
        last_error_ = "Failed to open PGM file: " + pgm_file_path;
        return false;
    }

    // Parse header
    if (!parsePgmHeader(file)) {
        file.close();
        return false;
    }

    // Read data
    if (!readPgmData(file)) {
        file.close();
        return false;
    }

    file.close();
    is_loaded_ = true;
    return true;
}

bool PgmToGridConverter::convertToGridMap()
{
    if (!is_loaded_) {
        last_error_ = "No PGM file loaded";
        return false;
    }
    
    last_error_.clear();
    is_converted_ = false;
    
    // Resize grid data
    grid_data_.resize(pgm_info_.data.size());
    
    // Convert each pixel
    for (size_t i = 0; i < pgm_info_.data.size(); ++i) {
        grid_data_[i] = pixelToOccupancy(pgm_info_.data[i]);
    }
    
    is_converted_ = true;
    return true;
}

const std::vector<uint8_t>& PgmToGridConverter::getGridMapData() const
{
    return grid_data_;
}

const PgmInfo& PgmToGridConverter::getPgmInfo() const
{
    return pgm_info_;
}

int PgmToGridConverter::getWidth() const
{
    return pgm_info_.width;
}

int PgmToGridConverter::getHeight() const
{
    return pgm_info_.height;
}

double PgmToGridConverter::getResolution() const
{
    return params_.resolution;
}

void PgmToGridConverter::getOrigin(double& origin_x, double& origin_y) const
{
    origin_x = params_.origin_x;
    origin_y = params_.origin_y;
}

const std::string& PgmToGridConverter::getLastError() const
{
    return last_error_;
}

bool PgmToGridConverter::parsePgmHeader(std::ifstream& file)
{
    std::string line;
    
    // Read magic number
    if (!std::getline(file, line)) {
        last_error_ = "Failed to read magic number";
        return false;
    }
    
    pgm_info_.magic_number = line;
    if (line != "P2" && line != "P5") {
        last_error_ = "Unsupported PGM format: " + line + " (only P2 and P5 supported)";
        return false;
    }
    
    // Skip comments and read dimensions
    skipComments(file);
    if (!(file >> pgm_info_.width >> pgm_info_.height)) {
        last_error_ = "Failed to read image dimensions";
        return false;
    }
    
    if (pgm_info_.width <= 0 || pgm_info_.height <= 0) {
        last_error_ = "Invalid image dimensions";
        return false;
    }
    
    // Read max value
    if (!(file >> pgm_info_.max_value)) {
        last_error_ = "Failed to read max value";
        return false;
    }
    
    if (pgm_info_.max_value <= 0 || pgm_info_.max_value > 255) {
        last_error_ = "Invalid max value: " + std::to_string(pgm_info_.max_value);
        return false;
    }
    
    // Skip the newline after max value
    file.ignore(1);
    
    return true;
}

bool PgmToGridConverter::readPgmData(std::ifstream& file)
{
    size_t data_size = pgm_info_.width * pgm_info_.height;
    pgm_info_.data.resize(data_size);
    
    if (pgm_info_.magic_number == "P5") {
        // Binary format
        file.read(reinterpret_cast<char*>(pgm_info_.data.data()), data_size);
        if (file.gcount() != static_cast<std::streamsize>(data_size)) {
            last_error_ = "Failed to read binary data";
            return false;
        }
    } else if (pgm_info_.magic_number == "P2") {
        // ASCII format
        for (size_t i = 0; i < data_size; ++i) {
            int value;
            if (!(file >> value)) {
                last_error_ = "Failed to read ASCII data at position " + std::to_string(i);
                return false;
            }
            pgm_info_.data[i] = static_cast<uint8_t>(value);
        }
    }
    
    return true;
}

void PgmToGridConverter::skipComments(std::ifstream& file)
{
    char c;
    while (file.peek() == '#') {
        // Skip comment line
        std::string comment;
        std::getline(file, comment);
    }
    
    // Skip whitespace
    while (file.peek() != EOF && std::isspace(file.peek())) {
        file.get(c);
    }
}

int8_t PgmToGridConverter::pixelToOccupancy(uint8_t pixel_value) const
{
    // Normalize pixel value to 0-1 range
    double occupy_prob = static_cast<double>(pgm_info_.max_value - pixel_value) / pgm_info_.max_value;

    // Apply negate if specified
    if (params_.negate) {
        occupy_prob = 1.0 - occupy_prob;
    }
    if(occupy_prob > params_.occupied_threshold){
        return -127;
    }
    else if(occupy_prob < params_.free_threshold){
        return 127;
    }
    else{
        return 0;
    } 
    //return logLUT_.p2l(occupy_prob);
}

bool PgmToGridConverter::loadParamsFromYaml(const std::string& yaml_file_path) {
    return parseYamlFile(yaml_file_path, params_);
}

bool PgmToGridConverter::parseYamlFile(const std::string& yaml_file_path, GridMapParams& params) {
    std::ifstream file(yaml_file_path);
    if (!file.is_open()) {
        last_error_ = "Failed to open YAML file: " + yaml_file_path;
        return false;
    }

    std::string line;
    std::regex image_regex(R"(image\s*:\s*(.+))");
    std::regex resolution_regex(R"(resolution\s*:\s*([\d\.]+))");
    std::regex origin_regex(R"(origin\s*:\s*\[\s*([-\d\.]+)\s*,\s*([-\d\.]+)\s*,\s*([-\d\.]+)\s*\])");
    std::regex negate_regex(R"(negate\s*:\s*(\d+))");
    std::regex occupied_thresh_regex(R"(occupied_thresh\s*:\s*([\d\.]+))");
    std::regex free_thresh_regex(R"(free_thresh\s*:\s*([\d\.]+))");

    std::smatch match;

    while (std::getline(file, line)) {
        // Remove comments
        size_t comment_pos = line.find('#');
        if (comment_pos != std::string::npos) {
            line = line.substr(0, comment_pos);
        }

        // Trim whitespace
        line.erase(0, line.find_first_not_of(" \t"));
        line.erase(line.find_last_not_of(" \t") + 1);

        if (line.empty()) continue;

        if (std::regex_search(line, match, image_regex)) {
            params.image_file = match[1].str();
            // Remove quotes if present
            if (params.image_file.front() == '"' && params.image_file.back() == '"') {
                params.image_file = params.image_file.substr(1, params.image_file.length() - 2);
            }
        } else if (std::regex_search(line, match, resolution_regex)) {
            params.resolution = std::stod(match[1].str());
        } else if (std::regex_search(line, match, origin_regex)) {
            params.origin_x = std::stod(match[1].str());
            params.origin_y = std::stod(match[2].str());
            params.origin_theta = std::stod(match[3].str());
        } else if (std::regex_search(line, match, negate_regex)) {
            params.negate = (std::stoi(match[1].str()) != 0);
        } else if (std::regex_search(line, match, occupied_thresh_regex)) {
            params.occupied_threshold = std::stod(match[1].str());
        } else if (std::regex_search(line, match, free_thresh_regex)) {
            params.free_threshold = std::stod(match[1].str());
        }
    }

    file.close();

    // Validate required parameters
    if (params.image_file.empty()) {
        last_error_ = "Missing 'image' parameter in YAML file";
        return false;
    }

    if (params.resolution <= 0) {
        last_error_ = "Invalid or missing 'resolution' parameter in YAML file";
        return false;
    }

    return true;
}

std::shared_ptr<rpos::stcm::GridMapLayer> PgmToGridConverter::createGridMapLayer() {
    if (!is_loaded_ || !is_converted_) {
        last_error_ = "PGM data not loaded or converted. Call loadPgm() and convertToGrid() first.";
        return nullptr;
    }

    try {
        // Create origin location
        rpos::core::Location origin(params_.origin_x, params_.origin_y, 0.0);

        // Create dimension
        rpos::core::Vector2i dimension(pgm_info_.width, pgm_info_.height);

        // Create resolution
        rpos::core::Vector2f resolution(params_.resolution, params_.resolution);

        // Create metadata
        rpos::core::Metadata metadata;
        metadata.dict()["occupied_threshold"] = std::to_string(params_.occupied_threshold);
        metadata.dict()["free_threshold"] = std::to_string(params_.free_threshold);
        metadata.dict()["negate"] = params_.negate ? "1" : "0";

        // Create GridMapLayer
        auto grid_layer = std::make_shared<rpos::stcm::GridMapLayer>();

        // Set metadata
        grid_layer->metadata() = metadata;

        // Set properties
        grid_layer->setOrigin(origin);
        grid_layer->setDimension(dimension);
        grid_layer->setResolution(resolution);
        grid_layer->mapData() = grid_data_;

        // Set required MapLayer properties
        grid_layer->setType(rpos::stcm::GridMapLayer::Type);
        grid_layer->setUsage(RPOS_COMPOSITEMAP_USAGE_EXPLORE);  // Standard usage for navigation
        grid_layer->setName("grid_map");

        return grid_layer;

    } catch (const std::exception& e) {
        last_error_ = "Failed to create GridMapLayer: " + std::string(e.what());
        return nullptr;
    }
}

bool PgmToGridConverter::savePgmFile(const std::string& file_path, int width, int height,
                                     const std::vector<int8_t>& grid_data) const
{
    std::ofstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        const_cast<PgmToGridConverter*>(this)->last_error_ = "Failed to create PGM file: " + file_path;
        return false;
    }

    // Write PGM header
    file << "P5\n";
    file << width << " " << height << "\n";
    file << "255\n";

    // Convert grid data back to pixel values for saving
    // New mapping: -127=obstacle, 127=free, 0=unknown
    for (const auto& value : grid_data) {
        uint8_t pixel_value;
        if (value == 0) {
            pixel_value = 128; // Unknown = gray
        } else if (value == 127) {
            pixel_value = 255; // Free space = white
        } else if (value == -127) {
            pixel_value = 0;   // Occupied/obstacle = black
        } else {
            pixel_value = 128; // Any unexpected value = gray (unknown)
        }
        file.write(reinterpret_cast<const char*>(&pixel_value), 1);
    }

    file.close();
    return true;
}

bool PgmToGridConverter::saveYamlFile(const std::string& file_path,
                                      const rpos::core::Location& origin,
                                      const rpos::core::Vector2f& resolution,
                                      const rpos::core::Metadata* metadata) const
{
    std::ofstream file(file_path);
    if (!file.is_open()) {
        const_cast<PgmToGridConverter*>(this)->last_error_ = "Failed to create YAML file: " + file_path;
        return false;
    }

    // Extract PGM filename from full path
    std::string pgm_filename = file_path;
    size_t last_slash = pgm_filename.find_last_of("/\\");
    if (last_slash != std::string::npos) {
        pgm_filename = pgm_filename.substr(last_slash + 1);
    }
    // Replace .yaml extension with .pgm
    size_t dot_pos = pgm_filename.find_last_of('.');
    if (dot_pos != std::string::npos) {
        pgm_filename = pgm_filename.substr(0, dot_pos) + ".pgm";
    }

    // Extract parameters from metadata if available, otherwise use defaults
    std::string negate_value = "0";
    std::string occupied_thresh = "0.65";
    std::string free_thresh = "0.196";

    if (metadata) {
        const auto& dict = metadata->dict();

        // Check for negate parameter
        auto negate_it = dict.find("negate");
        if (negate_it != dict.end()) {
            negate_value = negate_it->second;
        }

        // Check for occupied_threshold parameter
        auto occupied_it = dict.find("occupied_threshold");
        if (occupied_it != dict.end()) {
            occupied_thresh = occupied_it->second;
        }

        // Check for free_threshold parameter
        auto free_it = dict.find("free_threshold");
        if (free_it != dict.end()) {
            free_thresh = free_it->second;
        }
    }

    // Write YAML content
    file << "image: " << pgm_filename << "\n";
    file << "resolution: " << resolution.x() << "\n";
    file << "origin: [" << origin.x() << ", " << origin.y() << ", 0.0]\n";
    file << "negate: " << negate_value << "\n";
    file << "occupied_thresh: " << occupied_thresh << "\n";
    file << "free_thresh: " << free_thresh << "\n"; 
    file.close();
    return true;
}

bool PgmToGridConverter::saveGridMapToPgm(std::shared_ptr<rpos::stcm::GridMapLayer> grid_layer,
                                          const std::string& pgm_file_path,
                                          const std::string& yaml_file_path)
{
    if (!grid_layer) {
        last_error_ = "GridMapLayer is null";
        return false;
    }

    last_error_.clear();

    try {
        // Extract data from GridMapLayer
        const auto& origin = grid_layer->getOrigin();
        const auto& dimension = grid_layer->getDimension();
        const auto& resolution = grid_layer->getResolution();
        const auto& map_data = grid_layer->mapData();

        // Convert uint8_t map data back to int8_t grid data for saving
        std::vector<int8_t> grid_data;
        grid_data.reserve(map_data.size());

        for (uint8_t value : map_data) {
            if (value == 255) {
                grid_data.push_back(127);   // Free space (white) -> 127
            } else if (value == 0) {
                grid_data.push_back(-127);  // Occupied/obstacle (black) -> -127
            } else {
                grid_data.push_back(0);     // Unknown (gray) -> 0
            }
        }

        // Save PGM file
        if (!savePgmFile(pgm_file_path, dimension.x(), dimension.y(), grid_data)) {
            return false;
        }

        // Save YAML file with metadata from GridMapLayer
        const auto& layer_metadata = grid_layer->metadata();
        if (!saveYamlFile(yaml_file_path, origin, resolution, &layer_metadata)) {
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        last_error_ = "Failed to save GridMapLayer: " + std::string(e.what());
        return false;
    }
}

}} // namespace stcm_manager
