// Copyright (c) 2024 Slamtec
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "stcm_manager/poi_manager.hpp"
#include "map_manager.h"
#include "rpos/core/pose_entry.h"
#include "rpos/core/pose.h"

#include <string>
#include <algorithm>

namespace stcm_manager
{

POIManager::POIManager(std::shared_ptr<rclcpp::Node> node, rslamware::stcm_manager::MapManager& map_manager,
  std::shared_ptr<std::recursive_mutex> slamkit_platform_mutex)
: node_(node), map_manager_(map_manager), slamkit_platform_mutex_(slamkit_platform_mutex)
{
}

POIManager::~POIManager()
{
}

bool POIManager::initialize()
{
  try {
    rclcpp::QoS qos(1);
    qos.transient_local();   // Make messages persistent for late-joining subscribers
    qos.reliable();          // Ensure reliable delivery
    qos.keep_last(1);        // Only keep the last message

    add_poi_service_ = node_->create_service<interfaces::srv::AddPOI>(
        "add_poi", std::bind(&POIManager::addPOICallback, 
          this, std::placeholders::_1, std::placeholders::_2));

    delete_poi_service_ = node_->create_service<interfaces::srv::RemovePOI>(
        "remove_poi", std::bind(&POIManager::deletePOICallback, 
          this, std::placeholders::_1, std::placeholders::_2));

    update_poi_service_ = node_->create_service<interfaces::srv::UpdatePOI>(
        "update_poi", std::bind(&POIManager::updatePOICallback, 
          this, std::placeholders::_1, std::placeholders::_2));

    clear_all_poi_service_ = node_->create_service<interfaces::srv::RemoveAllPOI>(
        "remove_all_poi", std::bind(&POIManager::clearAllPOIsCallback, 
          this, std::placeholders::_1, std::placeholders::_2));

    get_poi_service_ = node_->create_service<interfaces::srv::GetPOI>(
        "get_poi", std::bind(&POIManager::getPOICallback, 
          this, std::placeholders::_1, std::placeholders::_2));

    list_poi_service_ = node_->create_service<interfaces::srv::ListPOI>(
        "list_poi", std::bind(&POIManager::listPOICallback, 
          this, std::placeholders::_1, std::placeholders::_2));

    RCLCPP_INFO(node_->get_logger(), "POI manager initialized");
    return true;
  } catch (const std::exception & e) {
    RCLCPP_ERROR(node_->get_logger(), "Failed to initialize POI manager: %s", e.what());
    return false;
  }
}

bool POIManager::addPOI(const interfaces::msg::POI & poi, std::string & error_msg)
{
  std::lock_guard<std::mutex> lock(pois_mutex_);

  if (pois_.find(poi.id) != pois_.end()) {
    RCLCPP_WARN(node_->get_logger(), "POI with ID %s already exists", poi.id.c_str());
    error_msg = "POI with ID " + poi.id + " already exists";
    return false;
  }

  try {
    std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
    auto slamkit_platform = map_manager_.getSlamwarePlatform();
    if (slamkit_platform) {
      RCLCPP_INFO(node_->get_logger(), "Using SlamwareCorePlatform to process POI");
      rpos::core::PoseEntry pose_entry;
      pose_entry.name = poi.id;
      pose_entry.pose = rpos::core::Pose(rpos::core::Location(poi.pose.x, poi.pose.y), rpos::core::Rotation(poi.pose.theta));
      pose_entry.metadata.set("display_name", poi.name);
      pose_entry.metadata.set("type", poi.type);
      if (!slamkit_platform->addPOI(pose_entry)) {
        RCLCPP_ERROR(node_->get_logger(), "Failed to add POI with ID %s", poi.id.c_str());
        error_msg = "Failed to add POI with ID " + poi.id;
        return false;
      }
    } else {
      RCLCPP_WARN(node_->get_logger(), "SlamwareCorePlatform not available");
      error_msg = "SlamwareCorePlatform not available";
      return false;
    }
  } catch (const rpos::system::detail::ExceptionBase& e) {
    RCLCPP_ERROR(node_->get_logger(), "Failed to add POI: %s", e.toString().c_str());
    error_msg = "Failed to add POI: " + e.toString();
    return false;
  }
  pois_[poi.id] = poi;
  RCLCPP_INFO(node_->get_logger(), "Added POI with ID %s", poi.id.c_str());
  return true;
}

bool POIManager::deletePOI(const std::string & id, std::string & error_msg)
{
  std::lock_guard<std::mutex> lock(pois_mutex_);

  auto it = pois_.find(id);
  if (it == pois_.end()) {
    RCLCPP_WARN(node_->get_logger(), "POI with ID %s not found", id.c_str());
    error_msg = "POI with ID " + id + " not found";
    return false;
  }

  try {
    std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
    auto slamkit_platform = map_manager_.getSlamwarePlatform();
    if (slamkit_platform) {
      RCLCPP_INFO(node_->get_logger(), "Using SlamwareCorePlatform to process POI");
      if (!slamkit_platform->erasePOI(id)) {
        RCLCPP_ERROR(node_->get_logger(), "Failed to delete POI with ID %s", id.c_str());
        error_msg = "Failed to delete POI with ID " + id;
        return false;
      }
    } else {
      RCLCPP_WARN(node_->get_logger(), "SlamwareCorePlatform not available");
      error_msg = "SlamwareCorePlatform not available";
      return false;
    }
  } catch (const rpos::system::detail::ExceptionBase& e) {
    RCLCPP_ERROR(node_->get_logger(), "Failed to delete POI: %s", e.toString().c_str());
    error_msg = "Failed to delete POI: " + e.toString();
    return false;
  }
  pois_.erase(it);
  RCLCPP_INFO(node_->get_logger(), "Deleted POI with ID %s", id.c_str());
  return true;
}

bool POIManager::updatePOI(const interfaces::msg::POI & poi, std::string & error_msg)
{
  std::lock_guard<std::mutex> lock(pois_mutex_);

  auto it = pois_.find(poi.id);
  if (it == pois_.end()) {
    RCLCPP_WARN(node_->get_logger(), "POI with ID %s not found for update", poi.id.c_str());
    error_msg = "POI with ID " + poi.id + " not found";
    return false;
  }

  try {
    std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
    auto slamkit_platform = map_manager_.getSlamwarePlatform();
    if (slamkit_platform) {
      RCLCPP_INFO(node_->get_logger(), "Using SlamwareCorePlatform to process POI");
      rpos::core::PoseEntry pose_entry;
      pose_entry.name = poi.id;
      pose_entry.pose = rpos::core::Pose(rpos::core::Location(poi.pose.x, poi.pose.y), rpos::core::Rotation(poi.pose.theta));
      pose_entry.metadata.set("display_name", poi.name);
      pose_entry.metadata.set("type", poi.type);
      if (!slamkit_platform->editPOI(pose_entry)) {
        RCLCPP_ERROR(node_->get_logger(), "Failed to update POI with ID %s", poi.id.c_str());
        error_msg = "Failed to update POI with ID " + poi.id;
        return false;
      }
    } else {
      RCLCPP_WARN(node_->get_logger(), "SlamwareCorePlatform not available");
      error_msg = "SlamwareCorePlatform not available";
      return false;
    }
  } catch (const rpos::system::detail::ExceptionBase& e) {
    RCLCPP_ERROR(node_->get_logger(), "Failed to update POI: %s", e.toString().c_str());
    error_msg = "Failed to update POI: " + e.toString();
    return false;
  }
  it->second = poi;
  RCLCPP_INFO(node_->get_logger(), "Updated POI with ID %s", poi.id.c_str());
  return true;
}

bool POIManager::clearAllPOIs(std::string & error_msg)
{
  std::lock_guard<std::mutex> lock(pois_mutex_);
  if (pois_.empty()) {
    RCLCPP_INFO(node_->get_logger(), "No POIs to clear");
    return true;
  }

  try {
    std::lock_guard<std::recursive_mutex> lock(*slamkit_platform_mutex_);
    auto slamkit_platform = map_manager_.getSlamwarePlatform();
    if (slamkit_platform) {
      RCLCPP_INFO(node_->get_logger(), "Using SlamwareCorePlatform to process POI");
      if (!slamkit_platform->clearPOIs()) {
        RCLCPP_ERROR(node_->get_logger(), "Failed to clear POIs");
        error_msg = "Failed to clear POIs";
        return false;
      }
    } else {
      RCLCPP_WARN(node_->get_logger(), "SlamwareCorePlatform not available");
      error_msg = "SlamwareCorePlatform not available";
      return false;
    }
  } catch (const rpos::system::detail::ExceptionBase& e) {
    RCLCPP_ERROR(node_->get_logger(), "Failed to clear POIs: %s", e.toString().c_str());
    error_msg = "Failed to clear POIs: " + e.toString();
    return false;
  }
  pois_.clear();
  RCLCPP_INFO(node_->get_logger(), "Cleared all POIs");
  return true;
}

bool POIManager::getPOI(const std::string & id, interfaces::msg::POI & poi) const
{
  std::lock_guard<std::mutex> lock(pois_mutex_);

  auto it = pois_.find(id);
  if (it == pois_.end()) {
    RCLCPP_WARN(node_->get_logger(), "POI with ID %s not found", id.c_str());
    return false;
  }

  poi = it->second;
  return true;
}

void POIManager::getAllPOIs(std::vector<interfaces::msg::POI> & pois) const
{
  std::lock_guard<std::mutex> lock(pois_mutex_);

  pois.clear();
  pois.reserve(pois_.size());
  for (const auto & pair : pois_) {
    pois.push_back(pair.second);
  }
}

void POIManager::addPOICallback(
  const std::shared_ptr<interfaces::srv::AddPOI::Request> request,
  std::shared_ptr<interfaces::srv::AddPOI::Response> response)
{
  try {
    bool success = addPOI(request->poi, response->message);

    if (success) {
      response->success = true;
      response->message = "POI added successfully";
    } else {
      response->success = false;
    }
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    RCLCPP_ERROR(node_->get_logger(), "Exception in addPOICallback: %s", e.what());
  }
}

void POIManager::deletePOICallback(
  const std::shared_ptr<interfaces::srv::RemovePOI::Request> request,
  std::shared_ptr<interfaces::srv::RemovePOI::Response> response)
{
  try {
    bool success = deletePOI(request->id, response->message);

    if (success) {
      response->success = true;
      response->message = "POI deleted successfully";
    } else {
      response->success = false;
    }
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    RCLCPP_ERROR(node_->get_logger(), "Exception in deletePOICallback: %s", e.what());
  }
}

void POIManager::updatePOICallback(
  const std::shared_ptr<interfaces::srv::UpdatePOI::Request> request,
  std::shared_ptr<interfaces::srv::UpdatePOI::Response> response)
{
  try {
    bool success = updatePOI(request->poi, response->message);

    if (success) {
      response->success = true;
      response->message = "POI updated successfully";
    } else {
      response->success = false;
    }
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    RCLCPP_ERROR(node_->get_logger(), "Exception in updatePOICallback: %s", e.what());
  }
}

void POIManager::clearAllPOIsCallback(
  const std::shared_ptr<interfaces::srv::RemoveAllPOI::Request> request,
  std::shared_ptr<interfaces::srv::RemoveAllPOI::Response> response)
{
  try {
    bool success = clearAllPOIs(response->message);

    if (success) {
      response->success = true;
      response->message = "All POIs cleared successfully";
    } else {
      response->success = false;
    }
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    RCLCPP_ERROR(node_->get_logger(), "Exception in clearAllPOIsCallback: %s", e.what());
  }
}

void POIManager::getPOICallback(
  const std::shared_ptr<interfaces::srv::GetPOI::Request> request,
  std::shared_ptr<interfaces::srv::GetPOI::Response> response)
{
  try {
    interfaces::msg::POI poi;
    bool success = getPOI(request->id, poi);

    if (success) {
      response->success = true;
      response->message = "POI retrieved successfully";
      response->poi = poi;
    } else {
      response->success = false;
      response->message = "Failed to retrieve POI: ID not found";
    }
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    response->poi = interfaces::msg::POI(); // Empty POI
    RCLCPP_ERROR(node_->get_logger(), "Exception in getPOICallback: %s", e.what());
  }
}

void POIManager::listPOICallback(
  const std::shared_ptr<interfaces::srv::ListPOI::Request> request,
  std::shared_ptr<interfaces::srv::ListPOI::Response> response)
{
  try {
    std::vector<interfaces::msg::POI> pois;
    getAllPOIs(pois);

    response->success = true;
    response->message = "Retrieved " + std::to_string(pois.size()) + " POIs";
    response->pois = pois;
  } catch (const std::exception& e) {
    response->success = false;
    response->message = std::string("Exception occurred: ") + e.what();
    response->pois.clear();
    RCLCPP_ERROR(node_->get_logger(), "Exception in listPOICallback: %s", e.what());
  }
}

}  // namespace stcm_manager