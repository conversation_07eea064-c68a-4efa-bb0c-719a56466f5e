
#include "map_manager.h"
#include "stcm_manager/srv/get_stcm_file.hpp"
#include "stcm_manager/srv/upload_stcm_file.hpp"
#include "stcm_manager/srv/get_known_area.hpp"
#include "stcm_manager/srv/clear_map.hpp"
#include "stcm_manager/srv/save_map.hpp"
#include <serialization/buffer_stream_adaptor.h>
#include <stcm/composite_map_writer.h>
#include <system/destruct_helper.h>
#include <interfaces/srv/set_slam_mode.hpp>
#include <interfaces/srv/get_slam_mode.hpp>
#include <rclcpp/rclcpp.hpp>
#include <filesystem>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <string_view>
// STCM handling is enabled through MapManager
// Direct STCM includes are handled in map_manager.h

namespace fs = std::filesystem;
static constexpr std::string_view kRobotMapFileName = "robot_map.stcm";
static constexpr std::string_view TEMPORARY_MAP_DIR_PATH = "/tmp/stcm_manager/temporary_map/";

class StcmServiceNode : public rclcpp::Node
{
public:
    StcmServiceNode() : Node("stcm_manager_node")
    {
        // Declare parameters with default values
        this->declare_parameter("map_storage_path", "/opt/rslamware_data/maps/");
        this->declare_parameter("keep_mask_topic", "/keepout_filter_mask");
        this->declare_parameter("speed_limit_area_topic", "/speed_limit");
        this->declare_parameter("map_server_topic", "/map");
        this->declare_parameter("quality_topic", "/localization_quality");
        this->declare_parameter("robot_events_topic", "/robot/events");
        this->declare_parameter("auto_load_map", true);
        this->declare_parameter("max_trajectory_nodes", 10000);
        this->declare_parameter("mapping_speed", 0.6f);
        this->declare_parameter("map_server_type", "cartographer");
        this->declare_parameter("platform_host", "127.0.0.1");
        this->declare_parameter("platform_port", 1445);

        // Get parameters
        storage_path_ = this->get_parameter("map_storage_path").as_string();

        reentrant_group_ = this->create_callback_group(rclcpp::CallbackGroupType::Reentrant);

        // Create services
        if (!fs::exists(storage_path_)) {
            fs::create_directories(storage_path_);
            RCLCPP_INFO(this->get_logger(), "Created storage directory: %s", storage_path_.c_str());
        }

        if (!fs::exists(TEMPORARY_MAP_DIR_PATH)) {
            fs::create_directories(TEMPORARY_MAP_DIR_PATH);
        }
        else{
            //clear temporary directory
            for (const auto& entry : fs::directory_iterator(TEMPORARY_MAP_DIR_PATH)) {
                if(fs::is_regular_file(entry.path()))
                {
                    fs::remove(entry.path());  
                }
            }
        }
        
        // Create services
        get_service_ = this->create_service<stcm_manager::srv::GetStcmFile>(
            "get_stcm_file",
            std::bind(&StcmServiceNode::handleGetStcmFile, this, std::placeholders::_1, std::placeholders::_2),
            rmw_qos_profile_services_default,
            reentrant_group_
        );

        upload_service_ = this->create_service<stcm_manager::srv::UploadStcmFile>(
            "upload_stcm_file",
            std::bind(&StcmServiceNode::handleUploadStcmFile, this, std::placeholders::_1, std::placeholders::_2),
            rmw_qos_profile_services_default,
            reentrant_group_
        );

        known_area_service_ = this->create_service<stcm_manager::srv::GetKnownArea>(
            "get_known_area",
            std::bind(&StcmServiceNode::handleGetKnownArea, this, std::placeholders::_1, std::placeholders::_2),
            rmw_qos_profile_services_default,
            reentrant_group_
        );

        clear_map_service_ = this->create_service<stcm_manager::srv::ClearMap>(
            "clear_map",
            std::bind(&StcmServiceNode::handleClearMap, this, std::placeholders::_1, std::placeholders::_2),
            rmw_qos_profile_services_default,
            reentrant_group_
        );

        save_map_service_ = this->create_service<stcm_manager::srv::SaveMap>(
            "save_map",
            std::bind(&StcmServiceNode::handleSaveMap, this, std::placeholders::_1, std::placeholders::_2),
            rmw_qos_profile_services_default,
            reentrant_group_
        );

        set_slam_mode_service_ = this->create_service<interfaces::srv::SetSLAMMode>(
            "set_slam_mode",
            std::bind(&StcmServiceNode::handleSetSLAMMode, this, std::placeholders::_1, std::placeholders::_2),
            rmw_qos_profile_services_default,
            reentrant_group_
        );

        get_slam_mode_service_ = this->create_service<interfaces::srv::GetSLAMMode>(
            "get_slam_mode",
            std::bind(&StcmServiceNode::handleGetSLAMMode, this, std::placeholders::_1, std::placeholders::_2),
            rmw_qos_profile_services_default,
            reentrant_group_
        );

        RCLCPP_INFO(this->get_logger(), "STCM Service Node started, storage path: %s", storage_path_.c_str());
    }

    /**
     * @brief Initialize MapManager after the node is fully constructed
     */
    void initializeMapManager() {
        // Get configuration parameters
        std::string virtual_wall_topic = this->get_parameter("keep_mask_topic").as_string();
        std::string speed_limit_area_topic = this->get_parameter("speed_limit_area_topic").as_string();
        std::string map_server_topic = this->get_parameter("map_server_topic").as_string();
        std::string quality_topic = this->get_parameter("quality_topic").as_string();
        std::string robot_events_topic = this->get_parameter("robot_events_topic").as_string();
        map_server_type_ = this->get_parameter("map_server_type").as_string();
        std::string platform_host = this->get_parameter("platform_host").as_string();
        int platform_port = this->get_parameter("platform_port").as_int();

        // Initialize MapManager with configuration
        map_manager_ = std::make_unique<rslamware::stcm_manager::MapManager>(
            shared_from_this(), virtual_wall_topic, speed_limit_area_topic, map_server_topic,
            quality_topic, robot_events_topic, map_server_type_, platform_host, platform_port);


        // Initialize map with default map file path (async)
        std::string default_map_file = (fs::path(storage_path_) / kRobotMapFileName).string();
        map_manager_->initialize(default_map_file); 
    }

private:
    // Service handlers
    void handleGetStcmFile(
        const std::shared_ptr<stcm_manager::srv::GetStcmFile::Request> request,
        std::shared_ptr<stcm_manager::srv::GetStcmFile::Response> response);
        
    void handleUploadStcmFile(
        const std::shared_ptr<stcm_manager::srv::UploadStcmFile::Request> request,
        std::shared_ptr<stcm_manager::srv::UploadStcmFile::Response> response);

    void handleGetKnownArea(
        const std::shared_ptr<stcm_manager::srv::GetKnownArea::Request> request,
        std::shared_ptr<stcm_manager::srv::GetKnownArea::Response> response);

    void handleClearMap(
        const std::shared_ptr<stcm_manager::srv::ClearMap::Request> request,
        std::shared_ptr<stcm_manager::srv::ClearMap::Response> response);

    void handleSaveMap(
        const std::shared_ptr<stcm_manager::srv::SaveMap::Request> request,
        std::shared_ptr<stcm_manager::srv::SaveMap::Response> response);

    void handleSetSLAMMode(
        const std::shared_ptr<interfaces::srv::SetSLAMMode::Request> request,
        std::shared_ptr<interfaces::srv::SetSLAMMode::Response> response);

    void handleGetSLAMMode(
        const std::shared_ptr<interfaces::srv::GetSLAMMode::Request> request,
        std::shared_ptr<interfaces::srv::GetSLAMMode::Response> response);

private:
    rclcpp::CallbackGroup::SharedPtr reentrant_group_;
    // Member variables
    rclcpp::Service<stcm_manager::srv::GetStcmFile>::SharedPtr get_service_;
    rclcpp::Service<stcm_manager::srv::UploadStcmFile>::SharedPtr upload_service_;
    rclcpp::Service<stcm_manager::srv::GetKnownArea>::SharedPtr known_area_service_;
    rclcpp::Service<stcm_manager::srv::ClearMap>::SharedPtr clear_map_service_;
    rclcpp::Service<stcm_manager::srv::SaveMap>::SharedPtr save_map_service_;

    rclcpp::Service<interfaces::srv::SetSLAMMode>::SharedPtr set_slam_mode_service_;
    rclcpp::Service<interfaces::srv::GetSLAMMode>::SharedPtr get_slam_mode_service_;

    std::string storage_path_;
    std::vector<std::string> allowed_extensions_;

    // Map manager
    std::unique_ptr<rslamware::stcm_manager::MapManager> map_manager_;

    std::atomic<bool> processing_request_ = false;

    std::string map_server_type_;
};


void StcmServiceNode::handleGetStcmFile(
    const std::shared_ptr<stcm_manager::srv::GetStcmFile::Request> request,
    std::shared_ptr<stcm_manager::srv::GetStcmFile::Response> response)
{
    RCLCPP_INFO(this->get_logger(), "Received get STCM file request");
   
    bool expected = false;
    if(!processing_request_.compare_exchange_strong(expected, true, std::memory_order_acq_rel, std::memory_order_relaxed))
    {
        RCLCPP_INFO(this->get_logger(), "another maprequest is in processing");
        response->success = false;
        response->message = "Another map request is in processing";
        return;
    }
    rpos_common::system::DestructHelper destruct_helper([this]() {
        processing_request_.store(false);
    });
      
    std::shared_ptr<rpos_common::stcm::CompositeMap> composite_map;
    try {
        if (map_server_type_ == "slamkit")
        {
            composite_map = map_manager_->createCompositeMapFromSlamkit(response->message);
            RCLCPP_INFO(this->get_logger(), "Successfully read STCM file from slamkit"); 
        }
        else if (map_server_type_ == "cartographer")
        {
            std::string target_map_dir = storage_path_;
            if(map_manager_->hasMapUpdated())
            {
                target_map_dir = TEMPORARY_MAP_DIR_PATH;
                std::filesystem::path dir_path(TEMPORARY_MAP_DIR_PATH);
                std::string mapfile_base = (dir_path / "map").string();
                std::string mapfile_pbstream = (dir_path / "map.pbstream").string();   
                RCLCPP_INFO(this->get_logger(), "Map has been updated, save map to temporary file");
                if (!map_manager_->savePgmMap(mapfile_base, mapfile_pbstream, response->message, request->require_lite_version)) {
                    response->success = false; 
                    RCLCPP_ERROR(this->get_logger(), "Failed to save map to temporary file: %s", response->message.c_str()); 
                    return;
                }
                RCLCPP_INFO(this->get_logger(), "Successfully save pgm map to temporary directory: %s", dir_path.string().c_str());
            }
            else
            {
                RCLCPP_INFO(this->get_logger(), "Map has not been updated, read STCM file from existing pbstream");
            }
        
            composite_map = map_manager_->createCompositeMapFromPgm(target_map_dir, request->require_lite_version, response->message);
            RCLCPP_INFO(this->get_logger(), "Successfully read STCM file from existing pbstream"); 
        }
        if(!composite_map)
        {
            response->success = false;
            RCLCPP_ERROR(this->get_logger(), response->message.c_str());
            return;
        }
        rpos_common::serialization::BufferStreamAdaptor buffer(&response->file_data); 
        rpos_common::stcm::CompositeMapWriter writer;
        writer.saveStream(buffer, *composite_map);
        response->success = true;
    } catch (const std::exception& e) {
        response->success = false;
        response->message = "get STCM file failure: " + std::string(e.what());
        RCLCPP_ERROR(this->get_logger(), "get STCM file failure: %s", e.what());
    }
}

void StcmServiceNode::handleUploadStcmFile(
    const std::shared_ptr<stcm_manager::srv::UploadStcmFile::Request> request,
    std::shared_ptr<stcm_manager::srv::UploadStcmFile::Response> response)
{
    RCLCPP_INFO(this->get_logger(), "Received upload STCM file request");

    bool expected = false;
    if(!processing_request_.compare_exchange_strong(expected, true, std::memory_order_acq_rel, std::memory_order_relaxed))
    {
        RCLCPP_INFO(this->get_logger(), "another maprequest is in processing");
        response->success = false;
        response->message = "Another map request is in processing";
        return;
    }
    rpos_common::system::DestructHelper destruct_helper([this]() {
        processing_request_.store(false);
    });

    try {
        
        std::string target_file_path = (fs::path(storage_path_) / kRobotMapFileName).string();  
        // Write file
        std::ofstream file(target_file_path, std::ios::binary);
        if (!file.is_open()) {
            response->success = false;
            response->message = "Failed to create file: " + target_file_path;
            RCLCPP_ERROR(this->get_logger(), "%s", response->message.c_str());
            return;
        }

        file.write(reinterpret_cast<const char*>(request->file_data.data()), request->file_data.size());
        file.close();

        response->success = map_manager_->onStcmFileUploaded(target_file_path, response->message);
        if (!response->success) {
            RCLCPP_ERROR(this->get_logger(), "Failed to load uploaded STCM file: %s", response->message.c_str());
            return;
        }

        RCLCPP_INFO(this->get_logger(), "Successfully uploaded file: (%lu bytes)", request->file_data.size());

    } catch (const std::exception& e) {
        response->success = false;
        response->message = "Error uploading file: " + std::string(e.what());
        RCLCPP_ERROR(this->get_logger(), "%s", response->message.c_str());
    }
}


void StcmServiceNode::handleGetKnownArea(
    const std::shared_ptr<stcm_manager::srv::GetKnownArea::Request>,
    std::shared_ptr<stcm_manager::srv::GetKnownArea::Response> response)
{
    try {
        if (!map_manager_) {
            RCLCPP_ERROR(this->get_logger(), "MapManager not initialized");
            // Return zero area on error
            response->known_area.x_min = 0.0f;
            response->known_area.y_min = 0.0f;
            response->known_area.width = 0.0f;
            response->known_area.height = 0.0f;
            return;
        }

        // Get known area directly as Rectangle2D from MapManager
        if (map_manager_->getKnownArea(response->known_area.x_min,
                                      response->known_area.y_min,
                                      response->known_area.width,
                                      response->known_area.height)) {

            RCLCPP_INFO(this->get_logger(), "Known area from map: x_min=%.2f, y_min=%.2f, width=%.2f, height=%.2f",
                       response->known_area.x_min, response->known_area.y_min,
                       response->known_area.width, response->known_area.height);
 
        } else {
            RCLCPP_WARN(this->get_logger(), "Map dimensions not available, returning default area");

            // Return default area when map dimensions are not available
            response->known_area.x_min = 0.0f;
            response->known_area.y_min = 0.0f;
            response->known_area.width = 0.0f;
            response->known_area.height = 0.0f; 
        }

    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "Error getting known area: %s", e.what());
        // Return zero area on error
        response->known_area.x_min = 0.0f;
        response->known_area.y_min = 0.0f;
        response->known_area.width = 0.0f;
        response->known_area.height = 0.0f;
    }
}

void StcmServiceNode::handleClearMap(
    const std::shared_ptr<stcm_manager::srv::ClearMap::Request>,
    std::shared_ptr<stcm_manager::srv::ClearMap::Response> response)
{
    RCLCPP_INFO(this->get_logger(), "Received clear map request");

    bool expected = false;
    if(!processing_request_.compare_exchange_strong(expected, true, std::memory_order_acq_rel, std::memory_order_relaxed))
    {
        RCLCPP_INFO(this->get_logger(), "another maprequest is in processing");
        response->success = false;
        response->message = "Another map request is in processing";
        return;
    }
    rpos_common::system::DestructHelper destruct_helper([this]() {
        processing_request_.store(false);
    });

    response->success = false;
    if(map_manager_->clearMap(response->message))
    {
        response->success = true;
        RCLCPP_INFO(this->get_logger(), "Map cleared successfully");
    }
    else
    {
        response->message = "Failed to clear map";
        RCLCPP_ERROR(this->get_logger(), "Failed to clear map: %s", response->message.c_str());
    }
}

void StcmServiceNode::handleSaveMap(
    const std::shared_ptr<stcm_manager::srv::SaveMap::Request>,
    std::shared_ptr<stcm_manager::srv::SaveMap::Response> response)
{
    RCLCPP_INFO(this->get_logger(), "Received save map request");

    bool expected = false;
    if(!processing_request_.compare_exchange_strong(expected, true, std::memory_order_acq_rel, std::memory_order_relaxed))
    {
        RCLCPP_INFO(this->get_logger(), "another maprequest is in processing");
        response->success = false;
        response->message = "Another map request is in processing";
        return;
    }
    rpos_common::system::DestructHelper destruct_helper([this]() {
        processing_request_.store(false);
    });

    response->success = false;
    response->file_path = "";

    try {
        if (!map_manager_) {
            response->message = "MapManager not initialized";
            RCLCPP_ERROR(this->get_logger(), "MapManager not initialized");
            return;
        }

        // Generate fixed file path: storage_path + kRobotMapFileName
        std::string target_file_path = (fs::path(storage_path_) / kRobotMapFileName).string();

        if (map_manager_->saveMap(target_file_path, response->message)) {
            response->success = true;
            response->file_path = target_file_path;
            RCLCPP_INFO(this->get_logger(), "Map saved successfully as: %s", target_file_path.c_str());
        } else {
            RCLCPP_ERROR(this->get_logger(), "Failed to save map to: %s, %s", target_file_path.c_str(),response->message.c_str());
        }

    } catch (const std::exception& e) {
        response->message = "Error saving map: " + std::string(e.what());
        RCLCPP_ERROR(this->get_logger(), response->message.c_str());
    }
}

void StcmServiceNode::handleSetSLAMMode(
    const std::shared_ptr<interfaces::srv::SetSLAMMode::Request> request,
    std::shared_ptr<interfaces::srv::SetSLAMMode::Response> response)
{
    RCLCPP_INFO(this->get_logger(), "Received set slam mode request");
    if(request->mode != "mapping" && request->mode != "localization"){
        response->success = false;
        response->message = "Invalid slam mode";
        return;
    }
    bool expected = false;
    if(!processing_request_.compare_exchange_strong(expected, true, std::memory_order_acq_rel, std::memory_order_relaxed))
    {
        RCLCPP_INFO(this->get_logger(), "another maprequest is in processing");
        response->success = false;
        response->message = "Another map request is in processing";
        return;
    }
    rpos_common::system::DestructHelper destruct_helper([this]() {
        processing_request_.store(false);
    });
    
    response->success = map_manager_->setSLAMMode(request->mode, request->reload_map, response->message); 
}

void StcmServiceNode::handleGetSLAMMode(
    const std::shared_ptr<interfaces::srv::GetSLAMMode::Request> request,
    std::shared_ptr<interfaces::srv::GetSLAMMode::Response> response)
{  
    response->success = map_manager_->getSLAMMode(response->mode, response->message);
}

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<StcmServiceNode>();

    // Initialize MapManager after node is fully constructed
    node->initializeMapManager();
    rclcpp::executors::MultiThreadedExecutor executor;
    executor.add_node(node);
    executor.spin();
    rclcpp::shutdown();
    return 0;
}
