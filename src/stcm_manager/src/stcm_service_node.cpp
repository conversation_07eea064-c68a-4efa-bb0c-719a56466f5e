#include <rclcpp/rclcpp.hpp>
#include <filesystem>
#include <fstream>
#include <sstream>
#include <iomanip>

#include "map_manager.h"
#include "stcm_manager/srv/get_stcm_file.hpp"
#include "stcm_manager/srv/upload_stcm_file.hpp"
#include "stcm_manager/srv/get_known_area.hpp"
#include "stcm_manager/srv/clear_map.hpp"
#include "stcm_manager/srv/save_map.hpp"
#include "stcm_manager/temp_directory_manager.hpp" 

// STCM handling is enabled through MapManager
// Direct STCM includes are handled in map_manager.h

namespace fs = std::filesystem;
static const char* kRobotMapFileName = "robot_map.stcm";

class StcmServiceNode : public rclcpp::Node
{
public:
    StcmServiceNode() : Node("stcm_manager_node")
    {
        // Declare parameters with default values
        this->declare_parameter("map_storage_path", "/home/<USER>/maps/");
        this->declare_parameter("keep_mask_topic", "/keepout_filter_mask");
        this->declare_parameter("map_server_topic", "/map");

        // Get parameters
        storage_path_ = this->get_parameter("map_storage_path").as_string();
        // Initialize temp directory manager
        temp_dir_manager_ = std::make_unique<stcm_manager::TempDirectoryManager>("/tmp/stcm_manager");

        // Create services
        if (!fs::exists(storage_path_)) {
            fs::create_directories(storage_path_);
            RCLCPP_INFO(this->get_logger(), "Created storage directory: %s", storage_path_.c_str());
        }
        
        // Create services
        get_service_ = this->create_service<stcm_manager::srv::GetStcmFile>(
            "get_stcm_file",
            std::bind(&StcmServiceNode::handleGetStcmFile, this, std::placeholders::_1, std::placeholders::_2)
        );

        upload_service_ = this->create_service<stcm_manager::srv::UploadStcmFile>(
            "upload_stcm_file",
            std::bind(&StcmServiceNode::handleUploadStcmFile, this, std::placeholders::_1, std::placeholders::_2)
        );

        known_area_service_ = this->create_service<stcm_manager::srv::GetKnownArea>(
            "get_known_area",
            std::bind(&StcmServiceNode::handleGetKnownArea, this, std::placeholders::_1, std::placeholders::_2)
        );

        clear_map_service_ = this->create_service<stcm_manager::srv::ClearMap>(
            "clear_map",
            std::bind(&StcmServiceNode::handleClearMap, this, std::placeholders::_1, std::placeholders::_2)
        );

        save_map_service_ = this->create_service<stcm_manager::srv::SaveMap>(
            "save_map",
            std::bind(&StcmServiceNode::handleSaveMap, this, std::placeholders::_1, std::placeholders::_2)
        );

        // Initialize virtual wall manager
        virtual_wall_manager_ = std::make_unique<stcm_manager::VirtualWallManager>(shared_from_this(), "virtual_wall_mask");
        if (!virtual_wall_manager_->initialize()) {
            RCLCPP_ERROR(this->get_logger(), "Failed to initialize virtual wall manager");
        }

        RCLCPP_INFO(this->get_logger(), "STCM Service Node started");
        RCLCPP_INFO(this->get_logger(), "Storage path: %s", storage_path_.c_str());
    }

    /**
     * @brief Initialize MapManager after the node is fully constructed
     */
    void initializeMapManager() {
        // Get configuration parameters
        std::string virtual_wall_topic = this->get_parameter("keep_mask_topic").as_string();
        std::string map_server_topic = this->get_parameter("map_server_topic").as_string();

        // Initialize MapManager with configuration
        map_manager_ = std::make_shared<rslamware::stcm_manager::MapManager>(
            shared_from_this(), virtual_wall_topic, map_server_topic);

        // Initialize map with default map file path (async)
        std::string default_map_file = (fs::path(storage_path_) / kRobotMapFileName).string();
        map_manager_->initialize(default_map_file);

        RCLCPP_INFO(this->get_logger(), "MapManager async initialization started");
    }

private:
    // Service handlers
    void handleGetStcmFile(
        const std::shared_ptr<stcm_manager::srv::GetStcmFile::Request> request,
        std::shared_ptr<stcm_manager::srv::GetStcmFile::Response> response);
        
    void handleUploadStcmFile(
        const std::shared_ptr<stcm_manager::srv::UploadStcmFile::Request> request,
        std::shared_ptr<stcm_manager::srv::UploadStcmFile::Response> response);

    void handleGetKnownArea(
        const std::shared_ptr<stcm_manager::srv::GetKnownArea::Request> request,
        std::shared_ptr<stcm_manager::srv::GetKnownArea::Response> response);

    void handleClearMap(
        const std::shared_ptr<stcm_manager::srv::ClearMap::Request> request,
        std::shared_ptr<stcm_manager::srv::ClearMap::Response> response);

    void handleSaveMap(
        const std::shared_ptr<stcm_manager::srv::SaveMap::Request> request,
        std::shared_ptr<stcm_manager::srv::SaveMap::Response> response);


    // Member variables
    rclcpp::Service<stcm_manager::srv::GetStcmFile>::SharedPtr get_service_;
    rclcpp::Service<stcm_manager::srv::UploadStcmFile>::SharedPtr upload_service_;
    rclcpp::Service<stcm_manager::srv::GetKnownArea>::SharedPtr known_area_service_;
    rclcpp::Service<stcm_manager::srv::ClearMap>::SharedPtr clear_map_service_;
    rclcpp::Service<stcm_manager::srv::SaveMap>::SharedPtr save_map_service_;

    std::string storage_path_;
    std::vector<std::string> allowed_extensions_;

    // Map manager
    std::shared_ptr<rslamware::stcm_manager::MapManager> map_manager_;
    
    // Virtual wall manager
    std::unique_ptr<stcm_manager::VirtualWallManager> virtual_wall_manager_;
    
    // Temp directory manager
    std::unique_ptr<stcm_manager::TempDirectoryManager> temp_dir_manager_;
};

void StcmServiceNode::handleGetStcmFile(
    const std::shared_ptr<stcm_manager::srv::GetStcmFile::Request> request,
    std::shared_ptr<stcm_manager::srv::GetStcmFile::Response> response)
{
    RCLCPP_INFO(this->get_logger(), "Received get STCM file request");
 
    // Create a temporary directory for this operation
    stcm_manager::TempDirectoryGuard temp_guard(*temp_dir_manager_, "get_stcm_");
    std::string temp_dir = temp_guard.getPath();
    std::string target_file = temp_guard.getFilePath("robot_map", "stcm");
    
    RCLCPP_DEBUG(this->get_logger(), "Using temporary directory: %s", temp_dir.c_str()); 
    
    try {
        // Save map to temporary file
        if (!map_manager_->saveMap(target_file)) {
            response->success = false;
            response->message = "Failed to create stcm file";
            RCLCPP_ERROR(this->get_logger(), "Failed to create stcm file: %s", target_file.c_str());
            return;
        } 

        // Open file in binary mode
        std::ifstream file(target_file, std::ios::binary | std::ios::ate);
        if (!file.is_open()) {
            response->success = false;
            response->message = "Failed to open stcm file";
            RCLCPP_ERROR(this->get_logger(), "Failed to open STCM file: %s", target_file.c_str());
            return;
        }

        // Get file size
        std::streamsize file_size = file.tellg();
        file.seekg(0, std::ios::beg);

        // Resize response data vector and read file content
        response->file_data.resize(file_size);
        if (!file.read(reinterpret_cast<char*>(response->file_data.data()), file_size)) {
            response->success = false;
            response->message = "Failed to read stcm file";
            RCLCPP_ERROR(this->get_logger(), "Failed to read STCM file: %s", target_file.c_str());
            return;
        }

        file.close();
        response->success = true;

        RCLCPP_INFO(this->get_logger(), "Successfully read STCM file (%ld bytes)", file_size);

    } catch (const std::exception& e) {
        response->success = false;
        response->message = "Error reading STCM file: " + std::string(e.what());
        RCLCPP_ERROR(this->get_logger(), "Error reading STCM file %s: %s",
                    target_file.c_str(), e.what());
    }
    
    // TempDirectoryGuard destructor will automatically clean up the temporary directory
}

void StcmServiceNode::handleUploadStcmFile(
    const std::shared_ptr<stcm_manager::srv::UploadStcmFile::Request> request,
    std::shared_ptr<stcm_manager::srv::UploadStcmFile::Response> response)
{
    RCLCPP_INFO(this->get_logger(), "Received upload STCM file request");

    try {
        
        std::string target_file_path = (fs::path(storage_path_) / kRobotMapFileName).string();  
        // Write file
        std::ofstream file(target_file_path, std::ios::binary);
        if (!file.is_open()) {
            response->success = false;
            response->message = "Failed to create file: " + target_file_path;
            RCLCPP_ERROR(this->get_logger(), "%s", response->message.c_str());
            return;
        }

        file.write(reinterpret_cast<const char*>(request->file_data.data()), request->file_data.size());
        file.close();

        response->success = map_manager_->onStcmFileUploaded(target_file_path, response->message);
        if (!response->success) {
            RCLCPP_ERROR(this->get_logger(), "Failed to load uploaded STCM file: %s", response->message.c_str());
            return;
        }

        RCLCPP_INFO(this->get_logger(), "Successfully uploaded file: (%lu bytes)", request->file_data.size());

    } catch (const std::exception& e) {
        response->success = false;
        response->message = "Error uploading file: " + std::string(e.what());
        RCLCPP_ERROR(this->get_logger(), "%s", response->message.c_str());
    }
}


void StcmServiceNode::handleGetKnownArea(
    const std::shared_ptr<stcm_manager::srv::GetKnownArea::Request>,
    std::shared_ptr<stcm_manager::srv::GetKnownArea::Response> response)
{
    try {
        if (!map_manager_) {
            RCLCPP_ERROR(this->get_logger(), "MapManager not initialized");
            // Return zero area on error
            response->known_area.x_min = 0.0f;
            response->known_area.y_min = 0.0f;
            response->known_area.width = 0.0f;
            response->known_area.height = 0.0f;
            return;
        }

        // Get known area directly as Rectangle2D from MapManager
        if (map_manager_->getKnownArea(response->known_area.x_min,
                                      response->known_area.y_min,
                                      response->known_area.width,
                                      response->known_area.height)) {

            RCLCPP_INFO(this->get_logger(), "Known area from map: x_min=%.2f, y_min=%.2f, width=%.2f, height=%.2f",
                       response->known_area.x_min, response->known_area.y_min,
                       response->known_area.width, response->known_area.height);
 
        } else {
            RCLCPP_WARN(this->get_logger(), "Map dimensions not available, returning default area");

            // Return default area when map dimensions are not available
            response->known_area.x_min = 0.0f;
            response->known_area.y_min = 0.0f;
            response->known_area.width = 0.0f;
            response->known_area.height = 0.0f; 
        }

    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "Error getting known area: %s", e.what());
        // Return zero area on error
        response->known_area.x_min = 0.0f;
        response->known_area.y_min = 0.0f;
        response->known_area.width = 0.0f;
        response->known_area.height = 0.0f;
    }
}

void StcmServiceNode::handleClearMap(
    const std::shared_ptr<stcm_manager::srv::ClearMap::Request>,
    std::shared_ptr<stcm_manager::srv::ClearMap::Response> response)
{
    RCLCPP_INFO(this->get_logger(), "Received clear map request");

    response->success = false;
    try {
        if (map_manager_) {
            if (map_manager_->clearMap()) {
                response->success = true;
                RCLCPP_INFO(this->get_logger(), "Map cleared successfully");
            } else {
                response->message = "Failed to clear map";
                RCLCPP_ERROR(this->get_logger(), "Failed to clear map");
            }
        } else {
            response->message = "MapManager not initialized";
            RCLCPP_ERROR(this->get_logger(), "MapManager not initialized");
        }

    } catch (const std::exception& e) {
        response->message = "Error clearing map: " + std::string(e.what());
        RCLCPP_ERROR(this->get_logger(), "Error clearing map: %s", e.what());
    }
}

void StcmServiceNode::handleSaveMap(
    const std::shared_ptr<stcm_manager::srv::SaveMap::Request>,
    std::shared_ptr<stcm_manager::srv::SaveMap::Response> response)
{
    RCLCPP_INFO(this->get_logger(), "Received save map request");

    response->success = false;
    response->file_path = "";

    try {
        if (!map_manager_) {
            response->message = "MapManager not initialized";
            RCLCPP_ERROR(this->get_logger(), "MapManager not initialized");
            return;
        }

        // Generate fixed file path: storage_path + kRobotMapFileName
        std::string target_file_path = (fs::path(storage_path_) / kRobotMapFileName).string();

        if (map_manager_->saveMap(target_file_path)) {
            response->success = true;
            response->file_path = target_file_path;
            RCLCPP_INFO(this->get_logger(), "Map saved successfully as: %s", target_file_path.c_str());
        } else {
            response->message = "Failed to save map to: " + target_file_path;
            RCLCPP_ERROR(this->get_logger(), "Failed to save map to: %s", target_file_path.c_str());
        }

    } catch (const std::exception& e) {
        response->message = "Error saving map: " + std::string(e.what());
        RCLCPP_ERROR(this->get_logger(), "Error saving map: %s", e.what());
    }
}

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<StcmServiceNode>();

    // Initialize MapManager after node is fully constructed
    node->initializeMapManager();

    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
