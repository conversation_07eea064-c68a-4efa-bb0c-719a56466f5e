#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution, PythonExpression
from launch.conditions import IfCondition, UnlessCondition
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    ENCRYPTION_MODE=False

    # Declare launch arguments
    mode_arg = DeclareLaunchArgument(
        'mode',
        default_value='real',
        description='Launch mode: real or simulation'
    )

    # Determine config file path based on mode
    def get_config_file_path():
        mode = LaunchConfiguration('mode')

        if not ENCRYPTION_MODE:
            # Use PythonExpression to conditionally select config file
            return PythonExpression([
                "'", get_package_share_directory('simulator'), "/params/rslamware.yaml' if '", mode, "' == 'simulation' else '",
                get_package_share_directory('rslamware_bringup'), "/config/rslamware.yaml'"
            ])
        else:
            launch_dir = os.path.dirname(os.path.realpath(__file__))
            config_dir = os.path.join(launch_dir, '..', 'config')
            config_dir = os.path.abspath(config_dir)
            return os.path.join(config_dir, 'combined_config.yaml')

    config_file_path = get_config_file_path()

    # Resolve map storage path based on mode
    def get_resolved_map_path():
        mode = LaunchConfiguration('mode')
        simulator_map_path = os.path.join(get_package_share_directory('simulator'), 'map')
        bringup_map_path = "/opt/rslamware_data/maps/"

        return PythonExpression([
            "'", simulator_map_path, "' if '", mode, "' == 'simulation' else '", bringup_map_path, "'"
        ])

    resolved_map_path = get_resolved_map_path()

    # STCM service node
    stcm_service_node = Node(
        package='stcm_manager',
        executable='stcm_manager_node',
        name='stcm_manager_node',
        parameters=[
            config_file_path,
            {
                # Override map_storage_path in simulation mode
                'map_storage_path': resolved_map_path
            }
        ],
        output='screen'
    )
    
    return LaunchDescription([
        mode_arg,
        stcm_service_node
    ])
